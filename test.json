本项目是通用的deep research，我想定向的从市场营销和自媒体宣传的角度针对用户的小红书账号进行行业热点调研、品牌生态、变现策略、品牌影响力分析、竞争对手和针对性的流量增长方案。输入的信息是结构化的，示例如下:

{
    "accountInfo": {
        "nickname": "一只酸奶牛",
        "accountId": "xxxxxxxx",
        "platform": "rednote",
        "desc": "一只酸奶牛ayogurtcow\\n小红书唯一官方账号\\n不定期宠粉福利派发～\\n健康   美味   时尚",
        "follows": 107,
        "liked": 85538,
        "posts": 25,
        "collected": 763,
        "comments": 196,
        "followers": 30
    },
    "noteList": [
        {
            "title": "今天好开心呀，给大家发周边~",
            "desc": "[烟花R][庆祝R]今天六一，牛牛知道你们想要什么~\\n那就是开心、开心、开心\\n如果没有时间大笑不如就发发呆\\n让烦恼放空，让快乐进入✨\\n让牛牛给大家发开心周边💝\\n一只酸奶牛x发呆的小女孩——联名限定\\n",
            "collected_count": 31,
            "likes": 52,
            "collected": 320,
            "createTimeMs": *************,
            "share_count": 11,
            "comments_count": 59,
            "comments_list": [
                {
                    "content": "终于喝到羽衣了！╮(╯▽╰)╭[请升级到App最新版本查看图片评论]"
                },
                {
                    "content": "最开心的就是参观到了骆馅饼的展览《别担心，我很开心》！！[请升级到App最新版本查看图片评论]"
                }
            ]
        }
    ],
    "industry": "Food & Beverage",
    "marketingGoal": "引流私域"
}

我写了一个example/redbook_marketing_analysis.py，请帮我看看是否符合要求。小红书的信息不太方便爬取，可以参考https://github.com/NanmiCoder/MediaCrawler 这个库