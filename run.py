# -*- coding: utf-8 -*-
from gevent import monkey

monkey.patch_all()

from service import app
from config import BIND_IP, PORT

# health
from service.api import health_blueprint
app.register_blueprint(health_blueprint, url_prefix="/")

# Rewrite
from service.api import rewrite_blueprint, analyze_blueprint
app.register_blueprint(rewrite_blueprint, url_prefix="/create")
app.register_blueprint(analyze_blueprint, url_prefix="/analyze")

# Related Search
from service.api import search_blueprint
app.register_blueprint(search_blueprint, url_prefix="/search")

# Tool
from service.api import tool_blueprint
app.register_blueprint(tool_blueprint, url_prefix="/tool")

# Sales
from service.api import sales_blueprint
app.register_blueprint(sales_blueprint, url_prefix="/sales")

if __name__ == '__main__':
    app.run(host=BIND_IP, port=PORT, debug=False)
