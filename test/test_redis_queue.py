import asyncio
import redis.asyncio as redis
import json
import time
import ssl
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, List, Optional, Any
import argparse
from dataclasses import dataclass, asdict
from enum import Enum
import webbrowser
from datetime import datetime

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from task import REDIS_CLUSTER_CONFIG, ENV

class ServiceType(Enum):
    """支持的服务类型"""
    COVER_GEN = "cover_gen"
    DIAGNOSIS = "diagnosis"
    STRATEGY = "strategy"
    REVIEW = "review"

@dataclass
class QueueConfig:
    """队列配置"""
    input_queue: str
    output_queue: str
    service_type: ServiceType
    description: str

# 服务配置映射
SERVICE_CONFIGS = {
    ServiceType.COVER_GEN: QueueConfig(
        input_queue=f"{ENV.lower()}:q:generate:cover:request",
        output_queue=f"{ENV.lower()}:q:generate:cover:response",
        service_type=ServiceType.COVER_GEN,
        description="封面生成服务"
    ),
    ServiceType.DIAGNOSIS: QueueConfig(
        input_queue=f"{ENV.lower()}:q:diagnosis:request",
        output_queue=f"{ENV.lower()}:q:diagnosis:response",
        service_type=ServiceType.DIAGNOSIS,
        description="账号诊断服务"
    ),
    ServiceType.STRATEGY: QueueConfig(
        input_queue=f"{ENV.lower()}:q:generate:report:socialmedia:request",
        output_queue=f"{ENV.lower()}:q:generate:report:socialmedia:response",
        service_type=ServiceType.STRATEGY,
        description="策略生成服务"
    ),
    ServiceType.REVIEW: QueueConfig(
        input_queue=f"{ENV.lower()}:q:generate:report:operation:request",
        output_queue=f"{ENV.lower()}:q:generate:report:operation:response",
        service_type=ServiceType.REVIEW,
        description="运营复盘服务"
    )
}

class RedisQueueTester:
    """Redis队列测试器"""
    
    def __init__(self, service_type: ServiceType, output_dir: str = "test_results", auto_open: bool = True):
        self.service_type = service_type
        self.config = SERVICE_CONFIGS[service_type]
        self.redis_pool = None
        self.logger = self._setup_logging()
        self.output_dir = output_dir
        self.auto_open = auto_open
        self._ensure_output_dir()
        
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
            self.logger.info(f"创建输出目录: {os.path.abspath(self.output_dir)}")
        
    def _setup_logging(self):
        """设置日志系统"""
        os.makedirs('logs', exist_ok=True)
        
        logger = logging.getLogger(f'redis_queue_tester_{self.service_type.value}')
        logger.setLevel(logging.INFO)
        logger.handlers.clear()
        
        file_handler = RotatingFileHandler(
            f'logs/redis_queue_test_{self.service_type.value}.log',
            maxBytes=10*1024*1024,
            backupCount=3,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger

    def _get_formatted_filename(self, task_id: str, file_type: str, extension: str = "html") -> str:
        """生成格式化的文件名"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        service_name = self.service_type.value
        filename = f"{service_name}_{file_type}_{task_id}_{timestamp}.{extension}"
        return os.path.join(self.output_dir, filename)

    def _save_file(self, content: str, filename: str, description: str) -> bool:
        """保存文件的通用方法"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(content)
            
            abs_path = os.path.abspath(filename)
            print(f"✅ {description}已保存到: {abs_path}")
            self.logger.info(f"{description}已保存到: {abs_path}")
            
            # 如果是HTML文件且启用自动打开，则在浏览器中打开
            if filename.endswith('.html') and self.auto_open:
                try:
                    webbrowser.open(f'file://{abs_path}')
                    print(f"🌐 已在浏览器中打开: {abs_path}")
                except Exception as e:
                    print(f"⚠️  无法自动打开浏览器: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存{description}失败: {e}")
            self.logger.error(f"保存{description}失败: {e}")
            return False

    def _save_json_result(self, data: Dict, task_id: str, description: str) -> bool:
        """保存JSON结果"""
        filename = self._get_formatted_filename(task_id, "result", "json")
        content = json.dumps(data, ensure_ascii=False, indent=2)
        return self._save_file(content, filename, f"{description}JSON结果")

    async def create_redis_connection(self):
        """创建Redis连接"""
        try:
            # 更新Redis配置
            redis_config = REDIS_CLUSTER_CONFIG.copy()
            redis_config.update({
                'ssl_cert_reqs': ssl.CERT_NONE,
                'ssl_ca_certs': None,
                'socket_connect_timeout': 10,
                'socket_timeout': 10,
                'max_connections': 20
            })
            
            self.redis_pool = redis.RedisCluster(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config['password'],
                ssl=redis_config['ssl'],
                ssl_cert_reqs=redis_config['ssl_cert_reqs'],
                ssl_ca_certs=redis_config['ssl_ca_certs'],
                decode_responses=redis_config['decode_responses']
            )
            
            # 测试连接
            await self.redis_pool.ping()
            self.logger.info("Redis连接创建成功")
            return True
            
        except Exception as e:
            self.logger.error(f"Redis连接失败: {str(e)}")
            return False

    async def close_redis_connection(self):
        """关闭Redis连接"""
        if self.redis_pool:
            await self.redis_pool.aclose()
            self.logger.info("Redis连接已关闭")

    async def send_task(self, task_data: Dict) -> bool:
        """发送任务到Redis队列"""
        try:
            # 双重序列化（模拟实际服务的行为）
            task_json = json.dumps(task_data, ensure_ascii=False)
            final_json = json.dumps(task_json, ensure_ascii=False)
            
            await self.redis_pool.rpush(self.config.input_queue, final_json)
            
            queue_length = await self.redis_pool.llen(self.config.input_queue)
            self.logger.info(f"任务已发送到队列 {self.config.input_queue}，队列长度: {queue_length}")
            
            # 输出发送的数据
            print(f"\n{'='*20} 发送的任务数据 {'='*20}")
            print(json.dumps(task_data, ensure_ascii=False, indent=2))
            print(f"{'='*60}\n")
            
            return True
            
        except Exception as e:
            self.logger.error(f"发送任务失败: {str(e)}")
            return False

    async def wait_for_response(self, timeout: int = 500) -> Optional[Dict]:
        """等待响应 - 持续监听直到收到FINISH或FAILED状态"""
        self.logger.info(f"等待来自队列 {self.config.output_queue} 的响应，超时时间: {timeout}秒")
        
        start_time = time.time()
        final_response = None
        response_count = 0
        last_progress = -1
        last_progress_time = start_time
        stuck_threshold = 120  # 2分钟没有进度变化视为卡住
        
        try:
            while True:
                # 计算剩余超时时间
                elapsed = time.time() - start_time
                remaining_timeout = max(1, timeout - elapsed)
                
                if elapsed >= timeout:
                    self.logger.error(f"等待响应超时 ({timeout}秒)")
                    return {"error": "响应超时"}
                
                try:
                    result = await asyncio.wait_for(
                        self.redis_pool.blpop(self.config.output_queue, timeout=int(remaining_timeout)),
                        timeout=remaining_timeout + 5
                    )
                    
                    if result:
                        _, response_json = result
                        response_count += 1
                        self.logger.info(f"收到第{response_count}个响应")
                        
                        # 双重反序列化
                        try:
                            # 第一次反序列化
                            step1 = json.loads(response_json)
                            if isinstance(step1, str):
                                # 第二次反序列化
                                response_data = json.loads(step1)
                            else:
                                response_data = step1
                            
                            # 打印中间状态
                            task_info = response_data.get("taskInfo", {})
                            status = task_info.get("aiTaskStatus", "UNKNOWN")
                            progress = task_info.get("aiTaskProgress", 0)
                            msg_cn = task_info.get("aiTaskMsgCN", "")
                            current_time = time.time()
                            
                            # 检查进度是否有变化
                            if progress != last_progress:
                                last_progress = progress
                                last_progress_time = current_time
                            
                            # 检查是否进度卡住
                            stuck_time = current_time - last_progress_time
                            stuck_indicator = ""
                            if stuck_time > stuck_threshold:
                                stuck_indicator = f" ⚠️ 进度卡住 {stuck_time:.0f}秒"
                            elif stuck_time > stuck_threshold / 2:
                                stuck_indicator = f" ⏳ 进度停滞 {stuck_time:.0f}秒"
                            
                            print(f"\n📊 状态更新 #{response_count}:")
                            print(f"   状态: {status}")
                            print(f"   进度: {progress}%{stuck_indicator}")
                            print(f"   消息: {msg_cn}")
                            print(f"   时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
                            
                            # 如果进度卡住太久，发出警告
                            if stuck_time > stuck_threshold:
                                self.logger.warning(f"⚠️ 任务进度可能卡住：{stuck_time:.0f}秒没有进度变化 (当前进度: {progress}%)")
                            
                            # 检查是否为最终状态
                            if status in ["FINISH", "FAILED"]:
                                self.logger.info(f"收到最终状态: {status}")
                                final_response = response_data
                                break
                            else:
                                # 中间状态，继续等待
                                self.logger.info(f"收到中间状态: {status} ({progress}%)，继续等待...")
                                continue
                                
                        except json.JSONDecodeError as e:
                            self.logger.error(f"响应JSON解析失败: {str(e)}")
                            return {"error": "响应格式错误", "raw_response": response_json}
                    else:
                        self.logger.warning("未收到响应")
                        continue
                        
                except asyncio.TimeoutError:
                    # 单次等待超时，检查总体超时
                    continue
                    
        except Exception as e:
            self.logger.error(f"等待响应时发生错误: {str(e)}")
            return {"error": f"等待响应错误: {str(e)}"}
        
        if final_response:
            print(f"\n🎉 任务完成！共收到 {response_count} 个状态更新")
            return final_response
        else:
            self.logger.error("未收到最终响应")
            return {"error": "未收到最终响应"}

    def get_mock_data(self, custom_data: Optional[Dict] = None) -> Dict:
        """获取模拟数据"""
        timestamp = int(time.time())
        
        if custom_data:
            return custom_data
            
        # 根据服务类型生成不同的模拟数据
        if self.service_type == ServiceType.COVER_GEN:
            return {
                "taskInfo": {
                    "taskId": f"test_cover_{timestamp}"
                },
                "style": "奢华自然意境风",
                "content": "打工人摸鱼指南\n教你如何在工作中合理休息\n提高工作效率的小技巧",
                "mediaList": [
                    {
                        "mediaType": "image",
                        "mediaUrl": "https://example.com/image1.jpg",
                        "mediaPath": "/path/to/image1.jpg"
                    }
                ]
            }
            
        elif self.service_type == ServiceType.DIAGNOSIS:
            return {
                "taskInfo": {
                    "env": "dev",
                    "taskId": ***********
                },
                "accountInfo": {
                    "nickname": "一只酸奶牛",
                    "desc": "一只酸奶牛ayogurtcow\\n小红书唯一官方账号\\n不定期宠粉福利派发～\\n健康   美味   时尚",
                    "follows": 107,
                    "liked": 85538,
                    "red_official_verify_content": "生鲜乳品"
                },
                "noteList": [
                    {
                    "title": "今天好开心呀，给大家发周边~",
                    "desc": "[烟花R][庆祝R]今天六一，牛牛知道你们想要什么~\\n那就是开心、开心、开心\\n如果没有时间大笑不如就发发呆\\n让烦恼放空，让快乐进入✨\\n让牛牛给大家发开心周边💝\\n一只酸奶牛x发呆的小女孩——联名限定\\n",
                    "collected_count": 31,
                    "likes": 52,
                    "share_count": 11,
                    "comments_count": 59,
                    "comments_list": [
                        {
                        "content": "终于喝到羽衣了！╮(╯▽╰)╭[请升级到App最新版本查看图片评论]"
                        },
                        {
                        "content": "最开心的就是参观到了骆馅饼的展览《别担心，我很开心》！！[请升级到App最新版本查看图片评论]"
                        }
                    ]
                    }
                ],
                "industry": "Food & Beverage",
                "marketingGoal": "引流私域"
                }
            
        elif self.service_type == ServiceType.STRATEGY:
            return {
                "taskInfo": {
                    "env": "dev",
                    "taskId": f"test_strategy_{timestamp}",
                    "socialMediaReportID": 1
                },
                "socialMediaReportData": {
                    "marketingObject": "引流私域",
                    "companyName": "Tanka",
                    "companyIndustry": "Technology",
                    "companyBrandPositioning": "The first messenger with AI long-term memory",
                    "companyCoreCompetency": "AI-assisted smart replies integrated into communication tools",
                    "companyTargetAudience": "Teams and businesses looking for enhanced communication tools",
                    "companyBrandVision": "AI communication tool that connect all"
                }
            }
            
        elif self.service_type == ServiceType.REVIEW:
            return {
                "taskInfo": {
                    "env": "dev",
                    "taskId": f"test_review_{timestamp}"
                },
                "accounts": [
                    {
                        "accountInfo": {
                            "nickname": "一只酸奶牛",
                            "accountId": "yizhisuannainiu",
                            "platform": "rednote",
                            "desc": "一只酸奶牛ayogurtcow\\n小红书唯一官方账号\\n不定期宠粉福利派发～\\n健康   美味   时尚",
                            "follows": 107,
                            "liked": 85538,
                            "posts": 25,
                            "collected": 763,
                            "comments": 196,
                            "followers": 30000
                        },
                        "noteList": [
                            {
                                "title": "今天好开心呀，给大家发周边~",
                                "desc": "[烟花R][庆祝R]今天六一，牛牛知道你们想要什么~\\n那就是开心、开心、开心\\n如果没有时间大笑不如就发发呆\\n让烦恼放空，让快乐进入✨\\n让牛牛给大家发开心周边💝\\n一只酸奶牛x发呆的小女孩——联名限定\\n",
                                "collected_count": 31,
                                "likes": 52,
                                "collected": 320,
                                "createTimeMs": 1711862400000,
                                "share_count": 11,
                                "comments_count": 59,
                                "comments_list": [
                                    {"content": "终于喝到羽衣了！╮(╯▽╰)╭"},
                                    {"content": "最开心的就是参观到了骆馅饼的展览《别担心，我很开心》！！"}
                                ]
                            }
                        ],
                        "industry": "Food & Beverage",
                        "marketingGoal": "引流私域"
                    }
                ],
                "scheduleInfo": {
                    "scheduleSuggestions": {
                        "generatedScheduleCount": 10,
                        "completedScheduleCount": 5,
                        "generatedSuggestionCount": 20,
                        "completedSuggestionCount": 15
                    }
                }
            }
        
        return {}

    async def run_test(self, custom_data: Optional[Dict] = None, wait_for_response: bool = True, timeout: int = 300):
        """运行测试"""
        self.logger.info(f"开始测试 {self.config.description}")
        
        # 创建Redis连接
        if not await self.create_redis_connection():
            self.logger.error("无法创建Redis连接，测试终止")
            return
        
        try:
            # 获取测试数据
            task_data = self.get_mock_data(custom_data)
            
            # 发送任务
            if await self.send_task(task_data):
                self.logger.info("任务发送成功")
                
                if wait_for_response:
                    # 等待响应
                    response = await self.wait_for_response(timeout)
                    
                    if response:
                        self._process_response(response, task_data.get("taskInfo", {}).get("taskId", "unknown"))
                    else:
                        self.logger.error("未收到有效响应")
                else:
                    self.logger.info("已发送任务，不等待响应")
            else:
                self.logger.error("任务发送失败")
                
        finally:
            await self.close_redis_connection()

    def _process_response(self, response: Dict, task_id: str):
        """处理响应"""
        print(f"\n{'='*20} 收到响应 {'='*20}")
        print(f"任务ID: {task_id}")
        print(f"响应时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"输出目录: {os.path.abspath(self.output_dir)}")
        
        # 先保存完整的响应JSON
        self._save_json_result(response, task_id, "完整响应")
        
        # 检查是否有错误
        if "error" in response:
            print(f"❌ 错误: {response['error']}")
            if "raw_response" in response:
                print(f"原始响应: {response['raw_response']}")
        else:
            print("✅ 响应成功")
            
            # 根据服务类型处理不同的响应
            if self.service_type == ServiceType.COVER_GEN:
                self._process_cover_response(response, task_id)
            elif self.service_type == ServiceType.DIAGNOSIS:
                self._process_diagnosis_response(response, task_id)
            elif self.service_type == ServiceType.STRATEGY:
                self._process_strategy_response(response, task_id)
            elif self.service_type == ServiceType.REVIEW:
                self._process_review_response(response, task_id)
        
        print(f"{'='*60}\n")

    def _process_cover_response(self, response: Dict, task_id: str):
        """处理封面生成响应"""
        cover_html = response.get("coverResult")
        if cover_html:
            filename = self._get_formatted_filename(task_id, "cover")
            self._save_file(cover_html, filename, "封面HTML")
        else:
            print("⚠️  未找到封面结果")

    def _process_diagnosis_response(self, response: Dict, task_id: str):
        """处理诊断响应"""
        diagnosis_result = response.get("diagnosisResult")
        if diagnosis_result:
            print(f"诊断结果: {diagnosis_result}")
        
        diagnosis_html = response.get("diagnosisHtml")
        if diagnosis_html:
            filename = self._get_formatted_filename(task_id, "diagnosis")
            self._save_file(diagnosis_html, filename, "诊断HTML报告")

    def _process_strategy_response(self, response: Dict, task_id: str):
        """处理策略响应"""
        strategy_html = response.get("reportHtml")
        if strategy_html:
            filename = self._get_formatted_filename(task_id, "strategy")
            self._save_file(strategy_html, filename, "策略HTML报告")

    def _process_review_response(self, response: Dict, task_id: str):
        """处理复盘响应"""
        review_html = response.get("reportHtml")
        if review_html:
            filename = self._get_formatted_filename(task_id, "review")
            self._save_file(review_html, filename, "复盘HTML报告")
        
        # 验证JSON结构
        report_json = response.get("reportJson")
        if report_json:
            print("📊 JSON结构验证:")
            
            # 保存JSON结构
            json_filename = self._get_formatted_filename(task_id, "review_json", "json")
            self._save_file(
                json.dumps(report_json, ensure_ascii=False, indent=2),
                json_filename,
                "复盘JSON结构"
            )
            
            # 检查必需的顶级字段
            required_fields = ["title", "brandOperationSummary", "nextMonthOptimization", "updateTimeMs"]
            for field in required_fields:
                if field in report_json:
                    print(f"  ✅ {field}: 存在")
                else:
                    print(f"  ❌ {field}: 缺失")
            
            # 检查accounts数组
            accounts = report_json.get("accounts", [])
            print(f"  📋 accounts数组: {len(accounts)}个账号")
            
            for i, account in enumerate(accounts):
                print(f"    账号{i+1}: {account.get('accountName', '未知')}")
                
                # 检查hotPostAnalysis
                hot_post_analysis = account.get("hotPostAnalysis", {})
                content_analysis = hot_post_analysis.get("contentAnalysis", [])
                print(f"      📈 contentAnalysis: {len(content_analysis)}个内容")
                
                for j, content in enumerate(content_analysis):
                    print(f"        内容{j+1}: {content.get('displayTitle', '无标题')}")
                    # 检查shares字段
                    if "shares" in content:
                        print(f"          ✅ shares: {content['shares']}")
                    else:
                        print(f"          ❌ shares: 缺失")
                    
                    # 检查其他必需字段
                    required_content_fields = ["likes", "collected", "comments", "createTimeMs"]
                    for field in required_content_fields:
                        if field in content:
                            print(f"          ✅ {field}: {content[field]}")
                        else:
                            print(f"          ❌ {field}: 缺失")
        else:
            print("⚠️  未找到reportJson")


def load_custom_data_from_file(file_path: str) -> Optional[Dict]:
    """从文件加载自定义数据"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ 文件未找到: {file_path}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析错误: {e}")
        return None
    except Exception as e:
        print(f"❌ 加载文件时发生错误: {e}")
        return None


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Redis队列测试工具")
    parser.add_argument(
        "--service", 
        choices=[st.value for st in ServiceType], 
        default=ServiceType.COVER_GEN.value,
        help="要测试的服务类型"
    )
    parser.add_argument(
        "--data-file", 
        type=str, 
        help="自定义数据文件路径（JSON格式）"
    )
    parser.add_argument(
        "--no-wait", 
        action="store_true", 
        help="发送任务后不等待响应"
    )
    parser.add_argument(
        "--timeout", 
        type=int, 
        default=600,  # 增加到10分钟
        help="等待响应的超时时间（秒）"
    )
    parser.add_argument(
        "--list-services", 
        action="store_true", 
        help="列出所有可用的服务类型"
    )
    parser.add_argument(
        "--output-dir", 
        type=str, 
        default="test_results", 
        help="输出文件保存目录（默认：test_results）"
    )
    parser.add_argument(
        "--no-auto-open", 
        action="store_true", 
        help="不自动在浏览器中打开HTML文件"
    )
    
    args = parser.parse_args()
    
    if args.list_services:
        print("可用的服务类型:")
        for service_type in ServiceType:
            config = SERVICE_CONFIGS[service_type]
            print(f"  {service_type.value}: {config.description}")
            print(f"    输入队列: {config.input_queue}")
            print(f"    输出队列: {config.output_queue}")
        return
    
    # 加载自定义数据
    custom_data = None
    if args.data_file:
        custom_data = load_custom_data_from_file(args.data_file)
        if custom_data is None:
            print("❌ 无法加载自定义数据，使用默认模拟数据")
    
    # 创建测试器
    service_type = ServiceType(args.service)
    tester = RedisQueueTester(
        service_type=service_type,
        output_dir=args.output_dir,
        auto_open=not args.no_auto_open
    )
    
    print(f"🚀 开始测试 {SERVICE_CONFIGS[service_type].description}")
    print(f"输入队列: {SERVICE_CONFIGS[service_type].input_queue}")
    print(f"输出队列: {SERVICE_CONFIGS[service_type].output_queue}")
    print(f"输出目录: {os.path.abspath(args.output_dir)}")
    
    # 运行测试
    await tester.run_test(
        custom_data=custom_data,
        wait_for_response=not args.no_wait,
        timeout=args.timeout
    )
    
    print("✅ 测试完成")


if __name__ == "__main__":
    try:
        # Windows特定：设置事件循环策略
        if sys.platform == 'win32':
            asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
        
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ 测试被用户中断")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
