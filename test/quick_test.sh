#!/bin/bash

# Redis 队列测试快速启动脚本

echo "🚀 Redis 队列测试工具"
echo "======================"

# 检查是否在正确的目录
if [ ! -f "test/test_redis_queue.py" ]; then
    echo "❌ 错误: 请在项目根目录运行此脚本"
    exit 1
fi

# 激活虚拟环境（如果存在）
if [ -d ".venv" ]; then
    echo "🔧 激活虚拟环境..."
    source .venv/bin/activate
fi

# 显示可用选项
echo ""
echo "可用的测试选项:"
echo "1. 封面生成服务 (cover_gen)"
echo "2. 账号诊断服务 (diagnosis)"
echo "3. 策略生成服务 (strategy)"
echo "4. 运营复盘服务 (review)"
echo "5. 列出所有服务"
echo "6. 自定义测试"
echo ""

read -p "请选择测试类型 [1-6]: " choice

case $choice in
    1)
        echo "🎨 测试封面生成服务..."
        python test/test_redis_queue.py --service cover_gen
        ;;
    2)
        echo "🔍 测试账号诊断服务..."
        python test/test_redis_queue.py --service diagnosis
        ;;
    3)
        echo "📊 测试策略生成服务..."
        python test/test_redis_queue.py --service strategy
        ;;
    4)
        echo "📋 测试运营复盘服务..."
        python test/test_redis_queue.py --service review
        ;;
    5)
        echo "📋 列出所有可用服务..."
        python test/test_redis_queue.py --list-services
        ;;
    6)
        echo "⚙️  自定义测试..."
        echo ""
        echo "可用的示例数据文件:"
        echo "- test/sample_data/cover_gen_sample.json"
        echo "- test/sample_data/diagnosis_sample.json"
        echo ""
        read -p "请输入服务类型 [cover_gen/diagnosis/strategy/review]: " service
        read -p "请输入数据文件路径 (可选，直接回车使用默认数据): " datafile
        
        if [ -n "$datafile" ]; then
            python test/test_redis_queue.py --service "$service" --data-file "$datafile"
        else
            python test/test_redis_queue.py --service "$service"
        fi
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "✅ 测试完成！"
echo "📁 查看生成的文件:"
echo "   - 日志文件: logs/redis_queue_test_*.log"
echo "   - 结果文件: test_*_result_*.html" 