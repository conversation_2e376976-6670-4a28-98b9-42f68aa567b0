{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Load raw data and examine"]}, {"cell_type": "markdown", "metadata": {}, "source": ["news data"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# import json\n", "# import pandas as pd\n", "\n", "# pt = 'news/Biden.json'\n", "\n", "# with open(pt, 'r') as f:\n", "#     data = json.load(f)\n", "\n", "# print(len(data))\n", "# data[0]\n", "\n", "\n", "# df = pd.DataFrame(data)\n", "# df.info()\n", "\n", "# df['doc_len'] = df['original_doc'].apply(lambda x: len(x.split()))\n", "# df['summary_len'] = df['summary'].apply(lambda x: len(x.split()))\n", "# df.describe()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["tweet data: 100 x3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 352 entries, 0 to 351\n", "Data columns (total 7 columns):\n", " #   Column            Non-Null Count  Dtype \n", "---  ------            --------------  ----- \n", " 0   id_str            352 non-null    object\n", " 1   user_name         352 non-null    object\n", " 2   user_description  352 non-null    object\n", " 3   keyword           352 non-null    object\n", " 4   full_text         352 non-null    object\n", " 5   lang              352 non-null    object\n", " 6   media             341 non-null    object\n", "dtypes: object(7)\n", "memory usage: 19.4+ KB\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_str</th>\n", "      <th>user_name</th>\n", "      <th>user_description</th>\n", "      <th>keyword</th>\n", "      <th>full_text</th>\n", "      <th>lang</th>\n", "      <th>media</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1734927366378439057</td>\n", "      <td><PERSON></td>\n", "      <td>All’s fair in love and poetry... New album THE...</td>\n", "      <td>taylor swift</td>\n", "      <td>I had the time of my life fighting dragons wit...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1654271542849576961</td>\n", "      <td>The Cult Of Del Rey</td>\n", "      <td>🌪 It’s never too late baby so don’t give up 🌪</td>\n", "      <td>taylor swift</td>\n", "      <td>🚨 | People reports that <PERSON> and <PERSON> ...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"photo\",\\n        \"r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1733992759134945571</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON></td>\n", "      <td>Sports and Gaming | @StarkRaveSports content c...</td>\n", "      <td>taylor swift</td>\n", "      <td>TAYLOR SWIFT IS CHEATING ON TRAVIS KELCE??</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1576956489717014528</td>\n", "      <td>PlayStation</td>\n", "      <td>Official Sony Interactive Entertainment accoun...</td>\n", "      <td>apple</td>\n", "      <td><PERSON><PERSON><PERSON>'s latest looks have been unpeeled on P...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1800257732622356943</td>\n", "      <td><PERSON><PERSON></td>\n", "      <td>Follow me to learn about AI and building start...</td>\n", "      <td>apple</td>\n", "      <td>Apple's AI is finally here\\n\\nHere are 12 ways...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                id_str            user_name  \\\n", "0  1734927366378439057         <PERSON>   \n", "1  1654271542849576961  The Cult Of Del Rey   \n", "2  1733992759134945571              IcyVert   \n", "3  1576956489717014528          PlayStation   \n", "4  1800257732622356943            <PERSON><PERSON>   \n", "\n", "                                    user_description       keyword  \\\n", "0  All’s fair in love and poetry... New album THE...  taylor swift   \n", "1      🌪 It’s never too late baby so don’t give up 🌪  taylor swift   \n", "2  Sports and Gaming | @StarkRaveSports content c...  taylor swift   \n", "3  Official Sony Interactive Entertainment accoun...         apple   \n", "4  Follow me to learn about AI and building start...         apple   \n", "\n", "                                           full_text lang  \\\n", "0  I had the time of my life fighting dragons wit...   en   \n", "1  🚨 | People reports that <PERSON> and <PERSON> ...   en   \n", "2         TAYLOR SWIFT IS CHEATING ON TRAVIS KELCE??   en   \n", "3  <PERSON><PERSON><PERSON>'s latest looks have been unpeeled on P...   en   \n", "4  Apple's AI is finally here\\n\\nHere are 12 ways...   en   \n", "\n", "                                               media  \n", "0  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  \n", "1  [\\n    {\\n        \"type\": \"photo\",\\n        \"r...  \n", "2  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  \n", "3  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  \n", "4  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "import pandas as pd\n", "import os\n", "\n", "\n", "ls = []\n", "pt = 'tweets'\n", "for f in os.listdir(pt):\n", "    with open(os.path.join(pt, f), 'r') as file:\n", "        data = json.load(file)\n", "    df = pd.DataFrame(data)\n", "    df['keyword'] = f.split('.')[0]\n", "    ls.append(df)\n", "\n", "df = pd.concat(ls)\n", "\n", "cols = ['id_str', 'user_name', 'user_description', 'keyword', 'full_text', 'lang', 'media']\n", "df['full_text'] = df['full_text'].str.split('https').str[0].str.strip()\n", "df = df[cols].dropna(subset='full_text')\n", "df = df.sample(frac=1).reset_index(drop=True)\n", "df.info()\n", "df.head()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["lang\n", "en     286\n", "pt      22\n", "ja       9\n", "es       8\n", "it       6\n", "tr       5\n", "zxx      4\n", "ar       2\n", "nl       1\n", "tl       1\n", "und      1\n", "art      1\n", "fr       1\n", "ko       1\n", "in       1\n", "ca       1\n", "pl       1\n", "de       1\n", "Name: count, dtype: int64"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df.lang.value_counts()"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["0.1875  not in english\n"]}], "source": ["print(len(df[df.lang!='en']) / len(df), ' not in english')\n", "df = df[df.lang=='en']"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Keyword extraction"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install keybert"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from keybert import KeyBERT\n", "\n", "kw_model = KeyBERT()"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚨 | People reports that <PERSON> and <PERSON> are officially dating.\n"]}, {"data": {"text/plain": ["[('swift lana', 0.5179),\n", " ('taylor swift', 0.4834),\n", " ('officially dating', 0.4645),\n", " ('lana del', 0.4526),\n", " ('del rey', 0.4181)]"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["spacy"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# !pip install spacy\n", "!python -m spacy download en_core_web_md"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [], "source": ["import spacy\n", "\n", "nlp = spacy.load(\"en_core_web_md\", exclude=['tagger', 'parser', 'ner', 'attribute_ruler', 'lemmatizer'])\n", "\n", "spacy_model = KeyBERT(model=nlp)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["openai"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from chat import ChatService\n", "\n", "aiservice = ChatService()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comparison**\n", "\n", "openai > keybert/miniLM >> spacy"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "==TEXT: BREAKING: <PERSON> just gave the clearest reasons why <PERSON>’s overturning <PERSON> v<PERSON> is devastating to millions of Americans. The only way to end this is by electing President <PERSON><PERSON> &amp; a democratic Congress. Retweet so all Americans see this.\n", "==keywords:\n", "default:  [('roe wade', 0.5231), ('overturning roe', 0.5151), ('trump overturning', 0.4984), ('reasons donald', 0.3823), ('breaking anne', 0.382)]\n", "Time elapsed: 0.50 seconds\n", "spacy:  [('clearest reasons', 0.679), ('democratic', 0.6604), ('democratic congress', 0.6422), ('reasons donald', 0.5988), ('electing president', 0.5905)]\n", "Time elapsed: 0.09 seconds\n", "openai:  (<PERSON>, '<PERSON>, <PERSON> v<PERSON>, <PERSON>, President <PERSON><PERSON>, democratic Congress, overturning, devastating, Americans, electing, Retweet')\n", "\t(Time elapsed: 1.49 seconds)\n", "\n", "\n", "==TEXT: Apple's AI is finally here\n", "\n", "Here are 12 ways you can use Apple Intelligence:\n", "==keywords:\n", "default:  [('apple ai', 0.826), ('apple intelligence', 0.7895), ('ai finally', 0.5347), ('use apple', 0.5343), ('ai', 0.5142)]\n", "Time elapsed: 0.05 seconds\n", "spacy:  [('ways use', 0.7162), ('apple intelligence', 0.6211), ('use apple', 0.6102), ('ways', 0.5803), ('use', 0.5641)]\n", "Time elapsed: 0.02 seconds\n", "openai:  (True, 'Apple, AI, intelligence, ways, use')\n", "\t(Time elapsed: 1.04 seconds)\n", "\n", "\n", "==TEXT: Reminder that <PERSON> was sued by a 14-year-old plaintiff who alleged that she was raped by <PERSON> and <PERSON><PERSON><PERSON> on numerous occasions and provided graphic details of the assaults. She only withdrew her case—two weeks before the 2016 election—due to incessant death threats.\n", "==keywords:\n", "default:  [('trump sued', 0.674), ('raped trump', 0.6023), ('trump epstein', 0.5442), ('alleged raped', 0.5099), ('plaintiff alleged', 0.4856)]\n", "Time elapsed: 0.29 seconds\n", "spacy:  [('numerous occasions', 0.6988), ('election incessant', 0.6764), ('withdrew case', 0.6225), ('death threats', 0.6089), ('occasions provided', 0.6033)]\n", "Time elapsed: 0.07 seconds\n", "openai:  (True, '<PERSON>, sued, 14-year-old plaintiff, raped, <PERSON><PERSON><PERSON>, graphic details, assaults, withdrew, case, 2016 election, death threats.')\n", "\t(Time elapsed: 1.22 seconds)\n", "\n", "\n", "==TEXT: #Project2025 The American Nazi Handbook\n", "==keywords:\n", "default:  [('nazi handbook', 0.8218), ('american nazi', 0.6059), ('nazi', 0.5371), ('project2025 american', 0.4082), ('handbook', 0.3878)]\n", "Time elapsed: 0.72 seconds\n", "spacy:  [('american nazi', 0.575), ('nazi handbook', 0.5342), ('nazi', 0.49), ('project2025 american', 0.3823), ('american', 0.3823)]\n", "Time elapsed: 0.01 seconds\n", "openai:  (True, 'American Nazi Handbook, Project2025')\n", "\t(Time elapsed: 0.94 seconds)\n"]}], "source": ["import random\n", "import time\n", "# FILEPATH: /Users/<USER>/watt/xbot-experiment/test.ipynb\n", "\n", "idx = random.randint(0, len(df)-1)\n", "doc = df.full_text.iloc[idx]\n", "\n", "def compare_extract_keywords(doc):\n", "    print('\\n\\n==TEXT:', doc)\n", "    print('==keywords:')\n", "\n", "    #default miniLM\n", "    start_time = time.time()\n", "    res = kw_model.extract_keywords(doc, keyphrase_ngram_range=(1,2))\n", "    print('default: ', res)\n", "    print(f'Time elapsed: {time.time() - start_time:.2f} seconds')\n", "\n", "    #spacy\n", "    start_time = time.time()\n", "    res = spacy_model.extract_keywords(doc, keyphrase_ngram_range=(1,2))\n", "    print('spacy: ', res)\n", "    print(f'Time elapsed: {time.time() - start_time:.2f} seconds')\n", "\n", "    #openai\n", "    start_time = time.time()\n", "    prompt = f\"\"\"\n", "    I have the following document:\n", "    <start>{doc}<end>\n", "    Based on the information above, extract the top keywords that best describe the topic of the text.\n", "    Make sure to only extract keywords that appear in the text.\n", "    keywords (separated by commas and order by relevance):\n", "    \"\"\"\n", "    res = aiservice.chat(prompt, 'gpt-3.5-turbo', temperature=0.1)\n", "    print('openai: ', res)\n", "    print(f'\\t(Time elapsed: {time.time() - start_time:.2f} seconds)')\n", "\n", "for idx in random.choices(range(len(df)), k=4):\n", "    doc = df.full_text.iloc[idx]\n", "    compare_extract_keywords(doc)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Try BERTopic"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/xbot-experiment/env/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n", "/Users/<USER>/xbot-experiment/env/lib/python3.9/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020\n", "  warnings.warn(\n"]}], "source": ["from bertopic import BERTopic\n", "from bertopic.representation import KeyBERTInspired\n", "from umap import UMAP\n", "\n", "representation_model=KeyBERTInspired()\n", "umap_model=UMAP(random_state=0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### try cluster tweets from 3 keywords"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'calculate_probabilities': False,\n", " 'ctfidf_model': ClassTfidfTransformer(),\n", " 'embedding_model': None,\n", " 'hdbscan_model': HDBSCAN(min_cluster_size=10, prediction_data=True),\n", " 'language': 'en',\n", " 'low_memory': <PERSON><PERSON><PERSON>,\n", " 'min_topic_size': 10,\n", " 'n_gram_range': (1, 1),\n", " 'nr_topics': None,\n", " 'representation_model': KeyBERTInspired(),\n", " 'seed_topic_list': None,\n", " 'top_n_words': 10,\n", " 'umap_model': UMAP(random_state=0),\n", " 'vectorizer_model': CountVectorizer(),\n", " 'verbose': True,\n", " 'zeroshot_min_similarity': 0.7,\n", " 'zeroshot_topic_list': None}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df = df[df.lang == 'en'].reset_index(drop=True)\n", "model = BERTopic(language='en', representation_model=representation_model, umap_model=umap_model,\n", "                 top_n_words=10, min_topic_size=10, verbose=True)\n", "# model = BERTopic(language='multilingual', top_n_words=10, min_topic_size=10, verbose=True)\n", "model.get_params()"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-07-05 15:37:33,405 - BERTopic - Embedding - Transforming documents to embeddings.\n", "Batches: 100%|██████████| 9/9 [00:03<00:00,  2.76it/s]\n", "2024-07-05 15:37:40,135 - BERTopic - Embedding - Completed ✓\n", "2024-07-05 15:37:40,136 - BERTopic - Dimensionality - Fitting the dimensionality reduction algorithm\n", "2024-07-05 15:37:49,346 - BERTopic - Dimensionality - Completed ✓\n", "2024-07-05 15:37:49,348 - BERTopic - Cluster - Start clustering the reduced embeddings\n", "2024-07-05 15:37:49,359 - BERTopic - Cluster - Completed ✓\n", "2024-07-05 15:37:49,367 - BERTopic - Representation - Extracting topics from clusters using representation models.\n", "2024-07-05 15:37:49,768 - BERTopic - Representation - Completed ✓\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Topic</th>\n", "      <th>Count</th>\n", "      <th>Name</th>\n", "      <th>Representation</th>\n", "      <th>Representative_Docs</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>-1</td>\n", "      <td>1</td>\n", "      <td>-1_miu_stabbing_guilty_trial</td>\n", "      <td>[miu, stabbing, guilty, trial, homicide, jury,...</td>\n", "      <td>[#BREAKING: <PERSON><PERSON> has been found guilty ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>111</td>\n", "      <td>0_trump_donald_election_democrats</td>\n", "      <td>[trump, donald, election, democrats, president...</td>\n", "      <td>[Disgusting. Here is a video of <PERSON> s...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>91</td>\n", "      <td>1_taylor_swift_poets_her</td>\n", "      <td>[taylor, swift, poets, her, grammys, she, kelc...</td>\n", "      <td>[TAYL<PERSON> SWIFT, taylor swift., taylor swift.]</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2</td>\n", "      <td>83</td>\n", "      <td>2_apple_iphone_appleevent_ipod</td>\n", "      <td>[apple, iphone, appleevent, ipod, apples, app,...</td>\n", "      <td>[🔮One of the more popular apps for 2023 for Ap...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Topic  Count                               Name  \\\n", "0     -1      1       -1_miu_stabbing_guilty_trial   \n", "1      0    111  0_trump_donald_election_democrats   \n", "2      1     91           1_taylor_swift_poets_her   \n", "3      2     83     2_apple_iphone_appleevent_ipod   \n", "\n", "                                      Representation  \\\n", "0  [miu, stabbing, guilty, trial, homicide, jury,...   \n", "1  [trump, donald, election, democrats, president...   \n", "2  [taylor, swift, poets, her, grammys, she, kelc...   \n", "3  [apple, iphone, appleevent, ipod, apples, app,...   \n", "\n", "                                 Representative_Docs  \n", "0  [#BREAKING: <PERSON><PERSON> has been found guilty ...  \n", "1  [Disgusting. Here is a video of <PERSON> s...  \n", "2       [TAY<PERSON><PERSON>, taylor swift., taylor swift.]  \n", "3  [🔮One of the more popular apps for 2023 for Ap...  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["topics, probs = model.fit_transform(df.full_text)\n", "\n", "model.get_topic_info()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# model.visualize_topics()"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Document</th>\n", "      <th>Topic</th>\n", "      <th>Name</th>\n", "      <th>Representation</th>\n", "      <th>Representative_Docs</th>\n", "      <th>Top_n_words</th>\n", "      <th>Probability</th>\n", "      <th>Representative_document</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td><PERSON> signing the Declaration of Indepe...</td>\n", "      <td>1</td>\n", "      <td>1_taylor_swift_poets_her</td>\n", "      <td>[taylor, swift, poets, her, grammys, she, kelc...</td>\n", "      <td>[TAYL<PERSON> SWIFT, taylor swift., taylor swift.]</td>\n", "      <td>taylor - swift - poets - her - grammys - she -...</td>\n", "      <td>0.475675</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>taylor swift when she wrote i can see you</td>\n", "      <td>1</td>\n", "      <td>1_taylor_swift_poets_her</td>\n", "      <td>[taylor, swift, poets, her, grammys, she, kelc...</td>\n", "      <td>[TAYL<PERSON> SWIFT, taylor swift., taylor swift.]</td>\n", "      <td>taylor - swift - poets - her - grammys - she -...</td>\n", "      <td>1.000000</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>This is why so many polls lean <PERSON>.\\n\\nAccor...</td>\n", "      <td>0</td>\n", "      <td>0_trump_donald_election_democrats</td>\n", "      <td>[trump, donald, election, democrats, president...</td>\n", "      <td>[Disgusting. Here is a video of <PERSON> s...</td>\n", "      <td>trump - donald - election - democrats - presid...</td>\n", "      <td>0.902119</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>This is INSANE. In a new interview, <PERSON>...</td>\n", "      <td>0</td>\n", "      <td>0_trump_donald_election_democrats</td>\n", "      <td>[trump, donald, election, democrats, president...</td>\n", "      <td>[Disgusting. Here is a video of <PERSON> s...</td>\n", "      <td>trump - donald - election - democrats - presid...</td>\n", "      <td>1.000000</td>\n", "      <td>True</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td><PERSON> just posted this on Truth 😂</td>\n", "      <td>0</td>\n", "      <td>0_trump_donald_election_democrats</td>\n", "      <td>[trump, donald, election, democrats, president...</td>\n", "      <td>[Disgusting. Here is a video of <PERSON> s...</td>\n", "      <td>trump - donald - election - democrats - presid...</td>\n", "      <td>1.000000</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                            Document  Topic  \\\n", "0  <PERSON> signing the Declaration of Indepe...      1   \n", "1          taylor swift when she wrote i can see you      1   \n", "2  This is why so many polls lean <PERSON>.\\n\\nAccor...      0   \n", "3  This is INSANE. In a new interview, <PERSON>...      0   \n", "4                  <PERSON> just posted this on Truth 😂      0   \n", "\n", "                                Name  \\\n", "0           1_taylor_swift_poets_her   \n", "1           1_taylor_swift_poets_her   \n", "2  0_trump_donald_election_democrats   \n", "3  0_trump_donald_election_democrats   \n", "4  0_trump_donald_election_democrats   \n", "\n", "                                      Representation  \\\n", "0  [taylor, swift, poets, her, grammys, she, kelc...   \n", "1  [taylor, swift, poets, her, grammys, she, kelc...   \n", "2  [trump, donald, election, democrats, president...   \n", "3  [trump, donald, election, democrats, president...   \n", "4  [trump, donald, election, democrats, president...   \n", "\n", "                                 Representative_<PERSON>s  \\\n", "0       [TAY<PERSON><PERSON>, taylor swift., taylor swift.]   \n", "1       [TAYL<PERSON>, taylor swift., taylor swift.]   \n", "2  [Disgusting. Here is a video of <PERSON> s...   \n", "3  [Disgusting. Here is a video of <PERSON> s...   \n", "4  [Disgusting. Here is a video of <PERSON> s...   \n", "\n", "                                         Top_n_words  Probability  \\\n", "0  taylor - swift - poets - her - grammys - she -...     0.475675   \n", "1  taylor - swift - poets - her - grammys - she -...     1.000000   \n", "2  trump - donald - election - democrats - presid...     0.902119   \n", "3  trump - donald - election - democrats - presid...     1.000000   \n", "4  trump - donald - election - democrats - presid...     1.000000   \n", "\n", "   Representative_document  \n", "0                    False  \n", "1                    False  \n", "2                    False  \n", "3                     True  \n", "4                    False  "]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["preds_df = model.get_document_info(df.full_text)\n", "preds_df.head()"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_str</th>\n", "      <th>user_name</th>\n", "      <th>user_description</th>\n", "      <th>keyword</th>\n", "      <th>full_text</th>\n", "      <th>lang</th>\n", "      <th>media</th>\n", "      <th>pred</th>\n", "      <th>prob</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1808739312206500055</td>\n", "      <td><PERSON> Facts🧣</td>\n", "      <td><PERSON> fan account🦋 || Turn on my notifi...</td>\n", "      <td>taylor swift</td>\n", "      <td><PERSON> signing the Declaration of Indepe...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"photo\",\\n        \"r...</td>\n", "      <td>taylor swift</td>\n", "      <td>0.475675</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1682003103615451136</td>\n", "      <td>matt⸆⸉</td>\n", "      <td>tn x2 spotify x1 deezer x1 🍉</td>\n", "      <td>taylor swift</td>\n", "      <td>taylor swift when she wrote i can see you</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"photo\",\\n        \"r...</td>\n", "      <td>taylor swift</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1806483654312603920</td>\n", "      <td><PERSON></td>\n", "      <td>Husband | Father | Designer | California Nativ...</td>\n", "      <td>trump</td>\n", "      <td>This is why so many polls lean <PERSON>.\\n\\nAccor...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>0.902119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1806087921511923949</td>\n", "      <td><PERSON></td>\n", "      <td>21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...</td>\n", "      <td>trump</td>\n", "      <td>This is INSANE. In a new interview, <PERSON>...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1806073763223490924</td>\n", "      <td>Libs of TikTok</td>\n", "      <td>News you can’t see anywhere else. 📧 submission...</td>\n", "      <td>trump</td>\n", "      <td><PERSON> just posted this on Truth 😂</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                id_str         user_name  \\\n", "0  1808739312206500055     Taylor Facts🧣   \n", "1  1682003103615451136            matt⸆⸉   \n", "2  1806483654312603920  <PERSON>   \n", "3  1806087921511923949      <PERSON>   \n", "4  1806073763223490924    Libs of TikTok   \n", "\n", "                                    user_description       keyword  \\\n", "0  <PERSON> fan account🦋 || Turn on my notifi...  taylor swift   \n", "1                       tn x2 spotify x1 deezer x1 🍉  taylor swift   \n", "2  Husband | Father | Designer | California Nativ...         trump   \n", "3  21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...         trump   \n", "4  News you can’t see anywhere else. 📧 submission...         trump   \n", "\n", "                                           full_text lang  \\\n", "0  <PERSON> signing the Declaration of Indepe...   en   \n", "1          taylor swift when she wrote i can see you   en   \n", "2  This is why so many polls lean <PERSON>.\\n\\nAccor...   en   \n", "3  This is INSANE. In a new interview, <PERSON>...   en   \n", "4                  <PERSON> just posted this on Truth 😂   en   \n", "\n", "                                               media          pred      prob  \n", "0  [\\n    {\\n        \"type\": \"photo\",\\n        \"r...  taylor swift  0.475675  \n", "1  [\\n    {\\n        \"type\": \"photo\",\\n        \"r...  taylor swift  1.000000  \n", "2  [\\n    {\\n        \"type\": \"video\",\\n        \"r...         trump  0.902119  \n", "3  [\\n    {\\n        \"type\": \"video\",\\n        \"r...         trump  1.000000  \n", "4  [\\n    {\\n        \"type\": \"video\",\\n        \"r...         trump  1.000000  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df['pred'] = preds_df['Topic'].replace({    \n", "    0: 'trump',\n", "    1: 'taylor swift',\n", "    2: 'apple'\n", "})\n", "df['prob'] = preds_df['Probability']\n", "df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### examine wrong predictions\n", "not too far off, some tweets contain both keywords, and some belong to neither"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["error rate: 0.04195804195804196\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>keyword</th>\n", "      <th>pred</th>\n", "      <th>prob</th>\n", "      <th>full_text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>apple</td>\n", "      <td>taylor swift</td>\n", "      <td>0.448058</td>\n", "      <td>what a nice way to make new friends</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>apple</td>\n", "      <td>trump</td>\n", "      <td>0.425339</td>\n", "      <td><PERSON><PERSON><PERSON>’s wife preparing  to go meet <PERSON> 😂</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>trump</td>\n", "      <td>taylor swift</td>\n", "      <td>0.603661</td>\n", "      <td>Can You Separate Ye<PERSON>ys From Kanye West? -</td>\n", "    </tr>\n", "    <tr>\n", "      <th>90</th>\n", "      <td>apple</td>\n", "      <td>taylor swift</td>\n", "      <td>0.494417</td>\n", "      <td>Our homies in @DearYouthband dropped a banger ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>91</th>\n", "      <td>apple</td>\n", "      <td>trump</td>\n", "      <td>0.707090</td>\n", "      <td>And dudes will say, \"Why do women act paranoid...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>96</th>\n", "      <td>trump</td>\n", "      <td>taylor swift</td>\n", "      <td>0.249004</td>\n", "      <td>#Project2025 The American Nazi Handbook</td>\n", "    </tr>\n", "    <tr>\n", "      <th>111</th>\n", "      <td>apple</td>\n", "      <td>taylor swift</td>\n", "      <td>0.517608</td>\n", "      <td>The girls didn't expect it😭</td>\n", "    </tr>\n", "    <tr>\n", "      <th>146</th>\n", "      <td>apple</td>\n", "      <td>-1</td>\n", "      <td>0.000000</td>\n", "      <td>#BREAKING: <PERSON><PERSON> has been found guilty i...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>181</th>\n", "      <td>trump</td>\n", "      <td>taylor swift</td>\n", "      <td>1.000000</td>\n", "      <td><PERSON> says <PERSON> is “unusually b...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>229</th>\n", "      <td>taylor swift</td>\n", "      <td>trump</td>\n", "      <td>0.644480</td>\n", "      <td>Never beating the sorcery allegations ✨🛬✨</td>\n", "    </tr>\n", "    <tr>\n", "      <th>241</th>\n", "      <td>apple</td>\n", "      <td>taylor swift</td>\n", "      <td>0.663412</td>\n", "      <td>THIS ONE IS FOR THE MISFITS, FOR THE OUTKASTS,...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>272</th>\n", "      <td>apple</td>\n", "      <td>taylor swift</td>\n", "      <td>0.443566</td>\n", "      <td>New Friendships. New Adventures. Hello Kitty I...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          keyword          pred      prob  \\\n", "9           apple  taylor swift  0.448058   \n", "12          apple         trump  0.425339   \n", "26          trump  taylor swift  0.603661   \n", "90          apple  taylor swift  0.494417   \n", "91          apple         trump  0.707090   \n", "96          trump  taylor swift  0.249004   \n", "111         apple  taylor swift  0.517608   \n", "146         apple            -1  0.000000   \n", "181         trump  taylor swift  1.000000   \n", "229  taylor swift         trump  0.644480   \n", "241         apple  taylor swift  0.663412   \n", "272         apple  taylor swift  0.443566   \n", "\n", "                                             full_text  \n", "9                  what a nice way to make new friends  \n", "12      <PERSON><PERSON><PERSON>’s wife preparing  to go meet <PERSON> 😂  \n", "26          Can You Separate <PERSON><PERSON><PERSON> From Kanye West? -  \n", "90   Our homies in @DearYouthband dropped a banger ...  \n", "91   And dudes will say, \"Why do women act paranoid...  \n", "96             #Project2025 The American Nazi Handbook  \n", "111                        The girls didn't expect it😭  \n", "146  #BREAKING: <PERSON><PERSON> has been found guilty i...  \n", "181  <PERSON> says <PERSON> is “unusually b...  \n", "229          Never beating the sorcery allegations ✨🛬✨  \n", "241  THIS ONE IS FOR THE MISFITS, FOR THE OUTKASTS,...  \n", "272  New Friendships. New Adventures. Hello Kitty I...  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["wrong_df = df[df.keyword != df.pred][['keyword', 'pred', 'prob', 'full_text']]\n", "print('error rate:', len(wrong_df)/len(df))\n", "wrong_df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Our homies in @DearYouthband dropped a banger of an album today! Make sure you show them some love!\n", "\n", "Heirloom -&gt;\n"]}], "source": ["idx = 90\n", "print(wrong_df.loc[idx].full_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### try cluster tweets form single keyword"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id_str</th>\n", "      <th>user_name</th>\n", "      <th>user_description</th>\n", "      <th>keyword</th>\n", "      <th>full_text</th>\n", "      <th>lang</th>\n", "      <th>media</th>\n", "      <th>pred</th>\n", "      <th>prob</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1806483654312603920</td>\n", "      <td><PERSON></td>\n", "      <td>Husband | Father | Designer | California Nativ...</td>\n", "      <td>trump</td>\n", "      <td>This is why so many polls lean <PERSON>.\\n\\nAccor...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>0.902119</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1806087921511923949</td>\n", "      <td><PERSON></td>\n", "      <td>21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...</td>\n", "      <td>trump</td>\n", "      <td>This is INSANE. In a new interview, <PERSON>...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>1806073763223490924</td>\n", "      <td>Libs of TikTok</td>\n", "      <td>News you can’t see anywhere else. 📧 submission...</td>\n", "      <td>trump</td>\n", "      <td><PERSON> just posted this on Truth 😂</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>1804431721854628319</td>\n", "      <td><PERSON>, MSW #BLM 🇺🇦🇺🇦🏳️‍🌈🌈🟦🟧</td>\n", "      <td>Hello guys,  #EndHomelessness. #Autismdad. #Co...</td>\n", "      <td>trump</td>\n", "      <td>This is the third African American person who ...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>0.872954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>1801368683031486511</td>\n", "      <td><PERSON></td>\n", "      <td>21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...</td>\n", "      <td>trump</td>\n", "      <td>🚨REPUBLICANS CAUGHT LYING! \\n\\nMAGA is circula...</td>\n", "      <td>en</td>\n", "      <td>[\\n    {\\n        \"type\": \"video\",\\n        \"r...</td>\n", "      <td>trump</td>\n", "      <td>1.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                 id_str                          user_name  \\\n", "2   1806483654312603920                   <PERSON>   \n", "3   1806087921511923949                       <PERSON>   \n", "4   1806073763223490924                     Libs of TikTok   \n", "11  1804431721854628319  <PERSON>, MSW #BLM 🇺🇦🇺🇦🏳️‍🌈🌈🟦🟧   \n", "14  1801368683031486511                       <PERSON>   \n", "\n", "                                     user_description keyword  \\\n", "2   Husband | Father | Designer | California Nativ...   trump   \n", "3   21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...   trump   \n", "4   News you can’t see anywhere else. 📧 submission...   trump   \n", "11  Hello guys,  #EndHomelessness. #Autismdad. #Co...   trump   \n", "14  21 | <PERSON> | @ha<PERSON><PERSON><PERSON><PERSON> (900,000) on Tik...   trump   \n", "\n", "                                            full_text lang  \\\n", "2   This is why so many polls lean <PERSON>.\\n\\nAccor...   en   \n", "3   This is INSANE. In a new interview, <PERSON>...   en   \n", "4                   <PERSON> just posted this on Truth 😂   en   \n", "11  This is the third African American person who ...   en   \n", "14  🚨REPUBLICANS CAUGHT LYING! \\n\\nMAGA is circula...   en   \n", "\n", "                                                media   pred      prob  \n", "2   [\\n    {\\n        \"type\": \"video\",\\n        \"r...  trump  0.902119  \n", "3   [\\n    {\\n        \"type\": \"video\",\\n        \"r...  trump  1.000000  \n", "4   [\\n    {\\n        \"type\": \"video\",\\n        \"r...  trump  1.000000  \n", "11  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  trump  0.872954  \n", "14  [\\n    {\\n        \"type\": \"video\",\\n        \"r...  trump  1.000000  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["trump_df = df[df.keyword == 'trump']\n", "trump_df.head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "\n", "trump_model = BERTopic(language='en', representation_model=KeyBERTInspired(), umap_model=UMAP(random_state=0),\n", "                       top_n_words=10, min_topic_size=5, verbose=True)\n", "\n", "topics, probs = trump_model.fit_transform(trump_df.full_text)\n", "\n", "trump_topics_df = trump_model.get_topic_info()\n", "trump_topics_df"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [], "source": ["# new_topics = trump_model.reduce_outliers(trump_df.full_text, topics)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["# trump_model.visualize_topics()\n", "# trump_model.visualize_hierarchy()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "==Topic  -1 -1_trump_donald_clinton_president\n", ">> Trump's Controversies Divide Supporters\n", "\n", "==Topic  0 0_trump24_trump_2024_election\n", ">> Trump threatens to overturn 2024 election\n", "\n", "==Topic  1 1_trump_donald_news_philadelphia\n", ">> Trump congratulates <PERSON><PERSON>\n", "\n", "==Topic  2 2_biden_trump_election_president\n", ">> <PERSON><PERSON> wins presidential election\n", "\n", "==Topic  3 3_trump_trumps_maga_democrats\n", ">> <PERSON>'s VP potential discussed by <PERSON><PERSON><PERSON>\n", "\n", "==Topic  4 4_trump_racists_donald_blacks\n", ">> <PERSON>'s racist remarks about <PERSON><PERSON> and <PERSON>\n", "\n", "==Topic  5 5_donald_trump_raped_alleged\n", ">> <PERSON>'s controversial stance on gender and allegations of sexual assault.\n"]}], "source": ["for i, row in trump_topics_df.iterrows():\n", "    if i < 0:\n", "        continue\n", "    docs = '\\n- '.join(row['Representative_Docs'])\n", "    keywords = row['Representation']\n", "    # use minimum adjective, yet eye catching, \n", "    # predicate or verb\n", "    prompt = f\"\"\"You are an expert in social media and SEO, please help me extract the main topic/event/theme covered in the following tweets.\n", "tweets:\n", "- {docs}\n", "(common keywords of these tweets are: {keywords})\n", "Requirement:\n", "- The topic should be short, succinct, such as commonly seen on the social medias trending list. \n", "- prefered format is SUBJECT verb OBEJECT\n", "topic:\"\"\"    \n", "    status, msg = aiservice.chat(prompt, model='gpt-3.5-turbo', temperature=0.1)\n", "    print('\\n==Topic ', i-1, row.Name)\n", "    # print(prompt)\n", "    print('>>', msg)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["BREAKING: In a terribly awkward moment, <PERSON> attempts to kiss a younger women who for good reason rejects him. Retweet so everyone sees this predator get denied.\n", "- 🇺🇸 <PERSON> says left-wing gender insanity being pushed on children is child abuse. \n", "\n", "If elected President in 2024, <PERSON> promises to ban gender-affirming care for minors in all 50 states, and says the United States' government will only recognize male and female as genders.\n", "- Reminder that <PERSON> was sued by a 14-year-old plaintiff who alleged that she was raped by <PERSON> and <PERSON><PERSON><PERSON> on numerous occasions and provided graphic details of the assaults. She only withdrew her case—two weeks before the 2016 election—due to incessant death threats.\n"]}], "source": ["print(docs)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["**TODO Preprocessing**\n", "- remove urls, hashtags, mentions, and punctuations\n", "- remove stopwords\n", "..."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## get subtopics for each topic/keyword"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["No sentence-transformers model found with name sentence-transformers/all-MiniLM-L6-v2. Creating a new one with mean pooling.\n"]}, {"ename": "OSError", "evalue": "Can't load the configuration of 'sentence-transformers/all-MiniLM-L6-v2'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure 'sentence-transformers/all-MiniLM-L6-v2' is the correct path to a directory containing a config.json file", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mRecursionError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/transformers/configuration_utils.py:689\u001b[0m, in \u001b[0;36mPretrainedConfig._get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    687\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    688\u001b[0m     \u001b[38;5;66;03m# Load from local folder or from cache or download from model Hub and cache\u001b[39;00m\n\u001b[0;32m--> 689\u001b[0m     resolved_config_file \u001b[38;5;241m=\u001b[39m \u001b[43mcached_file\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    690\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    691\u001b[0m \u001b[43m        \u001b[49m\u001b[43mconfiguration_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    692\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    693\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    694\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    695\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    696\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    697\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    698\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    699\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    700\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    701\u001b[0m \u001b[43m        \u001b[49m\u001b[43m_commit_hash\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcommit_hash\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    702\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    703\u001b[0m     commit_hash \u001b[38;5;241m=\u001b[39m extract_commit_hash(resolved_config_file, commit_hash)\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/transformers/utils/hub.py:402\u001b[0m, in \u001b[0;36mcached_file\u001b[0;34m(path_or_repo_id, filename, cache_dir, force_download, resume_download, proxies, token, revision, local_files_only, subfolder, repo_type, user_agent, _raise_exceptions_for_gated_repo, _raise_exceptions_for_missing_entries, _raise_exceptions_for_connection_errors, _commit_hash, **deprecated_kwargs)\u001b[0m\n\u001b[1;32m    400\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    401\u001b[0m     \u001b[38;5;66;03m# Load from URL or cache if already cached\u001b[39;00m\n\u001b[0;32m--> 402\u001b[0m     resolved_file \u001b[38;5;241m=\u001b[39m \u001b[43mhf_hub_download\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    403\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpath_or_repo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    404\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    405\u001b[0m \u001b[43m        \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mif\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[38;5;28;43mlen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m)\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m==\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;241;43m0\u001b[39;49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01melse\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43msubfolder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    406\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    407\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    408\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    409\u001b[0m \u001b[43m        \u001b[49m\u001b[43muser_agent\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43muser_agent\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    410\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    411\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    412\u001b[0m \u001b[43m        \u001b[49m\u001b[43mresume_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresume_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    413\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    414\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    415\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    416\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m GatedRepoError \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:1221\u001b[0m, in \u001b[0;36mhf_hub_download\u001b[0;34m(repo_id, filename, subfolder, repo_type, revision, library_name, library_version, cache_dir, local_dir, user_agent, force_download, proxies, etag_timeout, token, local_files_only, headers, endpoint, legacy_cache_layout, resume_download, force_filename, local_dir_use_symlinks)\u001b[0m\n\u001b[1;32m   1220\u001b[0m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m-> 1221\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_hf_hub_download_to_cache_dir\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1222\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Destination\u001b[39;49;00m\n\u001b[1;32m   1223\u001b[0m \u001b[43m        \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1224\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# File info\u001b[39;49;00m\n\u001b[1;32m   1225\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1226\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1227\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1228\u001b[0m \u001b[43m        \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1229\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# HTTP info\u001b[39;49;00m\n\u001b[1;32m   1230\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1231\u001b[0m \u001b[43m        \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1232\u001b[0m \u001b[43m        \u001b[49m\u001b[43metag_timeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1233\u001b[0m \u001b[43m        \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1234\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;66;43;03m# Additional options\u001b[39;49;00m\n\u001b[1;32m   1235\u001b[0m \u001b[43m        \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1236\u001b[0m \u001b[43m        \u001b[49m\u001b[43mforce_download\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mforce_download\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1237\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:1282\u001b[0m, in \u001b[0;36m_hf_hub_download_to_cache_dir\u001b[0;34m(cache_dir, repo_id, filename, repo_type, revision, headers, proxies, etag_timeout, endpoint, local_files_only, force_download)\u001b[0m\n\u001b[1;32m   1280\u001b[0m \u001b[38;5;66;03m# Try to get metadata (etag, commit_hash, url, size) from the server.\u001b[39;00m\n\u001b[1;32m   1281\u001b[0m \u001b[38;5;66;03m# If we can't, a HEAD request error is returned.\u001b[39;00m\n\u001b[0;32m-> 1282\u001b[0m (url_to_download, etag, commit_hash, expected_size, head_call_error) \u001b[38;5;241m=\u001b[39m \u001b[43m_get_metadata_or_catch_error\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1283\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrepo_id\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_id\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1284\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfilename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mfilename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1285\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrepo_type\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrepo_type\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1286\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1287\u001b[0m \u001b[43m    \u001b[49m\u001b[43mendpoint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mendpoint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1288\u001b[0m \u001b[43m    \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1289\u001b[0m \u001b[43m    \u001b[49m\u001b[43metag_timeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1290\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1291\u001b[0m \u001b[43m    \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1292\u001b[0m \u001b[43m    \u001b[49m\u001b[43mstorage_folder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mstorage_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1293\u001b[0m \u001b[43m    \u001b[49m\u001b[43mrelative_filename\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrelative_filename\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1294\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1296\u001b[0m \u001b[38;5;66;03m# etag can be None for several reasons:\u001b[39;00m\n\u001b[1;32m   1297\u001b[0m \u001b[38;5;66;03m# 1. we passed local_files_only.\u001b[39;00m\n\u001b[1;32m   1298\u001b[0m \u001b[38;5;66;03m# 2. we don't have a connection\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   1304\u001b[0m \u001b[38;5;66;03m# If the specified revision is a commit hash, look inside \"snapshots\".\u001b[39;00m\n\u001b[1;32m   1305\u001b[0m \u001b[38;5;66;03m# If the specified revision is a branch or tag, look inside \"refs\".\u001b[39;00m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:1722\u001b[0m, in \u001b[0;36m_get_metadata_or_catch_error\u001b[0;34m(repo_id, filename, repo_type, revision, endpoint, proxies, etag_timeout, headers, local_files_only, relative_filename, storage_folder)\u001b[0m\n\u001b[1;32m   1721\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m-> 1722\u001b[0m     metadata \u001b[38;5;241m=\u001b[39m \u001b[43mget_hf_file_metadata\u001b[49m\u001b[43m(\u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43metag_timeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1723\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m EntryNotFoundError \u001b[38;5;28;01mas\u001b[39;00m http_error:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/utils/_validators.py:114\u001b[0m, in \u001b[0;36mvalidate_hf_hub_args.<locals>._inner_fn\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    112\u001b[0m     kwargs \u001b[38;5;241m=\u001b[39m smoothly_deprecate_use_auth_token(fn_name\u001b[38;5;241m=\u001b[39mfn\u001b[38;5;241m.\u001b[39m\u001b[38;5;18m__name__\u001b[39m, has_token\u001b[38;5;241m=\u001b[39mhas_token, kwargs\u001b[38;5;241m=\u001b[39mkwargs)\n\u001b[0;32m--> 114\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mfn\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:1645\u001b[0m, in \u001b[0;36mget_hf_file_metadata\u001b[0;34m(url, token, proxies, timeout, library_name, library_version, user_agent, headers)\u001b[0m\n\u001b[1;32m   1644\u001b[0m \u001b[38;5;66;03m# Retrieve metadata\u001b[39;00m\n\u001b[0;32m-> 1645\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1646\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[38;5;124;43mHEAD\u001b[39;49m\u001b[38;5;124;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1647\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1648\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1649\u001b[0m \u001b[43m    \u001b[49m\u001b[43mallow_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1650\u001b[0m \u001b[43m    \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mTrue\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m   1651\u001b[0m \u001b[43m    \u001b[49m\u001b[43mproxies\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mproxies\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1652\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1653\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1654\u001b[0m hf_raise_for_status(r)\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:372\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    371\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m follow_relative_redirects:\n\u001b[0;32m--> 372\u001b[0m     response \u001b[38;5;241m=\u001b[39m \u001b[43m_request_wrapper\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    373\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    374\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    375\u001b[0m \u001b[43m        \u001b[49m\u001b[43mfollow_relative_redirects\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01m<PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    376\u001b[0m \u001b[43m        \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    377\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    379\u001b[0m     \u001b[38;5;66;03m# If redirection, we redirect only relative paths.\u001b[39;00m\n\u001b[1;32m    380\u001b[0m     \u001b[38;5;66;03m# This is useful in case of a renamed repository.\u001b[39;00m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/file_download.py:395\u001b[0m, in \u001b[0;36m_request_wrapper\u001b[0;34m(method, url, follow_relative_redirects, **params)\u001b[0m\n\u001b[1;32m    394\u001b[0m \u001b[38;5;66;03m# Perform request and return if status_code is not in the retry list.\u001b[39;00m\n\u001b[0;32m--> 395\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[43mget_session\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mparams\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    396\u001b[0m hf_raise_for_status(response)\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/requests/sessions.py:589\u001b[0m, in \u001b[0;36mSession.request\u001b[0;34m(self, method, url, params, data, headers, cookies, files, auth, timeout, allow_redirects, proxies, hooks, stream, verify, cert, json)\u001b[0m\n\u001b[1;32m    588\u001b[0m send_kwargs\u001b[38;5;241m.\u001b[39mupdate(settings)\n\u001b[0;32m--> 589\u001b[0m resp \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mprep\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43msend_kwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    591\u001b[0m \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m resp\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/requests/sessions.py:703\u001b[0m, in \u001b[0;36mSession.send\u001b[0;34m(self, request, **kwargs)\u001b[0m\n\u001b[1;32m    702\u001b[0m \u001b[38;5;66;03m# Send the request\u001b[39;00m\n\u001b[0;32m--> 703\u001b[0m r \u001b[38;5;241m=\u001b[39m \u001b[43madapter\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    705\u001b[0m \u001b[38;5;66;03m# Total elapsed time of the request (approximately)\u001b[39;00m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/huggingface_hub/utils/_http.py:66\u001b[0m, in \u001b[0;36mUniqueRequestIdAdapter.send\u001b[0;34m(self, request, *args, **kwargs)\u001b[0m\n\u001b[1;32m     65\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m---> 66\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43msend\u001b[49m\u001b[43m(\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     67\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m requests\u001b[38;5;241m.\u001b[39mRequestException \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/requests/adapters.py:667\u001b[0m, in \u001b[0;36mHTTPAdapter.send\u001b[0;34m(self, request, stream, timeout, verify, cert, proxies)\u001b[0m\n\u001b[1;32m    666\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 667\u001b[0m     resp \u001b[38;5;241m=\u001b[39m \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43murlopen\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    668\u001b[0m \u001b[43m        \u001b[49m\u001b[43mmethod\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    669\u001b[0m \u001b[43m        \u001b[49m\u001b[43murl\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    670\u001b[0m \u001b[43m        \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    671\u001b[0m \u001b[43m        \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrequest\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    672\u001b[0m \u001b[43m        \u001b[49m\u001b[43mredirect\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    673\u001b[0m \u001b[43m        \u001b[49m\u001b[43massert_same_host\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    674\u001b[0m \u001b[43m        \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    675\u001b[0m \u001b[43m        \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m,\u001b[49m\n\u001b[1;32m    676\u001b[0m \u001b[43m        \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mmax_retries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    677\u001b[0m \u001b[43m        \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    678\u001b[0m \u001b[43m        \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    679\u001b[0m \u001b[43m    \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    681\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (ProtocolError, \u001b[38;5;167;01mOSError\u001b[39;00m) \u001b[38;5;28;01mas\u001b[39;00m err:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/urllib3/connectionpool.py:789\u001b[0m, in \u001b[0;36mHTTPConnectionPool.urlopen\u001b[0;34m(self, method, url, body, headers, retries, redirect, assert_same_host, timeout, pool_timeout, release_conn, chunked, body_pos, preload_content, decode_content, **response_kw)\u001b[0m\n\u001b[1;32m    788\u001b[0m \u001b[38;5;66;03m# Make the request on the HTTPConnection object\u001b[39;00m\n\u001b[0;32m--> 789\u001b[0m response \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_make_request\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    790\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    791\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmethod\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    792\u001b[0m \u001b[43m    \u001b[49m\u001b[43murl\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    793\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtimeout_obj\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    794\u001b[0m \u001b[43m    \u001b[49m\u001b[43mbody\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mbody\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    795\u001b[0m \u001b[43m    \u001b[49m\u001b[43mheaders\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mheaders\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    796\u001b[0m \u001b[43m    \u001b[49m\u001b[43mchunked\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mchunked\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    797\u001b[0m \u001b[43m    \u001b[49m\u001b[43mretries\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mretries\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    798\u001b[0m \u001b[43m    \u001b[49m\u001b[43mresponse_conn\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mresponse_conn\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    799\u001b[0m \u001b[43m    \u001b[49m\u001b[43mpreload_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mpreload_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    800\u001b[0m \u001b[43m    \u001b[49m\u001b[43mdecode_content\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mdecode_content\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    801\u001b[0m \u001b[43m    \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mresponse_kw\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    802\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    804\u001b[0m \u001b[38;5;66;03m# Everything went great!\u001b[39;00m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/urllib3/connectionpool.py:466\u001b[0m, in \u001b[0;36mHTTPConnectionPool._make_request\u001b[0;34m(self, conn, method, url, body, headers, retries, timeout, chunked, response_conn, preload_content, decode_content, enforce_content_length)\u001b[0m\n\u001b[1;32m    465\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[0;32m--> 466\u001b[0m     \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_validate_conn\u001b[49m\u001b[43m(\u001b[49m\u001b[43mconn\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    467\u001b[0m \u001b[38;5;28;01mexcept\u001b[39;00m (SocketTimeout, BaseSSLError) \u001b[38;5;28;01mas\u001b[39;00m e:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/urllib3/connectionpool.py:1095\u001b[0m, in \u001b[0;36mHTTPSConnectionPool._validate_conn\u001b[0;34m(self, conn)\u001b[0m\n\u001b[1;32m   1094\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m conn\u001b[38;5;241m.\u001b[39mis_closed:\n\u001b[0;32m-> 1095\u001b[0m     \u001b[43mconn\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mconnect\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1097\u001b[0m \u001b[38;5;66;03m# TODO revise this, see https://github.com/urllib3/urllib3/issues/2791\u001b[39;00m\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/urllib3/connection.py:652\u001b[0m, in \u001b[0;36mHTTPSConnection.connect\u001b[0;34m(self)\u001b[0m\n\u001b[1;32m    650\u001b[0m server_hostname_rm_dot \u001b[38;5;241m=\u001b[39m server_hostname\u001b[38;5;241m.\u001b[39mrstrip(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m.\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m--> 652\u001b[0m sock_and_verified \u001b[38;5;241m=\u001b[39m \u001b[43m_ssl_wrap_socket_and_match_hostname\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    653\u001b[0m \u001b[43m    \u001b[49m\u001b[43msock\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43msock\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    654\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcert_reqs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcert_reqs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    655\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mssl_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    656\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_minimum_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mssl_minimum_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    657\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_maximum_version\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mssl_maximum_version\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    658\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_certs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_certs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    659\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_dir\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    660\u001b[0m \u001b[43m    \u001b[49m\u001b[43mca_cert_data\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mca_cert_data\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    661\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcert_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mcert_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    662\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkey_file\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_file\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    663\u001b[0m \u001b[43m    \u001b[49m\u001b[43mkey_password\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mkey_password\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    664\u001b[0m \u001b[43m    \u001b[49m\u001b[43mserver_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mserver_hostname_rm_dot\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    665\u001b[0m \u001b[43m    \u001b[49m\u001b[43mssl_context\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mssl_context\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    666\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtls_in_tls\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtls_in_tls\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    667\u001b[0m \u001b[43m    \u001b[49m\u001b[43massert_hostname\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43massert_hostname\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    668\u001b[0m \u001b[43m    \u001b[49m\u001b[43massert_fingerprint\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43massert_fingerprint\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    669\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    670\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39msock \u001b[38;5;241m=\u001b[39m sock_and_verified\u001b[38;5;241m.\u001b[39msocket\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/urllib3/connection.py:767\u001b[0m, in \u001b[0;36m_ssl_wrap_socket_and_match_hostname\u001b[0;34m(sock, cert_reqs, ssl_version, ssl_minimum_version, ssl_maximum_version, cert_file, key_file, key_password, ca_certs, ca_cert_dir, ca_cert_data, assert_hostname, assert_fingerprint, server_hostname, ssl_context, tls_in_tls)\u001b[0m\n\u001b[1;32m    765\u001b[0m     context \u001b[38;5;241m=\u001b[39m ssl_context\n\u001b[0;32m--> 767\u001b[0m \u001b[43mcontext\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify_mode\u001b[49m \u001b[38;5;241m=\u001b[39m resolve_cert_reqs(cert_reqs)\n\u001b[1;32m    769\u001b[0m \u001b[38;5;66;03m# In some cases, we want to verify hostnames ourselves\u001b[39;00m\n", "File \u001b[0;32m/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py:720\u001b[0m, in \u001b[0;36mSSLContext.verify_mode\u001b[0;34m(self, value)\u001b[0m\n\u001b[1;32m    718\u001b[0m \u001b[38;5;129m@verify_mode\u001b[39m\u001b[38;5;241m.\u001b[39msetter\n\u001b[1;32m    719\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mverify_mode\u001b[39m(\u001b[38;5;28mself\u001b[39m, value):\n\u001b[0;32m--> 720\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify_mode\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__set__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n", "File \u001b[0;32m/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py:720\u001b[0m, in \u001b[0;36mSSLContext.verify_mode\u001b[0;34m(self, value)\u001b[0m\n\u001b[1;32m    718\u001b[0m \u001b[38;5;129m@verify_mode\u001b[39m\u001b[38;5;241m.\u001b[39msetter\n\u001b[1;32m    719\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mverify_mode\u001b[39m(\u001b[38;5;28mself\u001b[39m, value):\n\u001b[0;32m--> 720\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify_mode\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__set__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n", "    \u001b[0;31m[... skipping similar frames: SSLContext.verify_mode at line 720 (1470 times)]\u001b[0m\n", "File \u001b[0;32m/Applications/Xcode.app/Contents/Developer/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/ssl.py:720\u001b[0m, in \u001b[0;36mSSLContext.verify_mode\u001b[0;34m(self, value)\u001b[0m\n\u001b[1;32m    718\u001b[0m \u001b[38;5;129m@verify_mode\u001b[39m\u001b[38;5;241m.\u001b[39msetter\n\u001b[1;32m    719\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mverify_mode\u001b[39m(\u001b[38;5;28mself\u001b[39m, value):\n\u001b[0;32m--> 720\u001b[0m     \u001b[38;5;28;43msuper\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mSSLContext\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mverify_mode\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[38;5;21;43m__set__\u001b[39;49m\u001b[43m(\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mvalue\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[0;31mRecursionError\u001b[0m: maximum recursion depth exceeded", "\nDuring handling of the above exception, another exception occurred:\n", "\u001b[0;31mOSError\u001b[0m                                   Traceback (most recent call last)", "Cell \u001b[0;32mIn[7], line 9\u001b[0m\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01msentence_transformers\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m SentenceTransformer\n\u001b[1;32m      8\u001b[0m \u001b[38;5;66;03m# Pre-calculate embeddings\u001b[39;00m\n\u001b[0;32m----> 9\u001b[0m embedding_model \u001b[38;5;241m=\u001b[39m \u001b[43mSentenceTransformer\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[38;5;124;43mall-MiniLM-L6-v2\u001b[39;49m\u001b[38;5;124;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m     10\u001b[0m representation_model\u001b[38;5;241m=\u001b[39mKeyBERTInspired()\n\u001b[1;32m     11\u001b[0m umap_model\u001b[38;5;241m=\u001b[39mUMAP(random_state\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/sentence_transformers/SentenceTransformer.py:299\u001b[0m, in \u001b[0;36mSentenceTransformer.__init__\u001b[0;34m(self, model_name_or_path, modules, device, prompts, default_prompt_name, similarity_fn_name, cache_folder, trust_remote_code, revision, local_files_only, token, use_auth_token, truncate_dim, model_kwargs, tokenizer_kwargs, config_kwargs, model_card_data)\u001b[0m\n\u001b[1;32m    287\u001b[0m         modules \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_load_sbert_model(\n\u001b[1;32m    288\u001b[0m             model_name_or_path,\n\u001b[1;32m    289\u001b[0m             token\u001b[38;5;241m=\u001b[39mtoken,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m    296\u001b[0m             config_kwargs\u001b[38;5;241m=\u001b[39mconfig_kwargs,\n\u001b[1;32m    297\u001b[0m         )\n\u001b[1;32m    298\u001b[0m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[0;32m--> 299\u001b[0m         modules \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_load_auto_model\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m    300\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmodel_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    301\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtoken\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtoken\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    302\u001b[0m \u001b[43m            \u001b[49m\u001b[43mcache_folder\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    303\u001b[0m \u001b[43m            \u001b[49m\u001b[43mrevision\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mrevision\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    304\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtrust_remote_code\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtrust_remote_code\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    305\u001b[0m \u001b[43m            \u001b[49m\u001b[43mlocal_files_only\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mlocal_files_only\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    306\u001b[0m \u001b[43m            \u001b[49m\u001b[43mmodel_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    307\u001b[0m \u001b[43m            \u001b[49m\u001b[43mtokenizer_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtokenizer_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    308\u001b[0m \u001b[43m            \u001b[49m\u001b[43mconfig_kwargs\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m    309\u001b[0m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    311\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m modules \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(modules, OrderedDict):\n\u001b[1;32m    312\u001b[0m     modules \u001b[38;5;241m=\u001b[39m OrderedDict([(\u001b[38;5;28mstr\u001b[39m(idx), module) \u001b[38;5;28;01mfor\u001b[39;00m idx, module \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(modules)])\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/sentence_transformers/SentenceTransformer.py:1324\u001b[0m, in \u001b[0;36mSentenceTransformer._load_auto_model\u001b[0;34m(self, model_name_or_path, token, cache_folder, revision, trust_remote_code, local_files_only, model_kwargs, tokenizer_kwargs, config_kwargs)\u001b[0m\n\u001b[1;32m   1321\u001b[0m tokenizer_kwargs \u001b[38;5;241m=\u001b[39m shared_kwargs \u001b[38;5;28;01mif\u001b[39;00m tokenizer_kwargs \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m {\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mshared_kwargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mtokenizer_kwargs}\n\u001b[1;32m   1322\u001b[0m config_kwargs \u001b[38;5;241m=\u001b[39m shared_kwargs \u001b[38;5;28;01mif\u001b[39;00m config_kwargs \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;28;01melse\u001b[39;00m {\u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mshared_kwargs, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mconfig_kwargs}\n\u001b[0;32m-> 1324\u001b[0m transformer_model \u001b[38;5;241m=\u001b[39m \u001b[43mTransformer\u001b[49m\u001b[43m(\u001b[49m\n\u001b[1;32m   1325\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel_name_or_path\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1326\u001b[0m \u001b[43m    \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_folder\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1327\u001b[0m \u001b[43m    \u001b[49m\u001b[43mmodel_args\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mmodel_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1328\u001b[0m \u001b[43m    \u001b[49m\u001b[43mtokenizer_args\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mtokenizer_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1329\u001b[0m \u001b[43m    \u001b[49m\u001b[43mconfig_args\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mconfig_kwargs\u001b[49m\u001b[43m,\u001b[49m\n\u001b[1;32m   1330\u001b[0m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m   1331\u001b[0m pooling_model \u001b[38;5;241m=\u001b[39m Pooling(transformer_model\u001b[38;5;241m.\u001b[39mget_word_embedding_dimension(), \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmean\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[1;32m   1332\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39mmodel_card_data\u001b[38;5;241m.\u001b[39mset_base_model(model_name_or_path, revision\u001b[38;5;241m=\u001b[39mrevision)\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/sentence_transformers/models/Transformer.py:53\u001b[0m, in \u001b[0;36mTransformer.__init__\u001b[0;34m(self, model_name_or_path, max_seq_length, model_args, tokenizer_args, config_args, cache_dir, do_lower_case, tokenizer_name_or_path)\u001b[0m\n\u001b[1;32m     50\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m config_args \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[1;32m     51\u001b[0m     config_args \u001b[38;5;241m=\u001b[39m {}\n\u001b[0;32m---> 53\u001b[0m config \u001b[38;5;241m=\u001b[39m \u001b[43mAutoConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mfrom_pretrained\u001b[49m\u001b[43m(\u001b[49m\u001b[43mmodel_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mconfig_args\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcache_dir\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[43mcache_dir\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m     54\u001b[0m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_load_model(model_name_or_path, config, cache_dir, \u001b[38;5;241m*\u001b[39m\u001b[38;5;241m*\u001b[39mmodel_args)\n\u001b[1;32m     56\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m max_seq_length \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_max_length\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m tokenizer_args:\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/transformers/models/auto/configuration_auto.py:965\u001b[0m, in \u001b[0;36mAutoConfig.from_pretrained\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    962\u001b[0m trust_remote_code \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mtrust_remote_code\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[1;32m    963\u001b[0m code_revision \u001b[38;5;241m=\u001b[39m kwargs\u001b[38;5;241m.\u001b[39mpop(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mcode_revision\u001b[39m\u001b[38;5;124m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[0;32m--> 965\u001b[0m config_dict, unused_kwargs \u001b[38;5;241m=\u001b[39m \u001b[43mPretrainedConfig\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mget_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    966\u001b[0m has_remote_code \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto_map\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict \u001b[38;5;129;01mand\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mAutoConfig\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mauto_map\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n\u001b[1;32m    967\u001b[0m has_local_code \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_type\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict \u001b[38;5;129;01mand\u001b[39;00m config_dict[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mmodel_type\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;129;01min\u001b[39;00m CONFIG_MAPPING\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/transformers/configuration_utils.py:632\u001b[0m, in \u001b[0;36mPretrainedConfig.get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    630\u001b[0m original_kwargs \u001b[38;5;241m=\u001b[39m copy\u001b[38;5;241m.\u001b[39mdeepcopy(kwargs)\n\u001b[1;32m    631\u001b[0m \u001b[38;5;66;03m# Get config dict associated with the base config file\u001b[39;00m\n\u001b[0;32m--> 632\u001b[0m config_dict, kwargs \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mcls\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m_get_config_dict\u001b[49m\u001b[43m(\u001b[49m\u001b[43mpretrained_model_name_or_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[38;5;241;43m*\u001b[39;49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[1;32m    633\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_commit_hash\u001b[39m\u001b[38;5;124m\"\u001b[39m \u001b[38;5;129;01min\u001b[39;00m config_dict:\n\u001b[1;32m    634\u001b[0m     original_kwargs[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_commit_hash\u001b[39m\u001b[38;5;124m\"\u001b[39m] \u001b[38;5;241m=\u001b[39m config_dict[\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m_commit_hash\u001b[39m\u001b[38;5;124m\"\u001b[39m]\n", "File \u001b[0;32m~/xbot-experiment/env/lib/python3.9/site-packages/transformers/configuration_utils.py:710\u001b[0m, in \u001b[0;36mPretrainedConfig._get_config_dict\u001b[0;34m(cls, pretrained_model_name_or_path, **kwargs)\u001b[0m\n\u001b[1;32m    707\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m\n\u001b[1;32m    708\u001b[0m     \u001b[38;5;28;01mexcept\u001b[39;00m \u001b[38;5;167;01mException\u001b[39;00m:\n\u001b[1;32m    709\u001b[0m         \u001b[38;5;66;03m# For any other exception, we throw a generic error.\u001b[39;00m\n\u001b[0;32m--> 710\u001b[0m         \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mEnvironmentError\u001b[39;00m(\n\u001b[1;32m    711\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mCan\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt load the configuration of \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpretrained_model_name_or_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m. If you were trying to load it\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    712\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m from \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://huggingface.co/models\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m, make sure you don\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt have a local directory with the same\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    713\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m name. Otherwise, make sure \u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mpretrained_model_name_or_path\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124m is the correct path to a directory\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    714\u001b[0m             \u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m containing a \u001b[39m\u001b[38;5;132;01m{\u001b[39;00mconfiguration_file\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m file\u001b[39m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    715\u001b[0m         )\n\u001b[1;32m    717\u001b[0m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[1;32m    718\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m gguf_file:\n", "\u001b[0;31mOSError\u001b[0m: Can't load the configuration of 'sentence-transformers/all-MiniLM-L6-v2'. If you were trying to load it from 'https://huggingface.co/models', make sure you don't have a local directory with the same name. Otherwise, make sure 'sentence-transformers/all-MiniLM-L6-v2' is the correct path to a directory containing a config.json file"]}], "source": ["from chat import ChatService\n", "from bertopic import BERTopic\n", "from bertopic.representation import KeyBERTInspired\n", "from umap import UMAP\n", "\n", "from sentence_transformers import SentenceTransformer\n", "\n", "# Pre-calculate embeddings\n", "embedding_model = SentenceTransformer('all-MiniLM-L6-v2')\n", "representation_model=KeyBERTInspired()\n", "umap_model=UMAP(random_state=0)\n", "\n", "aiservice = ChatService()\n", "\n", "def get_topic(keyword, get_ai_topics=True):\n", "    temp_df = df[df.keyword == keyword]\n", "    model = BERTopic(language='en', embedding_model=embedding_model, representation_model=representation_model, umap_model=umap_model,\n", "                 top_n_words=10, min_topic_size=10, verbose=True)\n", "    docs = temp_df.full_text\n", "    embeddings = embedding_model.encode(docs, show_progress_bar=True)\n", "    topics, _ = model.fit_transform(docs, embeddings)\n", "    topics_df = model.get_topic_info()\n", "\n", "    if get_ai_topics:\n", "        for i, row in topics_df.iterrows():\n", "            if i < 1:\n", "                continue\n", "            docs = '\\n- '.join(row['Representative_Docs'])\n", "            keywords = row['Representation']\n", "            # use minimum adjective, yet eye catching, \n", "            # predicate or verb\n", "            prompt = f\"\"\"You are an expert in social media and SEO, please help me extract the main topic/event/theme covered in the following tweets.\n", "        tweets:\n", "        - {docs}\n", "        (common keywords of these tweets are: {keywords})\n", "        Requirement:\n", "        - The topic should be short, succinct, such as commonly seen on the social medias trending list. \n", "        - prefered format is SUBJECT verb OBEJECT\n", "        topic:\"\"\"    \n", "            status, msg = aiservice.chat(prompt, model='gpt-3.5-turbo', temperature=0.1)\n", "            print('\\n==Topic ', i-1, row.Name)\n", "            # print(prompt)\n", "            print('>>', msg)\n", "    return temp_df, topics_df, model"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'get_topic' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[5], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m ts_df, ts_topics_df, ts_model \u001b[38;5;241m=\u001b[39m \u001b[43mget_topic\u001b[49m(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124<PERSON><PERSON><PERSON><PERSON> swift\u001b[39m\u001b[38;5;124m'\u001b[39m)\n", "\u001b[0;31mNameError\u001b[0m: name 'get_topic' is not defined"]}], "source": ["ts_df, ts_topics_df, ts_model = get_topic('taylor swift')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}