# 快速使用指南

## 🚀 快速开始

### 方法一：使用交互式脚本（推荐）

```bash
# 在项目根目录运行
./test/quick_test.sh
```

然后根据提示选择要测试的服务类型。

### 方法二：直接使用Python脚本

```bash
# 测试封面生成服务
python test/test_redis_queue.py --service cover_gen

# 测试账号诊断服务  
python test/test_redis_queue.py --service diagnosis

# 使用自定义数据文件
python test/test_redis_queue.py --service cover_gen --data-file test/sample_data/cover_gen_sample.json
```

## 📋 测试前准备

1. **确保Redis服务运行正常**
2. **启动对应的服务脚本**：
   ```bash
   # 启动封面生成服务（推荐使用新版本）
   python task/server/account_diagnosis/new_script_for_cover_gen.py
   # 或使用旧版本
   python task/server/account_diagnosis/script_for_cover_gen.py
   
   # 启动诊断服务
   python task/server/account_diagnosis/new_script_for_diagnosis.py
   # 或使用旧版本
   python task/server/account_diagnosis/diagnosis_core.py
   
   # 启动策略生成服务
   python task/server/account_diagnosis/new_script_for_strategy.py
   
   # 启动运营复盘服务
   python task/server/account_diagnosis/new_script_for_review.py
   ```

## 🎯 测试封面生成服务

### 启动服务
```bash
# 在一个终端窗口启动封面生成服务（推荐使用新版本）
python task/server/account_diagnosis/new_script_for_cover_gen.py

# 或使用旧版本
python task/server/account_diagnosis/script_for_cover_gen.py
```

### 发送测试任务
```bash
# 在另一个终端窗口发送测试任务
python test/test_redis_queue.py --service cover_gen

# 或使用自定义数据
python test/test_redis_queue.py --service cover_gen --data-file test/sample_data/cover_gen_sample.json
```

### 查看结果
- 生成的HTML文件：`test_cover_result_{task_id}.html`
- 日志文件：`logs/redis_queue_test_cover_gen.log`

## 🔍 测试账号诊断服务

### 启动服务
```bash
# 启动诊断服务（推荐使用新版本）
python task/server/account_diagnosis/new_script_for_diagnosis.py

# 或使用旧版本
python task/server/account_diagnosis/diagnosis_core.py
```

### 发送测试任务
```bash
python test/test_redis_queue.py --service diagnosis --data-file test/sample_data/diagnosis_sample.json
```

## 📊 查看可用的封面样式

封面生成支持多种样式，在 `prompts/style_templates/rednote_cover.json` 中定义：

- 奢华自然意境风
- 新潮工业反叛风  
- 软萌知识卡片风
- 商务简约信息卡片风
- 新构成主义教学风
- 数字极简票券风
- 柔和科技卡片风
- 现代商务资讯卡片风
- 流动科技蓝风格
- 极简格栅主义封面风格
- 简约醒目风
- 干净蓝色背景大字风

## 🛠️ 自定义测试数据

### 创建封面生成数据文件

```json
{
  "taskInfo": {
    "taskId": "my_custom_test"
  },
  "style": "奢华自然意境风",
  "content": "我的自定义封面内容\n副标题\n更多说明文字"
}
```

### 创建诊断数据文件

```json
{
  "taskInfo": {
    "taskId": "my_diagnosis_test"
  },
  "accountInfo": {
    "name": "我的测试账号",
    "description": "账号描述",
    "followers": 1000
  },
  "noteList": [
    {
      "title": "测试笔记",
      "content": "笔记内容",
      "likes": 100
    }
  ],
  "marketingGoal": "涨粉提升",
  "industry": "美食"
}
```

## 🐛 常见问题

### Redis连接失败
```
Redis连接失败: Connection refused
```
**解决方案**：检查Redis服务是否启动，确认配置正确。

### 任务处理超时
```
等待响应超时 (300秒)
```
**解决方案**：
1. 检查对应服务是否正在运行
2. 增加超时时间：`--timeout 600`
3. 查看服务日志排查问题

### JSON格式错误
```
JSON解析错误: Expecting ',' delimiter
```
**解决方案**：验证自定义数据文件的JSON格式是否正确。

## 📁 输出文件说明

| 文件类型 | 文件名格式 | 说明 |
|---------|-----------|------|
| 封面HTML | `test_cover_result_{task_id}.html` | 生成的封面HTML代码 |
| 诊断报告 | `test_diagnosis_report_{task_id}.html` | 账号诊断HTML报告 |
| 策略报告 | `test_strategy_report_{task_id}.html` | 策略生成HTML报告 |
| 复盘报告 | `test_review_report_{task_id}.html` | 运营复盘HTML报告 |
| 日志文件 | `logs/redis_queue_test_{service}.log` | 测试过程日志 |

## 🔧 高级用法

### 只发送任务，不等待响应
```bash
python test/test_redis_queue.py --service cover_gen --no-wait
```

### 设置自定义超时时间
```bash
python test/test_redis_queue.py --service diagnosis --timeout 600
```

### 批量测试
```bash
# 创建批量测试脚本
for service in cover_gen diagnosis strategy review; do
    echo "测试 $service 服务..."
    python test/test_redis_queue.py --service $service --timeout 60
done
``` 