# Redis 队列测试工具

这个工具用于测试 `task/server/account_diagnosis` 目录下的各种异步服务，包括封面生成、账号诊断、策略生成和运营复盘等服务。

## 功能特性

- 🚀 支持多种服务类型测试
- 📝 可配置的输入数据
- 🔄 自动处理Redis双重序列化
- 📊 智能响应处理和结果保存
- 📋 详细的日志记录

## 支持的服务类型

| 服务类型 | 描述 | 对应脚本 |
|---------|------|---------|
| `cover_gen` | 封面生成服务 | `script_for_cover_gen.py` |
| `diagnosis` | 账号诊断服务 | `diagnosis_core.py` |
| `strategy` | 策略生成服务 | `script_for_strategy.py` |
| `review` | 运营复盘服务 | `script_for_review.py` |

## 安装依赖

确保你已经安装了项目的依赖：

```bash
pip install -r requirements.txt
```

## 使用方法

### 基本用法

```bash
# 测试封面生成服务（默认）
python test/test_redis_queue.py

# 测试账号诊断服务
python test/test_redis_queue.py --service diagnosis

# 测试策略生成服务
python test/test_redis_queue.py --service strategy

# 测试运营复盘服务
python test/test_redis_queue.py --service review
```

### 高级用法

```bash
# 使用自定义数据文件
python test/test_redis_queue.py --service cover_gen --data-file test/sample_data/cover_gen_sample.json

# 只发送任务，不等待响应
python test/test_redis_queue.py --service diagnosis --no-wait

# 设置自定义超时时间（秒）
python test/test_redis_queue.py --service strategy --timeout 600

# 列出所有可用的服务类型
python test/test_redis_queue.py --list-services
```

## 自定义数据文件

你可以创建自己的JSON数据文件来测试特定场景。示例文件位于 `test/sample_data/` 目录：

- `cover_gen_sample.json` - 封面生成测试数据
- `diagnosis_sample.json` - 账号诊断测试数据

### 封面生成数据格式

```json
{
  "taskInfo": {
    "taskId": "your_test_id"
  },
  "style": "奢华自然意境风",
  "content": "你的封面内容文本",
  "mediaList": [
    {
      "mediaType": "image",
      "mediaUrl": "https://example.com/image.jpg",
      "mediaPath": "/path/to/image.jpg"
    }
  ]
}
```

### 账号诊断数据格式

```json
{
  "taskInfo": {
    "taskId": "your_test_id"
  },
  "accountInfo": {
    "name": "账号名称",
    "id": "账号ID",
    "description": "账号描述",
    "followers": 1000,
    "following": 100,
    "posts": 50,
    "avatarUrl": "头像URL"
  },
  "noteList": [
    {
      "title": "笔记标题",
      "content": "笔记内容",
      "likes": 100,
      "comments": 20,
      "favorites": 30,
      "publishTime": "2024-01-01T12:00:00Z"
    }
  ],
  "marketingGoal": "涨粉提升",
  "industry": "美食"
}
```

## 输出结果

测试完成后，工具会：

1. **控制台输出**: 显示发送的数据和收到的响应
2. **日志文件**: 保存在 `logs/redis_queue_test_{service_type}.log`
3. **结果文件**: 
   - 封面生成: `test_cover_result_{task_id}.html`
   - 诊断报告: `test_diagnosis_report_{task_id}.html`
   - 策略报告: `test_strategy_report_{task_id}.html`
   - 复盘报告: `test_review_report_{task_id}.html`

## 队列配置

工具会根据环境变量 `ENV` 自动配置Redis队列名称：

| 服务类型 | 输入队列 | 输出队列 |
|---------|---------|---------|
| 封面生成 | `{env}:q:generate:cover:request` | `{env}:q:generate:cover:response` |
| 账号诊断 | `{env}:q:diagnosis:request` | `{env}:q:diagnosis:response` |
| 策略生成 | `{env}:q:generate:report:socialmedia:request` | `{env}:q:generate:report:socialmedia:response` |
| 运营复盘 | `{env}:q:generate:report:operation:request` | `{env}:q:generate:report:operation:response` |

例如，在开发环境下的队列名称：
- 封面生成: `dev:q:generate:cover:request` / `dev:q:generate:cover:response`
- 策略生成: `dev:q:generate:report:socialmedia:request` / `dev:q:generate:report:socialmedia:response`

## 故障排除

### Redis连接失败
- 检查Redis服务是否运行
- 验证 `task/__init__.py` 中的 `REDIS_CLUSTER_CONFIG` 配置
- 确保网络连接正常

### 任务处理超时
- 检查对应的服务脚本是否正在运行
- 增加 `--timeout` 参数值
- 查看服务脚本的日志文件

### JSON解析错误
- 验证自定义数据文件的JSON格式
- 确保数据文件编码为UTF-8
- 检查必需字段是否完整

## 开发说明

如果你需要添加新的服务类型：

1. 在 `ServiceType` 枚举中添加新类型
2. 在 `SERVICE_CONFIGS` 中添加对应配置
3. 在 `get_mock_data()` 中添加默认数据生成逻辑
4. 在 `_process_response()` 中添加响应处理逻辑

## 示例输出

```
🚀 开始测试 封面生成服务
输入队列: dev:q:generate:cover:request
输出队列: dev:q:generate:cover:response

==================== 发送的任务数据 ====================
{
  "taskInfo": {
    "taskId": "test_cover_1704067200"
  },
  "style": "奢华自然意境风",
  "content": "打工人摸鱼指南\n教你如何在工作中合理休息\n提高工作效率的小技巧"
}
============================================================

==================== 收到响应 ====================
任务ID: test_cover_1704067200
响应时间: 2024-01-01 12:00:00
✅ 响应成功
✅ 封面HTML已保存到: /path/to/test_cover_result_test_cover_1704067200.html
============================================================

✅ 测试完成 