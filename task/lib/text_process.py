import re
import json
import string


def batch_list(long_list, batch_size=500):
    # 创建一个新的列表来存放所有批次
    batched_list = []

    # 计算总共需要的批次数
    total_batches = len(long_list) // batch_size + (1 if len(long_list) % batch_size != 0 else 0)

    # 将长列表按批次分割
    for i in range(total_batches):
        # 计算当前批次的起始和结束索引
        start_index = i * batch_size
        end_index = min((i + 1) * batch_size, len(long_list))

        # 将当前批次的元素添加到新的列表中
        batched_list.append(long_list[start_index:end_index])

    return batched_list


def str_count(input_str):
    """Count the number of English, Chinese, space, digit, and punctuation characters in a string"""
    # Define a set of Chinese double-width punctuation
    chinese_punctuation = {'。', '，', '、', '；', '：', '？', '！', '（', '）', '【', '】', '《', '》', '“', '”'}
    count_en = sum(c in string.ascii_letters for c in input_str)
    count_dg = sum(c.isdigit() for c in input_str)
    count_sp = sum(c.isspace() for c in input_str)
    count_pu = sum((c in string.punctuation or c in chinese_punctuation) and c not in chinese_punctuation for c in
                   input_str)  # Punctuation excluding Chinese punctuation
    count_zh = sum('\u4e00' <= c <= '\u9fff' for c in input_str) + sum(c in chinese_punctuation for c in input_str)
    count_other = len(input_str) - count_en - count_dg - count_sp - count_pu - count_zh
    return 2 * count_zh + count_en + count_sp + count_dg + count_pu + count_other


def split_string(input_string):
    parts = input_string.split("|")
    content = parts[0].strip()
    keywords = parts[1].split(",")
    if len(parts) < 3:
        return content, [keyword.strip() for keyword in keywords if keyword.strip()], []
    original_keys = parts[2].split(",")
    keyword_list = [keyword.strip() for keyword in keywords if keyword.strip()]
    original_key_list = [keyword.strip() for keyword in original_keys if keyword.strip()]
    return content, keyword_list, original_key_list


def flatten_dictionary(d):
    flattened_list = []
    for value in d.values():
        if isinstance(value, list) and value:
            flattened_list.extend(value)  # 如果值是列表，则将其元素添加到结果列表中
        elif isinstance(value, str) and value:
            flattened_list.append(value)  # 如果值是字符串，则直接添加到结果列表中
    return flattened_list


def load_char_types(input_file):
    """Load the character types of the input file"""
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    return data


def clean_json_string(json_string):
    # 去除前面的 ```json
    if json_string.startswith("```json"):
        json_string = json_string[len("```json"):]

    # 去除后面的 ```
    if json_string.endswith("```"):
        json_string = json_string[:len(json_string) - len("```")]

    # 去除前后多余的空白字符
    json_string = json_string.strip()

    return json_string


def replace_patterns(dict_data: dict, key: str):
    # Define the pattern to replace: ' and ', colons, and quotation marks
    pattern = r' and |:|"'
    replacement = ', '  # You can adjust this replacement as needed

    # Check if the key exists in the dictionary and that it's associated with a list or string
    if key in dict_data:
        if isinstance(dict_data[key], list):
            # Replace patterns in each string in the list
            dict_data[key] = [re.sub(pattern, replacement, item) for item in dict_data[key]]
        elif isinstance(dict_data[key], str):
            # Replace patterns in the string
            dict_data[key] = re.sub(pattern, replacement, dict_data[key])
    return dict_data


def truncate_paragraph(text: str, front_len: int = 900, back_len: int = 1500):
    """
    Truncate the text by keeping the first front_len and last back_len characters
    Args:
        text: str, the text to truncate
        front_len: int, the number of characters to keep from the beginning
        back_len: int, the number of characters to keep from the end

    Returns:
        The truncated text
    """
    if len(text) <= front_len + back_len:
        return False, text, ""

    return True, text[:front_len], text[-back_len:]


def truncate(text: str, direct: str = 'fd', token_len: int = 1000):
    """
    对超长文本进行截断,截取最大长度为token_len对应的文本
    Args:
        text: str, 需要截断的文本内容
        direct: str, 截断方向，"fd": 前向截取，"bd": 后向截取
        token_len: int, 截取最大tokens长度

    Returns:
        1.truncate_text: str,返回截取后的文本
        2.truncate_text的tokens长度
        3.输入输出字符是否改变
    """
    if not text or text.isspace():
        return "", 0, "unchanged"

    if len(text) <= token_len:
        return text, len(text), "unchanged"

    if direct == "fd":
        truncate_text = text[:token_len]
    else:
        truncate_text = text[-token_len:]

    punctuation_marks = ".?!。？！\n"
    if direct == "fd":
        last_punctuation = max(truncate_text.rfind(mark) for mark in punctuation_marks)
        truncate_text = truncate_text[:last_punctuation + 1] if last_punctuation != -1 else truncate_text
    else:
        first_punctuation = min((idx for idx, char in enumerate(truncate_text) if char in punctuation_marks),
                                default=-1)
        truncate_text = truncate_text[first_punctuation + 1:]

    return truncate_text, len(truncate_text), "changed"


def truncate_link_str(token_num: int, link_str: str, token_cap=30):
    token = token_num - int(len(link_str)*0.3)

    # 如果 token <= token_cap，需要截断 link_str
    if token <= token_cap:
        # 计算需要截断的长度
        truncate_length = len(link_str) - (token_cap - token)
        # 截断 link_str，使得 token 的值至少为 30
        link_str = link_str[:truncate_length]
        # 更新 token 值
        token = token_num - int(len(link_str)*0.3)

    return token, link_str
