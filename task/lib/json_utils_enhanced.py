#!/usr/bin/env python3
"""
增强版JSON处理工具模块
集成json_repair库，提供完整的JSON处理功能
这个模块可以完全替代原有的json_utils.py
"""

import json
import re
import sys
import os
import logging
from typing import Tuple, List, Any, Dict, Optional

# 尝试导入json_repair
try:
    import json_repair
    HAS_JSON_REPAIR = True
except ImportError:
    HAS_JSON_REPAIR = False
    logging.warning("json_repair library not found. Install with: pip install json-repair")

# 获取logger实例
logger = logging.getLogger(__name__)

# 配置选项
ENABLE_LLM_REPAIR = True  # 是否启用LLM修复功能
DEFAULT_LLM_MODEL = "gpt-4o-mini"  # 默认使用的LLM模型
USE_JSON_REPAIR = HAS_JSON_REPAIR  # 是否使用json_repair库


def deep_clean_control_characters(data: Any) -> Any:
    """
    递归清理数据中的控制字符
    对HTML内容进行特殊处理，保持其格式完整性
    """
    def is_html_content(text: str) -> bool:
        """检测字符串是否包含HTML内容"""
        if not isinstance(text, str):
            return False
        html_indicators = ['<html', '<body', '<div', '<p>', '<span', '<script', '<style', '<!DOCTYPE', '<h1', '<h2', '<h3']
        return any(indicator in text.lower() for indicator in html_indicators)
    
    def clean_string(text: str) -> str:
        """清理字符串中的控制字符"""
        if is_html_content(text):
            # 对于HTML内容，只清理真正有问题的控制字符，保留换行符
            cleaned = text.replace('\r\n', '\n')  # 统一换行符
            cleaned = cleaned.replace('\r', '\n')  # 统一换行符
            # 清理其他控制字符，但保留换行符和制表符
            cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
            return cleaned
        else:
            # 对于普通文本，进行标准的控制字符转义
            cleaned = text.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
            # 清理其他可能的控制字符
            cleaned = cleaned.replace('\b', '\\b').replace('\f', '\\f')
            # 清理Unicode控制字符
            cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', lambda m: f'\\u{ord(m.group()):04x}', cleaned)
            return cleaned
    
    if isinstance(data, str):
        return clean_string(data)
    elif isinstance(data, dict):
        return {key: deep_clean_control_characters(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [deep_clean_control_characters(item) for item in data]
    else:
        return data


def safe_json_dumps(data: Any, **kwargs) -> str:
    """
    安全的JSON序列化函数
    使用深度清理确保数据可以被正确序列化
    """
    try:
        # 深度清理数据
        cleaned_data = deep_clean_control_characters(data)
        
        # 设置安全的序列化参数
        safe_kwargs = {
            'ensure_ascii': False,  # 保持中文字符原样
            'separators': (',', ':'),  # 紧凑格式
            'allow_nan': False,  # 不允许NaN值
            **kwargs
        }
        
        # 尝试序列化
        result = json.dumps(cleaned_data, **safe_kwargs)
        
        # 验证结果可以被正确解析
        json.loads(result)
        
        return result
        
    except (TypeError, ValueError, RecursionError) as e:
        logger.warning(f"安全序列化失败: {e}")
        
        # 降级策略：强制ASCII编码
        try:
            result = json.dumps(data, ensure_ascii=True, separators=(',', ':'))
            json.loads(result)  # 验证
            return result
        except Exception as e2:
            logger.error(f"所有序列化策略都失败了: {e2}")
            return '{"error": "serialization_failed", "message": "Unable to serialize data safely"}'


def detect_json_issues(json_str: str) -> List[str]:
    """
    检测JSON字符串中的潜在问题
    """
    issues = []
    
    if not isinstance(json_str, str):
        issues.append(f"输入不是字符串类型: {type(json_str)}")
        return issues
    
    # 检查是否为空
    if not json_str.strip():
        issues.append("输入为空字符串")
        return issues
    
    # 检查控制字符
    if re.search(r'[\x00-\x1f]', json_str):
        issues.append("包含控制字符（如换行符、制表符）")
    
    # 检查是否被markdown包装
    if '```json' in json_str or '```' in json_str:
        issues.append("可能被markdown代码块包装")
    
    # 检查常见的JSON格式问题
    if re.search(r'[^\\]"[^,:}\]]*"[^,:}\]]*:', json_str):
        issues.append("可能存在未转义的引号")
    
    # 检查多余的逗号
    if re.search(r',\s*[}\]]', json_str):
        issues.append("存在多余的逗号")
    
    # 检查缺少引号的键
    if re.search(r'{\s*[a-zA-Z_][a-zA-Z0-9_]*\s*:', json_str):
        issues.append("键可能缺少引号")
    
    return issues


def enhanced_json_repair(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    使用json_repair库进行JSON修复的增强版本
    """
    if not HAS_JSON_REPAIR:
        if logger:
            logger.warning("json_repair库不可用，回退到原有实现")
        return False, "json_repair library not available"
    
    try:
        if logger:
            logger.debug(f"使用json_repair修复JSON，长度: {len(json_str)}")
        
        # 使用json_repair进行修复
        fixed_data = json_repair.repair_json(
            json_str, 
            return_objects=True,  # 直接返回对象，提高性能
            ensure_ascii=False,   # 保持中文字符
            skip_json_loads=False  # 先尝试标准解析
        )
        
        if logger:
            logger.info("json_repair修复成功")
        return True, fixed_data
        
    except Exception as e:
        if logger:
            logger.warning(f"json_repair修复失败: {str(e)}")
        return False, f"json_repair failed: {str(e)}"


def fallback_json_repair(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    回退的JSON修复方法（原有逻辑的简化版）
    """
    try:
        # 直接尝试解析
        result = json.loads(json_str)
        return True, result
    except json.JSONDecodeError:
        pass
    
    # 基础修复：处理常见的字符问题
    try:
        def fix_string_value(match):
            value = match.group(1)
            value = value.replace('\\', '\\\\')
            value = value.replace('\n', '\\n')
            value = value.replace('\r', '\\r')
            value = value.replace('\t', '\\t')
            value = value.replace('"', '\\"')
            return f'"{value}"'
        
        string_pattern = r'"([^"]*(?:\n|\r|\t)[^"]*)"'
        fixed = re.sub(string_pattern, fix_string_value, json_str, flags=re.DOTALL)
        
        result = json.loads(fixed)
        if logger:
            logger.info("回退修复成功")
        return True, result
        
    except json.JSONDecodeError as e:
        if logger:
            logger.error(f"回退修复也失败了: {e}")
        return False, f"JSON修复失败: {e}"


def clean_and_validate_json(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    增强版JSON清理和验证函数
    优先使用json_repair，回退到原有逻辑
    """
    if not isinstance(json_str, str):
        return False, f"输入不是字符串类型: {type(json_str)}"
    
    # 记录原始输入的调试信息
    if logger:
        logger.debug(f"原始JSON字符串长度: {len(json_str)}")
    
    # 1. 基础预处理：移除markdown标记和多余空白
    cleaned = json_str.strip()
    
    if not cleaned:
        return False, "输入为空字符串"
    
    # 提取markdown中的JSON
    json_match = re.search(r'```json\s*\n(.*?)\n```', cleaned, re.DOTALL)
    if json_match:
        cleaned = json_match.group(1).strip()
    else:
        # 移除markdown标记
        cleaned = re.sub(r'^```json\s*|\s*```$', '', cleaned, flags=re.DOTALL)
        # 移除JSON前后的非JSON内容
        cleaned = re.sub(r'^[^{[]*(?=[{[])', '', cleaned, flags=re.DOTALL).strip()
        cleaned = re.sub(r'(?<=[}\]])[^}\]]*$', '', cleaned, flags=re.DOTALL).strip()
    
    if not cleaned:
        return False, "清理后的JSON字符串为空"
    
    # 2. 优先使用json_repair进行修复
    if USE_JSON_REPAIR and HAS_JSON_REPAIR:
        success, result = enhanced_json_repair(cleaned, logger)
        if success:
            return True, result
        else:
            if logger:
                logger.info("json_repair失败，回退到原有修复方法")
    
    # 3. 回退到原有的修复逻辑
    return fallback_json_repair(cleaned, logger)


def validate_and_fix_ai_json(ai_response: str, expected_root_key: Optional[str] = None, logger=None) -> Tuple[bool, Any]:
    """
    专门用于处理AI返回的JSON响应的函数
    """
    if not isinstance(ai_response, str):
        if logger:
            logger.error(f"AI响应不是字符串类型: {type(ai_response)}")
        return False, f"AI响应不是字符串类型: {type(ai_response)}"
    
    # 使用增强版清理函数
    success, result = clean_and_validate_json(ai_response, logger)
    
    if not success:
        return False, result
    
    # 如果指定了期望的根键，尝试提取
    if expected_root_key and isinstance(result, dict):
        final_result = result.get(expected_root_key, result)
        return True, final_result
    
    return True, result


def fix_json_with_llm(invalid_json: str, logger=None) -> Tuple[bool, Any]:
    """
    使用小模型（如gpt-4o-mini）修复无效的JSON
    这是最后的修复手段，当所有其他方法都失败时使用
    """
    try:
        # 导入GPT调用函数
        try:
            from .call_claude import gpt
        except ImportError:
            try:
                from call_claude import gpt
            except ImportError:
                if logger:
                    logger.warning("无法导入GPT模块，跳过LLM修复")
                return False, "无法导入GPT模块"
        
        # 构建修复提示
        system_prompt = """你是一个JSON修复专家。你的任务是修复无效的JSON字符串，使其成为有效的JSON格式。

要求：
1. 保持原始数据的语义和结构不变
2. 只修复格式问题，不改变内容
3. 输出必须是有效的JSON格式
4. 不要添加任何解释文字
5. 如果原始内容包含中文，保持中文不变

请直接输出修复后的有效JSON，不要包含任何其他文字。"""

        user_prompt = f"""请修复以下无效的JSON字符串：

{invalid_json}

请直接输出修复后的有效JSON，不要包含任何其他文字。"""

        if logger:
            logger.info("尝试使用LLM修复JSON...")
        
        # 调用GPT进行修复
        success, response = gpt(
            sys_prompt=system_prompt,
            user_prompt=user_prompt,
            json_schema='flexible',
            model='gpt-4o-mini',
            temperature=0.1
        )
        
        if not success:
            if logger:
                logger.error(f"LLM调用失败: {response}")
            return False, f"LLM调用失败: {response}"
        
        # 如果response已经是dict类型，直接返回
        if isinstance(response, dict):
            if logger:
                logger.info("LLM JSON修复成功")
            return True, response
        
        # 如果response是字符串，尝试解析
        try:
            fixed_data = json.loads(response)
            if logger:
                logger.info("LLM JSON修复成功")
            return True, fixed_data
        except json.JSONDecodeError as e:
            if logger:
                logger.error(f"LLM修复后的JSON仍然无效: {e}")
            return False, f"LLM修复后的JSON仍然无效: {e}"
            
    except Exception as e:
        if logger:
            logger.error(f"LLM修复过程中发生异常: {e}")
        return False, f"LLM修复异常: {e}"


def enhanced_validate_and_fix_ai_json(ai_response: str, expected_root_key: Optional[str] = None, 
                                     use_llm: bool = None, logger=None) -> Tuple[bool, Any]:
    """
    终极增强版AI JSON验证和修复函数
    结合json_repair和LLM修复的完整解决方案
    """
    # 首先尝试常规修复方法
    success, result = validate_and_fix_ai_json(ai_response, expected_root_key, logger)
    
    if success:
        # 成功后清理控制字符
        cleaned_result = deep_clean_control_characters(result)
        if logger:
            logger.info("AI JSON解析成功，已清理控制字符")
        return True, cleaned_result
    
    # 如果失败且启用LLM修复
    if (use_llm if use_llm is not None else ENABLE_LLM_REPAIR):
        if logger:
            logger.info("常规修复失败，尝试使用LLM修复...")
        
        llm_success, llm_result = fix_json_with_llm(ai_response, logger)
        
        if llm_success:
            # 提取根键
            if expected_root_key and isinstance(llm_result, dict):
                final_result = llm_result.get(expected_root_key, llm_result)
            else:
                final_result = llm_result
            
            # 清理控制字符
            cleaned_final_result = deep_clean_control_characters(final_result)
            if logger:
                logger.info("LLM JSON修复成功，已清理控制字符")
            return True, cleaned_final_result
        else:
            if logger:
                logger.error(f"LLM修复也失败了: {llm_result}")
    
    # 所有修复方法都失败
    return False, result


def safe_redis_serialize(data: Any, **kwargs) -> str:
    """
    Redis安全序列化函数
    执行双重序列化以符合Redis存储要求
    """
    try:
        # 第一次序列化：将数据转换为JSON字符串
        first_json = safe_json_dumps(data, **kwargs)
        
        # 第二次序列化：将JSON字符串再次序列化为Redis存储格式
        second_json = json.dumps(first_json, ensure_ascii=False)
        
        return second_json
        
    except Exception as e:
        logger.error(f"Redis序列化失败: {e}")
        # 返回错误信息的双重序列化
        error_data = {"error": "serialization_failed", "message": str(e)}
        error_json = json.dumps(error_data, ensure_ascii=False)
        return json.dumps(error_json, ensure_ascii=False)


def safe_redis_serialize_with_validation(data: dict) -> str:
    """
    带验证的Redis序列化函数
    确保序列化后的数据可以正确反序列化
    """
    try:
        # 执行双重序列化
        serialized = safe_redis_serialize(data)
        
        # 验证反序列化
        step1 = json.loads(serialized)  # 第一次反序列化得到JSON字符串
        step2 = json.loads(step1)       # 第二次反序列化得到原始数据
        
        # 基本完整性检查
        if isinstance(data, dict) and isinstance(step2, dict):
            for key in ['status', 'taskInfo']:
                if key in data and key not in step2:
                    logger.warning(f"Redis序列化验证失败：缺少关键字段 {key}")
                    raise ValueError(f"序列化后缺少关键字段: {key}")
        
        logger.info("Redis序列化验证成功")
        return serialized
        
    except Exception as e:
        error_msg = f"Redis序列化验证失败: {str(e)}"
        logger.error(error_msg)
        # 返回错误响应的序列化
        error_response = {
            "error": "redis_serialization_failed",
            "message": error_msg,
            "taskInfo": data.get("taskInfo", {}) if isinstance(data, dict) else {}
        }
        return safe_redis_serialize(error_response)


def create_error_response(error_message: str, task_info: Optional[Dict] = None) -> Dict:
    """
    创建标准化的错误响应
    """
    return {
        "status": "failed",
        "error": error_message,
        "taskInfo": task_info or {},
        "timestamp": __import__('time').time()
    }


def create_success_response(data: Any, task_info: Optional[Dict] = None) -> Dict:
    """
    创建标准化的成功响应
    """
    response = {
        "status": "success",
        "taskInfo": task_info or {},
        "timestamp": __import__('time').time()
    }
    
    if isinstance(data, dict):
        response.update(data)
    else:
        response["data"] = data
    
    return response


# 为了完全兼容，提供所有原有函数的别名
def enhanced_clean_and_validate_json(json_str: str, use_llm: bool = None, logger=None) -> Tuple[bool, Any]:
    """
    增强版JSON清理和验证函数，集成LLM修复
    这是为了兼容原有代码的别名函数
    """
    # 首先尝试常规修复
    success, result = clean_and_validate_json(json_str, logger)
    
    if success:
        return True, result
    
    # 如果失败且启用LLM修复
    if (use_llm if use_llm is not None else ENABLE_LLM_REPAIR):
        if logger:
            logger.info("常规修复失败，尝试使用LLM修复...")
        
        llm_success, llm_result = fix_json_with_llm(json_str, logger)
        if llm_success:
            return True, llm_result
    
    return False, result


# 导出所有主要函数
__all__ = [
    'clean_and_validate_json',
    'validate_and_fix_ai_json', 
    'enhanced_validate_and_fix_ai_json',
    'enhanced_clean_and_validate_json',
    'deep_clean_control_characters',
    'safe_json_dumps',
    'safe_redis_serialize',
    'safe_redis_serialize_with_validation',
    'create_error_response',
    'create_success_response',
    'detect_json_issues',
    'fix_json_with_llm',
    'HAS_JSON_REPAIR',
    'USE_JSON_REPAIR'
]


if __name__ == "__main__":
    # 简单测试
    test_json = '''{"title": "测试\n包含换行", "content": "内容"}'''
    
    print("测试增强版JSON工具:")
    print(f"json_repair可用: {HAS_JSON_REPAIR}")
    
    success, result = clean_and_validate_json(test_json)
    print(f"修复结果: {success}")
    if success:
        print(f"修复后数据: {result}") 