#!/usr/bin/env python3
"""
JSON Utils 测试示例
演示如何使用 json_utils.py 中的各种函数来处理AI生成的JSON响应中的常见问题
"""

import os
import sys
import json

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from task.lib.json_utils_enhanced import (
    clean_and_validate_json,
    validate_and_fix_ai_json,
    detect_json_issues,
    safe_json_dumps,
    safe_redis_serialize,
    create_error_response,
    create_success_response
)

def test_problematic_json_responses():
    """测试各种有问题的JSON响应"""
    print("=== 测试有问题的JSON响应 ===\n")
    
    # 测试用例1: 包含未转义换行符的JSON
    test_case_1 = '''{"title": "小红书运营策略
    详细分析", "content": "这是一个
    多行内容的示例"}'''
    
    print("测试用例1: 包含未转义换行符")
    print(f"原始JSON: {repr(test_case_1)}")
    issues = detect_json_issues(test_case_1)
    print(f"检测到的问题: {issues}")
    fixed = clean_and_validate_json(test_case_1)
    print(f"修复结果: {fixed}")
    print()
    
    # 测试用例2: 包含制表符的JSON
    test_case_2 = '''{"description": "策略	详情", "notes": "包含	制表符"}'''
    
    print("测试用例2: 包含制表符")
    print(f"原始JSON: {repr(test_case_2)}")
    issues = detect_json_issues(test_case_2)
    print(f"检测到的问题: {issues}")
    fixed = clean_and_validate_json(test_case_2)
    print(f"修复结果: {fixed}")
    print()
    
    # 测试用例3: 被markdown包装的JSON
    test_case_3 = '''```json
    {"title": "诊断报告", "status": "success"}
    ```'''
    
    print("测试用例3: 被markdown包装的JSON")
    print(f"原始JSON: {repr(test_case_3)}")
    issues = detect_json_issues(test_case_3)
    print(f"检测到的问题: {issues}")
    fixed = validate_and_fix_ai_json(test_case_3)
    print(f"修复结果: {fixed}")
    print()
    
    # 测试用例4: 复杂的AI响应
    test_case_4 = '''这是AI的回复，包含JSON数据：
    
    ```json
    {
        "diagnosis": "账号分析结果
        需要优化内容策略",
        "recommendations": ["提高内容质量", "增加互动	频率"],
        "score": 85
    }
    ```
    
    以上是分析结果。'''
    
    print("测试用例4: 复杂的AI响应")
    print(f"原始响应: {repr(test_case_4)}")
    issues = detect_json_issues(test_case_4)
    print(f"检测到的问题: {issues}")
    fixed = validate_and_fix_ai_json(test_case_4)
    print(f"修复结果: {fixed}")
    print()

def test_redis_serialization():
    """测试Redis序列化功能"""
    print("=== 测试Redis序列化功能 ===\n")
    
    # 测试正常数据
    normal_data = {
        "taskInfo": {"taskId": "12345"},
        "result": "封面生成成功",
        "status": "success"
    }
    
    print("测试正常数据:")
    print(f"原始数据: {normal_data}")
    serialized = safe_redis_serialize(normal_data)
    print(f"序列化结果: {repr(serialized)}")
    
    # 验证可以正确反序列化
    try:
        step1 = json.loads(serialized)
        step2 = json.loads(step1)
        print(f"反序列化验证: {step2}")
        print("✓ Redis序列化测试通过\n")
    except Exception as e:
        print(f"✗ Redis序列化测试失败: {e}\n")
    
    # 测试包含特殊字符的数据
    special_data = {
        "content": "包含换行符\n和制表符\t的内容",
        "description": "特殊字符测试"
    }
    
    print("测试包含特殊字符的数据:")
    print(f"原始数据: {special_data}")
    serialized_special = safe_redis_serialize(special_data)
    print(f"序列化结果: {repr(serialized_special)}")
    
    # 验证可以正确反序列化
    try:
        step1 = json.loads(serialized_special)
        step2 = json.loads(step1)
        print(f"反序列化验证: {step2}")
        print("✓ 特殊字符序列化测试通过\n")
    except Exception as e:
        print(f"✗ 特殊字符序列化测试失败: {e}\n")

def test_response_creators():
    """测试响应创建函数"""
    print("=== 测试响应创建函数 ===\n")
    
    # 测试成功响应
    task_info = {"taskId": "test-123", "type": "cover_generation"}
    success_response = create_success_response(task_info, {"coverResult": "HTML内容"})
    print("成功响应:")
    print(f"结果: {success_response}")
    print()
    
    # 测试错误响应
    error_response = create_error_response("生成失败", task_info)
    print("错误响应:")
    print(f"结果: {error_response}")
    print()

def test_cover_gen_integration():
    """测试封面生成脚本的集成场景"""
    print("=== 测试封面生成脚本集成场景 ===\n")
    
    # 模拟从Redis接收到的任务数据（可能包含问题）
    redis_input = '''{
        "taskInfo": {
            "taskId": "cover-001",
            "type": "cover_generation"
        },
        "style": "工业极简",
        "content": "小红书封面设计
        主题：美食分享"
    }'''
    
    print("模拟Redis输入数据:")
    print(f"原始数据: {repr(redis_input)}")
    
    # 检测问题
    issues = detect_json_issues(redis_input)
    print(f"检测到的问题: {issues}")
    
    # 清理和验证
    cleaned_data = clean_and_validate_json(redis_input)
    print(f"清理后的数据: {cleaned_data}")
    
    if cleaned_data:
        # 模拟处理结果
        result_data = {
            "taskInfo": cleaned_data.get("taskInfo", {}),
            "coverResult": "<html>生成的封面HTML</html>",
            "status": "success"
        }
        
        # 序列化用于Redis存储
        redis_output = safe_redis_serialize(result_data)
        print(f"Redis输出数据: {repr(redis_output)}")
        print("✓ 封面生成集成测试通过\n")
    else:
        print("✗ 封面生成集成测试失败\n")

def test_diagnosis_integration():
    """测试诊断脚本的集成场景"""
    print("=== 测试诊断脚本集成场景 ===\n")
    
    # 模拟AI返回的JSON响应（可能包含问题）
    ai_json_response = '''```json
    {
        "diagnosis": "账号内容质量需要提升
        建议增加原创内容比例",
        "score": 75,
        "recommendations": [
            "优化内容	策略",
            "提高发布频率"
        ],
        "details": {
            "content_quality": "中等",
            "engagement": "良好"
        }
    }
    ```'''
    
    print("模拟AI JSON响应:")
    print(f"原始响应: {repr(ai_json_response)}")
    
    # 检测问题
    issues = detect_json_issues(ai_json_response)
    print(f"检测到的问题: {issues}")
    
    # 验证和修复AI JSON
    fixed_json = validate_and_fix_ai_json(ai_json_response)
    print(f"修复后的JSON: {fixed_json}")
    
    if fixed_json:
        print("✓ 诊断脚本集成测试通过\n")
    else:
        print("✗ 诊断脚本集成测试失败\n")

def main():
    """运行所有测试"""
    print("JSON Utils 综合测试\n")
    print("=" * 50)
    
    test_problematic_json_responses()
    test_redis_serialization()
    test_response_creators()
    test_cover_gen_integration()
    test_diagnosis_integration()
    
    print("=" * 50)
    print("所有测试完成")

if __name__ == "__main__":
    main() 