#!/usr/bin/env python3
"""
增强版JSON处理工具模块
集成json_repair库，同时保留原有的业务逻辑和功能
"""

import json
import re
import sys
import os
import logging
from typing import Tuple, List, Any, Dict, Optional

# 尝试导入json_repair，如果不存在则回退到原有实现
try:
    import json_repair
    HAS_JSON_REPAIR = True
except ImportError:
    HAS_JSON_REPAIR = False
    print("Warning: json_repair library not found. Falling back to original implementation.")

# 获取logger实例
logger = logging.getLogger(__name__)

# 配置选项
ENABLE_LLM_REPAIR = True  # 是否启用LLM修复功能
DEFAULT_LLM_MODEL = "gpt-4o-mini"  # 默认使用的LLM模型
USE_JSON_REPAIR = HAS_JSON_REPAIR  # 是否使用json_repair库


def enhanced_json_repair(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    使用json_repair库进行JSON修复的增强版本
    
    Args:
        json_str: 待修复的JSON字符串
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not HAS_JSON_REPAIR:
        if logger:
            logger.warning("json_repair库不可用，回退到原有实现")
        return False, "json_repair library not available"
    
    try:
        # 使用json_repair进行修复
        if logger:
            logger.debug(f"使用json_repair修复JSON，长度: {len(json_str)}")
        
        # 尝试直接修复并返回对象
        fixed_data = json_repair.repair_json(
            json_str, 
            return_objects=True,  # 直接返回对象，提高性能
            ensure_ascii=False,   # 保持中文字符
            skip_json_loads=False  # 先尝试标准解析
        )
        
        if logger:
            logger.info("json_repair修复成功")
        return True, fixed_data
        
    except Exception as e:
        if logger:
            logger.warning(f"json_repair修复失败: {str(e)}")
        return False, f"json_repair failed: {str(e)}"


def clean_and_validate_json_v2(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    增强版JSON清理和验证函数，优先使用json_repair
    
    Args:
        json_str: 待处理的JSON字符串
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not isinstance(json_str, str):
        return False, f"输入不是字符串类型: {type(json_str)}"
    
    # 记录原始输入的调试信息
    if logger:
        logger.debug(f"原始JSON字符串长度: {len(json_str)}")
    
    # 1. 基础预处理：移除markdown标记和多余空白
    cleaned = json_str.strip()
    
    if not cleaned:
        return False, "输入为空字符串"
    
    # 提取markdown中的JSON
    json_match = re.search(r'```json\s*\n(.*?)\n```', cleaned, re.DOTALL)
    if json_match:
        cleaned = json_match.group(1).strip()
    else:
        # 移除markdown标记
        cleaned = re.sub(r'^```json\s*|\s*```$', '', cleaned, flags=re.DOTALL)
        # 移除JSON前后的非JSON内容
        cleaned = re.sub(r'^[^{[]*(?=[{[])', '', cleaned, flags=re.DOTALL).strip()
        cleaned = re.sub(r'(?<=[}\]])[^}\]]*$', '', cleaned, flags=re.DOTALL).strip()
    
    if not cleaned:
        return False, "清理后的JSON字符串为空"
    
    # 2. 优先使用json_repair进行修复
    if USE_JSON_REPAIR and HAS_JSON_REPAIR:
        success, result = enhanced_json_repair(cleaned, logger)
        if success:
            return True, result
        else:
            if logger:
                logger.info("json_repair失败，回退到原有修复方法")
    
    # 3. 回退到原有的修复逻辑（简化版）
    try:
        # 直接尝试解析
        result = json.loads(cleaned)
        return True, result
    except json.JSONDecodeError as e:
        if logger:
            logger.warning(f"标准JSON解析失败: {e}")
    
    # 4. 基础修复：处理常见的字符问题
    try:
        # 修复字符串中的特殊字符
        def fix_string_value(match):
            value = match.group(1)
            value = value.replace('\\', '\\\\')
            value = value.replace('\n', '\\n')
            value = value.replace('\r', '\\r')
            value = value.replace('\t', '\\t')
            value = value.replace('"', '\\"')
            return f'"{value}"'
        
        string_pattern = r'"([^"]*(?:\n|\r|\t)[^"]*)"'
        fixed = re.sub(string_pattern, fix_string_value, cleaned, flags=re.DOTALL)
        
        result = json.loads(fixed)
        if logger:
            logger.info("基础修复成功")
        return True, result
        
    except json.JSONDecodeError as e:
        if logger:
            logger.error(f"所有修复方法都失败了: {e}")
        return False, f"JSON修复失败: {e}"


def validate_and_fix_ai_json_v2(ai_response: str, expected_root_key: Optional[str] = None, logger=None) -> Tuple[bool, Any]:
    """
    增强版AI JSON验证和修复函数，专门处理AI响应
    
    Args:
        ai_response: AI返回的响应字符串
        expected_root_key: 期望的根键名（如 'strategyReport'）
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not isinstance(ai_response, str):
        if logger:
            logger.error(f"AI响应不是字符串类型: {type(ai_response)}")
        return False, f"AI响应不是字符串类型: {type(ai_response)}"
    
    # 使用增强版清理函数
    success, result = clean_and_validate_json_v2(ai_response, logger)
    
    if not success:
        return False, result
    
    # 如果指定了期望的根键，尝试提取
    if expected_root_key and isinstance(result, dict):
        final_result = result.get(expected_root_key, result)
        return True, final_result
    
    return True, result


# 导入原有的其他功能函数（保持兼容性）
# 这里我们可以从原始的json_utils.py导入其他必要的函数
def import_original_functions():
    """导入原始json_utils.py中的其他函数以保持兼容性"""
    try:
        # 尝试导入原有的函数
        import sys
        import os
        
        # 添加原有模块路径
        current_dir = os.path.dirname(__file__)
        sys.path.insert(0, current_dir)
        
        from json_utils import (
            deep_clean_control_characters,
            safe_json_dumps,
            safe_redis_serialize,
            safe_redis_serialize_with_validation,
            create_error_response,
            create_success_response,
            detect_json_issues,
            fix_json_with_llm,
            ENABLE_LLM_REPAIR
        )
        
        # 将这些函数添加到当前模块的全局命名空间
        globals().update({
            'deep_clean_control_characters': deep_clean_control_characters,
            'safe_json_dumps': safe_json_dumps,
            'safe_redis_serialize': safe_redis_serialize,
            'safe_redis_serialize_with_validation': safe_redis_serialize_with_validation,
            'create_error_response': create_error_response,
            'create_success_response': create_success_response,
            'detect_json_issues': detect_json_issues,
            'fix_json_with_llm': fix_json_with_llm,
        })
        
        return True
        
    except ImportError as e:
        logger.warning(f"无法导入原有函数: {e}")
        return False

# 在模块加载时导入原有函数
import_original_functions()


def enhanced_validate_and_fix_ai_json_v2(ai_response: str, expected_root_key: Optional[str] = None, 
                                         use_llm: bool = None, logger=None) -> Tuple[bool, Any]:
    """
    终极增强版AI JSON验证和修复函数
    结合json_repair和LLM修复的完整解决方案
    
    Args:
        ai_response: AI返回的响应字符串
        expected_root_key: 期望的根键名
        use_llm: 是否使用LLM进行修复
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    # 首先尝试新的修复方法
    success, result = validate_and_fix_ai_json_v2(ai_response, expected_root_key, logger)
    
    if success:
        # 成功后清理控制字符
        if 'deep_clean_control_characters' in globals():
            cleaned_result = deep_clean_control_characters(result)
            if logger:
                logger.info("AI JSON解析成功，已清理控制字符")
            return True, cleaned_result
        return True, result
    
    # 如果失败且启用LLM修复
    if (use_llm if use_llm is not None else ENABLE_LLM_REPAIR) and 'fix_json_with_llm' in globals():
        if logger:
            logger.info("常规修复失败，尝试使用LLM修复...")
        
        llm_success, llm_result = fix_json_with_llm(ai_response, logger)
        
        if llm_success:
            # 提取根键
            if expected_root_key and isinstance(llm_result, dict):
                final_result = llm_result.get(expected_root_key, llm_result)
            else:
                final_result = llm_result
            
            # 清理控制字符
            if 'deep_clean_control_characters' in globals():
                cleaned_final_result = deep_clean_control_characters(final_result)
                if logger:
                    logger.info("LLM JSON修复成功，已清理控制字符")
                return True, cleaned_final_result
            
            return True, final_result
        else:
            if logger:
                logger.error(f"LLM修复也失败了: {llm_result}")
    
    # 所有修复方法都失败
    return False, result


# 为了向后兼容，提供别名
clean_and_validate_json = clean_and_validate_json_v2
validate_and_fix_ai_json = validate_and_fix_ai_json_v2
enhanced_validate_and_fix_ai_json = enhanced_validate_and_fix_ai_json_v2


if __name__ == "__main__":
    # 简单测试
    test_json = '''{"title": "测试
    包含换行", "content": "内容"}'''
    
    print("测试json_repair集成:")
    print(f"HAS_JSON_REPAIR: {HAS_JSON_REPAIR}")
    
    success, result = clean_and_validate_json_v2(test_json)
    print(f"修复结果: {success}, {result}") 