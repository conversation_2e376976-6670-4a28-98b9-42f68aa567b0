from task.lib.media_utils import load_video, frame2img, Video_transform, get_font
from lmdeploy import pipeline, ChatTemplateConfig, TurbomindEngineConfig
from lmdeploy.vl import load_image
import json
import config
import requests

rdp_host = config.RDP_HOST
rdp_url = rdp_host + config.RDP_CDN_URL
rdp_token = config.RDP_TOKEN


def get_media_resource(media_list: list):
    url_list = []
    for media_ in media_list:
        media_key = media_.get("s3_key", "")
        if media_key == "":
            url_list.append(media_.get("url", ""))
        else:
            headers = {"Content-Type": "application/json", "rdp-token": rdp_token}
            body = {
                "object_key_list": [media_key]
            }
            try:
                response_ = requests.post(rdp_url, json=body, headers=headers, timeout=10)
                if response_.status_code != 200:
                    url_list.append(media_.get("url", ""))
                response = response_.json().get("result", {}).get("data", {})[0]
                if not response.get("object_url"):
                    url_list.append(media_.get("url", ""))
                url_list.append(response.get("object_url"))
            except Exception:
                url_list.append(media_.get("url", ""))

    return True, url_list


def extract_json(system_output):
    # Find the start and end of the JSON content
    start = system_output.find('{')
    end = system_output.rfind('}') + 1

    if start == -1 or end == 0:
        print(f"No valid JSON found in the input: {system_output}")
        return False, f"No valid JSON found in the input"

    # Extract the JSON string
    json_str = system_output[start:end]

    # Parse the JSON string
    try:
        json_data = json.loads(json_str)
        return True, json_data
    except json.JSONDecodeError as e:
        print(f"Error parsing JSON: {e}")
        return False, f"Error parsing JSON: {e}"


class MediaExtractor:
    def __init__(self):
        self.model = 'OpenGVLab/InternVL2_5-4B'
        self.system_prompt = """You are an advanced AI assistant specialized in analyzing images and extracting meaningful 
            information. Your task is to examine images carefully, understand their overall scenario and meaning, and provide 
            high-level keywords that accurately represent the content and context of the image."""
        self.img_user_prompt = """
            Please analyze the provided image and extract the overall scenario:
            Describe the main scene or situation depicted in the image in 1 sentence. Brief and concise.
            DO NOT put 'unusual_element' or 'celebrity_name' if they are not present in the image.
            Note restrain output within 120 characters. 

        The output should be in JSON format and don't include any other information. Example:
        {
            "description": "A man is standing in front of a building..."
        }
        """
        self.video_user_prompt = """
Follow these steps:
1. Carefully understand and analyze the video.
2. Identify the 3 most relevant key words that capture the main elements or themes.
3. Look for any 1 or 2 striking or unusual elements, or the presence of a celebrity or influencer.
4. Combine the key words and unusual elements/celebrity into a single string, separating each item with a comma.
5. Avoid using vague or generic terms, and focus on specific, descriptive words.

Your output should be a single string containing only the identified words and elements, without any additional text,
explanations, or formatting. Remember, your final output should look similar to this example:
"White House, prisoner swap, press conference, American flags, Joe Biden"

DO NOT put 'unusual_element' or 'celebrity_name' if they are not present in the video.
Do NOT include any other information or explanation in your response. Provide only the requested string of words.
        """
        # self.img_user_prompt = """
        # You are an experienced journalist who is very familiar with all kinds of celebrities, news events, and famous places. Now there is a picture. Please use your rich experience to complete the following requirements: 1. If there are people in the photo, extract the celebrities that may be involved; 2. Summarize the things described in the photo in a concise sentence
        # """
        # self.video_user_prompt = """
        # You are an experienced journalist who is very familiar with all kinds of celebrities, news events, and famous places. Now there is frames of a video. Please use your rich experience to complete the following requirements: 1. If there are people in the video, extract the celebrities that may be involved; 2. Summarize what the video presents with a concise sentence
        # """
        self.pipe = pipeline(self.model, backend_config=TurbomindEngineConfig(session_len=8192))

    def img_extract_keywords(self, image_url):
        try:
            image = load_image(image_url)
        except Exception as e:
            return False, f'Error {e}'
        try:
            image.convert("RGB")
        except Exception as e:
            return False, f'Error {e}'
        response = self.pipe((self.img_user_prompt, image))
        status, json_data = extract_json(response.text)
        if not status:
            response_ = self.pipe((self.img_user_prompt, image))
            status, json_data = extract_json(response_.text)
            if not status:
                return False, f'Error parsing json'
        return json_data.get("description", [])

    def img_extract_keywords_key(self, key_):
        status_, image_urls = get_media_resource([key_])
        if not status_:
            return False, image_urls
        try:
            image = load_image(image_urls[0])
        except Exception as e:
            return False, f'Error {e}'
        try:
            image.convert("RGB")
        except Exception as e:
            return False, f'Error {e}'
        response = self.pipe((self.img_user_prompt, image))
        status, json_data = extract_json(response.text)
        if not status:
            response_ = self.pipe((self.img_user_prompt, image))
            status, json_data = extract_json(response_.text)
            if not status:
                return False, f'Error parsing json'
        return json_data.get("description", [])

    def image_extract_keywords_from_list(self, key_list: list):
        keywords_list = []
        status_, image_resource_list = get_media_resource(key_list)
        if not status_:
            return False, key_list
        try:
            prompts = [(self.img_user_prompt, load_image(img_url)) for img_url in image_resource_list]
            response = self.pipe(prompts)
            for r in response:
                status, json_data = extract_json(r.text)
                if not status:
                    continue
                keywords_list.append(json_data.get("description", []))
            return keywords_list, key_list
        except Exception as e:
            print(f"Error loading image: {e}")
            return False, key_list

    def image_test_list(self, img_list: list):
        output_list = []
        try:
            prompts = [(self.img_user_prompt, load_image(img_url)) for img_url in img_list]
            response = self.pipe(prompts)
            for r in response:
                output_list.append(r.text)
            return True, output_list
        except Exception as e:
            print(f"Error loading image: {e}")
            return False, "Error loading image"

    def video_test(self, video_url: str):
        try:
            video = load_video(video_url)
        except Exception as e:
            return False, f"Error loading video: {e}"
        img = frame2img(video, get_font())
        img = Video_transform(img)
        response = self.pipe((self.video_user_prompt, img))
        output_text = response.text
        if len(output_text) > 120:
            response = self.pipe((self.video_user_prompt, img))
            output_text = response.text
            if len(output_text) > 120:
                # truncate the output text
                output_text = output_text[:120]
        return True, output_text

    def video_extract_keywords(self, key_):
        status_, object_ = get_media_resource([key_])
        if not status_:
            return False, object_
        video_url = object_[0]
        try:
            video = load_video(video_url)
        except Exception as e:
            return False, f"Error loading video: {e}"
        img = frame2img(video, get_font())
        img = Video_transform(img)
        response = self.pipe((self.video_user_prompt, img))
        output_text = response.text
        if len(output_text) > 120:
            response = self.pipe((self.video_user_prompt, img))
            output_text = response.text
            if len(output_text) > 120:
                # truncate the output text
                output_text = output_text[:120]
        return True, output_text


if __name__ == "__main__":
    # Image test
    # status, output_list = get_media_resource(["c2fb1c53e70e3831ab0662aba3748a0c/e102ee996ede2215f39890dacbf9035d"])
    # if status:
    #     type_ = output_list[0].get("file_type")
    #     print("Type: ", type_)
    #     url_ = output_list[0].get("object_url")
    #     try:
    #         image = load_image(url_)
    #     except Exception as e:
    #         print("Error: ", e)
    # Video test 1
    # status, output_list = get_media_resource(["c2fb1c53e70e3831ab0662aba3748a0c/a2e7af6676bb25d84a346d6b0b0db7d3"])
    # if status:
    #     type_ = output_list[0].get("file_type")
    #     print("Type: ", type_)
    #     url_ = output_list[0].get("object_url")
    #     try:
    #         video = load_video(url_)
    #     except Exception as e:
    #         print("Error: ", e)
    # Video test 2
    media_model = MediaExtractor()
    output = media_model.video_extract_keywords(
        "c2fb1c53e70e3831ab0662aba3748a0c/a2e7af6676bb25d84a346d6b0b0db7d3")
    if output:
        print("Video keywords: ", output)

