#!/usr/bin/env python3
"""
通用异步任务处理框架
支持Redis队列任务处理
"""

import asyncio
import multiprocessing as mp
import redis.asyncio as redis
import json
import time
import os
import sys
import logging
import signal
import ssl
import contextvars
import threading
from abc import ABC, abstractmethod
from typing import Dict, Optional, Any, Tuple, Callable
from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor
from logging.handlers import RotatingFileHandler

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from task import REDIS_CLUSTER_CONFIG, ENV
from task.lib.json_utils_enhanced import (
    detect_json_issues,
    safe_redis_serialize_with_validation,
    create_error_response
)
from task.lib.logging_utils import (
    log_task_start, log_task_complete, log_progress, 
    log_ai_call, log_redis_operation, log_json_data
)

# 创建一个上下文变量来存储任务ID
task_id_var = contextvars.ContextVar('task_id', default='N/A')

class TaskIdFilter(logging.Filter):
    """自定义日志过滤器以添加任务ID"""
    def filter(self, record):
        record.task_id = task_id_var.get()
        return True

class AsyncRedisManager:
    """异步Redis连接管理器"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.pool = None
        self.logger = self._setup_logger()
        
        # 更新Redis配置
        self.redis_config = REDIS_CLUSTER_CONFIG.copy()
        self.redis_config.update({
            'ssl_cert_reqs': ssl.CERT_NONE,
            'ssl_ca_certs': None,
            'socket_connect_timeout': 5,
            'socket_timeout': 5,
            'max_connections': 20
        })
    
    def _setup_logger(self):
        """设置专用日志器"""
        logger = logging.getLogger(f'redis_manager_{self.service_name}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            
            file_handler = RotatingFileHandler(
                f'logs/{self.service_name}_redis.log',
                maxBytes=10*1024*1024,
                backupCount=5,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - [task_id:%(task_id)s] - %(message)s'
            )
            file_handler.setFormatter(formatter)
            file_handler.addFilter(TaskIdFilter())
            
            logger.addHandler(file_handler)
        
        return logger
    
    async def create_pool(self):
        """创建Redis连接池"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                self.pool = redis.RedisCluster(**self.redis_config)
                await self.pool.ping()
                self.logger.info(f"{self.service_name} Redis连接池创建成功")
                return True
                
            except Exception as e:
                self.logger.warning(f"Redis连接失败，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    return False
                await asyncio.sleep(2 ** attempt)
        
        return False
    
    async def read_task_from_queue(self, queue_name: str, timeout: int = 30) -> Optional[Dict]:
        """从Redis队列读取任务"""
        try:
            task_data = await self.pool.blpop(queue_name, timeout=timeout)
            if task_data:
                _, input_json = task_data
                
                # 解析JSON数据
                if isinstance(input_json, str):
                    input_data = json.loads(input_json)
                    if isinstance(input_data, str):
                        input_data = json.loads(input_data)
                else:
                    input_data = input_json
                    
                if isinstance(input_data, dict):
                    return input_data
                else:
                    self.logger.error("解析的数据不是字典格式")
                    
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析错误: {str(e)}")
        except redis.ConnectionError as e:
            self.logger.error(f"Redis连接错误: {str(e)}")
            # 尝试重新创建连接
            await self.create_pool()
        except Exception as e:
            self.logger.error(f"读取任务时发生错误: {str(e)}")
        
        return None
    
    async def write_result_to_queue(self, queue_name: str, data: Dict) -> bool:
        """将结果写入Redis队列"""
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 使用带验证的序列化函数
                redis_json = safe_redis_serialize_with_validation(data)
                await self.pool.rpush(queue_name, redis_json)
                
                queue_length = await self.pool.llen(queue_name)
                self.logger.info(f"结果已写入{queue_name}，队列长度: {queue_length}")
                return True
                
            except redis.ConnectionError as e:
                self.logger.warning(f"Redis写入连接错误，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    return False
                # 重新创建连接池
                await self.create_pool()
                await asyncio.sleep(1)
            except Exception as e:
                self.logger.error(f"写入Redis失败，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    return False
                await asyncio.sleep(1)
        
        return False
    
    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.aclose()
            self.logger.info(f"{self.service_name} Redis连接池已关闭")

class BaseTaskProcessor(ABC):
    """基础任务处理器抽象类"""
    
    def __init__(self, service_name: str):
        self.service_name = service_name
        self.logger = self._setup_logger()
    
    def _setup_logger(self):
        """设置专用日志器"""
        logger = logging.getLogger(f'task_processor_{self.service_name}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            
            file_handler = RotatingFileHandler(
                f'logs/{self.service_name}_processor.log',
                maxBytes=10*1024*1024,
                backupCount=5,
                encoding='utf-8'
            )
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - [task_id:%(task_id)s] - %(message)s'
            )
            file_handler.setFormatter(formatter)
            file_handler.addFilter(TaskIdFilter())
            
            logger.addHandler(file_handler)
        
        return logger
    
    @abstractmethod
    async def process_task(self, input_data: Dict) -> Tuple[bool, Dict]:
        """
        处理具体任务的抽象方法
        
        Args:
            input_data: 输入数据
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 结果数据)
        """
        pass
    
    @abstractmethod
    def get_task_type_from_input(self, input_data: Dict) -> str:
        """
        从输入数据中获取任务类型
        
        Args:
            input_data: 输入数据
            
        Returns:
            str: 任务类型标识
        """
        pass
    
    def create_error_response(self, task_info: Dict, error_msg: str) -> Dict:
        """创建错误响应"""
        return {
            "taskInfo": task_info,
            "error": error_msg,
            "status": "failed"
        }
    
    def create_success_response(self, task_info: Dict, results: Dict) -> Dict:
        """创建成功响应"""
        response = {
            "taskInfo": task_info,
            "status": "success"
        }
        response.update(results)
        return response

class AsyncTaskService:
    """通用异步任务服务"""
    
    def __init__(self, 
                 service_name: str,
                 input_queue: str,
                 output_queue: str,
                 task_processor: BaseTaskProcessor,
                 max_concurrent_tasks: int = 5,
                 use_process_pool: bool = False,
                 max_workers: int = 4):
        
        self.service_name = service_name
        self.input_queue = input_queue
        self.output_queue = output_queue
        self.task_processor = task_processor
        self.max_concurrent_tasks = max_concurrent_tasks
        self.use_process_pool = use_process_pool
        self.max_workers = max_workers
        
        # 组件初始化
        self.redis_manager = AsyncRedisManager(service_name)
        self.logger = self._setup_logger()
        
        # 进程/线程池
        self.executor = None
        if use_process_pool:
            self.executor = ProcessPoolExecutor(max_workers=max_workers)
        else:
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 停止标志
        self.stop_event = asyncio.Event()
        
    def _setup_logger(self):
        """设置主服务日志器"""
        logger = logging.getLogger(f'async_{self.service_name}_service')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            os.makedirs('logs', exist_ok=True)
            
            file_handler = RotatingFileHandler(
                f'logs/async_{self.service_name}_service.log',
                maxBytes=10*1024*1024,
                backupCount=5,
                encoding='utf-8'
            )
            
            console_handler = logging.StreamHandler()
            
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - [task_id:%(task_id)s] - %(message)s'
            )
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            # 添加过滤器
            task_id_filter = TaskIdFilter()
            file_handler.addFilter(task_id_filter)
            console_handler.addFilter(task_id_filter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    async def process_single_task(self, input_data: Dict):
        """处理单个任务"""
        task_id = input_data.get("taskInfo", {}).get("taskId", "未知任务")
        task_type = self.task_processor.get_task_type_from_input(input_data)
        
        # 设置任务上下文
        token = task_id_var.set(task_id)
        
        # 记录任务开始
        log_task_start(self.logger, task_type, task_id)
        start_time = time.time()
        
        try:
            # 处理任务
            status, results = await self.task_processor.process_task(input_data)
            
            # 构建返回数据
            task_info = input_data.get("taskInfo", {})
            if status:
                return_data = self.task_processor.create_success_response(task_info, results)
                log_task_complete(self.logger, task_type, task_id, "success", time.time() - start_time)
            else:
                error_msg = results.get('error', '未知错误')
                return_data = self.task_processor.create_error_response(task_info, error_msg)
                log_task_complete(self.logger, task_type, task_id, "failed", time.time() - start_time)
            
            # 写入结果到Redis
            if await self.redis_manager.write_result_to_queue(self.output_queue, return_data):
                self.logger.info(f"任务结果已写入Redis: {task_id}")
            else:
                self.logger.error(f"任务结果写入Redis失败: {task_id}")
                
        except Exception as e:
            error_msg = f"处理任务时发生严重错误: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 记录错误完成日志
            log_task_complete(self.logger, task_type, task_id, "failed", time.time() - start_time)
            
            # 写入错误结果
            error_data = self.task_processor.create_error_response(
                input_data.get("taskInfo", {}), error_msg
            )
            
            try:
                if await self.redis_manager.write_result_to_queue(self.output_queue, error_data):
                    self.logger.info(f"错误信息已写入Redis: {task_id}")
                else:
                    self.logger.error(f"错误信息写入Redis失败: {task_id}")
            except Exception as write_error:
                self.logger.error(f"写入错误信息失败: {str(write_error)}")
        
        finally:
            # 重置上下文
            task_id_var.reset(token)
    
    async def task_consumer(self):
        """任务消费者 - 从Redis读取任务并处理"""
        semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        
        async def process_with_semaphore(input_data):
            async with semaphore:
                await self.process_single_task(input_data)
        
        while not self.stop_event.is_set():
            try:
                # 从Redis获取任务
                input_data = await self.redis_manager.read_task_from_queue(self.input_queue)
                if input_data:
                    # 创建任务协程
                    asyncio.create_task(process_with_semaphore(input_data))
                else:
                    # 没有任务时短暂等待
                    await asyncio.sleep(0.1)
                    
            except Exception as e:
                self.logger.error(f"任务消费者发生错误: {str(e)}")
                await asyncio.sleep(5)
    
    async def start(self):
        """启动服务"""
        try:
            self.logger.info(f"启动{self.service_name}异步任务服务...")
            
            # 创建Redis连接
            if not await self.redis_manager.create_pool():
                raise Exception("无法创建Redis连接")
            
            # 启动任务消费者
            await self.task_consumer()
            
        except KeyboardInterrupt:
            self.logger.info("收到停止信号")
        except Exception as e:
            self.logger.error(f"服务运行错误: {str(e)}")
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """优雅关闭服务"""
        self.logger.info(f"正在关闭{self.service_name}服务...")
        
        # 设置停止事件
        self.stop_event.set()
        
        # 关闭执行器
        if self.executor:
            self.executor.shutdown(wait=True)
        
        # 关闭Redis连接
        await self.redis_manager.close()
        
        self.logger.info(f"{self.service_name}服务已安全关闭")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信号 {signum}")
            asyncio.create_task(self.shutdown())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

def create_service(service_name: str,
                  input_queue: str,
                  output_queue: str,
                  task_processor: BaseTaskProcessor,
                  **kwargs) -> AsyncTaskService:
    """
    创建异步任务服务的工厂函数
    
    Args:
        service_name: 服务名称
        input_queue: 输入队列名称
        output_queue: 输出队列名称
        task_processor: 任务处理器实例
        **kwargs: 其他配置参数
        
    Returns:
        AsyncTaskService: 配置好的服务实例
    """
    return AsyncTaskService(
        service_name=service_name,
        input_queue=input_queue,
        output_queue=output_queue,
        task_processor=task_processor,
        **kwargs
    )

async def run_service(service: AsyncTaskService):
    """运行服务的便捷函数"""
    service.setup_signal_handlers()
    await service.start()

# 使用示例
if __name__ == "__main__":
    # 这里是使用示例，实际使用时在具体脚本中实现
    class ExampleTaskProcessor(BaseTaskProcessor):
        async def process_task(self, input_data: Dict) -> Tuple[bool, Dict]:
            # 示例任务处理逻辑
            await asyncio.sleep(1)  # 模拟处理时间
            return True, {"result": "处理成功"}
        
        def get_task_type_from_input(self, input_data: Dict) -> str:
            return "example"
    
    # 创建服务
    processor = ExampleTaskProcessor("example")
    service = create_service(
        service_name="example",
        input_queue="test:input",
        output_queue="test:output",
        task_processor=processor
    )
    
    # 运行服务
    asyncio.run(run_service(service)) 