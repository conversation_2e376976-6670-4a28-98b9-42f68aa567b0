from jinja2 import Environment, FileSystemLoader

# Set up Jinja2 environment
env = Environment(loader=FileSystemLoader('prompts'))


class PromptsFusion:
    def __init__(self):
        self.template_single_user = env.get_template('user/batch_tweet_tag.j2')
        self.template_single_system = env.get_template('system/rewrite_batch.j2')
        self.template_tag_user = env.get_template('user/categorize.j2')
        self.template_tag_system = env.get_template('system/categorize_sys.j2')
        self.template_summry_system = env.get_template('system/summary.j2')
        self.template_summry_user = env.get_template('user/summary_user.j2')

    def get_single_system(self, data):
        return self.template_single_system.render(data)

    def get_single_user(self, data):
        return self.template_single_user.render(data)

    def get_tag_user(self, data):
        return self.template_tag_user.render(data)

    def get_tag_system(self, data):
        return self.template_tag_system.render(data)

    def get_summary_system(self, data):
        return self.template_summry_system.render(data)

    def get_summary_user(self, data):
        return self.template_summry_user.render(data)





