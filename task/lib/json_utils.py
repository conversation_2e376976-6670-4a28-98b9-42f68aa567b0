#!/usr/bin/env python3
"""
JSON处理工具模块
提供JSON清理、验证、修复和安全序列化功能
"""

import json
import re
import sys
import os
import logging
from typing import Tuple, List, Any, Dict, Optional, Union

# 获取logger实例
logger = logging.getLogger(__name__)

# 配置选项
ENABLE_LLM_REPAIR = True  # 是否启用LLM修复功能
DEFAULT_LLM_MODEL = "gpt-4o-mini"  # 默认使用的LLM模型


def deep_clean_control_characters(data: Any) -> Any:
    """
    递归清理数据中的控制字符
    对HTML内容进行特殊处理，保持其格式完整性
    
    Args:
        data: 要清理的数据
    
    Returns:
        Any: 清理后的数据
    """
    def is_html_content(text: str) -> bool:
        """检测字符串是否包含HTML内容"""
        if not isinstance(text, str):
            return False
        html_indicators = ['<html', '<body', '<div', '<p>', '<span', '<script', '<style', '<!DOCTYPE', '<h1', '<h2', '<h3']
        return any(indicator in text.lower() for indicator in html_indicators)
    
    def clean_string(text: str) -> str:
        """清理字符串中的控制字符"""
        if is_html_content(text):
            # 对于HTML内容，只清理真正有问题的控制字符，保留换行符
            # 只清理可能导致JSON解析错误的字符
            cleaned = text.replace('\r\n', '\n')  # 统一换行符
            cleaned = cleaned.replace('\r', '\n')  # 统一换行符
            # 清理其他控制字符，但保留换行符和制表符
            cleaned = re.sub(r'[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]', '', cleaned)
            return cleaned
        else:
            # 对于普通文本，进行标准的控制字符转义
            cleaned = text.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
            # 清理其他可能的控制字符
            cleaned = cleaned.replace('\b', '\\b').replace('\f', '\\f')
            # 清理Unicode控制字符
            cleaned = re.sub(r'[\x00-\x1f\x7f-\x9f]', lambda m: f'\\u{ord(m.group()):04x}', cleaned)
            return cleaned
    
    if isinstance(data, str):
        return clean_string(data)
    elif isinstance(data, dict):
        # 递归清理字典
        return {key: deep_clean_control_characters(value) for key, value in data.items()}
    elif isinstance(data, list):
        # 递归清理列表
        return [deep_clean_control_characters(item) for item in data]
    else:
        # 其他类型直接返回
        return data


def safe_html_content_handler(data: Any) -> Any:
    """
    安全处理包含HTML内容的数据
    将HTML内容进行Base64编码或安全转义
    
    Args:
        data: 要处理的数据
    
    Returns:
        Any: 处理后的数据
    """
    import base64
    import html
    
    def is_html_content(text: str) -> bool:
        """检测字符串是否包含HTML内容"""
        html_indicators = ['<html', '<body', '<div', '<p>', '<span', '<script', '<style', '<!DOCTYPE']
        return any(indicator in text.lower() for indicator in html_indicators)
    
    def process_string(text: str) -> Union[dict, str]:
        """处理字符串内容"""
        if is_html_content(text):
            # 对于HTML内容，使用Base64编码
            encoded = base64.b64encode(text.encode('utf-8')).decode('ascii')
            return {
                "_type": "html_base64",
                "_content": encoded,
                "_original_length": len(text)
            }
        else:
            # 对于普通文本，进行安全转义
            escaped = html.escape(text, quote=True)
            # 再进行控制字符清理
            cleaned = escaped.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
            return cleaned
    
    if isinstance(data, str):
        return process_string(data)
    elif isinstance(data, dict):
        return {key: safe_html_content_handler(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [safe_html_content_handler(item) for item in data]
    else:
        return data


def restore_html_content(data: Any) -> Any:
    """
    恢复经过安全处理的HTML内容
    
    Args:
        data: 要恢复的数据
    
    Returns:
        Any: 恢复后的数据
    """
    import base64
    import html
    
    def restore_string(obj):
        """恢复字符串内容"""
        if isinstance(obj, dict) and obj.get("_type") == "html_base64":
            # 恢复Base64编码的HTML
            try:
                return base64.b64decode(obj["_content"]).decode('utf-8')
            except Exception:
                return obj  # 如果恢复失败，返回原对象
        elif isinstance(obj, str):
            # 恢复HTML转义
            return html.unescape(obj)
        else:
            return obj
    
    if isinstance(data, dict):
        if data.get("_type") == "html_base64":
            return restore_string(data)
        else:
            return {key: restore_html_content(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [restore_html_content(item) for item in data]
    else:
        return restore_string(data)


def ultra_safe_json_dumps(data, **kwargs):
    """
    Ultra-safe JSON serialization with comprehensive error handling
    """
    try:
        # 深度清理数据（已经包含了HTML智能处理）
        cleaned_data = deep_clean_control_characters(data)
        
        # 设置安全的序列化参数
        safe_kwargs = {
            'ensure_ascii': False,  # 保持中文字符原样
            'separators': (',', ':'),  # 紧凑格式
            'allow_nan': False,  # 不允许NaN值
            **kwargs
        }
        
        # 尝试序列化
        result = json.dumps(cleaned_data, **safe_kwargs)
        
        # 验证结果可以被正确解析
        json.loads(result)
        
        return result
        
    except (TypeError, ValueError, RecursionError) as e:
        logger.warning(f"Ultra-safe serialization failed: {e}")
        
        # 降级策略1：基础清理
        try:
            result = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            json.loads(result)  # 验证
            return result
        except Exception as e2:
            logger.warning(f"Fallback strategy 1 failed: {e2}")
            
            # 降级策略2：强制ASCII编码
            try:
                result = json.dumps(data, ensure_ascii=True, separators=(',', ':'))
                json.loads(result)  # 验证
                return result
            except Exception as e3:
                logger.error(f"All serialization strategies failed: {e3}")
                # 最后的安全网
                return '{"error": "serialization_failed", "message": "Unable to serialize data safely"}'


def safe_json_dumps(data: Any, **kwargs) -> str:
    """
    安全的JSON序列化，确保输出格式正确
    
    Args:
        data: 要序列化的数据
        **kwargs: json.dumps的其他参数
    
    Returns:
        str: 序列化后的JSON字符串
    """
    try:
        # 设置默认参数
        default_kwargs = {
            'ensure_ascii': False,
            'separators': (',', ':'),  # 紧凑格式，避免多余空白
            'sort_keys': False
        }
        default_kwargs.update(kwargs)
        
        return json.dumps(data, **default_kwargs)
    except (TypeError, ValueError) as e:
        # 如果序列化失败，返回错误信息的JSON
        error_data = {"error": f"JSON序列化失败: {str(e)}", "original_type": str(type(data))}
        return json.dumps(error_data, ensure_ascii=False)


def detect_json_issues(json_str: str) -> List[str]:
    """
    检测JSON字符串中的潜在问题
    
    Args:
        json_str: JSON字符串
    
    Returns:
        List[str]: 发现的问题列表
    """
    issues = []
    
    if not isinstance(json_str, str):
        issues.append(f"不是字符串类型: {type(json_str)}")
        return issues
    
    # 检测未转义的换行符
    if '\n' in json_str and '\\n' not in json_str.replace('\n', ''):
        issues.append("包含未转义的换行符")
    
    # 检测未转义的制表符
    if '\t' in json_str and '\\t' not in json_str.replace('\t', ''):
        issues.append("包含未转义的制表符")
    
    # 检测未转义的回车符
    if '\r' in json_str and '\\r' not in json_str.replace('\r', ''):
        issues.append("包含未转义的回车符")
    
    # 检测可能的引号问题
    quote_count = json_str.count('"')
    escaped_quote_count = json_str.count('\\"')
    if (quote_count - escaped_quote_count * 2) % 2 != 0:
        issues.append("引号数量不匹配")
    
    # 检测括号平衡
    open_braces = json_str.count('{')
    close_braces = json_str.count('}')
    if open_braces != close_braces:
        issues.append(f"花括号不平衡: {open_braces} 开括号, {close_braces} 闭括号")
    
    open_brackets = json_str.count('[')
    close_brackets = json_str.count(']')
    if open_brackets != close_brackets:
        issues.append(f"方括号不平衡: {open_brackets} 开括号, {close_brackets} 闭括号")
    
    return issues


def safe_redis_serialize(data: Any, **kwargs) -> str:
    """
    为Redis存储准备的安全序列化函数
    执行双重序列化：先将数据转为JSON，再将JSON字符串转为带转义的字符串
    
    Args:
        data: 要序列化的数据
        **kwargs: json.dumps的其他参数
    
    Returns:
        str: 适合Redis存储的序列化字符串
    """
    try:
        # 第一次序列化：数据 -> JSON字符串（使用超安全方法）
        first_json = ultra_safe_json_dumps(data, **kwargs)
        
        # 第二次序列化：JSON字符串 -> 带转义的字符串（用于Redis存储）
        redis_ready = json.dumps(first_json, ensure_ascii=False)
        
        return redis_ready
        
    except Exception as e:
        # 如果序列化失败，返回错误信息
        error_data = {"error": f"Redis序列化失败: {str(e)}", "original_type": str(type(data))}
        error_json = json.dumps(error_data, ensure_ascii=False)
        return json.dumps(error_json, ensure_ascii=False)


def validate_and_fix_ai_json(ai_response: str, expected_root_key: Optional[str] = None, logger=None) -> Tuple[bool, Any]:
    """
    专门用于处理AI返回的JSON响应的函数
    
    Args:
        ai_response: AI返回的响应字符串
        expected_root_key: 期望的根键名（如 'strategyReport'）
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not isinstance(ai_response, str):
        if logger:
            logger.error(f"AI响应不是字符串类型: {type(ai_response)}, 值: {repr(ai_response)}")
        return False, f"AI响应不是字符串类型: {type(ai_response)}"
    
    # 使用通用的清理和验证函数
    success, result = clean_and_validate_json(ai_response, logger)
    
    if not success:
        return False, result
    
    # 如果指定了期望的根键，尝试提取
    if expected_root_key and isinstance(result, dict):
        final_result = result.get(expected_root_key, result)
        return True, final_result
    
    return True, result


def create_error_response(error_message: str, task_info: Optional[Dict] = None) -> Dict:
    """
    创建标准化的错误响应
    
    Args:
        error_message: 错误信息
        task_info: 任务信息（可选）
    
    Returns:
        Dict: 标准化的错误响应
    """
    error_response: Dict[str, Any] = {
        "status": "failed",
        "error": error_message
    }
    
    if task_info:
        error_response["taskInfo"] = task_info
    
    return error_response


def create_success_response(data: Any, task_info: Optional[Dict] = None) -> Dict:
    """
    创建标准化的成功响应
    
    Args:
        data: 响应数据
        task_info: 任务信息（可选）
    
    Returns:
        Dict: 标准化的成功响应
    """
    success_response: Dict[str, Any] = {
        "status": "success"
    }
    
    if task_info:
        success_response["taskInfo"] = task_info
    
    # 将数据添加到响应中
    if isinstance(data, dict):
        success_response.update(data)
    else:
        success_response["data"] = data
    
    return success_response


def fix_json_with_llm(invalid_json: str, logger=None) -> Tuple[bool, Any]:
    """
    使用小模型（如gpt-4o-mini）修复无效的JSON
    这是最后的修复手段，当所有其他方法都失败时使用
    
    Args:
        invalid_json: 无效的JSON字符串
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 修复后的数据或错误信息)
    """
    try:
        # 导入GPT调用函数
        try:
            from .call_claude import gpt
        except ImportError:
            # 如果相对导入失败，尝试绝对导入
            try:
                from call_claude import gpt
            except ImportError:
                if logger:
                    logger.warning("无法导入GPT模块，跳过LLM修复")
                return False, "无法导入GPT模块"
        
        # 构建修复提示
        system_prompt = """你是一个JSON修复专家。你的任务是修复无效的JSON字符串，使其成为有效的JSON格式。

要求：
1. 保持原始数据的语义和结构不变
2. 只修复格式问题，不改变内容
3. 输出必须是有效的JSON格式
4. 不要添加任何解释文字
5. 如果原始内容包含中文，保持中文不变

常见需要修复的问题：
- 未转义的换行符、制表符、引号
- 不匹配的括号
- 缺失的逗号或多余的逗号
- 不正确的引号使用
- 字符串值中的特殊字符

请直接输出修复后的有效JSON，不要包含任何其他文字。"""

        user_prompt = f"""请修复以下无效的JSON字符串：

{invalid_json}

请直接输出修复后的有效JSON，不要包含任何其他文字。"""

        if logger:
            logger.info("尝试使用LLM修复JSON...")
        
        # 调用GPT进行修复，使用json_object模式确保输出有效JSON
        success, response = gpt(
            sys_prompt=system_prompt,
            user_prompt=user_prompt,
            json_schema='flexible',
            model='gpt-4o-mini',
            temperature=0.1
        )
        
        if not success:
            if logger:
                logger.error(f"LLM调用失败: {response}")
            return False, f"LLM调用失败: {response}"
        
        # 如果response已经是dict类型（gpt函数已经解析了JSON），直接返回
        if isinstance(response, dict):
            if logger:
                logger.info("LLM JSON修复成功")
            return True, response
        
        # 如果response是字符串，尝试解析
        try:
            fixed_data = json.loads(response)
            if logger:
                logger.info("LLM JSON修复成功")
            return True, fixed_data
        except json.JSONDecodeError as e:
            if logger:
                logger.error(f"LLM修复后的JSON仍然无效: {e}")
            return False, f"LLM修复后的JSON仍然无效: {e}"
            
    except Exception as e:
        if logger:
            logger.error(f"LLM修复过程中发生异常: {e}")
        return False, f"LLM修复异常: {e}"


def enhanced_clean_and_validate_json(json_str: str, use_llm: Optional[bool] = None, logger=None) -> Tuple[bool, Any]:
    """
    增强版JSON清理和验证函数，集成LLM修复
    
    Args:
        json_str: 待处理的JSON字符串
        use_llm: 是否使用LLM进行修复
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    # 首先尝试原有的清理和验证方法
    success, result = clean_and_validate_json(json_str, logger)
    
    if success:
        return True, result
    
    # 如果原有方法失败且启用LLM修复
    if (use_llm if use_llm is not None else ENABLE_LLM_REPAIR):
        if logger:
            logger.info("原有清理方法失败，尝试使用LLM修复...")
        
        # 使用LLM修复
        llm_success, llm_result = fix_json_with_llm(json_str, logger)
        
        if llm_success:
            return True, llm_result
        else:
            if logger:
                logger.error(f"LLM修复也失败了: {llm_result}")
    
    # 所有修复方法都失败
    return False, result


def enhanced_validate_and_fix_ai_json(ai_response: str, expected_root_key: Optional[str] = None, 
                                     use_llm: Optional[bool] = None, logger=None) -> Tuple[bool, Any]:
    """
    增强版AI JSON验证和修复函数，集成LLM修复
    
    Args:
        ai_response: AI返回的响应字符串
        expected_root_key: 期望的根键名（如 'strategyReport'）
        use_llm: 是否使用LLM进行修复
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not isinstance(ai_response, str):
        if logger:
            logger.error(f"AI响应不是字符串类型: {type(ai_response)}, 值: {repr(ai_response)}")
        return False, f"AI响应不是字符串类型: {type(ai_response)}"
    
    # 首先尝试原有的修复方法
    success, result = validate_and_fix_ai_json(ai_response, expected_root_key, logger)
    
    if success:
        # 成功解析后，立即清理控制字符
        cleaned_result = deep_clean_control_characters(result)
        if logger:
            logger.info("AI JSON解析成功，已清理控制字符")
        return True, cleaned_result
    
    # 如果原有方法失败且启用LLM修复
    if (use_llm if use_llm is not None else ENABLE_LLM_REPAIR):
        if logger:
            logger.info("原有修复方法失败，尝试使用LLM修复...")
        
        # 使用LLM修复
        llm_success, llm_result = fix_json_with_llm(ai_response, logger)
        
        if llm_success:
            # 如果指定了期望的根键，尝试提取
            if expected_root_key and isinstance(llm_result, dict):
                final_result = llm_result.get(expected_root_key, llm_result)
            else:
                final_result = llm_result
            
            # LLM修复成功后，也要清理控制字符
            cleaned_final_result = deep_clean_control_characters(final_result)
            if logger:
                logger.info("LLM JSON修复成功，已清理控制字符")
            return True, cleaned_final_result
        else:
            if logger:
                logger.error(f"LLM修复也失败了: {llm_result}")
    
    # 所有修复方法都失败
    return False, result


def validate_redis_serialization(data: Any, logger=None) -> Tuple[bool, str]:
    """
    验证Redis序列化的完整性
    确保数据可以正确地进行双重序列化和反序列化
    
    Args:
        data: 要验证的数据
        logger: 日志记录器
    
    Returns:
        Tuple[bool, str]: (是否成功, 序列化结果或错误信息)
    """
    try:
        # 执行双重序列化
        serialized = safe_redis_serialize(data)
        
        # 验证反序列化
        step1 = json.loads(serialized)  # 第一次反序列化得到JSON字符串
        step2 = json.loads(step1)       # 第二次反序列化得到原始数据
        
        # 验证数据完整性（基本检查）
        if isinstance(data, dict) and isinstance(step2, dict):
            # 检查关键字段是否存在
            for key in ['status', 'taskInfo']:
                if key in data and key not in step2:
                    if logger:
                        logger.warning(f"Redis序列化验证失败：缺少关键字段 {key}")
                    return False, f"序列化后缺少关键字段: {key}"
        
        if logger:
            logger.info("Redis序列化验证成功")
        return True, serialized
        
    except Exception as e:
        error_msg = f"Redis序列化验证失败: {str(e)}"
        if logger:
            logger.error(error_msg)
        return False, error_msg


def safe_redis_serialize_with_validation(data: dict, **kwargs) -> str:
    """
    带验证的安全Redis序列化函数，生成双重序列化的紧凑JSON字符串
    
    解决HTML内容中未转义换行符导致JSON解析失败的问题：
    1. 对HTML字符串进行深度清理，确保所有换行符和特殊字符被正确转义
    2. 执行双重序列化：第一次ensure_ascii=False（保持中文可读），第二次ensure_ascii=True（Redis安全）
    3. 验证双重反序列化能正确解析
    
    Args:
        data: 要序列化的Python字典
        **kwargs: json.dumps的其他参数
    
    Returns:
        str: 双重序列化的紧凑JSON字符串，符合调用方期望格式
    
    Raises:
        ValueError: 当数据结构无法序列化时
    """
    try:
        # 深度清理数据，特别是HTML字符串中的控制字符
        # cleaned_data = deep_clean_control_characters(data)
        
        # 第一次序列化：Python对象 -> JSON字符串
        # ensure_ascii=False 保证中文字符可读性
        first_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        
        # 第二次序列化：JSON字符串 -> 转义的JSON字符串
        result = json.dumps(first_json, ensure_ascii=False)
        
        # 验证双重反序列化能正确解析
        try:
            step1 = json.loads(result)  # 得到JSON字符串
            step2 = json.loads(step1)   # 得到原始数据
        except (json.JSONDecodeError, TypeError) as e:
            raise ValueError(f"双重序列化验证失败: {e}")
        
        return result
        
    except (TypeError, ValueError) as e:
        print(f"CRITICAL: JSON serialization failed: {e}")
        raise ValueError(f"Failed to serialize data to double JSON format: {e}")


def safe_redis_deserialize(redis_data: str, restore_html: bool = True) -> Tuple[bool, Any]:
    """
    安全的Redis数据反序列化函数
    
    Args:
        redis_data: 从Redis读取的数据
        restore_html: 是否恢复HTML内容
    
    Returns:
        Tuple[bool, Any]: (是否成功, 反序列化结果或错误信息)
    """
    try:
        # 第一次反序列化：Redis字符串 -> JSON字符串
        json_str = json.loads(redis_data)
        
        # 第二次反序列化：JSON字符串 -> 数据对象
        data = json.loads(json_str)
        
        # 如果需要，恢复HTML内容
        if restore_html:
            data = restore_html_content(data)
        
        return True, data
        
    except Exception as e:
        return False, f"Redis反序列化失败: {str(e)}"


def get_serialization_strategy_recommendation(data: Any) -> str:
    """
    根据数据特征推荐最佳的序列化策略
    
    Args:
        data: 要分析的数据
    
    Returns:
        str: 推荐的策略描述
    """
    def analyze_data(obj, depth=0):
        """分析数据特征"""
        features = {
            'has_html': False,
            'has_control_chars': False,
            'max_depth': depth,
            'total_strings': 0,
            'large_strings': 0
        }
        
        if isinstance(obj, str):
            features['total_strings'] += 1
            if len(obj) > 1000:
                features['large_strings'] += 1
            
            # 检测HTML
            if any(tag in obj.lower() for tag in ['<html', '<body', '<div', '<script']):
                features['has_html'] = True
            
            # 检测控制字符
            if any(char in obj for char in ['\n', '\r', '\t']):
                features['has_control_chars'] = True
                
        elif isinstance(obj, dict):
            features['max_depth'] = max(features['max_depth'], depth)
            for value in obj.values():
                sub_features = analyze_data(value, depth + 1)
                features['has_html'] = features['has_html'] or sub_features['has_html']
                features['has_control_chars'] = features['has_control_chars'] or sub_features['has_control_chars']
                features['max_depth'] = max(features['max_depth'], sub_features['max_depth'])
                features['total_strings'] += sub_features['total_strings']
                features['large_strings'] += sub_features['large_strings']
                
        elif isinstance(obj, list):
            for item in obj:
                sub_features = analyze_data(item, depth + 1)
                features['has_html'] = features['has_html'] or sub_features['has_html']
                features['has_control_chars'] = features['has_control_chars'] or sub_features['has_control_chars']
                features['max_depth'] = max(features['max_depth'], sub_features['max_depth'])
                features['total_strings'] += sub_features['total_strings']
                features['large_strings'] += sub_features['large_strings']
        
        return features
    
    features = analyze_data(data)
    
    recommendations = []
    
    if features['has_html']:
        recommendations.append("检测到HTML内容，建议使用Base64编码策略")
    
    if features['has_control_chars']:
        recommendations.append("检测到控制字符，建议使用深度清理策略")
    
    if features['large_strings'] > 0:
        recommendations.append(f"检测到{features['large_strings']}个大字符串，建议使用压缩策略")
    
    if features['max_depth'] > 5:
        recommendations.append(f"数据嵌套深度为{features['max_depth']}，建议使用递归清理策略")
    
    if not recommendations:
        recommendations.append("数据结构简单，可以使用标准JSON序列化")
    
    return "; ".join(recommendations)


def clean_and_validate_json(json_str: str, logger=None) -> Tuple[bool, Any]:
    """
    轻量级JSON清理和验证函数
    
    Args:
        json_str: 待处理的JSON字符串
        logger: 日志记录器
    
    Returns:
        Tuple[bool, Any]: (是否成功, 解析结果或错误信息)
    """
    if not isinstance(json_str, str):
        return False, f"输入不是字符串类型: {type(json_str)}"
    
    # 记录原始输入的调试信息
    if logger:
        logger.debug(f"原始JSON字符串长度: {len(json_str)}")
        logger.debug(f"原始JSON前100字符: {repr(json_str[:100])}")
    
    # 1. 基础清理：移除markdown标记和多余空白
    cleaned = json_str.strip()
    
    # 检查是否为空字符串
    if not cleaned:
        return False, "输入为空字符串"
    
    # 尝试提取markdown中的JSON
    json_match = re.search(r'```json\s*\n(.*?)\n```', cleaned, re.DOTALL)
    if json_match:
        cleaned = json_match.group(1).strip()
    else:
        # 如果没有找到markdown块，尝试移除开头和结尾的markdown标记
        cleaned = re.sub(r'^```json\s*|\s*```$', '', cleaned, flags=re.DOTALL)
        # 移除JSON前的非JSON内容
        cleaned = re.sub(r'^[^{[]*(?=[{[])', '', cleaned, flags=re.DOTALL).strip()
        # 移除JSON后的非JSON内容
        cleaned = re.sub(r'(?<=[}\]])[^}\]]*$', '', cleaned, flags=re.DOTALL).strip()
    
    # 再次检查清理后是否为空
    if not cleaned:
        return False, "清理后的JSON字符串为空"
    
    # 记录清理后的调试信息
    if logger:
        logger.debug(f"清理后JSON字符串长度: {len(cleaned)}")
        logger.debug(f"清理后JSON前100字符: {repr(cleaned[:100])}")
        
    # 检查第一个字符是否是有效的JSON开始字符
    if cleaned[0] not in '{[':
        if logger:
            logger.warning(f"JSON字符串不以有效字符开始，第一个字符是: {repr(cleaned[0])}")
        # 尝试找到第一个有效的JSON开始位置
        start_pos = -1
        for i, char in enumerate(cleaned):
            if char in '{[':
                start_pos = i
                break
        
        if start_pos > 0:
            cleaned = cleaned[start_pos:]
            if logger:
                logger.info(f"从位置 {start_pos} 开始提取JSON")
        elif start_pos == -1:
            return False, f"未找到有效的JSON开始字符，字符串开头: {repr(cleaned[:50])}"
    
    # 2. 检测和修复常见的格式问题
    first_error = None
    second_error = None
    third_error = None
    fourth_error = None
    
    try:
        # 首先尝试直接解析
        result = json.loads(cleaned)
        return True, result
    except json.JSONDecodeError as e:
        first_error = e
        if logger:
            logger.warning(f"初次JSON解析失败: {e}, 尝试修复...")
    
    # 3. 修复常见问题 - 智能字符串值修复
    try:
        # 使用正则表达式找到字符串值并修复其中的特殊字符
        def fix_string_value(match):
            value = match.group(1)
            # 转义字符串值中的特殊字符
            value = value.replace('\\', '\\\\')  # 先转义反斜杠
            value = value.replace('\n', '\\n')
            value = value.replace('\r', '\\r')
            value = value.replace('\t', '\\t')
            value = value.replace('"', '\\"')
            return f'"{value}"'
        
        # 匹配字符串值的正则表达式
        string_pattern = r'"([^"]*(?:\n|\r|\t)[^"]*)"'
        fixed = re.sub(string_pattern, fix_string_value, cleaned, flags=re.DOTALL)
        
        # 尝试解析修复后的JSON
        result = json.loads(fixed)
        if logger:
            logger.info("JSON智能修复成功")
        return True, result
        
    except json.JSONDecodeError as e:
        second_error = e
        if logger:
            logger.warning(f"JSON智能修复后仍解析失败: {e}")
            logger.debug(f"智能修复后的JSON前200字符: {repr(fixed[:200])}")
    
    # 4. 检查是否是双重编码的JSON（Redis常见问题）
    try:
        # 如果字符串看起来像是被双重编码的，尝试解码
        if cleaned.startswith('"') and cleaned.endswith('"') and '\\' in cleaned:
            decoded_once = json.loads(cleaned)
            if isinstance(decoded_once, str):
                # 再次尝试解析
                result = json.loads(decoded_once)
                if logger:
                    logger.info("JSON双重编码修复成功")
                return True, result
    except (json.JSONDecodeError, TypeError):
        pass
    
    # 5. 暴力修复：直接替换所有特殊字符
    try:
        # 简单粗暴地替换所有特殊字符
        fixed = cleaned.replace('\n', '\\n').replace('\r', '\\r').replace('\t', '\\t')
        
        result = json.loads(fixed)
        if logger:
            logger.info("JSON暴力修复成功")
        return True, result
        
    except json.JSONDecodeError as e:
        third_error = e
        if logger:
            logger.warning(f"JSON暴力修复后仍解析失败: {e}")
            logger.debug(f"暴力修复后的JSON前200字符: {repr(fixed[:200])}")
    
    # 6. 最后尝试：逐个字符处理
    try:
        # 构建一个字符级别的修复器
        result_chars = []
        in_string = False
        escape_next = False
        
        for i, char in enumerate(cleaned):
            if escape_next:
                result_chars.append(char)
                escape_next = False
                continue
                
            if char == '\\':
                result_chars.append(char)
                escape_next = True
                continue
                
            if char == '"' and not escape_next:
                in_string = not in_string
                result_chars.append(char)
                continue
                
            if in_string:
                if char == '\n':
                    result_chars.append('\\n')
                elif char == '\r':
                    result_chars.append('\\r')
                elif char == '\t':
                    result_chars.append('\\t')
                else:
                    result_chars.append(char)
            else:
                result_chars.append(char)
        
        final_json = ''.join(result_chars)
        result = json.loads(final_json)
        if logger:
            logger.info("JSON字符级修复成功")
        return True, result
        
    except json.JSONDecodeError as e:
        fourth_error = e
        
    # 所有修复方法都失败，返回详细错误信息
    error_details = []
    if first_error:
        error_details.append(f"直接解析: {first_error}")
    if second_error:
        error_details.append(f"智能修复: {second_error}")
    if third_error:
        error_details.append(f"暴力修复: {third_error}")
    if fourth_error:
        error_details.append(f"字符级修复: {fourth_error}")
    
    final_error = "所有JSON修复尝试均失败。错误详情: " + "; ".join(error_details)
    if logger:
        logger.error(final_error)
        logger.debug(f"最终失败的JSON前200字符: {repr(cleaned[:200])}")
    
    return False, final_error 


def safe_redis_json_write(redis_client, key: str, data: Any, **kwargs) -> bool:
    """
    安全的Redis JSON写入，带有多重验证和错误处理
    """
    try:
        # 使用ultra_safe_json_dumps进行序列化
        json_str = ultra_safe_json_dumps(data, **kwargs)
        
        # 写入Redis
        redis_client.set(key, json_str)
        
        # 验证写入 - 读取并解析
        verification = redis_client.get(key)
        if verification:
            json.loads(verification.decode('utf-8'))
            logger.info(f"✅ Redis写入验证成功: {key}")
            return True
        else:
            logger.error(f"❌ Redis写入验证失败: 无法读取 {key}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Redis JSON写入失败 {key}: {e}")
        
        # 尝试备用策略
        try:
            # 使用最保守的序列化方式
            fallback_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            redis_client.set(key, fallback_json)
            
            # 验证备用策略
            verification = redis_client.get(key)
            if verification:
                json.loads(verification.decode('utf-8'))
                logger.warning(f"⚠️ Redis备用写入成功: {key}")
                return True
                
        except Exception as fallback_error:
            logger.error(f"❌ Redis备用写入也失败 {key}: {fallback_error}")
            
        return False 