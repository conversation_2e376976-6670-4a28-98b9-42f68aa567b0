import config
import requests
import json


def claude(system_prompt, user_prompt, model=None, temperature=None):
    headers = {'watt-gpt-token': config.WATT_AI_GPT_TOKEN}
    data = {
        "model": model if model else "claude-sonnet-4-20250514",
        "max_tokens": 12000,
        "stream": False,
        "temperature": temperature if temperature else 0.8,
        "top_k": 0,
        "system": system_prompt,
        "messages": [
            {
                "role": "user",
                "content": user_prompt
            }
        ]
    }
    try:
        response = requests.request("POST", config.WATT_AI_GPT_HOST + "/v1/anthropic/chat/completions",
                                    headers=headers, json=data, timeout=200)
    except Exception as e:
        return False, str(e)
    if response.status_code != 200:
        return False, str(response)
    try:
        response = response.json()
    except Exception as e:
        return False, str(e)
    if response['status'] != 0:
        return False, str(response)
    output_string = response['result']['data']['content'][0]['text']
    return True, output_string


def gemini(system_prompt, user_prompt, model=None, temperature=None, json_schema=None, max_out_tokens=12288, thinking_config=None):
    if not model:
        model = config.GEMINI_FLASH_MODEL
    body = {
        "model": model,
        "temperature": temperature,
        "contents": [
            {
                "parts": [
                    {
                        "text": system_prompt
                    }
                ],
                "role": "model"
            },
            {
                "parts": [
                    {
                        "text": user_prompt
                    }
                ],
                "role": "user"
            }
        ],
        "maxOutputTokens": max_out_tokens,
        "stream": False
    }
    if json_schema:
        body["responseMimeType"] = "application/json"
        body["responseSchema"] = json_schema
    
    # 只有在明确支持thinking_config的模型上才添加此配置
    # 对于不支持的模型，完全跳过thinking_config
    if thinking_config:
        body["thinkingConfig"] = thinking_config
    elif model == config.GEMINI_PRO_MODEL:
        # 只对已知支持的模型添加thinking_config
        try:
            body["thinkingConfig"] = {
                "includeThoughts": False,
                "thinkingBudget": 128
            }
        except:
            # 如果添加失败，移除thinking_config
            pass
    # 对于其他模型，不添加thinking_config
    
    header = {"Content-Type": "application/json",
              "watt-gpt-token": config.WATT_AI_GPT_TOKEN}
    url = config.WATT_AI_GPT_HOST + "/v1/gcp/chat/completions"
    try:
        response = requests.post(url, json=body, headers=header, timeout=300)
    except Exception as e:
        return False, str(e), None
    if response.status_code != 200:
        return False, f"HTTP {response.status_code}: {response.text}", None
    try:
        response_data = response.json()
    except Exception as e:
        return False, f"JSON decode error: {e}, response: {response.text[:500]}", None
    
    # 提取 token 使用量信息
    usage_metadata = None
    try:
        if 'result' in response_data and 'data' in response_data['result']:
            usage_metadata = response_data['result']['data'].get('usageMetadata', {})
    except Exception:
        usage_metadata = {}
    
    # 更安全的响应解析
    try:
        # 检查响应结构
        if 'result' not in response_data:
            return False, f"Missing 'result' in response: {response_data}", usage_metadata
        
        result = response_data['result']
        if 'data' not in result:
            return False, f"Missing 'data' in result: {result}", usage_metadata
        
        data = result['data']
        if 'candidates' not in data:
            return False, f"Missing 'candidates' in data: {data}", usage_metadata
        
        candidates = data['candidates']
        if not candidates or len(candidates) == 0:
            return False, f"Empty candidates: {data}", usage_metadata
        
        candidate = candidates[0]
        if 'content' not in candidate:
            return False, f"Missing 'content' in candidate: {candidate}", usage_metadata
        
        content = candidate['content']
        if 'parts' not in content:
            return False, f"Missing 'parts' in content: {content}", usage_metadata
        
        parts = content['parts']
        if not parts or len(parts) == 0:
            return False, f"Empty parts: {content}", usage_metadata
        
        part = parts[0]
        if 'text' not in part:
            return False, f"Missing 'text' in part: {part}", usage_metadata
        
        output_string = part['text']
        return True, output_string, usage_metadata
        
    except Exception as e:
        return False, f"Response parsing error: {e}, response structure: {response_data}", usage_metadata


def gpt(sys_prompt, user_prompt, json_schema='', model='gpt-4o-mini', temperature=0.6):
    body = {
        "model": model,
        'temperature': temperature,
        "messages": [
            {
                "role": "system",
                "content": sys_prompt
            },
            {
                "role": "user",
                "content": user_prompt
            }
        ]
    }
    header = {"Content-Type": "application/json",
              "watt-gpt-token": config.WATT_AI_GPT_TOKEN}
    if json_schema:
        if json_schema == 'flexible':
            body["response_format"] = {"type": "json_object"}
        else:
            body["response_format"] = {"type": "json_schema", "json_schema": json_schema}
    url = config.WATT_AI_GPT_HOST + '/v1/openai/chat/completions'
    try:
        response = requests.post(url, json=body, headers=header, timeout=300)
    except Exception as e:
        return False, str(e)
    if response.status_code != 200:
        return False, str(response)
    try:
        response = response.json()
    except Exception as e:
        return False, str(e)
    try:
        # 始终先提取纯净的内容字符串
        content_string = response['result']['data']['choices'][0]['message']['content']
        
        if json_schema:
            # 如果需要JSON格式，解析字符串为JSON对象
            output_string = json.loads(content_string)
        else:
            # 如果不需要JSON格式，直接返回字符串内容
            output_string = content_string
            
    except Exception as error:
        return False, f"Decode Error of output from GPT: {error}"
    return True, output_string