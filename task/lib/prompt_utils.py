"""
Prompt 模板工具模块
用于处理 Jinja2 模板的解析和渲染，实现代码与 prompt 的解耦
"""

import os
import re
import logging
from jinja2 import Environment, FileSystemLoader
from typing import Tuple, Dict, Any, Optional, List

logger = logging.getLogger(__name__)

# 全局缓存Jinja2环境
_jinja_env = None

def get_jinja_env():
    """获取或创建Jinja2环境，使用单例模式"""
    global _jinja_env
    if _jinja_env is None:
        # 使用绝对路径，避免工作目录问题
        current_dir = os.path.dirname(os.path.abspath(__file__))
        prompts_dir = os.path.join(current_dir, '..', 'server', 'account_diagnosis', 'prompts')
        prompts_dir = os.path.abspath(prompts_dir)
        
        _jinja_env = Environment(
            loader=FileSystemLoader(prompts_dir), 
            lstrip_blocks=True, 
            trim_blocks=True
        )
        # 添加 tojson 过滤器
        import json
        _jinja_env.filters['tojson'] = lambda obj: json.dumps(obj, ensure_ascii=False, indent=2)
    return _jinja_env

def clean_html_output(html_content: str) -> str:
    """
    清理 HTML 输出，移除可能的 markdown 代码块标记和其他无关内容
    
    Args:
        html_content: 原始 HTML 内容
        
    Returns:
        str: 清理后的 HTML 内容
    """
    if not html_content:
        return html_content
    
    # 移除各种可能的markdown代码块标记
    # 处理开头的标记
    html_content = re.sub(r'^```html\s*\n?', '', html_content, flags=re.IGNORECASE | re.MULTILINE)
    html_content = re.sub(r'^```HTML\s*\n?', '', html_content, flags=re.IGNORECASE | re.MULTILINE)
    html_content = re.sub(r'^```\s*\n?', '', html_content, flags=re.MULTILINE)
    
    # 处理结尾的标记
    html_content = re.sub(r'\n?```\s*$', '', html_content, flags=re.MULTILINE)
    html_content = re.sub(r'```\s*$', '', html_content, flags=re.MULTILINE)
    
    # 移除可能出现在中间的markdown标记
    html_content = re.sub(r'```html', '', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r'```HTML', '', html_content, flags=re.IGNORECASE)
    html_content = re.sub(r'```', '', html_content)
    
    # 移除可能的引号转义
    html_content = html_content.replace('\\"', '"')
    html_content = html_content.replace('\\n', '\n')
    
    # 查找完整的 HTML 文档
    html_pattern = r'<!DOCTYPE html.*?</html>'
    match = re.search(html_pattern, html_content, flags=re.DOTALL | re.IGNORECASE)
    
    if match:
        # 如果找到完整的 HTML 文档，返回它
        extracted_html = match.group().strip()
        
        # 最后一次清理：确保没有残留的markdown标记
        extracted_html = re.sub(r'```.*?```', '', extracted_html, flags=re.DOTALL)
        extracted_html = re.sub(r'```', '', extracted_html)
        
        # 使用专门的markdown artifact清理函数
        extracted_html = remove_markdown_artifacts(extracted_html)
        
        return extracted_html
    else:
        # 如果没有找到完整的 HTML 文档，返回清理后的内容
        logger.warning("未找到完整的 HTML 文档结构，返回清理后的原始内容")
        
        # 最后一次清理
        cleaned_content = html_content.strip()
        cleaned_content = re.sub(r'```.*?```', '', cleaned_content, flags=re.DOTALL)
        cleaned_content = re.sub(r'```', '', cleaned_content)
        
        # 使用专门的markdown artifact清理函数
        cleaned_content = remove_markdown_artifacts(cleaned_content)
        
        return cleaned_content


def remove_markdown_artifacts(content: str) -> str:
    """
    移除HTML内容中残留的markdown标记，确保不会有```html字样
    
    Args:
        content: 需要清理的HTML内容
        
    Returns:
        str: 清理后的HTML内容
    """
    if not content:
        return content
    
    # 移除所有可能的markdown代码块标记
    # 处理各种情况：```html、```HTML、``` html、```等
    patterns = [
        r'```html.*?\n?',  # ```html开头标记
        r'```HTML.*?\n?',  # ```HTML开头标记  
        r'```\s*html.*?\n?',  # ``` html开头标记（有空格）
        r'```\s*\n?',  # 单独的```标记
        r'\n?```\s*$',  # 结尾的```标记
        r'```',  # 任何剩余的```
    ]
    
    for pattern in patterns:
        content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.MULTILINE)
    
    # 移除转义的引号
    content = content.replace('\\"', '"')
    content = content.replace('\\n', '\n')
    
    return content.strip()


def clean_json_output(json_content: str) -> str:
    """
    清理 JSON 输出，移除可能的 markdown 代码块标记和其他无关内容
    
    Args:
        json_content: 原始 JSON 内容
        
    Returns:
        str: 清理后的 JSON 内容
    """
    if not json_content:
        return json_content
    
    # 移除开头的 markdown 代码块标记
    json_content = re.sub(r'^```json\s*\n?', '', json_content, flags=re.IGNORECASE | re.MULTILINE)
    json_content = re.sub(r'^```\s*\n?', '', json_content, flags=re.MULTILINE)
    
    # 移除结尾的 markdown 代码块标记
    json_content = re.sub(r'\n?```\s*$', '', json_content, flags=re.MULTILINE)
    
    # 查找 JSON 对象
    json_pattern = r'\{.*\}'
    match = re.search(json_pattern, json_content, flags=re.DOTALL)
    
    if match:
        return match.group().strip()
    else:
        logger.warning("未找到有效的 JSON 对象，返回清理后的原始内容")
        return json_content.strip()

def clean_markdown_output(markdown_content: str) -> str:
    """
    清理 Markdown 输出，移除可能的代码块标记
    
    Args:
        markdown_content: 原始 Markdown 内容
        
    Returns:
        str: 清理后的 Markdown 内容
    """
    if not markdown_content:
        return markdown_content
    
    # 移除开头的 markdown 代码块标记
    markdown_content = re.sub(r'^```markdown\s*\n?', '', markdown_content, flags=re.IGNORECASE | re.MULTILINE)
    markdown_content = re.sub(r'^```\s*\n?', '', markdown_content, flags=re.MULTILINE)
    
    # 移除结尾的 markdown 代码块标记
    markdown_content = re.sub(r'\n?```\s*$', '', markdown_content, flags=re.MULTILINE)
    
    return markdown_content.strip()

def parse_prompt_template(template_name: str, data: Dict[str, Any]) -> Tuple[str, str]:
    """
    解析 prompt 模板，返回系统提示词和用户提示词
    
    Args:
        template_name: 模板文件名（如 'html_generation.j2'）
        data: 模板渲染所需的数据
        
    Returns:
        tuple: (system_prompt, user_prompt)
        
    Raises:
        Exception: 模板渲染失败时抛出异常
    """
    try:
        env = get_jinja_env()
        template = env.get_template(template_name)
        rendered_content = template.render(data)
        
        # 使用分隔符分割系统提示词和用户提示词
        if '---SEPARATOR---' in rendered_content:
            system_prompt, user_prompt = rendered_content.split('---SEPARATOR---', 1)
            return system_prompt.strip(), user_prompt.strip()
        else:
            logger.warning(f"模板 {template_name} 中未找到分隔符，返回整个内容作为用户提示词")
            return "", rendered_content.strip()
            
    except Exception as e:
        logger.error(f"解析模板 {template_name} 时发生错误: {str(e)}")
        raise

def get_diagnosis_prompt(data: Dict[str, Any]) -> str:
    """
    获取诊断提示词（保持向后兼容）
    
    Args:
        data: 包含账号信息的数据字典
        
    Returns:
        str: 渲染后的诊断提示词
    """
    try:
        env = get_jinja_env()
        template = env.get_template('diagnosis.j2')
        return template.render(data)
    except Exception as e:
        logger.error(f"渲染诊断模板时发生错误: {str(e)}")
        # 避免递归错误，返回基础错误信息
        return f"模板渲染失败: {str(e)}"

def get_enhanced_diagnosis_prompt(data: Dict[str, Any], search_results: Optional[List[Dict[str, Any]]] = None) -> str:
    """
    获取增强版诊断提示词（支持深度搜索结果）
    
    Args:
        data: 包含账号信息的数据字典
        search_results: 深度搜索结果列表
        
    Returns:
        str: 渲染后的诊断提示词
    """
    try:
        env = get_jinja_env()
        template = env.get_template('diagnosis.j2')
        
        # 合并数据和搜索结果
        enhanced_data = data.copy()
        if search_results:
            enhanced_data['search_results'] = search_results
            
        return template.render(enhanced_data)
    except Exception as e:
        logger.error(f"渲染增强版诊断模板时发生错误: {str(e)}")
        # 回退到基础版本
        return get_diagnosis_prompt(data)

def get_html_generation_prompts(diagnosis_result: str, search_results: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, str]:
    """
    获取 HTML 生成的系统提示词和用户提示词
    
    Args:
        diagnosis_result: 诊断结果文本
        search_results: 深度搜索结果列表（可选）
        
    Returns:
        tuple: (system_prompt, user_prompt)
    """
    data: Dict[str, Any] = {'diagnosis_result': diagnosis_result}
    if search_results:
        data['search_results'] = search_results
    return parse_prompt_template('html_generation.j2', data)

def get_json_generation_prompts(diagnosis_result: str, search_results: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, str]:
    """
    获取 JSON 生成的系统提示词和用户提示词
    
    Args:
        diagnosis_result: 诊断结果文本
        search_results: 深度搜索结果列表（可选）
        
    Returns:
        tuple: (system_prompt, user_prompt)
    """
    data: Dict[str, Any] = {'diagnosis_result': diagnosis_result}
    if search_results:
        data['search_results'] = search_results
    return parse_prompt_template('json_generation.j2', data)

def get_sales_proposal_prompts(diagnosis_result: str, search_results: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, str]:
    """
    获取销售提案生成的系统提示词和用户提示词
    
    Args:
        diagnosis_result: 诊断结果文本
        search_results: 深度搜索结果列表（可选）
        
    Returns:
        tuple: (system_prompt, user_prompt)
    """
    data: Dict[str, Any] = {'diagnosis_result': diagnosis_result}
    if search_results:
        data['search_results'] = search_results
    return parse_prompt_template('sales_proposal_generation.j2', data)

def get_sales_proposal_html_prompts(proposal_result: str, search_results: Optional[List[Dict[str, Any]]] = None) -> Tuple[str, str]:
    """
    获取销售提案 HTML 生成的系统提示词和用户提示词
    
    Args:
        proposal_result: 销售提案结果文本
        search_results: 深度搜索结果列表（可选）
        
    Returns:
        tuple: (system_prompt, user_prompt)
    """
    data: Dict[str, Any] = {'proposal_result': proposal_result}
    if search_results:
        data['search_results'] = search_results
    return parse_prompt_template('proposal_html_generation.j2', data) 