from colorama import Fore, Style
import requests
from datetime import datetime, timezone

from config import LOG_LEVEL, WATT_REC_HOST
from task import TaskLog

TOP_SOURCE = 'twitter 24 hours trends' #'twitter trending list'

debug = LOG_LEVEL=='DEBUG'
def log(*args, level='info', highlight=True):
    if debug:
        if highlight:
            print(Fore.YELLOW) if level=='info' else print(Fore.RED)

        print(*args)

        if highlight:
            print(Style.RESET_ALL)
    else:
        #join *args to one string
        msg = ' '.join([str(i) for i in args])
        if 'error' in msg.lower():
            level = 'error'

        if level == 'info':
            TaskLog.info(msg)
        elif level == 'error':
            TaskLog.error(msg)
        elif level == 'warning':
            TaskLog.warning(msg)
        else:
            TaskLog.info(msg)


def upload_log(traceid: str, success=True, msg='', data={}, level='warn'):
    #DEBUG, INFO, WARN, ERROR, CRITICAL

    url = WATT_REC_HOST + '/api/v1/dataHub/report'
    app = 'task-ai-topic'
    timestamp = datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%S.%fZ')
    msg = msg if msg else "keyword to topic success"
    data = [
  {
    "traceId": traceid ,
    "appName": app,
    "appBatchId": f"{traceid}-{app}_00001",
    'logLevel': level.upper(),
    "code": 0 if success else -1, #"0-成功，-1 失败",
    "msg": msg[:1000],
    'data': data,
    "timestamp": timestamp,    
  }
]       

    res = requests.post(url, json=data)
    if res.status_code == 200 and res.json()['status'] == 0:        
        return True
    else:
        log(f'Error uploading log data to {url}', level='warning')    
        return False


def datetime_str(utc=True):
    if utc:
        return datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
    else:
        return datetime.now().strftime('%Y-%m-%dT%H:%M:%S')  