## 抽取memory prompt

memory_prompt = '''
You are given a list of tweets posted by a user.

<tweet_list>
{tweet_list}
</tweet_list>

User Informaton:
- profile: {profile}
- personality: {personality}
- memory: {memory}

Your task is to analyze the tweets and update the user's memory step by step, following the instructions below:

1. Content Deconstruction (Chain of Thought)
Analyze the following elements of each tweet:
- Current mood: negative/stable/positive
- Topic of interest: topic, sentiment to the topic (positive/neutral/negative) and intensity (range from 0 to 1)
- Opinion and belief: political/social/technological
- Influence to the user
- Writing style: humorous, professional, passionate, concise, gentle

Note: if tweet content is empty or simple emojis or gibberish, then just focus on the visual content. Otherwise, analyze both the text and visual content.
- Thoughts:
The user's current mood is 'xxx'. The user is interested in the topic of 'xxx'. The sentiment to the topic is 'xxx' with an intensity of 'xxx'. The user's opinion/belief in political/social/technological is 'xxx'. The tweet is very significant/insignificant to the user. The writing style is 'xxx'.

2. Summarize the Analysis (Chain of Thought)
- Summarize the user's current mood, average mood, and mood trend.
- Based on the topic of interest and sentiment in each tweet, update the user's interest list. The existing interest list is {interest_list}. If the topic belongs to one category in the interest list, update the intensity and sentiment and add the tweet id to the historyTweetId list. As for the topics which do not belong to any category in the interest list, classify them into different categories/keywords. To each keyword/category, summarize the search keyword (long description of the keyword), intensity, sentiment of the responding tweets and add tweet ids to the historyTweetId list.
- Based on the user's opinion and belief in political/social/technological, update the user's opinionsAndBeliefs list. For political category, update the leaning, intensity, and topics. For social category, update the stance, intensity, and topics. For technological category, update the stance, intensity, and topics.
- Based on the significant life events in the tweets, update the user's significantLifeEvents list. Summarize the event, date, description, sentiment, and impact.
- Summarize the user's writing style. The avaliable writing styles are humorous, professional, passionate, concise, gentle.

3. Output JSON
Finaly, compile your above analysis into a JSON format with the following structure:
{memory_output_structure}
'''

memory_output_structure = {
    "memory": {
        "mood": {
            "current": "xxx",
            "average": "xxx",
            "trend": "xxx"
        },
        "Interests": {
            "keywords": [
                {
                    "name": "xxx(short words)",
                    "search_keyword": "xxx (long sentence to describe the keyword)",
                    "intensity": 0.5,
                    "sentiment": "xxx",
                    "historyTweetId": ["tweet_id1", "tweet_id2"]
                }
            ],
            "opinionsAndBeliefs": {
                "political": { 
                    "leaning": "xxx",
                    "intensity": 0.5, 
                    "topics": ["xxx", "xxx"] 
                    },
                "social": { 
                    "stance": "xxx",
                    "intensity": 0.5, 
                    "topics": ["xxx", "xxx"] 
                    },
                "technological": { 
                    "stance": "xxx",
                    "intensity": 0.5, 
                    "topics": ["xxx", "xxx"] 
                    }
            },
            "significantLifeEvents": [ 
                {
                    "event": "xxx",
                    "date": "20xx-xx-xx",
                    "description": "xxxx",
                    "sentiment": "xxxx",
                    "impact": "xxx" 
                }
            ]
        },
        "writingStyle": "xxx"
    }
}

media_prompt_image = """You are given a tweet with one image posted by a user.

<tweet_text>
{tweet_text}
</tweet_text>

Your task is to analyze what the user is trying to express with the image in chain of thought.

- You should describe the content of the image, the emotions or messages it may convey.
- Based on the tweet text, analyze what the user is trying to express with the images.

Finally, output your analysis in JSON format with the following structure: {media_output_structure}
"""

media_prompt_video = """You are given a tweet with video frames posted by a user. The first image is the key frame of the video.

<tweet_text>
{tweet_text}
</tweet_text>

Your task is to analyze what the user is trying to express with these video frames in chain of thought.

- You should describe the content of the video frames, the emotions or messages they may convey.
- Based on the tweet text, analyze what the user is trying to express with the video frames.

Finally, output your analysis in JSON format with the following structure: {media_output_structure}
"""

media_output_structure = {
    "media": {
        "description": "xxx",
        "user_expression": "xxx"
    }
}

company_init_system_prompt = """You are skilled at searching company information and summarizing company profiles based on provided information and search results with predefined company schemas. Your goal is to analyze these company information and develop a compelling, well-rounded company profile that strictly adheres to the given schema.
"""

company_init_user_prompt = """Here is the basic brand information:
{company_info}

And here is a simple profile of the brand:
{company_profile}

Here is the json schema you must follow:

<brand_schema>
{company_schema}
</brand_schema>

Instructions:

1. Search the brand information on the internet. Carefully analyze the provided brand information and the search results. 

2. The founding time must be in the format of 'yyyy-mm'.

3. When you are analyzing target customers, you should consider the age range, the level of consumption, and the buying pain points for different customer groups.

4. In the introduction, you must not change the name of the brand and other factual information. When you are generating the introduction of the brand, you should consider the given introduction in brand profile. You must not generate self-contradictory introduction or inconsistent introduction that confuse the reader. The introduction of the brand should include only these key elements: business area, product features, target audience, brand vision, brand philosophy and brand value. And the introduction should limit within 50 tokens. You must not generate the words more than 50 tokens.

5. When you are generating the keywords, you should consider the given keywords in the brand profile. You must not generate duplicate keyword. If the keyword is already in the given keywords, just update the intensity. You should generate as many distinct keywords as you can based on the brand information.

Remember, your object is to generate a brand profile that is both authentic to the brand's essence and engaging for readers. Strive for a balance between staying true to the source and showcasing your creative flair.
Now. proceed with your character development and creation based on the provided brand schema.
"""

company_init_schema = {
            "type": "object",
            "properties": {
                "introduction": {
                    "type": "string",
                    "description": "A brief introduction of the brand within 50 tokens."
                },
                "keywords": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "The name of the keywords the highlight the feature of the brand within two words"
                            },
                            "intensity": {
                                "type": "number",
                                "description": "The weight of the influence to the brand, range from 0 to 1"
                            }    
                        },
                        "required": ["name", "intensity"]
                    },
                    "description": "The keywords that highlight the features of the brand. Generate as many distinct keywords as possible."
                },
                "foundingTime": {
                    "type": "string",
                    "description": "The founding time must be in the format of 'yyyy-mm'. If you don't know the founding time, just return empty sting."
                },
                "brandVision": {
                    "type": "string",
                    "description": "A brand vision describes its long-term goals and the impact it aspires to have on the world."
                },
                "brandValues": {
                    "type": "string",
                    "description": "Brand value refers to the perceived worth of a brand, based on consumer perception, loyalty, and recognition, often tied to the emotional connection the brand creates."
                },
                "targetAudience": {
                    "type": "array",
                    "description": "Refers to the specific group of consumers a brand aims to serve or attract with its products or services.",
                    "items": {
                        "type": "object",
                        "properties": {
                            "desc": {
                                "type": "string",
                                "description": "The brief description of the target audience, including age, profession."
                            },
                            "level": {
                                "type": "string",
                                "description": "The levels of consumer purchasing power or consumption capability.",
                                "enums": ["low", "middle", "high"]
                            },
                            "buypoints": {
                                "type": "string",
                                "description": "The buying pain points including the specific challenges or obstacles that customers experience during the purchasing process. In other words, the buy points for which the customers prefer buy the product instead of others."
                            } 
                        },
                        "required": ["desc", "level", "buypoints"]
                    }
                },
                "marketPosition": {
                    "type": "string",
                    "description": "Describe how a brand positions its product or service in the marketplace relative to competitors, and how it differentiates itself to appeal to its target audience"
                },
                "productFeatures": {
                    "type": "string",
                    "description": "The key features of the products of the brand",
                }
            },
            "required": ["introduction", "keywords"]
        }

company_search_system_prompt = "You are skilled at summarizing the search results about the competitor, recent news and market cases of the specific brand based on the brand information with predefined search schemas."

company_search_user_prompt = """Here is the basic brand information:
{company_info}

Here is the json schema you must follow:

<search_schema>
{search_schema}
</search_schema>

Here are the search results you need to analyze:

<competitor>
{competitor}
</competitor>

<recentNews>
{recentNews}
</recentNews>

<marketCases>
{marketCases}
</marketCases>

Instructions:

1. Search the competitors of the brand. If the competitor is already in brand info, just update the intensity. Otherwise, summarize the brand name, brief introduction and influence to the brand. For the url, you should give the official website url of the competitors. The url must be valid.

2. Search the recent and relevant news of the brand. If the news is already in brand info, just update the intensity. Otherwise, summarize the news title, brief introduction to the content and influence to the brand. For the url, you should give valid url of the news.

3. Search the market cases of the brand. If the market case is already in brand info, just update the intensity. Otherwise, summarize the title of the market case, brief introduction to the content and influence to the brand. For the url, you should give valid url of the market case. The number of the distinct market cases should be limited to no more than five.

4. Based on the competitor, recent news and market cases of the specific brand, update the keywords in the brand info. If the keyword is already in brand info, just update the intensity. Otherwise, generate the keyword within two words and summarize the influence to the brand.

5. All the generated information must be real. You must not generate fake information to cheat readers.

Remember, your object is to generate a brand profile that is both authentic to the brand's essence and engaging for readers. Strive for a balance between staying true to the source and showcasing your creative flair.
Now. proceed with your character development and creation based on the provided search schema.
"""

company_search_schema = {
            "type": "object",
            "properties": {
                "keywords": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "name": {
                                "type": "string",
                                "description": "The name of the keywords the highlight the feature of the brand within two words"
                            },
                            "intensity": {
                                "type": "number",
                                "description": "The weight of the influence to the brand, range from 0 to 1"
                            }    
                        },
                        "required": ["name", "intensity"]
                    },
                    "description": "The keywords that highlight the features of the brand. Generate as many distinct keywords as possible."
                },
                "competitor": {
                    "type": "array",
                    "description": "The competitors of the brand",
                    "items": {
                        "type": "object",
                        "properties": {
                            "company": {
                                "type": "string",
                                "description": "The name of the competitor"
                            },
                            "details": {
                                "type": "string",
                                "description": "The brief introduction of the competitor"
                            },
                            "url": {
                                "type": "string",
                                "description": "The official website url of the competitor"
                            },
                            "intensity": {
                                "type": "float",
                                "description": "The influence to the brand"
                            }
                        }
                    },
                    "required": ["company", "details", "url", "intensity"]
                },
                "recentNews": {
                    "type": "array",
                    "description": "The recent and relevant news of the brand",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "The title of the news"
                            },
                            "content": {
                                "type": "string",
                                "description": "The brief introduction of the news"
                            },
                            "url": {
                                "type": "string",
                                "description": "The url of the news"
                            },
                            "intensity": {
                                "type": "float",
                                "description": "The influence to the brand"
                            },
                            "time": {
                                "type": "float",
                                "description": "The happening time of the event in the news must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                            }
                        }
                    },
                    "required": ["title", "content", "url", "intensity", "time"]
                },
                "marketCases": {
                    "type": "array",
                    "description": "The successful market cases of the brand",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "The title of the market case"
                            },
                            "content": {
                                "type": "string",
                                "description": "The brief introduction of the market case"
                            },
                            "url": {
                                "type": "string",
                                "description": "The url of the market case"
                            },
                            "intensity": {
                                "type": "float",
                                "description": "The influence to the brand"
                            },
                            "time": {
                                "type": "float",
                                "description": "The happening time of the market cases must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                            }
                        }
                    },
                    "required": ["title", "content", "url", "intensity", "time"]
                }
            }
}

search_competitor_prompt = """Here is the basic brand information:
{company_info}

Your task is to search the competitors of the brand.

Rules:
1. Provide the name of the competitor, the detailed introduction of the competitor, the official website URL of the competitor, and the influence of the competitor on the brand.

2. The official website URL must be valid.
"""

search_recent_news_prompt = """Here is the basic brand information:
{company_info}

Your task is to search the recent and relevant news of the brand.

Rules:
1. Provide the title of the news, a detailed introduction to the content, the URL of the news, and the influence of the news on the brand.

2. Provide the accurate happening time of the news in the format of "yyyy-mm" or "yyyy-mm-dd".

3. The URL of the news must be valid.
"""

search_market_cases_prompt = """Here is the basic brand information:
{company_info}

Your task is to search the successful market cases of the brand.

Rules:
1. Provide the title of the market case, a detailed introduction to the content, the URL of the market case, and the influence of the market case on the brand.

2. Provide the accurate happening time of the market cases in the format of "yyyy-mm" or "yyyy-mm-dd".

3. The URL of the market case must be valid.

4. The market case should be a successful case that has a positive impact on the brand.

5. The number of the distinct market cases should be limited to no more than five.
"""

deep_search_time = """Here is the basic information of an event of the brand {company}:
{event_info}

Your task is to provide the most precise date of the event and rewrite the title and description of the event based on the official information.

Rules:
1. You should search the earliest or most official website source for this event.

2. You should ensure the information is accurate and include sources for verification.

3. The URLs of the source must be valid.
"""

market_case_series = """Here is the successful market cases of the brand {brand}:
{market_cases}

Your task is to sort out the timeline of these market cases and arrange these cases in reverse chronological order.

Here is the json schema you must follow:

<schema>
{schema}
</schema>

Rules:
1. You should summarize the precise time in each market cases, especially in these cases with deep_research_result. The format of the time must be 'yyyy-mm'.

2. You should summarize the title of the market case, brief introduction to the content in those cases with deep_research_result.

3. You should merge the market cases that are similar in content, have closely aligned timelines or share the same source URL.

4. You should evaluate the overall influence of each market case on the brand.

5. The URL of the market case must be valid.
"""

market_case_schema = {
    "type": "object",
    "properties": {
        "marketCases": {
            "type": "array",
            "description": "The successful market cases of the brand",
            "items": {
                "type": "object",
                "properties": {
                    "title": {
                        "type": "string",
                        "description": "The title of the market case"
                    },
                    "content": {
                        "type": "string",
                        "description": "The brief introduction of the market case"
                    },
                    "url": {
                        "type": "string",
                        "description": "The url of the market case"
                    },
                    "intensity": {
                        "type": "float",
                        "description": "The influence to the brand"
                    },
                    "time": {
                        "type": "float",
                        "description": "The happening time of the market cases must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                    }
                }
            },
            "required": ["title", "content", "url", "intensity", "time"]
        }
    }
}

### 2025.03.14 更新 根据品牌已知信息生成search query，使用pplx搜索，根据搜索结果生成brand profile ###
brand_basic_prompt_template = """You are a professional brand researcher tasked with conducting comprehensive market intelligence gathering based on a brand profile. Please follow these instructions to search and analyze information.
Here is the brand information:

[Brand Information]
{brand_info}

[Task Description]
Your task is to search the basic information of the brand including the following aspects:

- Founding date, the format is yyyy-mm or yyyy-mm-dd
- Brand values and vision
- Target audience, including age, profession, level of consumer purchasing power or consumption capability, the buy points for which the customers prefer buy the product instead of others
- Market positioning
- Product features
- Main competitors, including brand name, description, official website, and impact level
"""

brand_time_prompt_template = """You are a professional brand researcher tasked with conducting comprehensive market intelligence gathering based on a brand profile. Please follow these instructions to search and analyze information.
Here is the brand information:

[Brand Information]
{brand_info}

[Task Description]
Your task is to search the time of the brand including the following aspects:
- Recent news, including title, content, link, time, and impact level
- Successful market cases, including title, content, link, time, and impact level

Requirements:
If content is similar but comes from different URLs, please return the more official source. Each event should correspond to only one URL. Please include timestamp information whenever possible.
"""


query_generate_prompt_template = """You are a professional brand researcher tasked with conducting comprehensive market intelligence gathering based on a brand profile. Please follow these instructions to search and analyze information.
Here is the brand information:

[Brand Information]
{brand_info}

[Task Description]
Based on the provided brand information, judge whether you know the accurate information of the brand in the following aspects:

- Founding date, the format is yyyy-mm or yyyy-mm-dd
- Brand values and vision
- Target audience, including age, profession, level of consumer purchasing power or consumption capability, the buy points for which the customers prefer buy the product instead of others
- Market positioning
- Product features
- Main competitors, including brand name, description, official website, and impact level
- Recent news, including title, content, link, time, and impact level
- Successful market cases, including title, content, link, time, and impact level

If you don't know the accurate information, please generate four search queries to search the information following the following rules:
1. The search query should be concise and to the point.
2. The first search query should be able to search the information you don't know of the brand
3. The second search query should be able to search the information of the brand's competitors.
4. The third search query should be able to search the information of the brand's recent news.
5. The fourth search query should be able to search the information of the brand's successful market cases.

These queries should:
1. Be related to the brand and the specified aspect.
2. Help satisfy the aspect you don't know the accurate information.
3. The description of the brand in queries should be as accurate as possible to improve the accuracy of the search.

[Output Format]
You should output the search query and the detailed information in the specified JSON schema.
"""

query_generate_schema = {
    "name": "query_generate",
    "schema": {
        "type": "object",
        "strict": True,
        "properties": {
            "info_query": {
                "type": "string",
                "description": "Generate the search query to search the information of the brand in the specified aspect which you don't know the accurate information"
            },
            "competitor_query": {
                "type": "string",
                "description": "Generate the search query to search the information of the brand's competitors"
            },
            "news_query": {
                "type": "string",
                "description": "Generate the search query to search the information of the brand's recent news"
            },
            "market_query": {
                "type": "string",
                "description": "Generate the search query to search the information of the brand's successful market cases"
            }
        },
        "required": ["info_query", "competitor_query", "news_query", "market_query"],
        "additionalProperties": False
    }
}


gpt_process_prompt_template = """You are a professional brand analyst tasked with extracting structured information from search results to create a comprehensive brand profile. You will be provided with basic brand information and detailed search results about the brand. Your task is to analyze this data and generate a structured brand profile following the specified JSON schema.

<Brand Information>
{brand_info}
</Brand Information>

<Search Results>
{search_results}
</Search Results>

Task Requirements:
1. Brief introduction of the brand (within 50 tokens). If the introduction is not provided, please generate it based on the search results. Otherwise, please refine and enhance the introduction without altering its essential substance or key messaging based on the search results.

2. Keywords: Each keyword should be concise (no more than 2 tokens) and directly related to the brand. If the keyword is already provided in {keyword_list}, please reevaluate the intensity of the keyword and update the intensity.

3. Founding date: Please extract the founding date from the search results. If you cannot find the founding date, please return empty string. Otherwise, you must format the date as yyyy-mm.

4. Brand values and vision: Extract the brand values and vision from the search results. A brand vision describes its long-term goals and the impact it aspires to have on the world. A brand value refers to the perceived worth of a brand, based on consumer perception, loyalty, and recognition, often tied to the emotional connection the brand creates.

5. Target audience: Extract the target audience from the search results. The brief description of the target audience, including age, profession.
6. Market positioning: Extract the market positioning from the search results. Describe how a brand positions its product or service in the marketplace relative to competitors, and how it differentiates itself to appeal to its target audience.

7. Product features: Extract the key product features from the search results.

8. Main competitors: Extract the main competitors from the search results with the following information: brand name, description, official website, and impact level. You should ensure the official website is valid. If no official website is provided, please skip the competitor.

9. Recent news: Extract the recent news from the search results with the following information: title, content, link, time, and impact level. You should ensure the link is valid. If no link or time is provided, please skip the news. You should merge the recent news that are similar in content, have closely aligned timelines or share the same source URL. Sort these recent news in descending order by date.

10. Successful market cases: Extract the successful market cases from the search results with the following information: title, content, link, time, and impact level. You should ensure the link is valid. If no link or time is provided, please skip the market case. You should merge the market cases that are similar in content, have closely aligned timelines or share the same source URL. Sort these market cases in descending order by date.

11. You must ensure all the information is real. You must not generate fake information to cheat readers. 

12. Remember, your object is to generate a brand profile that is both authentic to the brand's essence and engaging for readers. Strive for a balance between staying true to the source and showcasing your creative flair.
"""

init_profile_schema = {
    "name": "brand_init_profile",
    "schema": {
        "type": "object",
        "strict": True,
        "properties": {
            "introduction": {
                "type": "string",
                "description": "A brief introduction of the brand within 50 tokens."
            },
            "foundingTime": {
                "type": "string",
                "description": "The founding time must be in the format of 'yyyy-mm'. If you don't know the founding time, just return empty sting."
            },
            "brandVision": {
                "type": "string",
                "description": "A brand vision describes its long-term goals and the impact it aspires to have on the world."
            },
            "brandValues": {
                "type": "string",
                "description": "Brand value refers to the perceived worth of a brand, based on consumer perception, loyalty, and recognition, often tied to the emotional connection the brand creates."
            },
            "targetAudience": {
                "type": "array",
                "description": "Refers to the specific group of consumers a brand aims to serve or attract with its products or services.",
                "items": {
                    "type": "object",
                    "properties": {
                        "desc": {
                            "type": "string",
                            "description": "The brief description of the target audience, including age, profession."
                        },
                        "level": {
                            "type": "string",
                            "description": "The levels of consumer purchasing power or consumption capability.",
                            "enums": ["low", "middle", "high"]
                        },
                        "buypoints": {
                            "type": "string",
                            "description": "The buying pain points including the specific challenges or obstacles that customers experience during the purchasing process. In other words, the buy points for which the customers prefer buy the product instead of others."
                        } 
                    },
                    "required": ["desc", "level", "buypoints"],
                    "additionalProperties": False
                }
            },
            "marketPosition": {
                "type": "string",
                "description": "Describe how a brand positions its product or service in the marketplace relative to competitors, and how it differentiates itself to appeal to its target audience"
            },
            "productFeatures": {
                "type": "string",
                "description": "The key features of the products of the brand",
            },
            "keywords": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "The name of the keywords the highlight the feature of the brand within two words"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The weight of the influence to the brand, range from 0 to 1"
                        }    
                    },
                    "required": ["name", "intensity"],
                    "additionalProperties": False
                },
                "description": "The keywords that highlight the features of the brand. Generate as many distinct keywords as possible."
            },
            "competitor": {
                "type": "array",
                "description": "The competitors of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "company": {
                            "type": "string",
                            "description": "The name of the competitor"
                        },
                        "details": {
                            "type": "string",
                            "description": "The brief introduction of the competitor"
                        },
                        "url": {
                            "type": "string",
                            "description": "The official website url of the competitor"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand, range from 0 to 1"
                        }
                    }
                },
                "required": ["company", "details", "url", "intensity"],
                "additionalProperties": False
            },
            "recentNews": {
                "type": "array",
                "description": "The recent and relevant news of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the news"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the news"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the news"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the event in the news must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            },
            "marketCases": {
                "type": "array",
                "description": "The successful market cases of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the market case"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the market case"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the market case"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the market cases must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            }
        },
        "additionalProperties": False,
        "required": ["introduction", "keywords"]
    }
}