from task import callWattAIRecAdmin

def memory_task_callback(task_result: dict, profile_id: str, task_type: str, task_status: int = 2):
    data = {
        "taskResult": task_result,
        "profileId": profile_id,
        "taskType": task_type,
        "taskStatus": task_status
    }
    uri = "/api/v1/data/profile/save"
    status, code, response_ = callWattAIRecAdmin.call(uri, data, timeout=20)
    return status, response_

def company_task_callback(task_result, task_type, task_status, resource_id = None, is_new = False, company_id = None):

    
    data = {
        "profileStatus": task_type,
        "taskStatus": task_status
    }

    if company_id:
        data["companyProfileId"] = company_id

    if task_result:
        data["taskResult"] = task_result
    
    if resource_id:
        data["resourceId"] = resource_id
        
    if is_new:
        data["isNew"] = is_new

    uri = "/api/v1/data/company/profile/save"

    status, code, response_ = callWattAIRecAdmin.call(uri, data, timeout=20)
    return status, response_

