import os
import sys
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

import json
import tiktoken
from datetime import datetime, timezone
from task import callWattGPT
from task.lib.memory_prompts import memory_prompt, memory_output_structure
from task.lib.task_callback import memory_task_callback
from task.dao.mongo.gmate.account_info import AccountInfo, TweetInfo
from task.lib.process_media import processMedia



def convert_timestamp(timestamp):
    # 将timestamp转换为utc时间
    if type(timestamp) == str:
        if timestamp.isdigit():
            timestamp_seconds = int(timestamp) / 1000
        else:
            return False, f"convert_timestamp error: timestamp is not digit"
    elif type(timestamp) == int:
        timestamp_seconds = timestamp / 1000
    else:
        return False, f"convert_timestamp error: timestamp type is not supported"
    
    utc_time = datetime.fromtimestamp(timestamp_seconds, tz=timezone.utc)

    return True, utc_time.strftime('%Y-%m-%d %H:%M:%S %Z')


class gmateMemory():
    def __init__(self, gpt_model: str = "gpt-4o"):
        self.call_gpt = callWattGPT
        self.gpt_model = gpt_model
        self.account_info = AccountInfo()
        self.tweet_info = TweetInfo()
        self.process_media = processMedia(self.gpt_model)
        self.encoder = tiktoken.get_encoding("cl100k_base")
        self.max_tokens = 100000
    
    def get_single_account_memory(self, account):
        account_id = account.get("_id", "")

        if not account_id:
            return False, f"gmateMemory.get_single_account_memory error: account_id is empty", account_id
        # 获取上次更新的最新的tweet_id，如果没有则为0，表示该用户需要全量更新
        last_updated_tweet_id = account.get("memoryTweetId", 0)

        # 获取用户的tweet_ids
        tweet_ids = account.get('xIds', [])

        # 如果lastUpdatedTweetId不为0，但tweet_ids为空，返回错误
        if last_updated_tweet_id != 0 and len(tweet_ids) == 0:
            return False, f"gmateMemory.get_single_account_memory error: memoryTweetId is not 0 but tweet_ids is empty", account_id
        # 如果lastUpdatedTweetId为0，但tweet_ids为空，返回错误
        if last_updated_tweet_id == 0 and len(tweet_ids) == 0:
            return False, f'gmateMemory.get_single_account_memory error: memoryTweetId is 0 and tweet_ids is empty', account_id
        
        # 增量更新memory, 获取新增的tweet
        tweet_update_ids = [tid for tid in tweet_ids if int(tid.split('_')[-1]) > int(last_updated_tweet_id)]

        if len(tweet_update_ids) == 0:
            return True, [], account_id
        # 根据tweet_ids获取对应的tweet信息
        get_tweet_status, tweet_list = self.tweet_info.getTweetInfoSingle(tweet_update_ids)
        # 如果获取tweet信息失败，返回错误，不更新该用户的memory
        if not get_tweet_status:
            return False, tweet_list, account_id
        
        # 获取用户profile
        account_profile = account.get("profileInfo", {})
        user_profile = account_profile.get("profile", {})

        # 初始化模版
        user_init_memory = {
            "mood": {
                "current": "",
                "average": "",
                "trend": ""
            },
            "Interests": {
                "keywords": [],
                "opinionsAndBeliefs": {
                    "political": {
                        "leaning": "",
                        "intensity": "",
                        "topics": []
                    },
                    "social": {
                        "stance": "",
                        "intensity": "",
                        "topics": []
                    },
                    "technological": {
                        "stance": "",
                        "intensity": "",
                        "topics": []
                    }
                },
                "significantLifeEvents": []
            }
        }
        if user_profile:
            # 获取用户静态信息，人设，记忆
            user_static_profile = user_profile.get("staticProfile", {})
            user_persona = user_profile.get("persona", {})
            user_memory = user_profile.get("memory", user_init_memory)
            user_keywords = account_profile.get("keywords", [])
            user_style = account_profile.get("style", [])
        else:
            user_static_profile = {}
            user_persona = {}
            user_memory = user_init_memory
            user_keywords = []
            user_style = []
        
        processed_tweet_list = []
        new_tweet_list = []
        for tw in tweet_list:
            if not tw.get('xPost'):
                continue
            if not tw['xPost'].get('tweetText'):
                continue
            if not tw['xPost'].get('tweetCreateTimeMs'):
                continue
            new_tweet_list.append(tw)
        new_tweet_list.sort(key=lambda x: int(x['xPost']['tweetCreateTimeMs']))
        # 预处理tweet_list
        for tweet in new_tweet_list:
            status, processed_tweet = self.preprocess_tweet(tweet)
            # 如果预处理失败，跳过该tweet
            if not status:
                print(f"Failed to preprocess tweet: {tweet}")
                continue
            processed_tweet_list.append(processed_tweet)
        
        # 获取用户记忆
        get_memory_status, memory, current_tweet_id = self.get_memory(processed_tweet_list,
                                                                      user_static_profile, 
                                                                      user_persona, 
                                                                      user_memory,
                                                                      user_keywords,
                                                                      user_style
                                                                    )

        # 如果获取用户记忆失败，返回错误，不更新该用户的memory
        if not get_memory_status:
            return False, memory, account_id
        else:
            updated_user_memory, updated_user_keywords, updated_user_style = memory

            # 更新用户profile
            new_profile = {
                "profile": {
                    "memory": updated_user_memory
                },
                "keywords": updated_user_keywords,
                "style": updated_user_style,
                "memoryTweetId": current_tweet_id
            }

            return True, new_profile, account_id
    
    def update_memory(self, user_memory, user_keywords, user_style, new_memory, tweet_list):
        """
        更新用户记忆
        """
        new_mood = new_memory.get('mood')
        if new_mood:
            current_mood = new_mood.get('current')
            average_mood = new_mood.get('average')
            trend = new_mood.get('trend')
            if current_mood:
                user_memory['mood']['current'] = current_mood
            if average_mood:
                user_memory['mood']['average'] = average_mood
            if trend:
                user_memory['mood']['trend'] = trend

        # tweetId - tweetCreateTimeMs mapping
        tweet_id_time_mapping = {}
        for tweet in tweet_list:
            tweet_id = tweet.get('tweetId')
            tweet_create_time = tweet.get('tweetCreateTimeMs')
            tweet_id_time_mapping[tweet_id] = tweet_create_time

        new_interest = new_memory.get('Interests')
        if new_interest:
            keywords = new_interest.get('keywords')
            if keywords:
                new_interest_keywords = [k["name"] for k in keywords]
                old_interest_keywords = [k.get("name") for k in user_memory['Interests']['keywords']]
                for index, keyword in enumerate(new_interest_keywords):
                    if keyword not in old_interest_keywords:
                        new_keyword_in_memory = keywords[index]
                        new_keyword_in_memory_history_tweet_id = new_keyword_in_memory.get('historyTweetId')
                        new_keyword_in_memory_history_tweet_id = list(set(new_keyword_in_memory_history_tweet_id))
                        new_keyword_in_memory_timestamp = [tweet_id_time_mapping[str(tid)] for tid in new_keyword_in_memory_history_tweet_id if tweet_id_time_mapping.get(str(tid))]

                        if len(new_keyword_in_memory_timestamp) != 0:
                            new_keyword_in_memory_timestamp.sort()
                        
                            start_status, new_keyword_in_memory_start_date = convert_timestamp(new_keyword_in_memory_timestamp[0])
                            if not start_status:
                                new_keyword_in_memory_start_date = ""
                            
                            end_status, new_keyword_in_memory_end_date = convert_timestamp(new_keyword_in_memory_timestamp[-1])
                            if not end_status:
                                new_keyword_in_memory_end_date = ""
                        else:
                            new_keyword_in_memory_start_date = ""
                            new_keyword_in_memory_end_date = ""
                        
                        new_keyword_in_memory['startDate'] = new_keyword_in_memory_start_date
                        new_keyword_in_memory['lastUpdatedDate'] = new_keyword_in_memory_end_date
                        new_keyword_in_memory_history_tweet_id.sort(reverse=True)
                        new_keyword_in_memory['historyTweetId'] = new_keyword_in_memory_history_tweet_id

                        user_memory['Interests']['keywords'].append(new_keyword_in_memory)
                    else:
                        new_intensity = keywords[index]['intensity']
                        new_sentiment = keywords[index]['sentiment']
                        new_history_tweet_id = keywords[index]['historyTweetId']
                        old_index = old_interest_keywords.index(keyword)
                        user_memory['Interests']['keywords'][old_index]['intensity'] = new_intensity
                        user_memory['Interests']['keywords'][old_index]['sentiment'] = new_sentiment

                        if type(new_history_tweet_id) != list:
                            new_history_tweet_id = [new_history_tweet_id]
                        
                        new_history_tweet_id_timestamp = [tweet_id_time_mapping.get(str(tid)) for tid in new_history_tweet_id if tweet_id_time_mapping.get(str(tid))]
                        
                        if len(new_history_tweet_id_timestamp) > 0:
                            new_history_tweet_id_timestamp.sort()
                            end_status, new_history_end_date = convert_timestamp(new_history_tweet_id_timestamp[-1])
                        else:
                            new_history_end_date = ""

                        if not end_status:
                            new_history_end_date = ""

                        user_memory['Interests']['keywords'][old_index]['lastUpdatedDate'] = new_history_end_date
                        if 'historyTweetId' not in user_memory['Interests']['keywords'][old_index]:
                            user_memory['Interests']['keywords'][old_index]['historyTweetId'] = new_history_tweet_id
                        else:
                            user_memory['Interests']['keywords'][old_index]['historyTweetId'].extend(new_history_tweet_id)
                        user_memory['Interests']['keywords'][old_index]['historyTweetId'] = list(set(user_memory['Interests']['keywords'][old_index]['historyTweetId']))

                        user_memory['Interests']['keywords'][old_index]['historyTweetId'].sort(reverse=True)

            new_keywords_return = []
            for keyword in user_memory['Interests']['keywords']:
                new_keywords_return.append({"name": keyword["name"], "intensity": keyword["intensity"]})

            user_keywords = new_keywords_return

            new_personality = new_interest.get('opinionsAndBeliefs')
            if new_personality:
                new_political = new_personality.get('political')
                new_social = new_personality.get('social')
                new_technological = new_personality.get('technological')

                if new_political:
                    new_political_leaning = new_political.get('leaning')
                    if new_political_leaning and new_political_leaning.lower() != 'none':
                        user_memory['Interests']['opinionsAndBeliefs']['political']['leaning'] = new_political_leaning
                    new_political_intensity = new_political.get('intensity')
                    if new_political_intensity:
                        user_memory['Interests']['opinionsAndBeliefs']['political']['intensity'] = new_political_intensity
                    new_political_topics = new_political.get('topics')
                    if new_political_topics:
                        for topic in new_political_topics:
                            if topic.lower() not in user_memory['Interests']['opinionsAndBeliefs']['political']['topics']:
                                user_memory['Interests']['opinionsAndBeliefs']['political']['topics'].append(topic.lower())
                
                if new_social:
                    new_social_stance = new_social.get('stance')
                    if new_social_stance and new_social_stance.lower() != 'none':
                        user_memory['Interests']['opinionsAndBeliefs']['social']['stance'] = new_social_stance
                    new_social_intensity = new_social.get('intensity')
                    if new_social_intensity:
                        user_memory['Interests']['opinionsAndBeliefs']['social']['intensity'] = new_social_intensity
                    new_social_topics = new_social.get('topics')
                    if new_social_topics:
                        for topic in new_social_topics:
                            if topic.lower() not in user_memory['Interests']['opinionsAndBeliefs']['social']['topics']:
                                user_memory['Interests']['opinionsAndBeliefs']['social']['topics'].append(topic.lower())
                
                if new_technological:
                    new_technological_stance = new_technological.get('stance')
                    if new_technological_stance and new_technological_stance.lower() != 'none':
                        user_memory['Interests']['opinionsAndBeliefs']['technological']['stance'] = new_technological_stance
                    new_technological_intensity = new_technological.get('intensity')
                    if new_technological_intensity:
                        user_memory['Interests']['opinionsAndBeliefs']['technological']['intensity'] = new_technological_intensity
                    new_technological_topics = new_technological.get('topics')
                    if new_technological_topics:
                        for topic in new_technological_topics:
                            if topic.lower() not in user_memory['Interests']['opinionsAndBeliefs']['technological']['topics']:
                                user_memory['Interests']['opinionsAndBeliefs']['technological']['topics'].append(topic.lower())
            
            significant_events = new_interest.get('significantLifeEvents')
            old_significant_events = [k['event'] for k in user_memory['Interests'].get('significantLifeEvents')] if user_memory['Interests'].get('significantLifeEvents') else []
            if significant_events:
                for event in significant_events:
                    if event['event'] not in old_significant_events:
                        user_memory['Interests']['significantLifeEvents'].append(event)
        
        writing_style = new_memory.get('writingStyle')
        if writing_style:
            if writing_style not in user_style:
                user_style.append(writing_style)
        
        return user_memory, user_keywords, user_style 
               
    def preprocess_tweet(self, tweet):
        """
        预处理tweet
        """
        tweet_info = tweet['xPost']
        tweetId = tweet_info['tweetId']
        tweetText = tweet_info['tweetText']
        tweetType = tweet_info['tweetType']
        tweetCreateTimeMs = tweet_info['tweetCreateTimeMs']
        tweetMediaList = tweet_info['tweetMediaList']
        new_tweet = {
            "tweetId": tweetId,
            "tweetText": tweetText,
            "tweetType": tweetType,
            "tweetCreateTimeMs": tweetCreateTimeMs,
        }
        
        # 处理tweet中的media信息
        if tweetMediaList:
            media_list = []
            for media in tweetMediaList:
                mediaType = media['mediaType']
                if mediaType == 'image':
                    imgUrl = media['imgUrl']
                    media_list.append({"type": "image", "url": imgUrl})
                elif mediaType == 'video':
                    videoUrl = media['videoUrl']
                    keyframeUrl = media.get('keyframeUrl')
                    if not keyframeUrl:
                        media_list.append({"type": "video", "url": videoUrl})
                    else:
                        media_list.append({"type": "video", "url": videoUrl, "keyframeUrl": keyframeUrl})
                else:
                    return False, f"gmateMemory.preprocess_tweet error: media type not supported"
            
            tweet_media_status, tweet_media_content = self.process_media.extract_media_content(tweetText, media_list)
            # 如果提取media信息失败，返回错误，不处理该tweet
            if not tweet_media_status:
                return False, tweet_media_content
            else:
                new_tweet["tweetMediaContent"] = tweet_media_content
        return True, new_tweet
                
    def get_memory(self, 
                   tweet_list: list,
                   user_static_profile: dict,
                   user_persona: dict,
                   user_memory: dict,
                   user_keywords, 
                   user_style,
                   timeout: int = 120,
                   watttraceid: str = ""
                   ):
        """
        获取用户记忆
        args:
            tweet_list: 用户推文列表
            user_static_profile: 用户静态信息
            user_persona: 用户人设
            user_memory: 用户记忆
        """

        # 根据已有的memory，提取用户的兴趣列表
        try:
            interest_list = list(user_memory['Interest']['keywords'].keys())
        except Exception as e:
            interest_list = []
        
        # 构建memory prompt
        start = 0
        end = 0
        current_tweet = ""
        while end < len(tweet_list) and start < len(tweet_list):
            prompt_tweet_list = tweet_list[start:end+1]
            prompt = memory_prompt.format(
                tweet_list=prompt_tweet_list[::-1],
                profile=user_static_profile,
                personality=user_persona,
                memory=user_memory,
                interest_list=interest_list,
                memory_output_structure=memory_output_structure
            )
            if len(self.encoder.encode(prompt)) < self.max_tokens and end < len(tweet_list) - 1:
                end += 1
                continue
            
            msg = [{"role": "user", "content": prompt}]
            body = {
                "messages": msg,
                "model": self.gpt_model,
                "response_format": {"type": "json_object"}
            }
            
            status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body, watttraceid=watttraceid, timeout=timeout)

            if not status:
                if start == 0:
                    return False, f"gmateMemory.get_memory error: failed to get memory - {response}", ""
                else:
                    current_tweet = tweet_list[start - 1].get('tweetId')
                    return True, [user_memory, user_keywords, user_style], current_tweet
            else:
                try:
                    res = response["result"]["data"]["choices"][0]['message']['content']
                    memory = json.loads(res)['memory']
                except Exception as e:
                    if start == 0:
                        return False, f"gmateMemory.get_memory error: failed to parse memory - {str(e)}", ""
                    else:
                        current_tweet = tweet_list[start - 1].get('tweetId')
                        return True, [user_memory, user_keywords, user_style], current_tweet
                
                updated_user_memory, updated_user_keywords, updated_user_style = self.update_memory(user_memory, user_keywords, user_style, memory, prompt_tweet_list)
                old_user_memory = user_memory
                old_user_keywords = user_keywords
                old_style = user_style
                old_interest_list = interest_list
                user_memory = updated_user_memory
                user_keywords = updated_user_keywords
                user_style = updated_user_style
                current_tweet = prompt_tweet_list[-1].get('tweetId')
                try:
                    interest_list = list(user_memory['Interest']['keywords'].keys())
                except Exception as e:
                    interest_list = old_interest_list
                start = end + 1
                end = start
        if len(current_tweet) == 0:
            return False, f"gmateMemory.get_memory error: failed to get tweet id - {tweet_list}", ""
        return True, [user_memory, user_keywords, user_style], current_tweet


if __name__ == "__main__":
    gmate_memory = gmateMemory()
    gmate_memory.pipeline()