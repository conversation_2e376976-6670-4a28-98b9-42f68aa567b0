## 实现brand init的检索得到品牌的基本信息，包含以下几点：
### 简介：不超过50个词，对品牌的介绍
### 关键词： 每个关键词不超过2个词，以及对公司的影响intensity
### 成立时间：返回要么是yyyy-mm要么是空字符串
### 品牌价值观、愿景
### 目标群体
### 市场定位
### 产品特点
### 竞品：品牌，相关介绍，官方网站，影响程度
### 最新的新闻：标题，内容，链接，时间yyyy-mm，影响程度
### 市场案例：标题，内容，链接，时间yyyy-mm，影响程度

# step 1. 根据用户填写的brand info，让gpt生成search query, 分为brand info(缺什么就查什么)，competitor(竞争对手)，news(新闻)，market cases(市场案例)
# step 2. 根据search query，使用pplx进行搜索，得到搜索结果
# step 3. 根据搜索结果，使用gpt进行处理，推理是否满足要求，得到brand profile

# deep search则逻辑变成
# step 4. 如果不满足要求，则使用gpt生成新的search query，然后重复step 2-3
# step 5. 如果满足要求，则停止

# improvement
# 考虑提前反思，生成不满足情况下的search query
# 每次read的结果增多

import os
import sys
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

import json
import re

from task import callWattGPT
from task.lib.search.company_profile_prompt import *


def check_date_format(data_str):
    # 1 -> yyyy, 2 -> yyyy-mm, 3 -> yyyy-mm-dd
    pattern_yyyy_mm = r"^\d{4}-\d{2}$"
    pattern_yyyy = r"^\d{4}$"
    pattern_yyyy_dd = r"^\d{4}-\d{2}-\d{2}$"

    if re.match(pattern_yyyy_mm, data_str):
        return 2
    elif re.match(pattern_yyyy_dd, data_str):
        return 3
    elif re.match(pattern_yyyy, data_str):
        return 1
    else:
        return 1


class CompanyProfileInit:
    def __init__(self, gpt_model: str = "gpt-4o", pplx_model: str = "sonar"):
        self.gpt_model = gpt_model
        self.pplx_model = pplx_model

    def process_init_profile(self, company_info, profile_info, url_context: dict = None):
        """
        详细解析只被初始化公司的信息
        """

        # 1. 获取公司信息
        company_name = company_info.get('company', '')
        company_url = company_info.get('companyURL', '')
        company_industry = company_info.get('industry', '')
        company_business_area = company_info.get('businessArea', '')
        profile_keywords = profile_info.get('keywords', [])

        # 如果公司名称不存在，则返回错误
        if len(company_name) == 0:
            return False, f"company name is empty - {company_info}"

        # 2. 生成search query
        pplx_search_list = []
        
        brand_basic_prompt = brand_basic_prompt_template.format(brand_info=json.dumps(company_info, indent=4, ensure_ascii=False))
            
        brand_time_prompt = brand_time_prompt_template.format(brand_info=json.dumps(company_info, indent=4, ensure_ascii=False))

        pplx_search_list.append({
            "model": self.pplx_model,
            "messages": [
                {"role": "user", "content": brand_basic_prompt}
            ]
        })
        
        pplx_search_list.append({
            "model": self.pplx_model,
            "messages": [
                {"role": "user", "content": brand_time_prompt}
            ],
            "search_recency_filter": "month"
        })
        
        pplx_search_status, code, pplx_search_result = callWattGPT.gcallPplxChannelChatCompletions(pplx_search_list, timeout=120)
        if not pplx_search_status:
            return False, f"failed to search details of the information - {pplx_search_result}"
        
        search_results = []
        try:
            for i, pplx_result in enumerate(pplx_search_result):
                status, code, result = pplx_result
                if not status:
                    continue
                search_res = result['result']['data']['choices'][0]['message']['content']
                search_citations = result['result']['data']['citations']
                search_results.append({
                    "result": search_res,
                    "citations": search_citations
                })
        except Exception as e:
            return False, f"failed to parse the pplx search result - {str(e)}\n{pplx_search_result}"

        # 5. gpt处理pplx_result
        # 在此处增加url解析的内容来辅助判断是否内容是否正确
        if url_context:
            company_info["urlCrawlContent"] = url_context
        gpt_process_prompt = gpt_process_prompt_template.format(
            brand_info=json.dumps(company_info, indent=4, ensure_ascii=False),
            search_results=json.dumps(search_results, indent=4, ensure_ascii=False),
            keyword_list=json.dumps(profile_keywords, indent=4, ensure_ascii=False)
        )
        body = {
            "model": self.gpt_model,
            "messages": [
                {"role": "user", "content": gpt_process_prompt}
            ],
            "response_format": {
                "type": "json_schema",
                "json_schema": init_profile_schema
            }
        }
        gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=120)
        if not gpt_status:
            return False, f"failed to process the search result - {gpt_result}"
        
        try:
            result = gpt_result['result']['data']['choices'][0]['message']['content']
            gpt_result = json.loads(result)
        except Exception as e:
            return False, f"failed to parse the gpt result - {str(e)}\n{gpt_result}"
        
        postprocess_status, result = self.postprocess(gpt_result, company_info, profile_info)

        if not postprocess_status:
            return False, f"failed to postprocess the gpt result - {result}"
        
        # 6. 如果满足要求，则返回结果
        return True, result
    
    def postprocess(self, result, company_info, profile_info):
        # 首先确定init的任务中不会存在已有的recent news, competitor, market cases
        competitors = result.get('competitor')
        if competitors:
            new_competitors = []
            try:
                for i, company in enumerate(competitors):
                    company_name = company.get('company')
                    url = company.get('url')
                    intensity = company.get("intensity")
                    if company_name and url and intensity:
                        new_competitors.append(company)
                result['competitor'] = sorted(new_competitors, key=lambda x: x['intensity'], reverse=True)
            except Exception as e:
                return False, f"failed to postprocess the competitor - {str(e)}\n{competitors}"
            
        recent_news = result.get('recentNews')
        if recent_news:
            new_recent_news = []
            try:
                for i, news in enumerate(recent_news):
                    title = news.get('title')
                    url = news.get('url')
                    intensity = news.get('intensity')
                    time = news.get('time')
                    if title and url and intensity and check_date_format(time)==2:
                        new_recent_news.append(news)
                result['recentNews'] = sorted(new_recent_news, key=lambda x: x['time'], reverse=True)
            except Exception as e:
                return False, f"failed to postprocess the recent news - {str(e)}\n{recent_news}"
        
        successful_market_cases = result.get('successfulMarketCases')
        if successful_market_cases:
            new_successful_market_cases = []
            try:
                for i, market_case in enumerate(successful_market_cases):
                    title = market_case.get('title')
                    url = market_case.get('url')
                    intensity = market_case.get('intensity')
                    time = market_case.get('time')
                    if title and url and intensity and check_date_format(time)==2:
                        new_successful_market_cases.append(market_case)
                result['successfulMarketCases'] = sorted(new_successful_market_cases, key=lambda x: x['time'], reverse=True)
            except Exception as e:
                return False, f"failed to postprocess the successful market cases - {str(e)}\n{successful_market_cases}"
        
        # 处理brand value, brand vision, founding time, target audience, 原始可能有的尽量不做更改
        brand_values = result.get('brandValues', '')
        brand_vision = result.get('brandVision', '')
        founding_time = result.get('foundingTime', '')
        target_audience = result.get('targetAudience', '')
        keywords = result.get('keywords', [])
        original_brand_values = company_info.get('brandValues', '')
        original_brand_vision = company_info.get('brandVision', '')
        original_founding_time = company_info.get('foundingTime', '')
        original_target_audience = profile_info.get('targetAudience', [])
        original_keywords = profile_info.get('keywords', [])

        if len(founding_time) > 0:
            if check_date_format(founding_time) != 2:
                result['foundingTime'] = original_founding_time
        
        if len(brand_values) <= 0:
            result['brandValues'] = original_brand_values
        
        if len(brand_vision) <= 0:
            result['brandVision'] = original_brand_vision
        
        if len(target_audience) <= 0:
            result['targetAudience'] = original_target_audience
        
        if result['targetAudience']:
            try:
                target_user = [audience['desc'] for audience in target_audience]
                result['targetUsers'] = ', '.join(target_user)
            except Exception as e:
                print(f"Process target user error - {str(e)} - {target_audience}")
                result['targetUsers'] = ''
        
        if len(keywords) <= 0:
            result['keywords'] = original_keywords
        else:
            try:
                keywords_name = [kw['name'] for kw in keywords if kw.get('name')]
                if len(original_keywords) > 0:
                    for okw in original_keywords:
                        name = okw.get('name')
                        if name and name not in keywords_name:
                            keywords.append(okw)
                
                result['keywords'] = sorted(keywords, key=lambda x: x['intensity'], reverse=True)
                subscribe_keywords = [keyword['name'] for keyword in result['keywords']][:5]
                result['subscribeKeywords'] = subscribe_keywords
            except Exception as e:
                return False, f"failed to postprocess the keywords - {str(e)}\n{keywords}"
        
        return True, result

        
if __name__ == "__main__":
    # 单元测试
    company_profile_init = CompanyProfileInit()
    company_info = {
        "company": "Tanka",
        "companyURL": "",
        "industry": "Technology",
        "businessArea": "AI Communication"
    }
    profile_info = {
        "keywords": [
            {"name": "AI Assistant", "intensity": 0.97}
        ]
    }
    status, result = company_profile_init.process_init_profile(company_info, profile_info)
    print(result)




