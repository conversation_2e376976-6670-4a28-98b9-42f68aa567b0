# utf-8
## 对brand进行深度搜索
### 对brand的market case
### news更新，daily搜索就行
### competitor更新，daily搜索就行

# 1. 根据brand info，生成search query
## search query的需求，唯一确定brand, 能够根据brand搜索到market case相关信息
## 使用gpt生成search query

# 2. 根据search query，使用pplx进行搜索
## 使用pplx进行搜索，得到搜索结果
## 使用gpt进行处理，推理是否满足要求，生成新的market case

import os
import sys
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

import json
import re
import tiktoken
from task import callWattGPT, TAVILY_API_KEY
from task.lib.company_profile_prompt import *


def tavily_search(query: str, max_results: int = 10, time_range: str = "year"):
    """
    使用serper进行搜索
    """
    import requests
    import random

    url = "https://api.tavily.com/search"
    api_key = random.choice(TAVILY_API_KEY)
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    body = {
        "query": query,
        "search_depth": "advanced",
        "max_results": max_results,
        "time_range": time_range,
        "include_raw_content": True
        }
    try:
        response = requests.request("POST", url, headers=headers, json=body, timeout=60)
    except Exception as e:
        return False, 99999, str(e)
    
    if response.status_code != 200:
        return False, 99998, str(response)
    
    try:
        response = response.json()
    except Exception as e:
        return False, 99997, str(e)

    query = response['query']
    results = []
    for res in response['results']:
        current_res = {}
        if 'score' in res:
            current_res['score'] = res['score']
            if current_res['score'] < 0.1:
                continue
        
        if 'title' in res:
            current_res['title'] = res['title']
        else:
            continue

        if 'url' in res:
            current_res['url'] = res['url']
        else:
            continue

        if 'content' in res:
            current_res['content'] = res['content']
        else:
            continue

        if 'raw_content' in res:
            current_res['raw_content'] = res['raw_content']
        results.append(current_res)

    return True, 0, {"query": query, "results": results}

def check_date_format(data_str):
    # 1 -> yyyy, 2 -> yyyy-mm, 3 -> yyyy-mm-dd
    pattern_yyyy_mm = r"^\d{4}-\d{2}$"
    pattern_yyyy = r"^\d{4}$"
    pattern_yyyy_dd = r"^\d{4}-\d{2}-\d{2}$"

    if re.match(pattern_yyyy_mm, data_str):
        return 2
    elif re.match(pattern_yyyy_dd, data_str):
        return 3
    elif re.match(pattern_yyyy, data_str):
        return 1
    else:
        return 1


def process_postsearch_result(search_results, encoder, max_tokens: int = 1000000):
    """
    从search_results列表中提取数据, 构建一个不超过max_tokens的字符串。
    从每个字典results的列表第一个开始取, 然后拼成一个string, 如果string长度小于100000tokens, 就继续加每个字典列表的第二个, 依次加加到tokens到底最大tokens
    """
    current_tokens = 0
    # 找出所有results列表中的最大长度
    max_results_length = max([len(item["results"]) for item in search_results]) if search_results else 0

    if max_results_length == 0:
        return False, "no search results"

    result_string = ""
    extracted_result = {}
    # 按照索引顺序提取results
    try:
        for i in range(max_results_length):
            # 对于每个search_results中的字典
            for item in search_results:
                # 检查该字典的results列表是否有足够的元素

                if item['query'] not in extracted_result:
                    extracted_result[item['query']] = []

                if i < len(item["results"]):
                    # 获取当前元素
                    current_result = item["results"][i]
                    
                    search_res_string = json.dumps(current_result, ensure_ascii=False)
                    # 估计添加这个元素后的token数
                    estimated_new_tokens = current_tokens + len(encoder.encode(search_res_string))
                    
                    # 如果添加后不会超过最大token限制，则添加它
                    if estimated_new_tokens <= max_tokens:
                        # 可以在这里为每个结果添加一些格式化信息，例如查询和结果编号
                        formatted_result = f"Query: {item['query']}\nResult #{i+1}:\n{search_res_string}\n\n"
                        result_string += formatted_result
                        current_tokens = estimated_new_tokens
                        extracted_result[item['query']].append(search_res_string)
                    else:
                        # 如果添加会超过限制，结束处理
                        break
    except Exception as e:
        return False, str(e)
    prompt = []
    for query, results in extracted_result.items():
        if len(results) > 0:
            prompt.append({
                "query": query,
                "results": results
            })

    return True, prompt


class CompanyProfileUpdate:
    def __init__(self, gpt_model: str = "gpt-4o", pplx_model: str = "sonar"):
        self.gpt_model = gpt_model
        self.pplx_model = pplx_model
        self.encoder = tiktoken.get_encoding("cl100k_base")
        self.max_tokens = 50000

    def process_update_profile(self, company_info: str, _type: str):
        """
        Company Info里面包含companyURL, companyName, industry等信息, 以及已有的market case, competitor, news
        要通过gpt生成search query,
        特别要注意的地方：
        1. 确定brand的唯一性, 在search query中如何体现, 在验证的时候如何体现
        2. 确定search query的准确性, 是否能够搜索到足够多的信息, 是否能够搜索到最新的信息
        3. 确定search query的全面性, 是否能够搜索到所有的相关信息
        4. 确定search query的时效性, 是否能够搜索到最新的信息

        做法还是想分开搜索, pplx搜索news和competitor,可以设为day进行搜索
        market cases的搜索使用deep search, 先让O3-mini生成plan, 再进行搜索
        """

        
        news_competitor_status, news_competitor_result = self.generate_news_competitor(company_info)
        if not news_competitor_status:
            return False, f"generate news and competitor failed - {news_competitor_result}"
        
        if _type == "update_init":
            market_case_status, market_case_result = self.generate_market_case(company_info)
            if not market_case_status:
                print(f"generate market case failed - {market_case_result}")
                market_year_status, market_year_result = self.generate_market_case_daily(company_info, "year")
                if not market_year_status:
                    return False, f"generate market case failed - {market_year_result}"
                market_case_result = market_year_result
        else:
            market_case_status, market_case_result = self.generate_market_case_daily(company_info, "day")
            if not market_case_status:
                return False, f"generate market case failed - {market_case_result}"
            
        
        prompt = reflection_prompt_template.format(
            brand_info=json.dumps(company_info, ensure_ascii=False), 
            news_competitor=news_competitor_result, 
            market_case=market_case_result)
        body = {
            "model": self.gpt_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "response_format": {
                "type": "json_schema",
                "json_schema": update_profile_schema
            }
        }
        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=120)
        if not status:
            return False, f"generate report failed - {code}"
        
        try:
            result = response['result']['data']['choices'][0]['message']['content']
            gpt_result = json.loads(result)
            final_result = self.postprocess_result(company_info, gpt_result)
            return True, final_result
        except Exception as e:
            return False, f"failed to parse the gpt result - {str(e)}\n{response}"

    def generate_news_competitor(self, company_info: str):
        """
        使用pplx进行news和competitor的搜索
        """
        prompt = news_competitor_prompt.format(company_info=company_info)
        body = {
            "model": self.pplx_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "search_recency_filter": "day"
        }
        status, code, response = callWattGPT.callPplxChannelChatCompletions(body)
        if not status:
            return False, f"generate news and competitor failed - {response}"
        
        try:
            search_res = response['result']['data']['choices'][0]['message']['content']
            search_citation = response['result']['data']['citations']
            search_results = {
                "result": search_res,
                "citations": search_citation
            }
        except Exception as e:
            return False, f"failed to parse the pplx search result - {str(e)}\n{response}"

        return True, search_results
    
    def generate_market_case_daily(self, company_info: str, interval: str = "day"):
        """
        使用pplx进行market case的daily  搜索
        """
        prompt = market_case_daily_prompt.format(company_info=company_info)
        body = {
            "model": self.pplx_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "search_recency_filter": interval
        }
        status, code, response = callWattGPT.callPplxChannelChatCompletions(body)
        if not status:
            return False, f"generate news and competitor failed - {code}"
        
        try:
            search_res = response['result']['data']['choices'][0]['message']['content']
            search_citation = response['result']['data']['citations']
            search_results = {
                "result": search_res,
                "citations": search_citation
            }
        except Exception as e:
            return False, f"failed to parse the pplx search result - {str(e)}\n{response}"

        return True, search_results
    
    def generate_market_case(self, company_info: str):
        """
        使用O3-mini生成market case的plan
        """

        prompt = planner_query_writer_instructions.format(brand_info=json.dumps(company_info), number_of_queries=5)
        body = {
            "model": self.gpt_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "response_format": {
                "type": "json_object"
            }
        }
        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=120)
        if not status:
            return False, f"generate market case plan failed - {code}"
        
        try:
            result = response['result']['data']['choices'][0]['message']['content']
            gpt_result = json.loads(result)
            queries = gpt_result['queries']
        except Exception as e:
            return False, f"failed to parse the gpt result - {str(e)}\n{response}"
        
        search_results = []
        for query in queries:
            status, code, response = tavily_search(query)
            if not status:
                continue
            search_results.append(response)
        if len(search_results) == 0:
            return False, "no search results"
        # 处理search results, sr_prompt是处理后的结果
        sr_status, sr_prompt = process_postsearch_result(search_results, self.encoder, self.max_tokens)
        if not sr_status:
            return False, f"process search results failed - {sr_prompt}"

        prompt = reflection_prompt.format(brand_info=json.dumps(company_info), market_case=json.dumps(sr_prompt))
        body = {
            "model": self.gpt_model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
        }

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=300)
        if not status:
            return False, f"generate market case report failed - {code}"
        
        try:
            result = response['result']['data']['choices'][0]['message']['content']
            return True, result
        except Exception as e:
            return False, f"failed to parse the gpt result - {str(e)}\n{response}"
        
    def postprocess_result(self, old_profile, new_profile):
        """
        对于新旧market cases, news, competitors进行去重以及合并更新
        """
        old_market_cases = old_profile.get('marketCases', [])
        new_market_cases = new_profile.get('marketCases', [])
        old_news = old_profile.get('recentNews', [])
        new_news = new_profile.get('recentNews', [])
        old_competitors = old_profile.get('competitor', [])
        new_competitors = new_profile.get('competitor', [])
        
        # 合并市场案例 (Market Cases)
        merged_market_cases = []
        # 创建一个字典，使用URL作为键来识别重复项
        market_cases_dict = {}
        
        # 先处理旧的市场案例
        for case in old_market_cases:
            # 使用URL作为唯一标识符
            key = case.get('url', '')
            # 如果没有URL，尝试使用标题和时间作为标识符
            if not key:
                key = f"{case.get('title', '')}"
            
            if key:
                market_cases_dict[key] = case
        
        # 再处理新的市场案例，如果有重复则替换或合并
        for case in new_market_cases:
            # 使用同样的方法获取键  
            key = case.get('url', '')
            if not key:
                key = f"{case.get('title', '')}"
                
            if key:
                # 如果已存在该案例，选择intensity更高的，或者直接使用新的
                if key in market_cases_dict:
                    old_case = market_cases_dict[key]
                    # 如果新案例的影响力更高或相等，采用新案例
                    if case.get('intensity', 0) >= old_case.get('intensity', 0):
                        market_cases_dict[key]['intensity'] = case['intensity']
                    # 否则保留旧案例但可能更新某些字段
                    else:
                        # 如果新案例有时间信息但旧案例没有，则更新时间
                        if case.get('time') and not old_case.get('time'):
                            old_case['time'] = case['time']
                        market_cases_dict[key] = old_case
                else:
                    # 如果不存在，直接添加
                    market_cases_dict[key] = case
        
        # 将字典转回列表，并按时间排序（如果有）
        merged_market_cases = list(market_cases_dict.values())
        # 检查是否有title, content, url, intensity, time这五个字段，如果没有则跳过
        merged_market_cases = [case for case in merged_market_cases if case.get('title') and case.get('content') and case.get('url') and case.get('intensity') and case.get('time')]
        merged_market_cases.sort(key=lambda x: x.get('time', ''), reverse=True)
        
        # 合并新闻 (News)
        merged_news = []
        # 创建一个字典，使用URL作为键来识别重复项
        news_dict = {}
        
        # 先处理旧的新闻
        for news in old_news:
            key = news.get('url', '')
            if not key:
                key = f"{news.get('title', '')}"
            
            if key:
                news_dict[key] = news
        
        # 再处理新的新闻
        for news in new_news:
            key = news.get('url', '')
            if not key:
                key = f"{news.get('title', '')}"
                
            if key:
                # 如果已存在该新闻，选择intensity更高的，或者直接使用新的
                if key in news_dict:
                    old_news_item = news_dict[key]
                    # 如果新新闻的影响力更高或相等，采用新新闻
                    if news.get('intensity', 0) >= old_news_item.get('intensity', 0):
                        news_dict[key]['intensity'] = news['intensity']
                    # 否则保留旧新闻但可能更新某些字段
                    else:
                        # 如果新新闻有时间信息但旧新闻没有，则更新时间
                        if news.get('time') and not old_news_item.get('time'):
                            old_news_item['time'] = news['time']
                        news_dict[key] = old_news_item
                else:
                    # 如果不存在，直接添加
                    news_dict[key] = news
        
        # 将字典转回列表，并按时间排序（如果有）
        merged_news = list(news_dict.values())
        # 检查是否有title, content, url, intensity, time这五个字段，如果没有则跳过
        merged_news = [news for news in merged_news if news.get('title') and news.get('content') and news.get('url') and news.get('intensity') and news.get('time')]
        merged_news.sort(key=lambda x: x.get('time', ''), reverse=True)
        
        # 合并竞争对手 (Competitors)
        merged_competitors = []
        # 创建一个字典，使用公司名称作为键来识别重复项
        competitors_dict = {}
        
        # 先处理旧的竞争对手
        for competitor in old_competitors:
            key = competitor.get('company', '').lower().strip()  # 忽略大小写和空格
            
            if key:
                competitors_dict[key] = competitor
        
        # 再处理新的竞争对手
        for competitor in new_competitors:
            key = competitor.get('company', '').lower().strip()  # 忽略大小写和空格
                
            if key:
                # 如果已存在该竞争对手，选择intensity更高的，或者直接使用新的
                if key in competitors_dict:
                    old_competitor = competitors_dict[key]
                    # 如果新竞争对手的影响力更高，采用新竞争对手
                    if competitor.get('intensity', 0) > old_competitor.get('intensity', 0):
                        competitors_dict[key]['intensity'] = competitor['intensity']
                    # 如果相等，合并信息
                    elif competitor.get('intensity', 0) == old_competitor.get('intensity', 0):
                        # 优先使用新的URL和详情，如果有的话
                        if competitor.get('url') and not old_competitor.get('url'):
                            old_competitor['url'] = competitor['url']
                        if competitor.get('details') and not old_competitor.get('details'):
                            old_competitor['details'] = competitor['details']
                        competitors_dict[key] = old_competitor
                else:
                    # 如果不存在，直接添加
                    competitors_dict[key] = competitor
        
        # 将字典转回列表，并按intensity排序
        merged_competitors = list(competitors_dict.values())
        # 检查是否有company, url, details, intensity这四个字段，如果没有则跳过
        merged_competitors = [competitor for competitor in merged_competitors if competitor.get('company') and competitor.get('url') and competitor.get('details') and competitor.get('intensity')]
        merged_competitors.sort(key=lambda x: x.get('intensity', 0), reverse=True)
        
        # 更新合并后的profile
        updated_profile = old_profile.copy()
        updated_profile['marketCases'] = merged_market_cases
        updated_profile['recentNews'] = merged_news
        updated_profile['competitor'] = merged_competitors
        
        return updated_profile

if __name__ == "__main__":
    company_info = {
        "companyURL": "",
        "companyName": "Tanka",
        "industry": "AI Communication"
    }
    update_profile = CompanyProfileUpdate()
    status, response = update_profile.process_update_profile(company_info, "update_init")
    if not status:
        print(f"generate market case failed")
    else:
        print(response)