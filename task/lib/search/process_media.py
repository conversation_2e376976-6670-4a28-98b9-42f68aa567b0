import json
import cv2
import requests
import base64
import os
import time
import hashlib
from task import callWattGPT, TaskLog
from task.lib.memory_prompts import media_prompt_image, media_prompt_video, media_output_structure

class processMedia():
    def __init__(self, gpt_model: str = 'gpt-4o'):
        self.gpt_model = gpt_model

        self.video_types = {
            "video/mp4": ".mp4",
            "video/webm": ".webm",
            "video/quicktime": ".mov",
            "video/3gpp": ".3gp",
            "video/x-msvideo": ".avi",
            "video/x-flv": ".flv",
            "video/x-matroska": ".mkv",
            "video/x-ms-wmv": ".wmv",
            "video/mpeg": ".mpeg"
        }

    def extract_media_content(self, tweet_text, tweet_media_list):
        # 根据tweet_text和tweet_media_list提取文本和媒体内容

        # 提取媒体内容，批处理喂给gpt解析
        call_gpt_body_list = []
        for tweet_media in tweet_media_list:
            media_type = tweet_media.get("type")
            media_url = tweet_media.get("url")
            if media_type == "image":
                # 处理图片，直接喂给gpt
                user_prompt = media_prompt_image.format(tweet_text=tweet_text, media_output_structure=json.dumps(media_output_structure, indent=4))
                image_content = [{"type": "text", "text": user_prompt}, {"type": "image_url", "image_url": {"url": media_url}}]
                body = {
                    "model": self.gpt_model,
                    "response_format": {"type": "json_object"},
                    "messages": [
                        {"role": "user",
                        "content": image_content}],
                    "max_tokens": 1000
                }
                call_gpt_body_list.append(body)

            elif media_type == "video":
                key_frame = tweet_media.get("keyframeUrl")
                # 下载视频，如果下载失败就跳过该视频
                status, video_path = self.download_video(media_url)
                if status:
                    # 提取视频帧，喂给gpt，提取失败就跳过该视频
                    status, video_frames = self.extract_frames_from_video(video_path)
                    if status:
                        # video prompt
                        user_prompt = media_prompt_video.format(tweet_text=tweet_text, media_output_structure=json.dumps(media_output_structure, indent=4))
                        # 判断关键帧是否存在，存在则加入到image_content中
                        if key_frame:
                            image_content = [{"type": "text", "text": user_prompt}, {"type": "image_url", "image_url": {"url": key_frame}}]
                        else:
                            image_content = [{"type": "text", "text": user_prompt}]
                        # 将视频帧加入到image_content中
                        for frame in video_frames:
                            image_content.append(
                                {"type": "image_url", 
                                 "image_url": {
                                     "url": f"data:image/jpeg;base64,{frame}"}
                                })
                        
                        body = {
                            "model": self.gpt_model,
                            "response_format": {"type": "json_object"},
                            "messages": [
                                {"role": "user",
                                "content": image_content}],
                            "max_tokens": 1000
                        }
                        call_gpt_body_list.append(body)
                    else:
                        TaskLog.error(f"extract video frames failed - {media_url} - {video_frames}")
                        continue
                else:
                    TaskLog.error(f"download video failed - {media_url} - {video_path}")
                    continue
            else:
                return False, "processMedia.extract_media_content Error: media type not supported"
            
        if len(call_gpt_body_list) == 0:
            return False, "processMedia.extract_media_content Error: media content fetch failed"
        
        status, code, response = callWattGPT.gCallOpenaiChannelChatCompletions(call_gpt_body_list, timeout=120)

        if not status:
            return False, response
        
        desc_list = []
        for resp in response:
            resp_status, resp_code, resp_response = resp
            if not resp_status:
                TaskLog.error(f"processMedia.extract_media_content Error: {resp_response}")
                continue
            else:
                try:
                    res = resp_response["result"]["data"]["choices"][0]['message']['content']
                    desc = json.loads(res)
                    desc_list.append(desc)
                except Exception as e:
                    TaskLog.error(f"processMedia.extract_media_content Error: {str(e)}")
                    continue
        
        return True, desc_list

    def download_video(self, video_url):
        """
        download video from url
        """
        timestamp_ms = int(time.time() * 1000)
        md5_hash = hashlib.md5(str(timestamp_ms).encode('utf-8')).hexdigest()
        try:
            response = requests.get(video_url)
        except Exception as e:
            return False, f"download video error: {str(e)}"
        if not os.path.exists('/tmp'):
            os.makedirs('/tmp')

        if response.status_code == 200:
            content_type = response.headers['content-type']
            if content_type in self.video_types.keys():
                video_type = self.video_types[content_type]
                video_name = f"/tmp/{md5_hash}{video_type}"
                with open(video_name, 'wb') as f:
                    f.write(response.content)

                return True, video_name
            else:
                return False, "video type not supported"
        else:
            return False, "download video failed"
        
    def extract_frames_from_video(self, video_path, num_frames=10):
        # Load video from the saved video path
        try:
            video = cv2.VideoCapture(video_path)
        except Exception as e:
            os.remove(video_path)
            return False, str(e)
        # Check if video opened successfully
        if not video.isOpened():
            os.remove(video_path)
            return False, "Couldn't read video file"
        frame_count = int(video.get(cv2.CAP_PROP_FRAME_COUNT))  # Get video properties
        # Determine the interval for sampling frames
        step = max(frame_count // num_frames, 1)

        frames = []
        for i in range(num_frames):
            frame_number = i * step
            video.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = video.read()
            if not ret:
                break
            # Convert the frame to base64
            _, buffer = cv2.imencode('.jpg', frame)
            base64_str = base64.b64encode(buffer).decode('utf-8')
            frames.append(base64_str)

        video.release()
        os.remove(video_path)
        return True, frames


if __name__ == "__main__":
    process_media = processMedia()
    video_url = "https://video.twimg.com/ext_tw_video/1871135619549941760/pu/vid/avc1/320x568/bI4iphHq-azirvZE.mp4?tag=12"
    process_media.download_video(video_url)