import os
import sys
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

import json
from task import callWattGPT
from task.lib.company_profile_prompt import *


class CompanyProfileKeywordsUpdate:
    def __init__(self, gpt_model: str = "gpt-4o"):
        self.gpt_model = gpt_model
    
    def update_keywords(self, old_company_info, new_brand_info):
        """
        更新keywords
        """
        
        prompt_template = """You are a brand intelligence analyst responsible for updating brand keyword profiles. You will be provided with two sets of information: Original brand profile (old_company_info) containing existing keywords and their impact intensity, Newly discovered brand information reflecting recent news and market cases.

        <old_company_info>
        {old_company_info}
        </old_company_info>

        <new_brand_info>
        {new_brand_info}
        </new_brand_info>
        
        [Task]
        Your task is to analyze both sets of information and produce an updated set of keywords that accurately reflects the brand's current positioning and characteristics.
        
        [Processing Instructions]
        - First, review all existing keywords in the original profile and their current intensity values
        - Next, thoroughly analyze the new brand information to:
            - Identify which existing keywords remain relevant
            - Discover new keywords that should be added
            - Determine how intensity values should be adjusted
        - For each keyword in the original profile:
            - If it remains relevant based on new information:
                - Adjust its intensity value (0-1 range) based on its current importance
        
        [Important Instructions]
        - If its significance has increased, increase the intensity value
        - If its significance has decreased, decrease the intensity value
        - For potential new keywords identified in the updated information:
            - Assess if they represent important brand characteristics
            - Ensure each new keyword is concise (1-2 words maximum)
            - Assign an appropriate intensity value (0-1 range) based on importance
            - Add them to the updated keyword list

        [Output Format]
        - Return the updated keyword list in the JSON format."""
    
        json_schema = {
            "name": "keywords_update",
            "schema": {
                "type": "object",
                "strict": True,
                "properties": {
                    "keywords": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {
                                    "type": "string",
                                    "description": "The name of the keywords the highlight the feature of the brand within two words"
                                },
                                "intensity": {
                                    "type": "number",
                                    "description": "The weight of the influence to the brand, range from 0 to 1"
                                }    
                            },
                            "required": ["name", "intensity"],
                            "additionalProperties": False
                        },
                        "description": "The keywords that highlight the features of the brand. Generate as many distinct keywords as possible."
                    }
                },
                "additionalProperties": False,
                "required": ["keywords"]
            }
        }

        prompt = prompt_template.format(
            old_company_info=json.dumps(old_company_info, ensure_ascii=False), 
            new_brand_info=json.dumps(new_brand_info, ensure_ascii=False)
        )
        body = {
            "model": self.gpt_model,
            "messages": [{"role": "user", "content": prompt}],
            "response_format": {"type": "json_schema", "json_schema": json_schema}
        }

        gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=120)
        if not gpt_status:
            return False, f"failed to call gpt - {gpt_result}"
        
        try:
            result = gpt_result['result']['data']['choices'][0]['message']['content']
            gpt_result = json.loads(result)['keywords']
            final_result = self.postprocess(old_company_info, gpt_result)
            return True, final_result
        except Exception as e:
            return False, f"failed to parse gpt result - {str(e)}\n{gpt_result}"
        

    def postprocess(self, old_company_info, new_keywords):
        """
        根据新的keywords更新old_company_info
        """

        old_keywords = old_company_info.get('keywords', [])
        
        # 1. 如果old_keywords中存在new_keywords中的关键词，则更新intensity
        for keyword in new_keywords:
            for old_keyword in old_keywords:
                if old_keyword['name'] == keyword['name']:
                    old_keyword['intensity'] = keyword['intensity']
        
        # 2. 如果old_keywords中不存在new_keywords中的关键词，则添加到old_keywords中
        for keyword in new_keywords:
            if keyword not in old_keywords:
                old_keywords.append(keyword)
        
        # 3. 对keywords按照intensity从大到小排序, 并选择前5个作为subscribe_keywords
        old_keywords = sorted(old_keywords, key=lambda x: x['intensity'], reverse=True)
        subscribe_keywords = [keyword['name'] for keyword in old_keywords][:5]
        old_company_info['subscribeKeywords'] = subscribe_keywords
        old_company_info['keywords'] = old_keywords

        return old_company_info