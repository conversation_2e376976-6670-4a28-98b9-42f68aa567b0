
### 2025.03.14 更新 根据品牌已知信息生成search query，使用pplx搜索，根据搜索结果生成brand profile ###
brand_basic_prompt_template = """You are a professional brand researcher tasked with conducting comprehensive market intelligence gathering based on a brand profile. Please follow these instructions to search and analyze information.
Here is the brand information:

[Brand Information]
{brand_info}

[Task Description]
Your task is to search the basic information of the brand including the following aspects:

- Founding date, the format is yyyy-mm or yyyy-mm-dd
- Brand values and vision
- Target audience, including age, profession, level of consumer purchasing power or consumption capability, the buy points for which the customers prefer buy the product instead of others
- Market positioning
- Product features
- Main competitors, including brand name, description, official website, and impact level
"""

brand_time_prompt_template = """You are a professional brand researcher tasked with conducting comprehensive market intelligence gathering based on a brand profile. Please follow these instructions to search and analyze information.
Here is the brand information:

[Brand Information]
{brand_info}

[Task Description]
Your task is to search the time of the brand including the following aspects:
- Recent news, including title, content, link, time, and impact level
- Successful market cases, including title, content, link, time, and impact level

Requirements:
If content is similar but comes from different URLs, please return the more official source. Each event should correspond to only one URL. Please include timestamp information whenever possible.
"""

gpt_process_prompt_template = """You are a professional brand analyst tasked with extracting structured information from search results to create a comprehensive brand profile. You will be provided with basic brand information and detailed search results about the brand. Your task is to analyze this data and generate a structured brand profile following the specified JSON schema.

<Brand Information>
{brand_info}
</Brand Information>

<Search Results>
{search_results}
</Search Results>

Task Requirements:
1. Brief introduction of the brand (within 50 tokens). If the introduction is not provided, please generate it based on the search results. Otherwise, please refine and enhance the introduction without altering its essential substance or key messaging based on the search results.

2. Keywords: Each keyword should be concise (no more than 2 tokens) and directly related to the brand. If the keyword is already provided in {keyword_list}, please reevaluate the intensity of the keyword and update the intensity.

3. Founding date: Please extract the founding date from the search results. If you cannot find the founding date, please return empty string. Otherwise, you must format the date as yyyy-mm.

4. Brand values and vision: Extract the brand values and vision from the search results. A brand vision describes its long-term goals and the impact it aspires to have on the world. A brand value refers to the perceived worth of a brand, based on consumer perception, loyalty, and recognition, often tied to the emotional connection the brand creates.

5. Target audience: Extract the target audience from the search results. The brief description of the target audience, including age, profession.
6. Market positioning: Extract the market positioning from the search results. Describe how a brand positions its product or service in the marketplace relative to competitors, and how it differentiates itself to appeal to its target audience.

7. Product features: Extract the key product features from the search results.

8. Main competitors: Extract the main competitors from the search results with the following information: brand name, description, official website, and impact level. You should ensure the official website is valid. If no official website is provided, please skip the competitor.

9. Recent news: Extract the recent news from the search results with the following information: title, content, link, time, and impact level. You should ensure the link is valid. If no link or time is provided, please skip the news. You should merge the recent news that are similar in content, have closely aligned timelines or share the same source URL. Sort these recent news in descending order by date.

10. Successful market cases: Extract the successful market cases from the search results with the following information: title, content, link, time, and impact level. You should ensure the link is valid. If no link or time is provided, please skip the market case. You should merge the market cases that are similar in content, have closely aligned timelines or share the same source URL. Sort these market cases in descending order by date.

11. You must ensure all the information is real. You must not generate fake information to cheat readers. 

12. Remember, your object is to generate a brand profile that is both authentic to the brand's essence and engaging for readers. Strive for a balance between staying true to the source and showcasing your creative flair.
"""

init_profile_schema = {
    "name": "brand_init_profile",
    "schema": {
        "type": "object",
        "strict": True,
        "properties": {
            "introduction": {
                "type": "string",
                "description": "A brief introduction of the brand within 50 tokens."
            },
            "foundingTime": {
                "type": "string",
                "description": "The founding time must be in the format of 'yyyy-mm'. If you don't know the founding time, just return empty sting."
            },
            "brandVision": {
                "type": "string",
                "description": "A brand vision describes its long-term goals and the impact it aspires to have on the world."
            },
            "brandValues": {
                "type": "string",
                "description": "Brand value refers to the perceived worth of a brand, based on consumer perception, loyalty, and recognition, often tied to the emotional connection the brand creates."
            },
            "targetAudience": {
                "type": "array",
                "description": "Refers to the specific group of consumers a brand aims to serve or attract with its products or services.",
                "items": {
                    "type": "object",
                    "properties": {
                        "desc": {
                            "type": "string",
                            "description": "The brief description of the target audience, including age, profession."
                        },
                        "level": {
                            "type": "string",
                            "description": "The levels of consumer purchasing power or consumption capability.",
                            "enums": ["low", "middle", "high"]
                        },
                        "buypoints": {
                            "type": "string",
                            "description": "The buying pain points including the specific challenges or obstacles that customers experience during the purchasing process. In other words, the buy points for which the customers prefer buy the product instead of others."
                        } 
                    },
                    "required": ["desc", "level", "buypoints"],
                    "additionalProperties": False
                }
            },
            "marketPosition": {
                "type": "string",
                "description": "Describe how a brand positions its product or service in the marketplace relative to competitors, and how it differentiates itself to appeal to its target audience"
            },
            "productFeatures": {
                "type": "string",
                "description": "The key features of the products of the brand",
            },
            "keywords": {
                "type": "array",
                "items": {
                    "type": "object",
                    "properties": {
                        "name": {
                            "type": "string",
                            "description": "The name of the keywords the highlight the feature of the brand within two words"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The weight of the influence to the brand, range from 0 to 1"
                        }    
                    },
                    "required": ["name", "intensity"],
                    "additionalProperties": False
                },
                "description": "The keywords that highlight the features of the brand. Generate as many distinct keywords as possible."
            },
            "competitor": {
                "type": "array",
                "description": "The competitors of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "company": {
                            "type": "string",
                            "description": "The name of the competitor"
                        },
                        "details": {
                            "type": "string",
                            "description": "The brief introduction of the competitor"
                        },
                        "url": {
                            "type": "string",
                            "description": "The official website url of the competitor"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand, range from 0 to 1"
                        }
                    }
                },
                "required": ["company", "details", "url", "intensity"],
                "additionalProperties": False
            },
            "recentNews": {
                "type": "array",
                "description": "The recent and relevant news of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the news"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the news"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the news"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the event in the news must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            },
            "marketCases": {
                "type": "array",
                "description": "The successful market cases of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the market case"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the market case"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the market case"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the market cases must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            }
        },
        "additionalProperties": False,
        "required": ["introduction", "keywords"]
    }
}


news_competitor_prompt = """Here is the basic brand information:
{company_info}

Your task is to search the recent and relevant news of the brand, and the competitor of the brand.

Rules:
1. Provide the title of the news, a detailed introduction to the content, the URL of the news, and the influence of the news on the brand.

2. Provide the accurate happening time of the news in the format of "yyyy-mm" or "yyyy-mm-dd".

3. Provide the name of the competitor, the detailed introduction of the competitor, the official website URL of the competitor, and the influence of the competitor on the brand.

4. The URL provided in the news and competitor must be valid.
"""

market_case_daily_prompt = """Here is the basic brand information:
{company_info}

Your task is to search the recent and relevant market cases of the brand.

Rules:
1. Provide the title of the market case, a detailed introduction to the content, the URL of the market case, and the influence of the market case on the brand.

2. Provide the accurate happening time of the market case in the format of "yyyy-mm" or "yyyy-mm-dd".

3. The URL provided in the market case must be valid.
"""


update_profile_schema = {
    "name": "brand_init_profile",
    "schema": {
        "type": "object",
        "strict": True,
        "properties": {
            "competitor": {
                "type": "array",
                "description": "The competitors of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "company": {
                            "type": "string",
                            "description": "The name of the competitor"
                        },
                        "details": {
                            "type": "string",
                            "description": "The brief introduction of the competitor"
                        },
                        "url": {
                            "type": "string",
                            "description": "The official website url of the competitor"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand, range from 0 to 1"
                        }
                    }
                },
                "required": ["company", "details", "url", "intensity"],
                "additionalProperties": False
            },
            "recentNews": {
                "type": "array",
                "description": "The recent and relevant news of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the news"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the news"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the news"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the event in the news must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            },
            "marketCases": {
                "type": "array",
                "description": "The successful market cases of the brand",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {
                            "type": "string",
                            "description": "The title of the market case"
                        },
                        "content": {
                            "type": "string",
                            "description": "The brief introduction of the market case"
                        },
                        "url": {
                            "type": "string",
                            "description": "The url of the market case"
                        },
                        "intensity": {
                            "type": "number",
                            "description": "The influence to the brand"
                        },
                        "time": {
                            "type": "string",
                            "description": "The happening time of the market cases must be in the format of 'yyyy-mm'. If you don't know the time, just return empty sting."
                        }
                    }
                },
                "required": ["title", "content", "url", "intensity", "time"],
                "additionalProperties": False
            }
        },
        "additionalProperties": False,
        "required": ["competitor", "recentNews", "marketCases"]
    }
}


reflection_prompt_template = """You are a professional brand analyst. Here is the brand information and the search results in news, competitor, market case.

<Brand Info>
{brand_info}
<Brand Info>

<News Competitor>
{news_competitor}
<News Competitor>

<Market Case>
{market_case}
<Market Case>

[Task]
Your task is to analyze the search results and provide a detailed report about the brand's competitor, recent news and market case.

[Analyze the information and search results]
1. Analyze the brand information carefully. There are some details about the brand, like companyURL, companyName, industry, news, competitor, market cases.

2. Analyze the news and competitor search results carefully. Filter out the news and competitor that are related to the brand and are not in the brand information.

3. Analyze the market case search results carefully. Filter out the successful market case that are related to the brand and are not in the brand information. A successful market case should demonstrate one or more of the following:
    - Successful product launches or marketing campaigns
    - Market expansion initiatives
    - Strategic partnerships or collaborations
    - Significant sales achievements or market share growth
    - Awards, recognitions, or certifications
    - Innovation milestones or product developments

4. For each search result, determine its relevance to the brand by checking:
    - Exact brand name matches (accounting for variations, subsidiaries, or parent companies)
    - Industry alignment (the content must relate to the brand's actual industry)
    - Contextual relevance (the content must discuss the brand directly, not merely mention it)
    - Source credibility (prioritize information from reputable sources)

[Generate the report]
1. Generate the report about the brand's competitor, recent news and market case in the json format.

2. In the report, you should provide:
    - news: title, brief introduction, url, time (format: yyyy-mm), influence
    - competitor: name, brief introduction, url, influence
    - market case: title, brief introduction, url, time (format: yyyy-mm), influence

3. The report should be in the json format.

[Important notes]
1. All urls should be valid.
2. All time should be in the format of "yyyy-mm".
3. All influence should be a number between 0 and 1.
4. All brief introduction should be a brief introduction of the content, no more than 100 words.
5. If there is a news or market case with no time information or no url, you should skip it.
6. Arrange all market cases and news in chronological order (oldest to newest).
7. Prioritize quality over quantity - it's better to have fewer, highly relevant cases than many loosely connected ones
"""

planner_query_writer_instructions = """You are performing research about its market cases for a brand.

<Brand Info>
{brand_info}
<Brand Info>

<Task>
Your goal is to generate {number_of_queries} web search queries that will help gather information for creating a chronological mapping of the brand's market cases.

The queries should:

1. Be related to the brand.
2. Help search for the market cases of the brand.
3. Structure queries using short essential phrases instead of complete sentences, as phrase-based queries typically yield more precise and relevant search results compared to sentence-based inquiries.
4. The web search tool is google search api. Please generate queries that may be possibly appeared in google search.
5. To enhance search comprehensiveness, you should generate queries of different lengths. Research indicates that queries containing 1 to 6 terms typically yield the most effective results.

Answer in json format: {{"queries": ["query1", "query2", "query3"]}}
"""


reflection_prompt = """You are a professional brand analyst. Here is the brand information and the search results in market case.

<Brand Info>
{brand_info}
<Brand Info>

<Market Case>
{market_case}
<Market Case>

[Task]
Your task is to analyze the search results and provide a detailed report about the brand's market cases.

[Filtering Criteria]
For each search result, determine its relevance to the brand by checking:
    - Exact brand name matches (accounting for variations, subsidiaries, or parent companies)
    - Industry alignment (the content must relate to the brand's actual industry)
    - Contextual relevance (the content must discuss the brand directly, not merely mention it)
    - Source credibility (prioritize information from reputable sources)

[Market Case Identification]
A successful market case should demonstrate one or more of the following:
    - Successful product launches or marketing campaigns
    - Market expansion initiatives
    - Strategic partnerships or collaborations
    - Significant sales achievements or market share growth
    - Awards, recognitions, or certifications
    - Innovation milestones or product developments

[Output Requirements]
Please organize the successful market cases in the following format:

- Brand Success Timeline
[YYYY-MM] Case Title
Brief description of the market case
Key achievements or metrics
Source of information
Relevance to brand strategy (optional)
[YYYY-MM] Case Title
Brief description...
(and so on)

[Important Notes]:
1. Arrange all cases in chronological order (oldest to newest)
2. If exact dates are unavailable, use the closest approximation and indicate this
3. Only include cases that are definitively about the target brand
4. Prioritize quality over quantity - it's better to have fewer, highly relevant cases than many loosely connected ones
5. For each case, provide a concise summary rather than extensive details

Please begin by reviewing the brand information to thoroughly understand the target brand before filtering the search results.
"""