import os
import sys
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

import json
import re
from task import callWattGPT
from task.dao.mongo.gmate.comany_info import CompanyInfo
from task.lib.memory_prompts import company_search_system_prompt, company_search_schema, company_search_user_prompt, company_init_schema, company_init_user_prompt, company_init_system_prompt, search_recent_news_prompt, search_competitor_prompt, search_market_cases_prompt, deep_search_time, market_case_schema, market_case_series


def check_date_format(data_str):
    # 1 -> yyyy, 2 -> yyyy-mm, 3 -> yyyy-mm-dd
    pattern_yyyy_mm = r"^\d{4}-\d{2}$"
    pattern_yyyy = r"^\d{4}$"
    pattern_yyyy_dd = r"^\d{4}-\d{2}-\d{2}$"

    if re.match(pattern_yyyy_mm, data_str):
        return 2
    elif re.match(pattern_yyyy_dd, data_str):
        return 3
    elif re.match(pattern_yyyy, data_str):
        return 1
    else:
        return 1


class CompanyDetails:
    def __init__(self, model: str, pplx_model: str):
        self.company_info = CompanyInfo()
        self.model = model
        self.pplx_model = pplx_model
    
    def process_init_profile(self, company_profile):
        """
        详细解析只被初始化公司的信息
        """

        # 1. 获取公司信息
        company_info = company_profile.get('companyInfo', {})

        profile_info = company_profile.get('profileInfo', {})

        # 2. 获取公司名称
        company_name = company_info.get('company', '')
        company_url = company_info.get('companyURL', '')
        company_industry = company_info.get('industry', '')
        company_business_area = company_info.get('businessArea', '')

        # TODO: 3. 如果有url，看看数据库里是否有解析数据
        url_context = company_profile.get('urlContext', {})
        if url_context:
            company_info["urlCrawlContent"] = url_context

        if len(company_name) == 0:
            return False, {"error": "company name is empty"}
    
        # 4. 获取公司简介
        user_prompt = company_init_user_prompt.format(
            company_info=json.dumps(company_info, indent=4, ensure_ascii=False),
            company_profile=json.dumps(profile_info, indent=4, ensure_ascii=False),
            company_schema=json.dumps(company_init_schema)
        )

        messages = [
            {"role": "system", "content": company_init_system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        body = {
            "model": self.model,
            "messages": messages,
            "response_format": {"type": "json_object"}
        }

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=120)

        if not status:
            return False, {"error": f'failed to generate details of the information - {response}'}
        

        try:
            result = response['result']['data']['choices'][0]['message']['content']
            profile_res = json.loads(result)
        
        except Exception as e:
            return False, {"error": f'details parsed error - {str(e)}'}
        
        brand_value = profile_res.get('brandValue', '')
        brand_vision = profile_res.get('brandVision', '')
        brand_philosophy = profile_res.get('brandPhilosophy', '')

        # founding time的格式必须为yyyy-mm，如果不是就返回空
        founding_time = profile_res.get('foundingTime', '')
        if len(founding_time) > 0:
            if check_date_format(founding_time) != 2:
                profile_res['foundingTime'] = ''


        target_audience = profile_res.get('targetAudience', [])
        if target_audience:
            try:
                target_user = [audience['desc'] for audience in target_audience]
                profile_res['targetUsers'] = ', '.join(target_user)
            except Exception as e:
                print(f"Process target user error - {str(e)} - {target_audience}")
                profile_res['targetUsers'] = ''

        profile_res['company'] = company_name
        profile_res['companyURL'] = company_url
        profile_res['industry'] = company_industry
        profile_res['businessArea'] = company_business_area

        return True, profile_res
    
    def process_search_profile(self, company_profile):
        """
        详细解析已有公司的信息
        """
        original_company = company_profile.get('company', '')
        if len(original_company) <= 0:
            return False, 'company name is empty'

        # 成功案例需要单独形成编年史，所以拎出来单独处理
        if company_profile.get('marketCases'):
            original_market_cases = company_profile.get('marketCases')
            company_profile.pop('marketCases')
        else:
            original_market_cases = []
        
        original_competitor = company_profile.get('competitor', [])
        original_recentNews = company_profile.get('recentNews', [])
        original_keywords = company_profile.get('keywords', [])

        search_status, search_res = self.search_company(company_profile)
        if not search_status:
            return False, search_res
        
        competitors = search_res.get('competitor', {})
        recent_news = search_res.get('recentNews', {})
        market_case = search_res.get('marketCases', {})

        # 1. 获取公司信息
        user_prompt = company_search_user_prompt.format(
            company_info=json.dumps(company_profile, indent=4, ensure_ascii=False),
            search_schema=json.dumps(company_search_schema, indent=4, ensure_ascii=False),
            competitor=json.dumps(competitors, indent=4, ensure_ascii=False),
            recentNews=json.dumps(recent_news, indent=4, ensure_ascii=False),
            marketCases=json.dumps(market_case, indent=4, ensure_ascii=False)
        )

        messages = [
            {"role": "system", "content": company_search_system_prompt},
            {"role": "user", "content": user_prompt}
        ]

        body = {
            "model": self.model,
            "messages": messages,
            "response_format": {"type": "json_object"}
        }

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=120)

        if not status:
            return False, {"error": f'failed to generate details of the information - {response}'}
        
        try:
            result = response['result']['data']['choices'][0]['message']['content']
            profile_res = json.loads(result)
        except Exception as e:
            return False, {"error": f'details parsed error - {str(e)}'}
        
        keywords = profile_res.get('keywords', [])
        competitors = profile_res.get('competitor', [])
        recent_news = profile_res.get('recentNews', [])
        market_case = profile_res.get('marketCases', [])

        # 如果gpt没有得到任何竞品、新闻和成功案例，则不会修改原有的内容
        if len(competitors) <= 0 and len(recent_news) <= 0 and len(market_case) <= 0:
            profile_res['competitor'] = original_competitor
            profile_res['recentNews'] = original_recentNews
            profile_res['marketCases'] = original_market_cases
            return False, profile_res
        
        # 处理结果
        # 处理关键词
        keywords_name = [kw['name'] for kw in keywords if kw.get('name')]
        if len(original_keywords) > 0:
            for okw in original_keywords:
                name = okw.get('name')
                if name and name not in keywords_name:
                    keywords.append(okw)

        top_keywords = sorted(keywords, key=lambda x: x['intensity'], reverse=True)
        subscribe_keywords = [keyword['name'] for keyword in top_keywords][:5]
        profile_res['keywords'] = top_keywords
        profile_res['subscribeKeywords'] = subscribe_keywords

        # 处理竞品
        competitors_company_url = []
        new_competitors = []
        for co in competitors:
            if type(co) == dict and co.get("company") and co.get('url') and co.get('intensity'):
                competitors_company_url.append((co['company'], co['url']))
                new_competitors.append(co)

        if len(original_competitor) > 0:
            for oc in original_competitor:
                if type(oc) == dict:
                    oc_company = oc.get('company')
                    oc_url = oc.get('url')
                    if oc_company and oc_url and (oc_company, oc_url) not in competitors_company_url and oc.get('intensity'):
                        new_competitors.append(oc)
        if len(new_competitors) > 0:
            top_competitors = sorted(new_competitors, key=lambda x: x['intensity'], reverse=True)
        else:
            top_competitors = original_competitor
        
        profile_res['competitor'] = top_competitors

        # 处理新闻
        rn_url = []
        new_recent_new = []
        if len(recent_news) > 0:
            for rn in recent_news:
                if type(rn) == dict:
                    if rn.get('url') and rn.get('time') and check_date_format(rn['time']) in [2, 3]:
                        new_recent_new.append(rn)

        if len(original_recentNews) > 0:
            for orn in original_recentNews:
                url = orn.get('url')
                if url and url not in rn_url:
                    recent_news.append(orn)

        top_recentNews = sorted(new_recent_new, key=lambda x: x['intensity'], reverse=True)

        profile_res['recentNews'] = top_recentNews

        # 处理案例，形成编年史
        deep_status, deep_repsonse = self.deep_search_time(market_case, original_company)
        if not deep_status:
            profile_res['marketCases'] = original_market_cases
        
        if original_market_cases:
            deep_repsonse.extend(original_market_cases)

        market_case_prompt = market_case_series.format(
            brand=original_company,
            market_cases=json.dumps(deep_repsonse, indent=4, ensure_ascii=False),
            schema=json.dumps(market_case_schema, indent=4, ensure_ascii=False)
        )

        market_messages = [
            {"role": "user", "content": market_case_prompt}
        ]

        market_body = {
            "model": self.model,
            "messages": market_messages,
            "response_format": {"type": "json_object"}
        }

        mr_status, code, mr_response = callWattGPT.callOpenaiChannelChatCompletions(body=market_body, timeout=120)
        if not mr_status:
            profile_res['marketCases'] = original_market_cases
            print(f'Summarize the timeline error - {mr_response}')
        
        try:
            mr_result = mr_response['result']['data']['choices'][0]['message']['content']
            mr_res = json.loads(mr_result)['marketCases']
        except Exception as e:
            print(f'Parsed market response error: {str(e)}')
            profile_res['marketCases'] = original_market_cases
            return False, profile_res
        
        new_market_case = []
        if type(mr_res) != list:
            profile_res['marketCases'] = original_market_cases
            print(f'Market reponses result is not a list - {mr_res}')
            return False, profile_res
        
        for mr in mr_res:
            if type(mr) != dict:
                continue
            if mr.get('time') and check_date_format(mr['time']) == 2:
                new_market_case.append(mr)

        profile_res['marketCases'] = new_market_case
        if len(profile_res['marketCases']) <= 0:
            return False, profile_res
        return True, profile_res


    def search_company(self, company_info):
        company = company_info.get('company', '')
        if len(company) <= 0:
            return False, 'company name is empty'
        
        market_cases_prompt = search_market_cases_prompt.format(
            company_info=json.dumps(company_info, indent=4, ensure_ascii=False)
        )        

        recent_news_prompt = search_recent_news_prompt.format(
            company_info=json.dumps(company_info, indent=4, ensure_ascii=False)
        )

        competitor_prompt = search_competitor_prompt.format(
            company_info=json.dumps(company_info, indent=4, ensure_ascii=False)
        )

        body_list = []

        for prompt in [market_cases_prompt, recent_news_prompt, competitor_prompt]:
            messages = [
                {"role": "user", "content": prompt}
            ]

            body = {
                "model": self.pplx_model,
                "messages": messages,
            }

            body_list.append(body)
        
        status, code, response = callWattGPT.gcallPplxChannelChatCompletions(body_list, timeout=120)

        if not status:
            return False, f'failed to generate details of the information - {response}'
        
        final_res = {}
        try:
            for i, key in enumerate(["marketCases", "recentNews", "competitor"]):
                status, code, result = response[i]
                if status:
                    search_res = result['result']['data']['choices'][0]['message']['content']
                    search_citations = result['result']['data']['citations']
                    final_res[key] = {"result": search_res, "citations": search_citations}
        except Exception as e:
            return False, f'details parsed error - {str(e)}'
        
        return True, final_res

    def deep_search_time(self, information_list, company):
        ## search the accurate date for the specific event
        body_list = []
        for information in information_list:
            user_prompt = deep_search_time.format(company=company, event_info=json.dumps(information, indent=4, ensure_ascii=False))

            messages = [
                {"role": "user", "content": user_prompt}
            ]
            body = {
                "model": self.pplx_model,
                "messages": messages,
            }
            body_list.append(body)
        status, code, response = callWattGPT.gcallPplxChannelChatCompletions(body_list, timeout=120)

        if not status:
            return False, f'failed to search details of the information - {response}'
        
        final_res = []
        
        for i, information in enumerate(information_list):
            try:
                status, code, result = response[i]
                if status:
                    search_res = result['result']['data']['choices'][0]['message']['content']
                    search_citations = result['result']['data']['citations']
                    information['deep_research_result'] = search_res
                    information['deep_research_citations'] = search_citations
                    final_res.append(information)
                else:
                    final_res.append(information)
            except Exception as e:
                print(f'details parsed error - {str(e)}')
                final_res.append(information)
        return True, final_res


if __name__ == "__main__":
    company_info = {
        "company": "Tanka",
        "industry": "Technology",
        "businessArea": "",
        "company_url": "https://www.tanka.com"
    }

    company_profile = {
        "companyInfo": company_info,
        "profileInfo": {
            "introduction": "Tanka is a technology company specializing in messaging and communication tools. The company focuses on developing seamless, efficient, and user-friendly communication solutions that enhance connectivity for businesses and individuals. With an emphasis on innovation, Tanka aims to redefine digital interactions through secure, reliable, and intuitive messaging platforms. By leveraging advanced technologies, the company enhances real-time collaboration and streamlines digital conversations. Tanka is dedicated to providing top-tier communication services that improve productivity and engagement in modern digital environments.",
            "subscribeKeywords": [
                "Communication Tools",
                "Messaging Solutions",
                "Secure Messaging",
                "Data Security",
                "Real-time Chat"
                ],
            "keywords": [
                {
                    "name": "Communication Tools",
                    "intensity": 1
                },
                {
                    "name": "Messaging Solutions",
                    "intensity": 0.9
                },
                {
                    "name": "Secure Messaging",
                    "intensity": 0.9
                },
                {
                    "name": "Data Security",
                    "intensity": 0.9
                },
                {
                    "name": "Real-time Chat",
                    "intensity": 0.85
                },
                {
                    "name": "Innovation",
                    "intensity": 0.85
                }
            ]

        }
    }


    company_details = CompanyDetails(model='gpt-4o', pplx_model='sonar-pro')
    status, result = company_details.process_init_profile(company_profile)
    print(status, result)
    if status:
        status, result = company_details.process_search_profile(result)
        print(status, result)
            