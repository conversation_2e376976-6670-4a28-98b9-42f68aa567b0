import os
import re

# from flask import current_app
from task import TaskLog, callWattGPT


class ChatService:
    def __init__(self, request_id='', logger=TaskLog):                     
        self.logger = logger
        self.request_id = request_id
        self.function_name_mapper = {}


    def log(self, string, type='info', local_only=False):
        if self.request_id:
            string = f"request ID: {self.request_id}\t{string}"

        if self.logger is None:
            print(string)
        elif not local_only or 'Users' in os.getcwd():
            if type == 'info':
                self.logger.info(string)
            elif type == 'error':
                self.logger.error(string)
            else:
                self.logger.warning(string)   

    def check_messages(self, msg):
        if isinstance(msg, list):
            return msg
        elif isinstance(msg, str):
            return [
            {'role': 'user',
             'content': msg,
             }
        ]
    
    def process_chat_response(self, result:tuple)-> (bool, str):
        con, code, response = result
        if not con:
            err = f'OpenAI error: internal error code {code}, error msg <{response}>'
            self.log(err, 'error')
            return con, err
        try:
            msg = response['result']['data']['choices'][0]['message']['content']
            if not msg:
                msg = ''
        except Exception as e:
            self.log(f'Fail to get message from OPENAI response: {e}', 'error')
            return False, e
        return con, msg


    def chat(self,
        messages: str or list,
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.6,
        seed: int = 0,
        timeout=200,
        request_id ='',
        json_mode=False,
        watttraceid='') -> (bool, str):
        
        if request_id: self.request_id = request_id
        
        body = {
            'messages': self.check_messages(messages),
            'model': model,
            'temperature': temperature,
            'seed': seed,
        }
        if json_mode:
            body['response_format'] = {'type': 'json_object'}
        result = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=timeout, watttraceid=watttraceid
                                                              if watttraceid else self.request_id)
        
        return self.process_chat_response(result)

    def chat_paralell(self,
        messages_list: list[list],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0,
        top_p: float = 0.1,                                           
        seed = 0,        
        timeout=20,
        request_id ='') -> (bool, list):
        
        if request_id: self.request_id = request_id
        body = [{
            'messages': self.check_messages(message),
            'model': model,
            'temperature': temperature,
            'top_p': top_p,
            'seed': seed,
        } for message in messages_list]
        
        con, code, result = callWattGPT.gCallOpenaiChannelChatCompletions(body, timeout, watttraceid=self.request_id)

        if not con:
            err = f'OpenAI error: internal error code {code}, error msg <{result}>'
            self.log(err, 'error')
            return con, []
        
        res_list = []
        for r in result:
            res_list.append(self.process_chat_response(r))
        return True, res_list


    def process_function_call_response(self, result:tuple)-> (bool, str, dict):
        con, code, response = result
        if not con:
            err = f'OpenAI error: internal error code {code}, error msg <{response}>'
            self.log(err, 'error')
        
        try:
            message = response['result']['data']['choices'][0]['message']
            msg = message['content']
            if not msg: #null
                msg = ''
            
            functions = {
                self.function_name_mapper.get(tool['function']['name'], tool['function']['name'])
                :tool['function']['arguments'] 
                    for tool in message.get('tool_calls', [])}    

        except Exception as e:
            self.log(f'Fail to parse results from OPENAI response: {e}', 'error')
            msg = ''
            functions = {}


        return con, msg, functions
    

    def function_call(self,
        messages: str or list,
        functions: list,        
        model: str = "gpt-3.5-turbo", 
        tool_choice: str = 'auto',
        temperature: float = 0,
        top_p: float = 0.1,                                           
        seed = 0,      
        timeout=10,
        request_id ='') -> (bool, str, dict):
        
        if request_id: self.request_id = request_id
        tools = [{
            'type': 'function',
            'function': f
        } for f in functions]

        body = {
            'messages': self.check_messages(messages),
            'tools': tools,
            'tool_choice': tool_choice,
            'model': model,
            'temperature': temperature,
            'top_p': top_p,
            'seed': seed,
        }
        result = callWattGPT.callOpenaiChannelChatCompletionsFunctions(body, timeout, watttraceid=self.request_id)

        return self.process_function_call_response(result)


    def get_tools(self, functions):
        '''correct function call name based on: ^[a-zA-Z0-9_-]{1,64}$, then return functions as tools format    
        '''
        tools = []
        p = '[^a-zA-Z0-9_-]'
        for f in functions:
            new_name = re.sub(p, '_', f['name'])            
            if len(new_name) > 64:
                new_name = new_name[:64].rsplit('_', maxsplit=1)[0]
            self.function_name_mapper.update({new_name: f['name']})
            f['name'] = new_name
            tools.append({
                'type': 'function',
                'function': f
            })
        return tools



    def function_call_parallel(self,
        messages_list: list[list],
        functions_list: list[list],        
        model: str = "gpt-3.5-turbo", 
        tool_choice: str = 'auto',
        temperature: float = 0,
        top_p: float = 0.1,                                           
        seed = 0,      
        timeout=10,
        request_id ='') -> (bool, list):
        
        if request_id: self.request_id = request_id
        assert len(messages_list) == len(functions_list)
        # get_tools = lambda functions: [{
        #     'type': 'function',
        #     'function': f
        # } for f in functions]

        body = [{
            'messages': self.check_messages(messages_list[i]),
            'tools': self.get_tools(functions_list[i]),
            'tool_choice': tool_choice,
            'model': model,
            'temperature': temperature,
            'top_p': top_p,
            'seed': seed,
        } for i in range(len(messages_list))]

        con, code, result = callWattGPT.gCallOpenaiChannelChatCompletionsFunctions(body, timeout, watttraceid=self.request_id)
        if not con:
            err = f'OpenAI error: internal error code {code}, error msg <{result}>'
            self.log(err, 'error')
            return con, []
        
        res_list = []
        for r in result:
            res_list.append(self.process_function_call_response(r))
        return True, res_list




if __name__ == '__main__':
    service = ChatService()
    res = service.chat_paralell(['hi, how are you', 'write a haiku about fuji mountain'])
    # service.chat('write a haku about bamboo')
    print(res)

