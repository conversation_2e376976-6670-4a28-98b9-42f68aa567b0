# -*- coding: utf-8 -*-
"""
彩色日志工具模块
提供统一的彩色日志格式和函数，供各个任务脚本使用
"""

import time
from typing import Optional


# ANSI 颜色代码
class Colors:
    """ANSI颜色代码类"""
    RESET = '\033[0m'
    BOLD = '\033[1m'
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BG_RED = '\033[101m'
    BG_GREEN = '\033[102m'
    BG_YELLOW = '\033[103m'
    BG_BLUE = '\033[104m'
    BG_MAGENTA = '\033[105m'
    BG_CYAN = '\033[106m'


def log_task_start(logger, task_type: str, task_id: str, social_media_report_id: Optional[str] = None, extra_info: Optional[dict] = None):
    """
    记录任务开始的醒目日志
    Args:
        logger: 日志记录器
        task_type: 任务类型 (strategy, diagnosis, cover, review等)
        task_id: 任务ID
        social_media_report_id: 社交媒体报告ID (可选)
        extra_info: 额外信息字典 (可选)
    """
    task_icons = {
        'strategy': '🚀',
        'diagnosis': '🔍', 
        'cover': '🎨',
        'review': '📝',
        'default': '⚡'
    }
    
    task_names = {
        'strategy': '策略任务',
        'diagnosis': '诊断任务',
        'cover': '封面生成任务',
        'review': '评估任务',
        'default': '任务'
    }
    
    icon = task_icons.get(task_type, task_icons['default'])
    name = task_names.get(task_type, task_names['default'])
    
    separator = "=" * 60
    logger.info(f"{Colors.CYAN}{Colors.BOLD}{separator}{Colors.RESET}")
    logger.info(f"{Colors.GREEN}{Colors.BOLD}{icon} {name}开始处理{Colors.RESET}")
    logger.info(f"{Colors.YELLOW}{Colors.BOLD}📋 任务ID: {Colors.CYAN}{task_id}{Colors.RESET}")
    
    if social_media_report_id:
        logger.info(f"{Colors.YELLOW}{Colors.BOLD}🎯 报告ID: {Colors.MAGENTA}{social_media_report_id}{Colors.RESET}")
    else:
        logger.info(f"{Colors.YELLOW}{Colors.BOLD}🎯 报告ID: {Colors.RED}未提供{Colors.RESET}")
    
    # 记录额外信息
    if extra_info:
        for key, value in extra_info.items():
            if value:
                logger.info(f"{Colors.YELLOW}{Colors.BOLD}📌 {key}: {Colors.WHITE}{value}{Colors.RESET}")
    
    logger.info(f"{Colors.CYAN}{Colors.BOLD}{separator}{Colors.RESET}")


def log_task_complete(logger, task_type: str, task_id: str, status: str, duration: Optional[float] = None, extra_info: Optional[dict] = None):
    """
    记录任务完成的醒目日志
    Args:
        logger: 日志记录器
        task_type: 任务类型
        task_id: 任务ID
        status: 任务状态 (success/failed)
        duration: 执行耗时 (可选)
        extra_info: 额外信息字典 (可选)
    """
    task_names = {
        'strategy': '策略任务',
        'diagnosis': '诊断任务',
        'cover': '封面生成任务',
        'review': '评估任务',
        'default': '任务'
    }
    
    name = task_names.get(task_type, task_names['default'])
    separator = "=" * 60
    status_color = Colors.GREEN if status == "success" else Colors.RED
    status_icon = "✅" if status == "success" else "❌"
    
    logger.info(f"{Colors.BLUE}{Colors.BOLD}{separator}{Colors.RESET}")
    logger.info(f"{status_color}{Colors.BOLD}{status_icon} {name}处理完成{Colors.RESET}")
    logger.info(f"{Colors.YELLOW}{Colors.BOLD}📋 任务ID: {Colors.CYAN}{task_id}{Colors.RESET}")
    logger.info(f"{Colors.YELLOW}{Colors.BOLD}📊 状态: {status_color}{status.upper()}{Colors.RESET}")
    
    if duration:
        logger.info(f"{Colors.YELLOW}{Colors.BOLD}⏱️  耗时: {Colors.WHITE}{duration:.2f}秒{Colors.RESET}")
    
    # 记录额外信息
    if extra_info:
        for key, value in extra_info.items():
            if value:
                logger.info(f"{Colors.YELLOW}{Colors.BOLD}📌 {key}: {Colors.WHITE}{value}{Colors.RESET}")
    
    logger.info(f"{Colors.BLUE}{Colors.BOLD}{separator}{Colors.RESET}")


def log_progress(logger, message: str, progress_type: str = "info"):
    """
    记录进度信息的彩色日志
    Args:
        logger: 日志记录器
        message: 进度消息
        progress_type: 进度类型 (info, warning, error, success)
    """
    icons = {
        'info': '🔄',
        'warning': '⚠️',
        'error': '❌',
        'success': '✅',
        'default': 'ℹ️'
    }
    
    colors = {
        'info': Colors.BLUE,
        'warning': Colors.YELLOW,
        'error': Colors.RED,
        'success': Colors.GREEN,
        'default': Colors.WHITE
    }
    
    icon = icons.get(progress_type, icons['default'])
    color = colors.get(progress_type, colors['default'])
    
    logger.info(f"{color}{Colors.BOLD}{icon} {message}{Colors.RESET}")


def log_ai_call(logger, model_name: str, task_name: str, start_time: Optional[float] = None, status: Optional[bool] = None):
    """
    记录AI调用的专用日志
    Args:
        logger: 日志记录器
        model_name: 模型名称
        task_name: 任务名称
        start_time: 开始时间 (可选)
        status: 调用状态 (可选)
    """
    if start_time is None:
        # 开始调用
        logger.info(f"{Colors.CYAN}{Colors.BOLD}🤖 开始AI调用: {Colors.YELLOW}{task_name}{Colors.CYAN} | 模型: {Colors.MAGENTA}{model_name}{Colors.RESET}")
    else:
        # 调用完成
        duration = time.time() - start_time
        if status:
            logger.info(f"{Colors.GREEN}{Colors.BOLD}✅ AI调用成功: {Colors.YELLOW}{task_name}{Colors.GREEN} | 耗时: {Colors.WHITE}{duration:.2f}秒{Colors.RESET}")
        else:
            logger.info(f"{Colors.RED}{Colors.BOLD}❌ AI调用失败: {Colors.YELLOW}{task_name}{Colors.RED} | 耗时: {Colors.WHITE}{duration:.2f}秒{Colors.RESET}")


def log_redis_operation(logger, operation: str, queue_name: str, status: bool = True, extra_msg: str = ""):
    """
    记录Redis操作的专用日志
    Args:
        logger: 日志记录器
        operation: 操作类型 (write, read, etc.)
        queue_name: 队列名称
        status: 操作状态
        extra_msg: 额外消息
    """
    if status:
        logger.info(f"{Colors.GREEN}{Colors.BOLD}📤 Redis{operation}成功: {Colors.CYAN}{queue_name}{Colors.WHITE} {extra_msg}{Colors.RESET}")
    else:
        logger.error(f"{Colors.RED}{Colors.BOLD}📤 Redis{operation}失败: {Colors.CYAN}{queue_name}{Colors.WHITE} {extra_msg}{Colors.RESET}")


def log_separator(logger, message: str = "", color: str = "CYAN"):
    """
    记录分割线日志
    Args:
        logger: 日志记录器
        message: 分割线中的消息 (可选)
        color: 颜色名称
    """
    color_code = getattr(Colors, color, Colors.CYAN)
    if message:
        separator = f"{'=' * 20} {message} {'=' * 20}"
    else:
        separator = "=" * 60
    
    logger.info(f"{color_code}{Colors.BOLD}{separator}{Colors.RESET}")


def log_json_data(logger, data: dict, task_type: str, data_type: str = "return"):
    """
    记录 JSON 数据内容的彩色日志
    Args:
        logger: 日志记录器
        data: 要记录的数据
        task_type: 任务类型 (strategy, diagnosis, etc.)
        data_type: 数据类型 (return, error)
    """
    import json
    
    # 根据数据类型选择颜色
    if data_type == "error":
        color = Colors.YELLOW
        label = "错误数据内容"
    else:
        color = Colors.CYAN
        label = "返回数据内容"
    
    formatted_json = json.dumps(data, ensure_ascii=False, indent=2)
    logger.info(f"{color}{task_type}任务{label}: {formatted_json}{Colors.RESET}")


def log_redis_json(logger, json_str: str, task_type: str, data_type: str = "return"):
    """
    记录 Redis 序列化 JSON 的彩色日志
    Args:
        logger: 日志记录器
        json_str: JSON 字符串
        task_type: 任务类型 (strategy, diagnosis, etc.)
        data_type: 数据类型 (return, error)
    """
    # 根据数据类型选择颜色
    if data_type == "error":
        color = Colors.RED
        label = "错误Redis序列化内容"
    else:
        color = Colors.MAGENTA
        label = "Redis序列化内容"
    
    logger.info(f"{color}{task_type}任务{label}: {json_str}{Colors.RESET}") 