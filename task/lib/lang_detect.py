import re

target_langs = ['en', 'zh-cn', 'zh-tw']


def lang_detect(text):
    # 使用正则表达式匹配中文字符
    simplified_chinese = re.compile(r'[\u4e00-\u9fff]')
    traditional_chinese = re.compile(r'[\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002b73f\U0002b740-\U0002b81f\U0002b820-\U0002ceaf\U0002ceb0-\U0002ebef]')
    english = re.compile(r'[a-zA-Z]')
    # 计算每种语言的字符数量
    simplified_count = len(simplified_chinese.findall(text))
    traditional_count = len(traditional_chinese.findall(text))
    english_count = len(english.findall(text))
    # 找出数量最多的语言
    max_count = max(simplified_count, traditional_count, english_count)
    if max_count == simplified_count:
        return 'zh-cn'
    elif max_count == traditional_count:
        return 'zh-tw'
    else:
        return 'en'


def main(text):
    import time
    start_time = time.time()  # 开始计时
    print(lang_detect(text))
    end_time = time.time()  # 结束计时
    time_elapsed = (end_time - start_time) * 1000  # 毫秒
    print(f"Time taken: {time_elapsed} ms")


if __name__ == "__main__":
    main("关于openai的最近动荡有哪些新闻呢")
