from task.dao import MongoDB as BaseMongoDB, bson2json
from tqdm import tqdm
from datetime import datetime, timedelta
from bson.objectid import ObjectId


class MongoDB(BaseMongoDB):
    """目标数据库 Mongodb
    """

    def __init__(self, collection="tweet_topics", database=None):
        super().__init__()
        self.collection_name = collection
        current_db = self.client[database] if database else self.db
        self.collection_conn = current_db[self.collection_name]

    def create_index(self, index, background=True):
        return self.collection_conn.create_index(index, background=background)
    
    def _conn(self):
        return self.dao[self.DATABASE][self.collection]    

    def countHisETLData(self):
        """
        """
        return self.collection_conn.count_documents({})

    def get_all_data(self, limit=0):
        cursor = self.collection_conn.find(limit=limit)
        return bson2json(list(cursor))

    def get_all_fields(self, query, projection, batch_size=1000):
        try:
            cursor = self.collection_conn.find(query, projection).limit(batch_size)
            result_list = []
            while True:
                batch = list(cursor)
                if not batch:
                    break
                result_list.extend(bson2json(batch))
                if len(batch) < batch_size:
                    break
            return result_list
        except Exception as e:
            print(f"Error in get_all_fields: {e}")
            return []

    def get_first_n_data(self, limit=0):
        return self.collection_conn.find(limit=limit)

    def get_last_n_data(self, limit=0, sort_by=''):
        if not sort_by:
            sort_by = '_id'

        if limit <= 0:
            return self.collection_conn.find().sort(sort_by, -1)
        else:
            return self.collection_conn.find().sort(sort_by, -1)[:limit]

    def singleSearch(self, key, value):
        # search one query by key
        cursor = self.collection_conn.find_one({key: value})
        try:
            return bson2json(cursor)
        except Exception:
            return False

    def find_data(self, query={}, id=None, limit=0):
        # default later inserts first
        if id is not None:
            query = {"_id": id}
        if limit:
            return self.collection_conn.find(query, limit=limit).sort('_id', -1)
        return self.collection_conn.find(query).sort('_id', -1)
    
    def find_data(self, query={}, id=None, limit=0, last_n_hours=0):
        #default later inserts first
        if id is not None:
            query = {"_id": id}
        elif last_n_hours and '_id' not in query:
            current_time = datetime.utcnow()
            start_time = current_time - timedelta(hours=last_n_hours)
            start_object_id = ObjectId.from_datetime(start_time)
            query.update({"_id": {"$gt": start_object_id}})                        

        if limit:
            res = self.collection_conn.find(query, limit=limit).sort('_id', -1)  
        else:
            res = self.collection_conn.find(query).sort('_id', -1)
        return list(res)

    def find_data_condition(self, query={}, id=None, limit=0, reversed=True, projection=None, batch_size=1000):
        # default later inserts first
        if id is not None:
            query = {"_id": id}
        sort_order = -1 if reversed else 1
        try:
            # 分页查询
            cursor = self.collection_conn.find(query, projection=projection).sort('_id', sort_order).limit(batch_size)
            result_list = []
            while True:
                batch = list(cursor)
                if not batch:
                    break
                result_list.extend(bson2json(batch))
                if len(batch) < batch_size:
                    break
            return result_list
        except Exception as e:
            print(f"Error in find_data_condition: {e}")
            return False

    def purge_collection(self):
        # get all data ids
        try:
            data = list(self.get_first_n_data())
            print(f'Deleting all of {len(data)} from collection: {self.collection_name}')
            for d in tqdm(data):
                self.collection_conn.delete_one({"_id": d['_id']})
            return True
        except Exception as e:
            print('Error', e)
            return False

    def get_formated_data(self, limit=0):
        res = self.get_last_n_data(limit)
        if res:
            df = pd.DataFrame(list(res)).rename(columns={'_id': 'candidate_id'})
            cols = ['candidate_id', 'tags', 'summary']
            return df[cols]
        return

    def insert_one(self, data, overwrite=False):
        try:
            if overwrite:
                return self.collection_conn.replace_one({"_id": data['_id']}, data, upsert=True).acknowledged
            return self.collection_conn.insert_one(data).acknowledged
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def insert(self, data):
        try:
            if not isinstance(data, list):
                data = [data]
            return self.collection_conn.insert_many(data).acknowledged
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def insert_many(self, data):
        try:
            if not isinstance(data, list):
                data = [data]
            return self.collection_conn.insert_many(data, ordered=False)
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def update_one(self, data):
        try:
            update_operation = {'$set': data}
            return self.collection_conn.update_one({"_id": data['_id']}, update_operation, upsert=True).acknowledged
        except Exception as e:
            print('Error updating data: ', e)
            return False

    def update_many(self, query, data, upsert=False):
        try:
            update_operation = {'$set': data}
            return self.collection_conn.update_many(query, update_operation, upsert=upsert).acknowledged
        except Exception as e:
            print('Error updating data: ', e)
            return False

    def update_field(self, field, field_value, data):
        try:
            update_operation = {'$set': data}
            return self.collection_conn.update_one({field: field_value}, update_operation, upsert=True).acknowledged
        except Exception as e:
            print('Error updating data: ', e)
            return False

    def bulk_insert(self, data, ordered=False):
        try:
            if not isinstance(data, list):
                data = [data]
            return self.collection_conn.bulk_write(data, ordered=ordered)
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def delete_data(self, id):
        try:
            return self.collection_conn.delete_one({"_id": id}).acknowledged
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def aggregate(self, pipeline: list):
        try:
            return list(self.collection_conn.aggregate(pipeline))
        except Exception as e:
            print('Error aggregating data: ', e)
            return False


if __name__ == '__main__':
    db = MongoDB('keyword')

    res = db.get_first_n_data()
    for r in res:
        new = {
            '_id': r['_id'],
            'ai_topic_status': False
        }
        status = db.update_one(new)
        if not status:
            print('Fail to update')
            print(new)
