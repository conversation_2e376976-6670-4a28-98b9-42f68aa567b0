from task.dao import MongoDB as BaseMongoDB, bson2json
import json


class MongoDBWriter(BaseMongoDB):
    """
    MongoDB Operation for creative writing
    """

    def __init__(self, collection: str = "creative_writing", database: str = None):
        super().__init__()
        self.collection_name = collection
        current_db = self.client[database] if database else self.db
        self.collection_conn = current_db[self.collection_name]

    def singleSearch(self, key, value):
        # search one query by key
        result = self.collection_conn.find_one({key: value})
        try:
            return True, bson2json(result)
        except Exception as e:
            return False, e

    def dict_single_Search(self, criteria):
        # search one query by dict
        result = self.collection_conn.find_one(criteria)
        try:
            return True, bson2json(result)
        except Exception as e:
            return False, e

    def batchSearch(self, key, value, limit=0):

        cursor = self.collection_conn.find({key: value}, limit=limit)
        try:
            result_list = list(cursor)  # 将Cursor对象转换为列表
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def duoBatchSearch(self, key1, value1, key2, value2, limit=0):
        query = {"$and": [{key1: {"$in": value1}}, {key2: {"$in": value2}}]}
        try:
            cursor = self.collection_conn.find(query, limit=limit)
            result_list = list(cursor)  # 将Cursor对象转换为列表
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def findData(self, limit=0):
        cursor = self.collection_conn.find(limit=limit)
        try:
            result_list = list(cursor)
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def findDataCondition(self, query, limit=0, last_scan_time=None, current_time=None, reverse=False):
        if last_scan_time and current_time:
            query = {
                "$and": [
                    query,
                    {"upload_time": {"$gte": last_scan_time, "$lte": current_time}}
                ]
            }
        if reverse:
            cursor = self.collection_conn.find(query, limit=limit).sort('_id', 1)
        else:
            cursor = self.collection_conn.find(query, limit=limit).sort('_id', -1)
        try:
            result_list = list(cursor)
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def findDataFilter(self, field_names, limit=0, last_scan_time=None, current_time=None):
        field_existence_query = {field_name: {'$exists': True, '$ne': None} for field_name in field_names}
        if last_scan_time and current_time:
            query = {
                "$and": [
                    field_existence_query,
                    {"upload_time": {"$gte": last_scan_time, "$lte": current_time}}
                ]
            }
        else:
            query = field_existence_query
        cursor = self.collection_conn.find(query, limit=limit)
        try:
            result_list = list(cursor)
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def countData(self):
        """
        count total number
        """
        try:
            return True, self.collection_conn.count_documents({})
        except Exception as e:
            return False, e

    def get_first_n_data(self, limit=0):
        return self.collection_conn().find(limit=limit)  # .sort("_id").limit(limit)

    def get_last_n_data(self, limit=0, sort_by=''):
        if not sort_by:
            sort_by = '_id'

        if limit <= 0:
            return self.collection_conn().find().sort(sort_by, -1)
        else:
            return self.collection_conn().find().sort(sort_by, -1)[:limit]

    def insertSingleData(self, data):
        try:
            return True, self.collection_conn.insert_one(data)
        except Exception as e:
            return False, e

    def insertBatchData(self, data):
        try:
            return True, self.collection_conn.insert_many(data)
        except Exception as e:
            return False, e

    def replaceSingleData(self, data):
        try:
            return True, self.collection_conn.replace_one(data[0], data[1], True)
        except Exception as e:
            return False, e

    def updateSingleData(self, data):
        try:
            return True, self.collection_conn.update_one(data[0], data[1], upsert=True)
        except Exception as e:
            return False, e

    def update_one(self, data):
        try:
            update_operation = {'$set': data}
            return True, self.collection_conn.update_one({"_id": data['_id']}, update_operation, upsert=True).acknowledged
        except Exception as e:
            print('Error updating data: ', e)
            return False, e

    def getSortedLastNData(self, limit, sort_key):
        try:
            if limit <= 0:
                return True, self.collection_conn.find().sort(sort_key, -1)
            else:
                return True, self.collection_conn.find().sort(sort_key, -1)[:limit]
        except Exception as e:
            return False, e

    def deleteData(self, key, value):
        try:
            return True, self.collection_conn.delete_one({key: value})
        except Exception as e:
            return False, str(e)

    def batchDeleteData(self, key, value, condition="$in"):

        """
        Args:
            condition: str, default = "$in", batch delete in; "$eq" -> equal; "exists" -> exists, value=true/false
        """

        try:
            return True, self.collection_conn.delete_many({key: {condition: value}})
        except Exception as e:
            return False, e

