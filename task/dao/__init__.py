import os
import sys
import json
import datetime
import decimal
from bson.objectid import ObjectId

# add task PYTHONPATH to import task
sys.path.append('/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-2]))

from task import task_mongo_client, task_mongoDatabase


class MongoJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime.datetime,)):
            return str(obj)
        if isinstance(obj, (datetime.date,)):
            return str(obj)
        elif isinstance(obj, (decimal.Decimal,)):
            return float(obj)
        elif isinstance(obj, (ObjectId,)):
            return str(obj)
        else:
            return obj


def bson2json(data):
    return json.loads(json.dumps(
        data, cls=MongoJSONEncoder, ensure_ascii=False))


class MongoDB:
    def __init__(self):      
        self.client = task_mongo_client
        self.db = self.client[task_mongoDatabase]


if __name__ == "__main__":
    pass
