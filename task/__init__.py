# -*- coding: utf-8 -*-
import gevent.monkey
gevent.monkey.patch_all()
import os
import sys
from task.lib.prompt_fusion import PromptsFusion
from config import *
from utils.init_log import InitLog
from utils.watt_service.call_watt_gpt import Call<PERSON>attGPT
from utils.mongo_conn import MongodbConn


if LOG_LEVEL == "DEBUG":
    print("APP_NAME:" + APP_NAME)
    print("LOG_LEVEL: {}".format(LOG_LEVEL))
    print("FLASK_LOG_PATH: {}".format(FLASK_LOG_PATH))

###############################################
# log
###############################################
print("======== 初始化日志 ========")
if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

TaskLog = InitLog(
    'TaskLog', 
    FLASK_LOG_PATH, 
    level=LOG_LEVEL, 
    apm=False
    ).init()

print("TaskLog 初始化完成: {}\n".format(TaskLog))


###############################################
print("======== 初始化 Watt GPT for Task ========")
try:
    callWattGPT = CallWattGPT(WATT_AI_GPT_HOST, WATT_AI_GPT_TOKEN)
    print("初始化CallGPT完成\n")
except Exception as e:
    print("初始化CallGPT失败 - {}: {}\n".format(e.__class__, e))
    TaskLog.error("初始化CallGPT失败 - {}: {}".format(e.__class__, e))
    sys.exit(201)


###############################################
# mongodb
###############################################
print("======== 初始化 Mongodb for Task ========")
try:
    _task_mongo_conn_creator = MongodbConn(MONGODB_CONFIG)
    task_mongo_client = _task_mongo_conn_creator.conn()
    task_mongoDatabase = MONGODB_DATABASE
    task_mongoConn = _task_mongo_conn_creator
    print("初始化 Task Mongodb Client 完成\n")
except Exception as e:
    print("初始化 Task Mongodb Client 失败 - {}: {}\n".format(e.__class__, e))
    TaskLog.error("初始化 Task Mongodb Client 失败 - {}: {}".format(e.__class__, e))
    sys.exit(201)

# prompts_fusion = PromptsFusion()
