import os
import sys
import argparse

pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey

monkey.patch_all()
from bson import ObjectId
from task import TaskLog
from task.lib.text_process import batch_list
from task.dao.mongo.mongdb_writer import MongoDBWriter
from task.server.create.generate_tweets import retrieve_tweets_details, gen_tweets_db
from colorama import Fore


tweet_db = MongoDBWriter(collection="tweet")


def clear_db():
    """
    Clear 'creative_writing' collection
    """
    # 1. First retrieve all existing tweets in 'tweet' collection that has expired.
    batch_size = 1000
    last_id = None

    while True:
        # query = {"upload_time": {"$lt": "2024-08-01T08:13:05Z"}}
        if last_id:
            query = {
                "$and": [
                    {"_id": {"$gt": ObjectId(last_id)}},  # 使用 ObjectId
                    {"$or": [
                        {"upload_time": {"$lt": "2024-08-01T09:13:05Z"}},
                        {"upload_time": {"$exists": False}}
                    ]}
                ]
            }
        else:
            query = {
                "$or": [
                    {"upload_time": {"$lt": "2024-08-01T09:13:05Z"}},
                    {"upload_time": {"$exists": False}}
                ]
            }

        status, result = tweet_db.findDataCondition(query, limit=batch_size, reverse=False)
        if not status:
            TaskLog.error(f"Error searching mongodb: {result}")
            return False

        if len(result) == 0:
            return True

        if not last_id:
            print(Fore.YELLOW + f"Total number of records: {len(result)}" + Fore.RESET)

        count = 0
        tweet_id_list = []
        for tweet in result:
            tweet_id_list.append(tweet.get("id_str", ""))
            if (count + 1) % 500 == 0:
                print(tweet.get("id_str", ""))
            last_id = tweet.get("_id", "")
            count += 1

        # 2. Delete all tweets in 'creative_writing' collection that has expired.
        status, result = gen_tweets_db.batchDeleteData("tweetId", tweet_id_list)
        if not status:
            TaskLog.error(f"Error deleting mongodb: {result}")
            return False
        print(Fore.YELLOW + f"Deleted {result.deleted_count} records" + Fore.RESET)

        if count < batch_size:
            break

    return True


def update_media_info():
    """
    Update media information for 'creative_writing' collection
    """
    # 1. Retrieve all existing (xId, tweet) combinations.
    status, existing_tweets = gen_tweets_db.findData(limit=0)
    if not status:
        TaskLog.error("Error: ", existing_tweets)
        return False, 99999, str(existing_tweets)
    else:
        print(Fore.YELLOW + f"number of existing tweets: {len(existing_tweets)}" + Fore.RESET)

    tweet_id_list = [tweet.get("tweetId", "") for tweet in existing_tweets]
    file_id_list = [ObjectId(tweet.get("_id", "")) for tweet in existing_tweets]
    batched_list = batch_list(tweet_id_list, 500)
    for i, batch in enumerate(batched_list):
        print(f"Batch {i+1}: {len(batch)} elements:")
        tweet_detail_list = retrieve_tweets_details(batch)
        if not tweet_detail_list:
            TaskLog.error(Fore.YELLOW + f"Tweet details not found" + Fore.RESET)
            return False, 99999, "Tweet details not found"
        else:
            len_tweet_list = len(tweet_detail_list)
            TaskLog.info(Fore.YELLOW + f"Tweet details found: {len_tweet_list}" + Fore.RESET)

        for tweet_detail, file_id in zip(tweet_detail_list, file_id_list):
            img_key_list = []
            video_key_list = []
            tweet_id = tweet_detail.get("tweetId", "")
            media_list = tweet_detail.get("mediaList", [])
            print(Fore.CYAN + f"Processing tweet: {tweet_id}" + Fore.RESET)
            if media_list:
                for item_ in media_list:
                    if item_.get("mediaType") == "image":
                        img_path = item_.get("imgPath", "")
                        img_key_list.append(img_path)
                    if item_.get("mediaType") == "video":
                        video_path = item_.get("videoPath", "")
                        # keyframe_key = item.get("keyframePath", "")
                        video_key_list.append(video_path)
            status, result = gen_tweets_db.update_one({"_id": file_id, "imgKeyList": img_key_list,
                                                       "videoKeyList": video_key_list})
            if not status:
                TaskLog.error(f"Error inserting mongodb: {result}")


def main():
    # update_media_info()
    result = clear_db()
    if result:
        print(Fore.GREEN + "Database cleared" + Fore.RESET)
    else:
        print(Fore.RED + "Error clearing database" + Fore.RESET)


if __name__ == '__main__':
    main()
