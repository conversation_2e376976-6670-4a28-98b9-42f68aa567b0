import config
import requests
from task import TaskLog
from task.server.create import load_user
from task.dao.mongo.mongdb_writer import MongoDBWriter
from task import prompts_fusion
from task.server.call_gpt import CallGpt
from colorama import Fore

gen_tweets_db = MongoDBWriter(collection="tweet")
prompt_gpt = CallGpt(model='gpt-4o-mini', temperature=0.4)
focus_list = [1, 2, 3, 4]
rec_host = config.WATT_REC_HOST
url_rec_hot_topic_list = "/api/v1/rec/topic"
url_rec_hot_tweet_list = "/api/v1/rec/topic/tweetDetail"
url_get_tweet_detail = "/api/v1/support/tweet/detailInfo"

writing_styles_mapping = load_user.creative_style_mapping
user_details = load_user.XUserDetails()


def categorize_tweet(content_list: list):
    """
    Categorize a tweet content based on original content.
    :param content_list: original content list
    :return: generated content
    """
    input_data = {"tweet_content": content_list}

    system_prompt = prompts_fusion.get_tag_system(input_data)
    user_prompt = prompts_fusion.get_tag_user(input_data)

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, json_object=False)
    if status_:
        return True, output_
    return False, '调用GPT接口推文归类失败'


def get_tweet_detail(tweet_id: str):
    data = {
        "tweetId": tweet_id
    }
    try:
        response_ = requests.post(f"{rec_host}{url_get_tweet_detail}", json=data, timeout=15)
        response_.raise_for_status()
    except Exception as e:
        TaskLog.error(f"Error retrieve tweet detail: {e}")
        return False, e
    response = response_.json()
    if response.get("code") != 0:
        return False, response.get("msg")
    return True, response.get("data")


def retrieve_and_tag(hotspot_size=40, tweet_size=100):
    """
    Retrieve hot topics and hot tweets, then tag the tweets.
    """
    # 1. Retrieve hot topic list according.
    for focus_area in focus_list:
        body = {
            "xId": 5,
            "location": 1,
            "focusArea": focus_area,
            "pageNo": 1,
            "pageSize": hotspot_size
        }
        try:
            response_ = requests.post(f"{rec_host}{url_rec_hot_topic_list}", json=body, timeout=10)
        except Exception as e:
            TaskLog.error(f"Hot topic retrieve Error: {e}")
            return False, 99999, str(response_)
        if response_.status_code != 200:
            return False, 99998, str(response_)
        response = response_.json().get("result", {}).get("data", {})
        sum_count = response.get("sumCount", 0)
        print(Fore.YELLOW+f"sumCount of hot topics: {sum_count}"+Fore.RESET)
        hot_topic_list = response.get("hotspotList", [])

        # 2. Retrieve all high ranked original tweets from url_rec_hot_tweet_list.
        for hotspot_info in hot_topic_list:
            hotspot_id = hotspot_info.get("hotspotId", "")
            hotspot_content = hotspot_info.get("hotspotContent", "")
            # TaskLog.info(f"hotspot_info: {hotspot_id} | {hotspot_content}")
            body_tweet_list = {
                "hotspotId": hotspot_id,
                "xId": 5,
                "pageNo": 1,
                "pageSize": tweet_size,
                "withContentMediaUrl": True,
                "withAuthorMediaUrl": True
            }
            try:
                response_ = requests.post(f"{rec_host}{url_rec_hot_tweet_list}", json=body_tweet_list, timeout=25)
            except Exception as e:
                TaskLog.error(f"Tweet list retrieve Error: {e}")
            if response_.status_code != 200:
                return False, 99998, str(response_)
            response = response_.json().get("result", {}).get("data", {})

            tweet_list = response.get("tweetDetailList", [])
            tweet_id_list = [tweet.get("tweetId", "") for tweet in tweet_list]
            # 5. Span database 'creative_writing' to filter out all tweets with existing users and original tweets,
            status, existing_tweets = gen_tweets_db.duoBatchSearch("xId", [5], "tweetId", tweet_id_list)
            if not status:
                TaskLog.error("Error: ", existing_tweets)
                return False, 99999, str(existing_tweets)
            else:
                TaskLog.info(f"number of existing tweets: {len(existing_tweets)}")

            existing_combinations = set()
            for tweet in existing_tweets:
                # if len(textlist) > 15:
                existing_combinations.add((tweet.get("xId", 0), tweet.get("tweetId", "")))


if __name__ == "__main__":
    categorize_tweet(["This is a test tweet."])
