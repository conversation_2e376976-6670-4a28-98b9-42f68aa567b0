import config
from utils.watt_service.call_watt_ai_service import CallAiService

hoc_host = config.HOC_BACK_HOST
hoc_token = config.HOC_TOKEN
user_info_uri = config.USER_INFO_URI
user_list_uri = config.USER_LIST_URI

# 创作风格:5种 1 幽默风趣、2 专业深度、3 激情煽动、4 简洁明了、5 亲切随和
creative_style_mapping = {
    1: "humorous",
    2: "professional depth",
    3: "passionate",
    4: "concise",
    5: "gentle"
}

# 聚焦领域:9个 1 新闻时事 2 地区资讯 3 副业主播 4 Web3炒币 5 副业影视剪辑 6 影视资讯 7 音乐资讯 8 综艺节目资讯 9 AI科技资讯
focus_area_mapping = {
    1: "新闻时事",
    2: "地区资讯",
    3: "副业主播",
    4: "Web3炒币",
    5: "副业影视剪辑",
    6: "影视资讯",
    7: "音乐资讯",
    8: "综艺节目资讯",
    9: "AI科技资讯"
}


class XUserDetails:
    def __init__(self):
        self.persona = {}
        self.user_list = None
        self.call_get_user_x_info = CallAiService(hoc_host)
        self.timeout = 10
        self.headers_ = {"token": 'Bearer ' + hoc_token}

    def get_user_list(self):
        uri = user_list_uri
        body = {
            "pageNo": 1,
            "pageSize": 40
        }
        status_, code_, response_ = self.call_get_user_x_info.call(uri, body, self.timeout, self.headers_)
        if not status_:
            if not self.user_list:
                print(f"Record not found: {code_}")
                return False, "Record not found"
            else:
                return True, "Use cached data"
        output_list = response_.get("result", {}).get("data", {}).get("userXIdList", [])
        self.user_list = []
        for item in output_list:
            self.user_list.extend(item.get('xIdList', []))
        return True, self.user_list

    def get_user_info(self, x_id: int):

        uri = user_info_uri
        body = {
            "xId": x_id,
            "isUpdate": False
        }
        status_, code_, response_ = self.call_get_user_x_info.call(uri, body, self.timeout, self.headers_)
        if not status_:
            print(f"Record not found: {code_}")

        try:
            details = response_.get("result", {}).get("data", {}).get("userXInfo", {})
            creative_style_int_list = details.get("creativeStyle", [])
            focus_area_int_list = details.get("focusArea", [])
            focus_area_list = [focus_area_mapping.get(x, "未知") for x in focus_area_int_list]
            follower_count = details.get("followerCount", 0)
            following_count = details.get("followingCount", 0)
        except Exception as e:
            print(f"Error in parsing user info: {e}")
            return False, "Error in parsing user info"

        self.persona["creative_style"] = creative_style_int_list
        self.persona["focus_area"] = focus_area_list
        self.persona["follower_count"] = follower_count
        self.persona["following_count"] = following_count

        return True, self.persona


if __name__ == '__main__':
    user_details = XUserDetails()
    user_details.get_user_list()
    print(user_details.user_list)
    for x_id in user_details.user_list:
        print(f"User id: {x_id}")
        user_details.get_user_info(x_id)
        print(f"Creative style: {user_details.creative_style_list}")
        print(f"Focus area: {user_details.focus_area_list}")
        print(f"Follower count: {user_details.follower_count}")
        print(f"Following count: {user_details.following_count}")
        print("====================================")
