import os
import sys
import argparse
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey
monkey.patch_all()
import config
import json
import requests
import time
import random
from task import TaskLog
from task.server.create import load_user
from task.dao.mongo.mongdb_writer import MongoDBWriter
from task.lib.prompt_fusion import PromptsFusion
from task.lib.lang_detect import lang_detect
from task.server.call_gpt import *
from colorama import Fore

gen_tweets_db = MongoDBWriter()
prompts_fusion = PromptsFusion()
prompt_gpt = CallGpt(model="gpt-4o-2024-11-20", temperature=0.8)
token_up_cn = 110
token_up_en = 190

rec_host = config.WATT_REC_HOST
url_rec_hot_topic_list = "/api/v1/rec/topic"
url_rec_hot_tweet_list = "/api/v1/rec/topic/tweetDetail"
url_get_tweet_detail = "/api/v1/support/tweet/detailInfo"

language_mapping = {
    1: "Simplified Chinese",
    2: "Traditional Chinese",
    3: "English",
    4: "Others"
}
focus_list = [1, 2, 3, 4]

writing_styles_mapping = load_user.creative_style_mapping

user_details = load_user.XUserDetails()


def generate_tweet(original_content: str, language: int, style_list: list):
    """
    Generate a tweet content based on original content and language.
    :param original_content: original content
    :param language: language
    :param style_list: style list
    :return: generated content
    """
    if language == 3:
        token_limit_up = token_up_en
        token_bot = token_limit_up - 40
    else:
        token_limit_up = token_up_cn
        token_bot = token_limit_up - 30

    style_mapping = {key: writing_styles_mapping[key] for key in style_list if key in writing_styles_mapping}

    language_str = language_mapping.get(language, 'Traditional Chinese')
    input_data = {"tweet_content": original_content, "token_limit": f"{token_bot}-{token_limit_up}",
                  "language": language_str, "writing_style": style_mapping}

    system_prompt = prompts_fusion.get_single_system(input_data)
    user_prompt = prompts_fusion.get_single_user(input_data)

    # if random.random() < 0.5:
    #     status_, output_ = prompt_claude.callClaude(system_prompt, user_prompt)
    #     if status_:
    #         return True, output_
    #     return False, '调用Claude接口生成推文失败'
    # else:
    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, temperature=1.0, json_object=True)
    if status_:
        try:
            rewritten_content = json.loads(output_)
            return True, rewritten_content
        except Exception as e:
            return False, f'调用GPT接口生成推文失败{str(e)}'
    return False, '调用GPT接口生成推文失败'


def generate_prompt(original_content: str, language: int, style_list: list):
    """
    Generate prompt based on original content and language.
    :param original_content: original content
    :param language: language
    :param style_list: style list
    :return: generated content
    """
    if language == 3:
        token_limit_up = token_up_en
        token_bot = token_limit_up - 40
    else:
        token_limit_up = token_up_cn
        token_bot = token_limit_up - 30

    style_mapping = {key: writing_styles_mapping[key] for key in style_list if key in writing_styles_mapping}

    language_str = language_mapping.get(language, 'Traditional Chinese')
    input_data = {"tweet_content": original_content, "token_limit": f"{token_bot}-{token_limit_up}",
                  "language": language_str, "writing_style": style_mapping}

    system_prompt = prompts_fusion.get_single_system(input_data)
    user_prompt = prompts_fusion.get_single_user(input_data)

    return system_prompt, user_prompt


def generate_tweets_con(sys_prompt_list: list, user_prompt_list: list):
    status_, output_list = prompt_gpt.gcallOpenaiGpt(sys_prompt_list, user_prompt_list, temperature=0.9,
                                                     json_object=True)
    if status_:
        return True, output_list
    return False, '调用GPT接口生成推文失败'


def retrieve_tweets_details(tweet_id_list):
    body_tweet_details = {
        "tweetIdList": tweet_id_list,
        "withContentMediaUrl": True,
        "withAuthorMediaUrl": True
    }
    try:
        response_ = requests.post(f"{rec_host}{url_get_tweet_detail}", json=body_tweet_details, timeout=100)
        response_.raise_for_status()
        response = response_.json().get("result", {}).get("data", {})
        return response.get("tweetDetailList", [])
    except Exception as e:
        TaskLog.error(f"Retrieve tweet details Error: {e}")
        return []


def generate_new_tweets(hotspot_size=20, tweet_size=50, batch_mode=True):
    """
    Generate new tweets based on original content and language.
    """

    # 1. Span all user information
    status_, user_list = user_details.get_user_list()
    if not status_:
        TaskLog.warning(f"User list not found: {user_list}")
        return False, f"User list not found: {user_list}"
    values_to_remove = {1, 2, 4}
    # Using filter and lambda to remove specific values
    filtered_list = list(filter(lambda x: x not in values_to_remove, user_list))
    TaskLog.info(filtered_list)

    # 2. Retrieve user information
    for user_ in filtered_list:
        print(Fore.CYAN + f"Processing user: {user_}" + Fore.RESET)
        status_, user_info = user_details.get_user_info(user_)
        if not status_:
            TaskLog.error(f"User info not found: {user_info}")
            return False, f"User info not found: {user_info}"
        writing_styles = user_info.get("creative_style", [])
        if not writing_styles:
            writing_styles = [1, 3, 5]
        # focus_areas = user_info.get("focus_area", [])

        # 3. Retrieve hot topic list according to user id.
        for focus_area in focus_list:
            body = {
                "xId": user_,
                "location": 1,
                "focusArea": focus_area,
                "pageNo": 1,
                "pageSize": hotspot_size
            }
            try:
                response_ = requests.post(f"{rec_host}{url_rec_hot_topic_list}", json=body, timeout=10)
            except Exception as e:
                TaskLog.error(f"Hot topic retrieve Error: {e}")
                return False, 99999, str(response_)
            if response_.status_code != 200:
                return False, 99998, str(response_)
            response = response_.json().get("result", {}).get("data", {})
            sum_count = response.get("sumCount", 0)
            TaskLog.info(f"sumCount of hot topics: {sum_count}")
            hot_topic_list = response.get("hotspotList", [])

            # 4. Retrieve all high ranked original tweets from url_rec_hot_tweet_list.
            for hotspot_info in hot_topic_list:
                hotspot_id = hotspot_info.get("hotspotId", "")
                hotspot_content = hotspot_info.get("hotspotContent", "")
                # TaskLog.info(f"hotspot_info: {hotspot_id} | {hotspot_content}")
                body_tweet_list = {
                    "hotspotId": hotspot_id,
                    "xId": user_,
                    "pageNo": 1,
                    "pageSize": tweet_size,
                    "withContentMediaUrl": True,
                    "withAuthorMediaUrl": True
                }
                try:
                    response_ = requests.post(f"{rec_host}{url_rec_hot_tweet_list}", json=body_tweet_list, timeout=25)
                except Exception as e:
                    TaskLog.error(f"Tweet list retrieve Error: {e}")
                if response_.status_code != 200:
                    return False, 99998, str(response_)
                response = response_.json().get("result", {}).get("data", {})

                tweet_list = response.get("tweetDetailList", [])
                tweet_id_list = [tweet.get("tweetId", "") for tweet in tweet_list]
                # 5. Span database 'creative_writing' to filter out all tweets with existing users and original tweets,
                status, existing_tweets = gen_tweets_db.duoBatchSearch("xId", [user_], "tweetId", tweet_id_list)
                if not status:
                    TaskLog.error("Error: ", existing_tweets)
                    return False, 99999, str(existing_tweets)
                else:
                    TaskLog.info(f"number of existing tweets: {len(existing_tweets)}")

                existing_combinations = set()
                for tweet in existing_tweets:
                    # if len(textlist) > 15:
                    existing_combinations.add((tweet.get("xId", 0), tweet.get("tweetId", "")))

                new_combinations = [{"xId": user_, "tweetId": tweet_id} for tweet_id in tweet_id_list
                                    if (user_, tweet_id) not in existing_combinations]

                tweet_id_list = [item.get("tweetId", '') for item in new_combinations]
                TaskLog.info(f"new tweets to be created: {tweet_id_list}")
                if not tweet_id_list:
                    continue
                # 6. 根据推文ID获取推文详情
                ##### To be deprecated!!
                tweet_detail_list = retrieve_tweets_details(tweet_id_list)
                if not tweet_detail_list:
                    TaskLog.error(Fore.YELLOW + f"Tweet details not found" + Fore.RESET)
                    continue
                else:
                    len_tweet_list = len(tweet_detail_list)
                    TaskLog.info(Fore.YELLOW + f"Tweet details found: {len_tweet_list}" + Fore.RESET)

                tweet_content_list = [item.get("content", '') for item in tweet_list if
                                      item.get("tweetId", '') in tweet_id_list]
                len_tweet_list = len(tweet_content_list)
                TaskLog.info(Fore.YELLOW + f"Tweet details found: {len_tweet_list}" + Fore.RESET)

                sys_prompt_list = []
                user_prompt_list = []
                language_list = []
                image_key_lists = []
                video_key_lists = []

                # for tweet_id, tweet_content in zip(tweet_id_list, tweet_content_list):
                for tweet_detail in tweet_detail_list:
                    img_key_list = []
                    video_key_list = []
                    tweet_id = tweet_detail.get("tweetId", "")
                    tweet_content = tweet_detail.get("content", "")

                    output_json = {}
                    if tweet_content:
                        media_list = tweet_detail.get("mediaList", [])
                        if media_list:
                            for item in media_list:
                                if item.get("mediaType") == "image":
                                    img_path = item.get("imgPath", "")
                                    img_key_list.append(img_path)
                                if item.get("mediaType") == "video":
                                    video_path = item.get("videoPath", "")
                                    # keyframe_key = item.get("keyframePath", "")
                                    video_key_list.append(video_path)
                        image_key_lists.append(img_key_list)
                        video_key_lists.append(video_key_list)

                        # 执行生成推文模块
                        lang_ = lang_detect(tweet_content)
                        language_ = 3
                        if lang_ == 'en':
                            language_ = 3
                        elif lang_ == 'zh-cn':
                            language_ = 1
                        elif lang_ == 'zh-tw':
                            language_ = 2
                        language_list.append(language_)
                        if not batch_mode:
                            status, generated_tweet = generate_tweet(tweet_content, language_, writing_styles)
                            if not status:
                                TaskLog.error("Error: ", generated_tweet)
                                return False, f"Error: {generated_tweet}"
                            text_list = generated_tweet.get("textList", [])
                            for text in text_list:
                                if isinstance(text, dict):
                                    content = text.get("content", "")
                                    tag_list = []
                                    tags = text.get("tags", [])
                                    for keyword in tags:
                                        if not keyword.startswith("#"):
                                            tag_list.append(f"#{keyword}")
                                        else:
                                            tag_list.append(keyword)
                                    content = content + " ".join(tag_list)
                                    text["content"] = content
                                    content = text.get("content", "")
                                    tag_list = []
                                    tags = text.get("tags", [])
                                    for keyword in tags:
                                        split_tags = keyword.replace("#", " #").split()
                                        corrected_tags = ['#' + tag.strip('#') for tag in split_tags]
                                        tag_list.extend(corrected_tags)
                                    content = content + " ".join(tag_list)
                                    text["content"] = content
                                    text["viewpoint"] = 1
                                    text["language"] = language_
                                else:
                                    print(Fore.RED + f"Text not valid dict {text}" + Fore.RESET)
                                    continue
                            output_json["textList"] = text_list
                            output_json["imgKeyList"] = img_key_list
                            output_json["videoKeyList"] = video_key_list

                            output_json["xId"] = user_
                            output_json["tweetId"] = tweet_id
                            output_json["hotspotContent"] = hotspot_content
                            # 将生成的推文存入数据库
                            status, result = gen_tweets_db.insertSingleData(output_json)
                            if not status:
                                TaskLog.error(f"Error inserting mongodb: {result}")
                        else:
                            sys_prompt, user_prompt = generate_prompt(tweet_content, language_, writing_styles)
                            sys_prompt_list.append(sys_prompt)
                            user_prompt_list.append(user_prompt)
                    else:
                        TaskLog.warning(Fore.RED + f"Content not found" + Fore.RESET)
                        continue
                if batch_mode:
                    # split into batch size
                    batch_size = 10
                    sys_prompt_list = [sys_prompt_list[i:i + batch_size] for i in
                                       range(0, len(sys_prompt_list), batch_size)]
                    user_prompt_list = [user_prompt_list[i:i + batch_size] for i in
                                        range(0, len(user_prompt_list), batch_size)]
                    language_list = [language_list[i:i + batch_size] for i in range(0, len(language_list), batch_size)]

                    for sys_prompt_list, user_prompt_list, language_list in zip(sys_prompt_list, user_prompt_list,
                                                                                language_list):

                        output_texts1 = []
                        status_, output_lists = generate_tweets_con(sys_prompt_list, user_prompt_list)
                        if not status_:
                            TaskLog.error(f"Error generating tweets: {output_lists}")
                            continue
                        for item in output_lists:
                            try:
                                json_data = json.loads(item)
                                text_list = json_data.get("textList", [])
                                output_texts1.append(text_list)
                            except json.JSONDecodeError as e:
                                TaskLog.error(f"Error decoding JSON: {e}")
                                continue

                        for output_text, lang_, image_key_list_, video_key_list_, tweet_id_ in zip(output_texts1,
                                                                                                   language_list,
                                                                                                   image_key_lists,
                                                                                                   video_key_lists,
                                                                                                   tweet_id_list):
                            output_json = {}
                            for text in output_text:
                                if isinstance(text, dict):
                                    content = text.get("content", "")
                                    tag_list = []
                                    tags = text.get("tags", [])
                                    for keyword in tags:
                                        split_tags = keyword.replace("#", " #").split()
                                        corrected_tags = ['#' + tag.strip('#') for tag in split_tags]
                                        tag_list.extend(corrected_tags)
                                    if isinstance(content, str):
                                        content = content + " ".join(tag_list)
                                    else:
                                        content = str(content) + " ".join(tag_list)
                                    text["content"] = content
                                    text["viewpoint"] = 1
                                    text["language"] = lang_
                                else:
                                    TaskLog.error(Fore.RED + f"Text not valid dict {text}" + Fore.RESET)
                                    continue
                            output_json["textList"] = output_text
                            output_json["imgKeyList"] = image_key_list_
                            output_json["videoKeyList"] = video_key_list_
                            output_json["xId"] = user_
                            output_json["tweetId"] = tweet_id_
                            output_json["hotspotContent"] = hotspot_content

                            # for output_text, lang_, tweet_id_ in zip(output_texts1, language_list, tweet_id_list):
                            #     output_json = {}
                            #     for text in output_text:
                            #         if isinstance(text, dict):
                            #             text["viewpoint"] = 1
                            #             text["language"] = lang_
                            #         else:
                            #             TaskLog.error(Fore.RED + f"Text not valid dict" + Fore.RESET)
                            #             continue
                            #     output_json["textList"] = output_text
                            #     output_json["xId"] = user_
                            #     output_json["tweetId"] = tweet_id_
                            #     output_json["hotspotContent"] = hotspot_content
                            # 将生成的推文存入数据库
                            status, result = gen_tweets_db.insertSingleData(output_json)
                            if not status:
                                TaskLog.error(Fore.RED + f"Error inserting mongodb: {result}" + Fore.RESET)
    return True


def main(args_):
    while True:
        TaskLog.info("Generating new tweets.")
        status = generate_new_tweets(args_.hotspot_size, args_.tweet_size)
        if not status:
            TaskLog.error("Error generating new tweets.")
            return False
        TaskLog.info("Successfully generated new tweets.")
        time.sleep(20)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Tweet hotspot catch and re-generation. -o 12 -t 10")

    parser.add_argument('-o', "--hotspot_size", type=int, default=12, help="Hotspot candidate size")
    parser.add_argument('-t', "--tweet_size", type=int, default=8, help="Tweet candidate size")

    args = parser.parse_args()
    main(args)
