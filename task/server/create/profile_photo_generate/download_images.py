import os
import requests
import csv
import argparse
from urllib.parse import urlparse


def download_files(input_file, output_dir):
    # Ensure output directory exists
    os.makedirs(output_dir, exist_ok=True)

    # Read and download files
    with open(input_file, 'r') as file:
        urls = csv.reader(file)

        for row in urls:
            for url in row:
                try:
                    # Strip quotes from URL
                    url = url.strip('"')

                    # Get filename from URL
                    filename = os.path.basename(urlparse(url).path)

                    # Create full output path
                    output_path = os.path.join(output_dir, filename)

                    # Download file
                    print(f"Downloading: {url}")
                    response = requests.get(url)
                    response.raise_for_status()

                    # Save file
                    with open(output_path, 'wb') as out_file:
                        out_file.write(response.content)

                    print(f"Successfully downloaded: {filename}")

                except Exception as e:
                    print(f"Error downloading {url}: {str(e)}")


def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description='Download files from URLs listed in a CSV file.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    # Add arguments
    parser.add_argument(
        '-i', '--input',
        default='urls.txt',
        help='Input file containing URLs (CSV format)'
    )

    parser.add_argument(
        '-o', '--output',
        default=os.path.join(os.getcwd(), 'downloaded_photos'),
        help='Output directory for downloaded files'
    )

    parser.add_argument(
        '-v', '--verbose',
        action='store_true',
        help='Enable verbose output'
    )

    # Parse arguments
    args = parser.parse_args()

    # Validate input file
    if not os.path.exists(args.input):
        print(f"Error: Input file '{args.input}' does not exist!")
        return

    # Print settings if verbose
    if args.verbose:
        print("Settings:")
        print(f"Input file: {args.input}")
        print(f"Output directory: {args.output}")
        print("Starting download...\n")

    # Download files
    try:
        download_files(args.input, args.output)
        print("\nDownload completed successfully!")
    except Exception as e:
        print(f"\nAn error occurred: {str(e)}")


if __name__ == "__main__":
    main()

