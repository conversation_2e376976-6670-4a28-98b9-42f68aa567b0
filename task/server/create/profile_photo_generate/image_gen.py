import grequests
import time
import json
import argparse


def make_requests(batch_num: int = 2, gender: str='female'):
    # 你的接口URL
    url = "http://dev-watt-ai-gateway.watt.chat/api/v1/hoc/GenProfilePhoto"

    # 创建9个并行请求
    requests = [grequests.post(url, json={"gender": gender, "num": batch_num}) for _ in range(9)]
    urls = []
    
    try:
        # 并行执行请求
        responses = grequests.map(requests)
        
        for response in responses:
            if response and response.status_code == 200:
                try:
                    data = response.json()
                    if data.get('status') == 0 and data.get('result', {}).get('data'):
                        urls.extend(data['result']['data'])
                except json.JSONDecodeError:
                    print("JSON解析错误")
            else:
                print(f"请求失败: {response.status_code if response else 'No response'}")
                return urls, False
                
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return urls, False
    return urls, True


def load_existing_urls():
    try:
        with open('urls.txt', 'r') as f:
            content = f.read().strip()
            return content.split(',') if content else []
    except FileNotFoundError:
        return []
    except Exception as e:
        print(f"读取文件时发生错误: {str(e)}")
        return []


def save_urls(urls):
    if urls:
        try:
            with open('urls.txt', 'w') as f:
                f.write(','.join(urls))
            print(f"成功写入 {len(urls)} 个URL到urls.txt")
        except Exception as e:
            print(f"写入文件时发生错误: {str(e)}")
    else:
        print("没有获取到任何URL")


def main():
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description='Generate profile photos using the Watt AI API.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    # Add arguments
    parser.add_argument(
        '-b', '--batch',
        default=2,
        type=int,
        help='Number of profile photos to generate in each batch'
    )
    parser.add_argument(
        '-g', '--gender',
        default='female',
        type=str,
        help='The gender of the profile photos to generate'
    )
    parser.add_argument(
        '-n', '--num',
        default=20,
        type=int,
        help='Number of batches to generate'
    )
    # Parse arguments
    args = parser.parse_args()

    all_urls = load_existing_urls()
    print(f"已加载 {len(all_urls)} 个现有URL")
    # 执行20次
    for i in range(args.num):
        print(f"执行第 {i+1} 次请求...")

        new_urls, success = make_requests(args.batch, args.gender)
        if new_urls:
            all_urls.extend(new_urls)
            print(f"新增 {len(new_urls)} 个URL")

        if not success:
            print("检测到请求失败，保存已获取的URLs并退出...")
            save_urls(all_urls)
            return

        # 每次成功获取新URLs后就保存一次
        save_urls(all_urls)

        if i < args.num - 1:
            time.sleep(4)

if __name__ == "__main__":
    main()
