"""
简化的深度研究模块
专注于核心功能：关键词扩展、Tavily和PPLX深度搜索、结果整合
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class SimplifiedDeepResearch:
    """简化的深度研究模块"""
    
    def __init__(self):
        # 导入搜索模块
        try:
            from task import callWattGPT
            self.callWattGPT = callWattGPT
            self.search_available = True
            logger.info("搜索模块导入成功")
        except ImportError as e:
            logger.error(f"无法导入搜索模块: {e}")
            self.search_available = False
        
        # 导入AI模块用于关键词扩展
        try:
            from task.lib.call_claude import gemini as _original_gemini
            
            def safe_gemini(sys_prompt, user_prompt, model=None):
                try:
                    response = _original_gemini(sys_prompt, user_prompt, model)
                    if isinstance(response, tuple) and len(response) >= 2:
                        status, result = response[0], response[1]
                        if status and isinstance(result, str) and len(result) > 0:
                            return True, result
                        else:
                            return False, f"Gemini调用失败: {result}"
                    else:
                        return False, f"Gemini返回格式异常: {response}"
                except Exception as e:
                    return False, f"Gemini函数调用异常: {str(e)}"
            
            self.gemini = safe_gemini
            self.ai_available = True
            logger.info("AI模块导入成功")
        except ImportError as e:
            logger.error(f"无法导入AI模块: {e}")
            self.ai_available = False
    
    async def expand_search_keywords(self, account_info: Dict, industry: str) -> List[str]:
        """扩展搜索关键词"""
        try:
            # 基础关键词
            base_keywords = [industry] if industry else []
            
            # 从账号信息提取关键词
            account_name = account_info.get('nickname', '')
            if account_name:
                base_keywords.append(account_name)
            
            # 如果AI可用，使用AI扩展关键词
            if self.ai_available:
                expanded_keywords = await self._ai_expand_keywords(base_keywords, industry, account_info)
                if expanded_keywords:
                    return expanded_keywords
            
            # 回退到规则扩展
            return self._rule_based_expand_keywords(base_keywords, industry, account_info)
            
        except Exception as e:
            logger.error(f"扩展搜索关键词失败: {e}")
            return [industry] if industry else ["账号分析"]
    
    async def _ai_expand_keywords(self, base_keywords: List[str], industry: str, account_info: Dict) -> Optional[List[str]]:
        """使用AI扩展关键词"""
        try:
            follower_count = account_info.get('follows', 0)
            
            prompt = f"""
基于以下信息，生成用于深度搜索的关键词：

行业：{industry}
基础关键词：{', '.join(base_keywords)}
粉丝数：{follower_count}

请生成5-8个相关的搜索关键词，用于深度研究该行业和账号类型的：
1. 行业趋势和热点
2. 营销策略和方法
3. 竞争对手分析
4. 成功案例研究

要求：
- 关键词要具体且实用
- 适合搜索引擎查询
- 包含{datetime.now().year}年的时效性
- 每行一个关键词

请直接返回关键词列表，不需要额外解释：
"""
            
            success, result = self.gemini(
                "你是一个专业的搜索关键词专家，擅长为不同行业生成精准的搜索关键词。",
                prompt
            )
            
            if success:
                # 解析关键词
                keywords = []
                for line in result.split('\n'):
                    line = line.strip()
                    if line and not line.startswith('-') and len(line) > 2:
                        # 清理可能的编号或符号
                        clean_keyword = line.replace('1.', '').replace('2.', '').replace('3.', '') \
                                          .replace('4.', '').replace('5.', '').replace('6.', '') \
                                          .replace('7.', '').replace('8.', '').replace('-', '') \
                                          .replace('•', '').strip()
                        if clean_keyword:
                            keywords.append(clean_keyword)
                
                if len(keywords) >= 3:
                    logger.info(f"AI扩展关键词成功: {len(keywords)}个")
                    return keywords[:8]  # 限制最多8个
            
            logger.warning("AI扩展关键词结果不理想")
            return None
            
        except Exception as e:
            logger.error(f"AI扩展关键词失败: {e}")
            return None
    
    def _rule_based_expand_keywords(self, base_keywords: List[str], industry: str, account_info: Dict) -> List[str]:
        """基于规则扩展关键词"""
        try:
            expanded = base_keywords.copy()
            current_year = str(datetime.now().year)
            
            # 添加时效性关键词
            for base_keyword in base_keywords:
                expanded.append(f"{current_year}年{base_keyword}趋势")
                expanded.append(f"{base_keyword}营销策略")
                expanded.append(f"{base_keyword}成功案例")
            
            # 根据行业添加特定关键词
            industry_keywords = {
                "美妆护肤": ["护肤品牌", "美妆博主", "化妆品评测", "护肤成分"],
                "科技数码": ["科技产品", "数码评测", "科技创新", "智能硬件"],
                "时尚穿搭": ["时尚博主", "穿搭技巧", "服装品牌", "时尚趋势"],
                "健康养生": ["健康生活", "养生方法", "健康知识", "运动健身"],
                "美食料理": ["美食博主", "料理技巧", "美食推荐", "餐厅评测"]
            }
            
            if industry in industry_keywords:
                expanded.extend(industry_keywords[industry])
            
            # 添加通用关键词
            general_keywords = [
                f"{industry}头部账号",
                f"{industry}KOL分析",
                f"{industry}内容运营",
                f"{industry}用户增长"
            ]
            expanded.extend(general_keywords)
            
            # 去重并限制数量
            unique_keywords = list(dict.fromkeys(expanded))[:10]
            logger.info(f"规则扩展关键词: {len(unique_keywords)}个")
            return unique_keywords
            
        except Exception as e:
            logger.error(f"规则扩展关键词失败: {e}")
            return base_keywords
    
    async def conduct_deep_search(self, keywords: List[str], progress_tracker) -> Dict[str, Any]:
        """执行深度搜索"""
        if not self.search_available:
            logger.error("搜索功能不可用")
            return {"error": "搜索功能不可用"}
        
        try:
            await progress_tracker.update_progress(7, "正在收集行业最新信息...")
            
            # 准备搜索查询
            search_queries = self._prepare_search_queries(keywords)
            
            await progress_tracker.update_progress(8, "正在分析行业动态与竞争格局...")
            
            # 执行Tavily搜索
            tavily_results = await self._execute_tavily_search(search_queries, progress_tracker)
            
            # 执行PPLX搜索
            pplx_results = await self._execute_pplx_search(search_queries, progress_tracker)
            
            await progress_tracker.update_progress(13, "正在整合搜索结果...")
            
            # 整合搜索结果
            integrated_results = await self._integrate_search_results(
                tavily_results, pplx_results, progress_tracker
            )
            
            await progress_tracker.update_progress(15, "行业分析完成，准备进行账号诊断...")
            
            return {
                "search_summary": {
                    "total_queries": len(search_queries),
                    "tavily_results": len(tavily_results),
                    "pplx_results": len(pplx_results),
                    "keywords_used": keywords
                },
                "integrated_insights": integrated_results,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"深度搜索失败: {e}")
            return {"error": f"深度搜索失败: {str(e)}"}
    
    def _prepare_search_queries(self, keywords: List[str]) -> List[Dict[str, str]]:
        """准备搜索查询"""
        queries = []
        
        # 为每个关键词生成多种查询类型
        for keyword in keywords[:5]:  # 限制关键词数量避免过多查询
            # 趋势查询
            queries.append({
                "type": "trend",
                "query": f"{keyword} {datetime.now().year}年最新趋势发展",
                "focus": "行业趋势"
            })
            
            # 策略查询
            queries.append({
                "type": "strategy",
                "query": f"{keyword} 营销策略 成功案例分析",
                "focus": "营销策略"
            })
            
            # 竞争分析查询
            queries.append({
                "type": "competition",
                "query": f"{keyword} 头部账号 竞争对手分析",
                "focus": "竞争分析"
            })
        
        return queries[:9]  # 限制总查询数量
    
    async def _execute_tavily_search(self, queries: List[Dict], progress_tracker) -> List[Dict]:
        """执行Tavily搜索"""
        try:
            await progress_tracker.update_progress(9, "正在检索最新行业资讯...")
            
            # 导入Tavily API配置
            try:
                import requests
                import random
                from task import TAVILY_API_KEY
                
                if not TAVILY_API_KEY or not isinstance(TAVILY_API_KEY, list):
                    logger.warning("Tavily API Key 未配置，使用PPLX作为替代")
                    return await self._execute_tavily_fallback(queries, progress_tracker)
                    
            except ImportError as e:
                logger.warning(f"无法导入Tavily依赖: {e}，使用PPLX作为替代")
                return await self._execute_tavily_fallback(queries, progress_tracker)
            
            results = []
            
            for i, query in enumerate(queries[:3]):  # 限制Tavily查询数量
                try:
                    # 根据查询类型选择合适的时间范围
                    time_range = self._get_time_range_for_query(query.get("type", "basic"), i)
                    time_suffix = self._get_time_aware_suffix(query.get("type", "basic"), time_range)
                    
                    # 构建时间感知的查询
                    enhanced_query = f"{query['query']} {time_suffix}"
                    
                    logger.info(f"执行Tavily搜索 ({time_range}): {enhanced_query[:50]}...")
                    
                    # Tavily API 配置
                    url = "https://api.tavily.com/search"
                    api_key = random.choice(TAVILY_API_KEY)
                    headers = {
                        "Authorization": f"Bearer {api_key}",
                        "Content-Type": "application/json"
                    }
                    
                    # 转换时间范围为Tavily API支持的格式
                    tavily_time_range = self._convert_to_tavily_time_range(time_range)
                    
                    body = {
                        "query": enhanced_query,
                        "search_depth": "advanced",
                        "max_results": 8,
                        "time_range": tavily_time_range,
                        "include_raw_content": True
                    }
                    
                    # 执行搜索请求
                    response = requests.post(url, headers=headers, json=body, timeout=60)
                    
                    if response.status_code != 200:
                        logger.error(f"Tavily搜索失败: {response.status_code}, 响应: {response.text}")
                        continue
                    
                    # 解析响应
                    try:
                        response_data = response.json()
                    except Exception as e:
                        logger.error(f"Tavily响应JSON解析失败: {e}")
                        continue
                    
                    # 验证响应数据结构
                    if not isinstance(response_data, dict) or 'results' not in response_data:
                        logger.error(f"Tavily响应格式异常: {list(response_data.keys()) if isinstance(response_data, dict) else type(response_data)}")
                        continue
                    
                    tavily_results = response_data.get('results', [])
                    if not isinstance(tavily_results, list):
                        logger.error(f"Tavily results字段格式异常")
                        continue
                    
                    # 处理搜索结果
                    processed_results = []
                    for j, res in enumerate(tavily_results):
                        if not isinstance(res, dict):
                            continue
                        
                        # 过滤低质量结果
                        if res.get('score', 0) < 0.2:
                            continue
                        
                        # 安全获取内容字段
                        snippet_content = res.get('content', '') or ''
                        raw_content = res.get('raw_content', '') or ''
                        content = raw_content or snippet_content
                        
                        if not content:
                            continue
                        
                        processed_result = {
                            'title': res.get('title', '') or '',
                            'url': res.get('url', '') or '',
                            'snippet': str(snippet_content)[:300],
                            'content': str(content)[:1000],
                            'relevance_score': res.get('score', 0.5),
                            'source': 'tavily',
                            'timestamp': time.time(),
                            'search_time_range': time_range
                        }
                        
                        # 确保必要字段存在且非空
                        if processed_result['title'] and processed_result['content']:
                            processed_results.append(processed_result)
                            logger.info(f"找到Tavily结果 {j+1}: {processed_result['title'][:30]}...")
                    
                    # 转换为原有格式
                    if processed_results:
                        combined_content = "\n\n".join([
                            f"标题: {r['title']}\n内容: {r['content'][:500]}" 
                            for r in processed_results[:3]
                        ])
                        
                        results.append({
                            "query": query["query"],
                            "type": query["type"],
                            "focus": query["focus"],
                            "content": combined_content,
                            "citations": [r['url'] for r in processed_results if r['url']],
                            "source": "tavily",
                            "quality_score": sum(r['relevance_score'] for r in processed_results) / len(processed_results),
                            "result_count": len(processed_results)
                        })
                        
                        await progress_tracker.update_progress(
                            9 + i, 
                            "正在挖掘行业内容与趋势分析..."
                        )
                    else:
                        logger.warning(f"Tavily查询无有效结果: {enhanced_query[:50]}")
                    
                except Exception as e:
                    logger.error(f"Tavily搜索查询失败: {e}")
                    continue
            
            logger.info(f"Tavily搜索完成: {len(results)}个查询结果")
            return results
            
        except Exception as e:
            logger.error(f"Tavily搜索失败: {e}")
            return await self._execute_tavily_fallback(queries, progress_tracker)
    
    def _get_time_range_for_query(self, query_type: str, attempt: int = 0) -> str:
        """根据查询类型和重试次数获取合适的时间范围"""
        time_ranges = {
            "basic": ["month", "quarter", "year"],
            "trend": ["quarter", "year", "two_years"],
            "strategy": ["quarter", "year", "two_years"],
            "competition": ["quarter", "year", "two_years"]
        }
        
        ranges = time_ranges.get(query_type, ["month", "year"])
        range_index = min(attempt, len(ranges) - 1)
        return ranges[range_index]
    
    def _get_time_aware_suffix(self, query_type: str, time_range: str) -> str:
        """根据查询类型和时间范围生成查询后缀"""
        current_year = str(datetime.now().year)
        last_year = str(int(current_year) - 1)
        
        if time_range == "month":
            return f"{current_year}年最新"
        elif time_range == "quarter":
            return f"{current_year}年"
        elif time_range == "year":
            if query_type in ["trend", "strategy"]:
                return f"{last_year}-{current_year}年"
            else:
                return f"{current_year}年"
        elif time_range == "two_years":
            return f"{str(int(current_year) - 1)}-{current_year}年"
        else:
            return f"{current_year}年"
    
    def _convert_to_tavily_time_range(self, time_range: str) -> str:
        """将内部时间范围转换为Tavily API支持的格式"""
        mapping = {
            "month": "month",
            "quarter": "month",  # Tavily没有quarter，使用month
            "year": "year",
            "two_years": "year"  # Tavily没有two_years，使用year
        }
        return mapping.get(time_range, "month")
    
    async def _execute_tavily_fallback(self, queries: List[Dict], progress_tracker) -> List[Dict]:
        """Tavily搜索失败时的PPLX替代方案"""
        logger.info("使用PPLX作为Tavily搜索的替代方案")
        
        results = []
        
        for i, query in enumerate(queries[:3]):
            try:
                # 构建时间感知的查询
                time_range = self._get_time_range_for_query(query.get("type", "basic"), i)
                time_suffix = self._get_time_aware_suffix(query.get("type", "basic"), time_range)
                enhanced_query = f"{query['query']} {time_suffix}"
                
                # 使用PPLX替代Tavily
                search_request = {
                    "model": "sonar",
                    "messages": [{"role": "user", "content": enhanced_query}],
                    "search_recency_filter": "month" if time_range in ["month", "quarter"] else "year",
                    "max_tokens": 1024,
                    "temperature": 0.1
                }
                
                status, code, result = self.callWattGPT.callPplxChannelChatCompletions(
                    search_request, timeout=45
                )
                
                if status and result:
                    content = result['result']['data']['choices'][0]['message']['content']
                    citations = result['result']['data'].get('citations', [])
                    
                    results.append({
                        "query": query["query"],
                        "type": query["type"],
                        "focus": query["focus"],
                        "content": content,
                        "citations": citations,
                        "source": "pplx_fallback",
                        "quality_score": 0.7,  # 默认质量分
                        "result_count": 1
                    })
                    
                    await progress_tracker.update_progress(
                        9 + i, 
                        f"PPLX替代搜索进度 {i+1}/{len(queries[:3])}"
                    )
                
            except Exception as e:
                logger.error(f"PPLX替代搜索失败: {e}")
                continue
        
        logger.info(f"PPLX替代搜索完成: {len(results)}个结果")
        return results
    
    async def _execute_pplx_search(self, queries: List[Dict], progress_tracker) -> List[Dict]:
        """执行PPLX搜索"""
        try:
            await progress_tracker.update_progress(11, "正在分析竞争对手策略...")
            
            results = []
            
            # 执行单个PPLX搜索，避免批量请求超时
            for i, query in enumerate(queries[3:6]):  # 使用不同的查询避免重复
                try:
                    # 根据查询类型选择合适的时间范围
                    time_range = self._get_time_range_for_query(query.get("type", "basic"), i)
                    time_suffix = self._get_time_aware_suffix(query.get("type", "basic"), time_range)
                    
                    # 构建时间感知的查询
                    enhanced_query = f"{query['query']} {time_suffix}"
                    
                    logger.info(f"执行PPLX搜索 ({time_range}): {enhanced_query[:50]}...")
                    
                    # 根据时间范围选择PPLX搜索过滤器
                    search_filter = "month" if time_range in ["month", "quarter"] else "year"
                    
                    # 使用最新的sonar模型
                    search_request = {
                        "model": "sonar",
                        "messages": [{"role": "user", "content": enhanced_query}],
                        "search_recency_filter": search_filter,
                        "max_tokens": 2048,
                        "temperature": 0.1
                    }
                    
                    # 执行单个PPLX搜索
                    status, code, result = self.callWattGPT.callPplxChannelChatCompletions(
                        search_request, timeout=120  # 增加超时时间
                    )
                    
                    if status and result:
                        # 解析PPLX响应
                        try:
                            if isinstance(result, dict) and 'result' in result:
                                result_data = result['result']
                                if isinstance(result_data, dict) and 'data' in result_data:
                                    data = result_data['data']
                                    if isinstance(data, dict) and 'choices' in data:
                                        choices = data['choices']
                                        if isinstance(choices, list) and len(choices) > 0:
                                            choice = choices[0]
                                            if isinstance(choice, dict) and 'message' in choice:
                                                message = choice['message']
                                                if isinstance(message, dict) and 'content' in message:
                                                    content = message['content']
                                                    citations = data.get('citations', [])
                                                    
                                                    # 计算内容质量评分
                                                    quality_score = self._calculate_content_quality(content, enhanced_query)
                                                    
                                                    results.append({
                                                        "query": query["query"],
                                                        "type": query["type"],
                                                        "focus": query["focus"],
                                                        "content": content,
                                                        "citations": citations,
                                                        "source": "pplx",
                                                        "quality_score": quality_score,
                                                        "result_count": 1,
                                                        "search_time_range": time_range,
                                                        "enhanced_query": enhanced_query
                                                    })
                                                    
                                                    logger.info(f"PPLX搜索结果 {i+1}: 质量评分 {quality_score:.2f}")
                                                    
                                                    await progress_tracker.update_progress(
                                                        11 + i, 
                                                        "正在整合行业研究和市场洞察..."
                                                    )
                            
                            if not results or len(results) <= i:
                                logger.warning(f"PPLX搜索响应格式异常或无有效内容")
                                
                        except Exception as e:
                            logger.error(f"PPLX搜索结果解析失败: {e}")
                            continue
                    else:
                        logger.warning(f"PPLX搜索请求失败: status={status}, code={code}")
                    
                except Exception as e:
                    logger.error(f"PPLX搜索查询失败: {e}")
                    continue
            
            logger.info(f"PPLX搜索完成: {len(results)}个结果")
            return results
            
        except Exception as e:
            logger.error(f"PPLX搜索失败: {e}")
            return []
    
    def _calculate_content_quality(self, content: str, query: str) -> float:
        """计算内容质量评分"""
        try:
            if not content or len(content) < 50:
                return 0.1
            
            # 基础评分因子
            length_score = min(len(content) / 1000, 1.0) * 0.3  # 内容长度评分
            
            # 关键词匹配评分
            query_words = set(query.lower().split())
            content_words = set(content.lower().split())
            keyword_match = len(query_words.intersection(content_words)) / max(len(query_words), 1)
            keyword_score = keyword_match * 0.4
            
            # 内容结构评分（检查是否有条理）
            structure_indicators = ['1.', '2.', '3.', '首先', '其次', '最后', '总结', '建议']
            structure_score = min(sum(1 for indicator in structure_indicators if indicator in content) / 3, 1.0) * 0.2
            
            # 专业性评分（检查是否包含专业术语）
            professional_terms = ['策略', '分析', '趋势', '发展', '市场', '用户', '数据', '增长', '营销', '品牌']
            professional_score = min(sum(1 for term in professional_terms if term in content) / 5, 1.0) * 0.1
            
            total_score = length_score + keyword_score + structure_score + professional_score
            
            return min(max(total_score, 0.1), 1.0)  # 确保评分在0.1-1.0之间
            
        except Exception as e:
            logger.error(f"计算内容质量评分失败: {e}")
            return 0.5  # 默认中等质量
    
    async def _integrate_search_results(self, tavily_results: List[Dict], 
                                      pplx_results: List[Dict], progress_tracker) -> Dict[str, Any]:
        """整合搜索结果"""
        try:
            await progress_tracker.update_progress(14, "正在提炼关键市场洞察...")
            
            all_results = tavily_results + pplx_results
            
            if not all_results:
                return {"error": "没有获取到有效的搜索结果"}
            
            # 按类型分类结果
            categorized_results = {
                "trend": [],
                "strategy": [],
                "competition": []
            }
            
            for result in all_results:
                result_type = result.get("type", "unknown")
                if result_type in categorized_results:
                    categorized_results[result_type].append(result)
            
            # 如果AI可用，生成智能洞察
            if self.ai_available:
                insights = await self._generate_ai_insights(categorized_results, progress_tracker)
            else:
                insights = self._generate_basic_insights(categorized_results)
            
            return {
                "categorized_results": categorized_results,
                "insights": insights,
                "result_counts": {k: len(v) for k, v in categorized_results.items()},
                "total_results": len(all_results)
            }
            
        except Exception as e:
            logger.error(f"整合搜索结果失败: {e}")
            return {"error": f"整合搜索结果失败: {str(e)}"}
    
    async def _generate_ai_insights(self, categorized_results: Dict, progress_tracker) -> Dict[str, str]:
        """生成AI洞察"""
        try:
            await progress_tracker.update_progress(14, "正在提炼行业核心趋势和机会...")
            
            # 构建分析提示
            analysis_data = ""
            for category, results in categorized_results.items():
                if results:
                    analysis_data += f"\n{category.upper()}相关信息：\n"
                    for result in results[:2]:  # 每类取前2个结果
                        analysis_data += f"- {result['content'][:200]}...\n"
            
            prompt = f"""
基于以下搜索结果，提供专业的行业洞察分析：

{analysis_data}

请从以下角度进行分析：
1. 关键趋势洞察：当前最重要的行业趋势是什么？
2. 策略建议：基于成功案例，有哪些可复制的策略？
3. 竞争格局：当前竞争环境的特点和机会在哪里？
4. 行动指南：具体的执行建议是什么？

请以简洁、实用的方式回答，每个角度控制在100字以内：
"""
            
            success, result = self.gemini(
                "你是一位资深的行业分析专家，擅长从大量信息中提取核心洞察。",
                prompt
            )
            
            if success:
                # 解析AI分析结果
                insights = {
                    "trend_insights": "从搜索结果中发现的关键趋势",
                    "strategy_recommendations": "基于成功案例的策略建议",
                    "competitive_analysis": "竞争格局和机会分析", 
                    "action_guidelines": "具体的执行指南"
                }
                
                # 简单的结果解析
                lines = result.split('\n')
                current_section = None
                
                for line in lines:
                    line = line.strip()
                    if '趋势' in line and '洞察' in line:
                        current_section = 'trend_insights'
                    elif '策略' in line and '建议' in line:
                        current_section = 'strategy_recommendations'
                    elif '竞争' in line and ('格局' in line or '分析' in line):
                        current_section = 'competitive_analysis'
                    elif '行动' in line and ('指南' in line or '建议' in line):
                        current_section = 'action_guidelines'
                    elif line and current_section and not line.startswith(('1.', '2.', '3.', '4.')):
                        insights[current_section] = line
                
                return insights
            
            # AI失败时的备用分析
            return self._generate_basic_insights(categorized_results)
            
        except Exception as e:
            logger.error(f"生成AI洞察失败: {e}")
            return self._generate_basic_insights(categorized_results)
    
    def _generate_basic_insights(self, categorized_results: Dict) -> Dict[str, str]:
        """生成基础洞察"""
        return {
            "trend_insights": f"发现{len(categorized_results.get('trend', []))}个趋势相关信息",
            "strategy_recommendations": f"收集{len(categorized_results.get('strategy', []))}个策略案例",
            "competitive_analysis": f"分析{len(categorized_results.get('competition', []))}个竞争相关信息",
            "action_guidelines": "基于搜索结果建议关注行业动态和竞争对手策略"
        }


# 全局实例
_deep_research_instance = None

def get_deep_research_instance() -> SimplifiedDeepResearch:
    """获取深度研究实例（单例模式）"""
    global _deep_research_instance
    if _deep_research_instance is None:
        _deep_research_instance = SimplifiedDeepResearch()
    return _deep_research_instance

async def conduct_simplified_deep_research(account_info: Dict, industry: str, progress_tracker) -> Tuple[bool, Dict]:
    """执行简化深度研究的便捷函数"""
    try:
        research_instance = get_deep_research_instance()
        
        # 1. 扩展关键词
        keywords = await research_instance.expand_search_keywords(account_info, industry)
        logger.info(f"扩展关键词完成: {len(keywords)}个关键词")
        
        # 2. 执行深度搜索
        search_results = await research_instance.conduct_deep_search(keywords, progress_tracker)
        
        if "error" in search_results:
            return False, search_results
        
        return True, search_results
        
    except Exception as e:
        logger.error(f"简化深度研究失败: {e}")
        return False, {"error": f"深度研究失败: {str(e)}"}