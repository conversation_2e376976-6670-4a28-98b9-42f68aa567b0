"""
Enhanced Diagnosis Service with Robust Error Handling
增强的诊断服务 - 集成智能错误处理和深度研究优化
"""

import asyncio
import time
import json
from typing import Dict, Tuple, Optional, Any, Callable
from colorama import Fore, Style

try:
    from .enhanced_base_service import EnhancedBaseAsyncService
    from .diagnosis_core import process_task_async as diagnosis_process_task
    from .enhanced_error_handling import CircuitBreakerConfig, ErrorCategory, ErrorSeverity
except ImportError:
    from enhanced_base_service import EnhancedBaseAsyncService
    from diagnosis_core import process_task_async as diagnosis_process_task
    from enhanced_error_handling import CircuitBreakerConfig, ErrorCategory, ErrorSeverity

class EnhancedDiagnosisService(EnhancedBaseAsyncService):
    """Enhanced Diagnosis Service with comprehensive error handling"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", 
                 timeout: int = 300, use_service_lock: bool = True, 
                 redis_connection_timeout: int = 30, redis_socket_timeout: int = 30):
        
        # Initialize parent with enhanced error handling
        super().__init__(
            "diagnosis", 
            f"{self._get_env().lower()}:q:diagnosis:request",
            f"{self._get_env().lower()}:q:diagnosis:response",
            max_concurrent_tasks, 
            run_mode, 
            timeout, 
            use_service_lock, 
            redis_connection_timeout, 
            redis_socket_timeout
        )
        
        # Setup diagnosis-specific circuit breakers
        self._setup_diagnosis_circuit_breakers()
        
        # Setup diagnosis-specific logging
        self._setup_diagnosis_core_logging()
        
        # Performance tracking for diagnosis-specific operations
        self.diagnosis_metrics = {
            "total_diagnoses": 0,
            "successful_diagnoses": 0,
            "failed_diagnoses": 0,
            "deep_research_attempts": 0,
            "deep_research_successes": 0,
            "fallback_to_basic": 0
        }
        
        self.logger.info(f"{Fore.MAGENTA}🏥 Enhanced Diagnosis Service initialized with advanced error handling{Style.RESET_ALL}")
    
    def _get_env(self):
        """Get environment variable"""
        try:
            from task import ENV
            return ENV
        except ImportError:
            return "production"
    
    def _setup_diagnosis_circuit_breakers(self):
        """Setup diagnosis-specific circuit breakers"""
        self.logger.info(f"{Fore.CYAN}🔧 Setting up diagnosis-specific circuit breakers{Style.RESET_ALL}")
        
        # Deep research circuit breaker - more tolerant
        deep_research_config = CircuitBreakerConfig(
            failure_threshold=2,  # Allow 2 failures before tripping
            recovery_timeout=45,   # 45 seconds recovery
            timeout=180.0,         # 3 minutes for deep research
            success_threshold=2    # Need 2 successes to close
        )
        self.error_handler.add_circuit_breaker("deep_research", deep_research_config)
        
        # HTML generation circuit breaker
        html_gen_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=90.0
        )
        self.error_handler.add_circuit_breaker("html_generation", html_gen_config)
        
        # JSON generation circuit breaker
        json_gen_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=120.0
        )
        self.error_handler.add_circuit_breaker("json_generation", json_gen_config)
        
        # Sales proposal circuit breaker
        sales_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=120.0
        )
        self.error_handler.add_circuit_breaker("sales_proposal", sales_config)
        
        self.logger.info(f"{Fore.GREEN}✅ Diagnosis circuit breakers configured{Style.RESET_ALL}")
    
    def _setup_diagnosis_core_logging(self):
        """Setup logging for diagnosis_core module"""
        import logging
        
        # Get diagnosis_core logger
        core_logger = logging.getLogger('task.server.account_diagnosis.diagnosis_core')
        core_logger.setLevel(logging.INFO)
        core_logger.handlers.clear()
        
        # Copy handlers from main service logger
        for handler in self.logger.handlers:
            core_logger.addHandler(handler)
        
        self.logger.info(f"{Fore.CYAN}📝 Diagnosis core logging configured{Style.RESET_ALL}")
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """Enhanced diagnosis task processing with comprehensive error handling"""
        start_time = time.time()
        
        try:
            # Validate input data
            validation_result = await self._validate_diagnosis_input(input_data)
            if not validation_result["valid"]:
                self.diagnosis_metrics["failed_diagnoses"] += 1
                return False, {"error": f"Input validation failed: {validation_result['error']}"}
            
            # Log diagnosis start with account info
            account_info = input_data.get("accountInfo", {})
            account_name = account_info.get("nickname", "Unknown Account")
            marketing_goal = input_data.get("marketingGoal", "Unknown Goal")
            
            self.logger.info(f"{Fore.MAGENTA}🏥 Starting diagnosis for account: {account_name} | Goal: {marketing_goal}{Style.RESET_ALL}")
            
            # Enhanced task processing with circuit breaker
            success, results = await self.enhanced_task_processing(
                self._enhanced_diagnosis_process,
                input_data
            )
            
            duration = time.time() - start_time
            
            if success:
                self.diagnosis_metrics["successful_diagnoses"] += 1
                self.log_performance_metric("diagnosis_duration", duration, "s")
                
                # Log result summary
                if isinstance(results, dict):
                    generated_reports = [key for key in results.keys() if results[key]]
                    self.logger.info(f"{Fore.GREEN}📋 Diagnosis completed successfully - Generated: {generated_reports}{Style.RESET_ALL}")
                
                return True, results
            else:
                self.diagnosis_metrics["failed_diagnoses"] += 1
                self.log_operation_error("Diagnosis", results.get('error', 'Unknown error'))
                return False, results
                
        except Exception as e:
            duration = time.time() - start_time
            self.diagnosis_metrics["failed_diagnoses"] += 1
            self.log_operation_error("Diagnosis Task", str(e))
            self.log_performance_metric("failed_diagnosis_duration", duration, "s")
            
            return False, {"error": f"Diagnosis task failed: {str(e)}"}
        finally:
            self.diagnosis_metrics["total_diagnoses"] += 1
    
    async def _validate_diagnosis_input(self, input_data: Dict) -> Dict[str, Any]:
        """Validate diagnosis input data with detailed feedback"""
        try:
            self.log_operation_start("Input Validation")
            
            required_fields = ["accountInfo", "noteList", "marketingGoal"]
            missing_fields = []
            
            for field in required_fields:
                if field not in input_data:
                    missing_fields.append(field)
            
            if missing_fields:
                error_msg = f"Missing required fields: {missing_fields}"
                self.log_operation_error("Input Validation", error_msg)
                return {"valid": False, "error": error_msg}
            
            # Validate account info
            account_info = input_data["accountInfo"]
            if not isinstance(account_info, dict) or not account_info.get("nickname"):
                error_msg = "Invalid accountInfo: missing nickname"
                self.log_operation_error("Input Validation", error_msg)
                return {"valid": False, "error": error_msg}
            
            # Validate note list
            note_list = input_data["noteList"]
            if not isinstance(note_list, list) or len(note_list) == 0:
                self.log_operation_warning("Input Validation", "Empty note list - diagnosis quality may be limited")
            
            # Validate marketing goal
            valid_goals = ["引流私域", "带货变现", "品牌曝光", "涨粉提升"]
            marketing_goal = input_data["marketingGoal"]
            if marketing_goal not in valid_goals:
                self.log_operation_warning("Input Validation", f"Unusual marketing goal: {marketing_goal}")
            
            self.log_operation_success("Input Validation")
            
            return {
                "valid": True,
                "account_name": account_info.get("nickname"),
                "note_count": len(note_list),
                "marketing_goal": marketing_goal
            }
            
        except Exception as e:
            error_msg = f"Validation exception: {str(e)}"
            self.log_operation_error("Input Validation", error_msg)
            return {"valid": False, "error": error_msg}
    
    async def _enhanced_diagnosis_process(self, input_data: Dict) -> Tuple[bool, Dict]:
        """Enhanced diagnosis process with intelligent fallback and monitoring"""
        try:
            # Attempt deep research mode first
            deep_research_enabled = True
            
            # Check if deep research circuit breaker is open
            deep_research_cb = self.error_handler.circuit_breakers.get("deep_research")
            if deep_research_cb and deep_research_cb.state.value == "open":
                self.logger.warning(f"{Fore.YELLOW}🚫 Deep research circuit breaker is OPEN - using basic mode{Style.RESET_ALL}")
                deep_research_enabled = False
                self.diagnosis_metrics["fallback_to_basic"] += 1
            
            if deep_research_enabled:
                try:
                    self.diagnosis_metrics["deep_research_attempts"] += 1
                    self.logger.info(f"{Fore.CYAN}🔍 Attempting diagnosis with deep research{Style.RESET_ALL}")
                    
                    # Use deep research with circuit breaker
                    success, results = await self.enhanced_deep_research_call(
                        diagnosis_process_task,
                        input_data,
                        enable_deep_research=True
                    )
                    
                    if success:
                        self.diagnosis_metrics["deep_research_successes"] += 1
                        self.logger.info(f"{Fore.GREEN}🎯 Deep research diagnosis completed successfully{Style.RESET_ALL}")
                        return success, results
                    else:
                        self.logger.warning(f"{Fore.YELLOW}⚠️  Deep research failed, falling back to basic mode: {results}{Style.RESET_ALL}")
                        deep_research_enabled = False
                        self.diagnosis_metrics["fallback_to_basic"] += 1
                        
                except Exception as e:
                    self.logger.warning(f"{Fore.YELLOW}⚠️  Deep research exception, falling back to basic mode: {str(e)}{Style.RESET_ALL}")
                    deep_research_enabled = False
                    self.diagnosis_metrics["fallback_to_basic"] += 1
            
            # Basic mode fallback
            if not deep_research_enabled:
                self.logger.info(f"{Fore.BLUE}🔧 Using basic diagnosis mode{Style.RESET_ALL}")
                
                success, results = await self.enhanced_task_processing(
                    diagnosis_process_task,
                    {**input_data, "enable_deep_research": False}
                )
                
                if success:
                    self.logger.info(f"{Fore.GREEN}✅ Basic diagnosis completed successfully{Style.RESET_ALL}")
                else:
                    self.logger.error(f"{Fore.RED}❌ Basic diagnosis also failed: {results}{Style.RESET_ALL}")
                
                return success, results
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}💥 Diagnosis process failed completely: {str(e)}{Style.RESET_ALL}")
            return False, {"error": f"Diagnosis process failed: {str(e)}"}
    
    async def enhanced_deep_research_call(self, research_func: Callable, *args, **kwargs):
        """Enhanced deep research call with specialized error handling"""
        start_time = time.time()
        research_name = getattr(research_func, '__name__', 'unknown_research')
        
        try:
            self.log_operation_start(f"Deep Research {research_name}", {
                "function": research_name,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys())
            })
            
            result = await self.error_handler.handle_with_circuit_breaker(
                research_func,
                "deep_research",
                f"research_{research_name}",
                {
                    "service": self.service_name,
                    "research_function": research_name
                },
                *args, **kwargs
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"Deep Research {research_name}", duration)
            self._record_operation_metric(f"research_{research_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"Deep Research {research_name}", str(e))
            self._record_operation_metric(f"research_{research_name}", duration, False)
            raise
    
    async def get_diagnosis_metrics(self) -> Dict[str, Any]:
        """Get diagnosis-specific metrics"""
        base_metrics = await self.get_operation_metrics()
        
        # Calculate success rates
        total_diagnoses = self.diagnosis_metrics["total_diagnoses"]
        success_rate = (self.diagnosis_metrics["successful_diagnoses"] / total_diagnoses * 100) if total_diagnoses > 0 else 0
        
        deep_research_attempts = self.diagnosis_metrics["deep_research_attempts"]
        deep_research_success_rate = (self.diagnosis_metrics["deep_research_successes"] / deep_research_attempts * 100) if deep_research_attempts > 0 else 0
        
        fallback_rate = (self.diagnosis_metrics["fallback_to_basic"] / total_diagnoses * 100) if total_diagnoses > 0 else 0
        
        diagnosis_specific = {
            "diagnosis_metrics": self.diagnosis_metrics.copy(),
            "success_rate": success_rate,
            "deep_research_success_rate": deep_research_success_rate,
            "fallback_rate": fallback_rate,
            "recommendations": self._get_performance_recommendations()
        }
        
        return {**base_metrics, **diagnosis_specific}
    
    def _get_performance_recommendations(self) -> list:
        """Get performance recommendations based on metrics"""
        recommendations = []
        
        total_diagnoses = self.diagnosis_metrics["total_diagnoses"]
        if total_diagnoses == 0:
            return ["No diagnoses processed yet"]
        
        success_rate = (self.diagnosis_metrics["successful_diagnoses"] / total_diagnoses * 100)
        fallback_rate = (self.diagnosis_metrics["fallback_to_basic"] / total_diagnoses * 100)
        
        if success_rate < 80:
            recommendations.append("🔴 Low success rate - investigate error patterns")
        
        if fallback_rate > 30:
            recommendations.append("🟡 High fallback rate - check deep research service health")
        
        deep_research_attempts = self.diagnosis_metrics["deep_research_attempts"]
        if deep_research_attempts > 0:
            deep_research_success_rate = (self.diagnosis_metrics["deep_research_successes"] / deep_research_attempts * 100)
            if deep_research_success_rate < 60:
                recommendations.append("🟡 Deep research struggling - consider tuning parameters")
        
        # Check circuit breaker states
        for name, cb in self.error_handler.circuit_breakers.items():
            if cb.state.value == "open":
                recommendations.append(f"🔴 Circuit breaker '{name}' is OPEN - investigate underlying issues")
            elif cb.state.value == "half_open":
                recommendations.append(f"🟡 Circuit breaker '{name}' is HALF_OPEN - monitoring recovery")
        
        if not recommendations:
            recommendations.append("🟢 All metrics look healthy")
        
        return recommendations
    
    async def get_enhanced_diagnosis_health(self) -> Dict[str, Any]:
        """Get comprehensive diagnosis service health"""
        base_health = await self.get_enhanced_service_health()
        diagnosis_metrics = await self.get_diagnosis_metrics()
        
        # Calculate diagnosis-specific health score
        diagnosis_health_score = self._calculate_diagnosis_health_score()
        
        return {
            **base_health,
            "diagnosis_health_score": diagnosis_health_score,
            "diagnosis_metrics": diagnosis_metrics["diagnosis_metrics"],
            "performance_recommendations": diagnosis_metrics["recommendations"]
        }
    
    def _calculate_diagnosis_health_score(self) -> float:
        """Calculate diagnosis-specific health score"""
        base_score = 100.0
        
        total_diagnoses = self.diagnosis_metrics["total_diagnoses"]
        if total_diagnoses == 0:
            return base_score
        
        # Success rate impact
        success_rate = (self.diagnosis_metrics["successful_diagnoses"] / total_diagnoses)
        if success_rate < 0.8:
            base_score -= (0.8 - success_rate) * 100
        
        # Fallback rate impact
        fallback_rate = (self.diagnosis_metrics["fallback_to_basic"] / total_diagnoses)
        if fallback_rate > 0.3:
            base_score -= (fallback_rate - 0.3) * 50
        
        # Circuit breaker impact
        open_breakers = sum(1 for cb in self.error_handler.circuit_breakers.values() if cb.state.value == "open")
        base_score -= open_breakers * 15
        
        return max(0.0, min(100.0, base_score))
    
    async def emergency_reset(self):
        """Emergency reset for diagnosis service"""
        self.logger.warning(f"{Fore.RED}🚨 EMERGENCY RESET initiated for diagnosis service{Style.RESET_ALL}")
        
        # Reset all circuit breakers
        await self.reset_circuit_breakers()
        
        # Clear metrics
        self.diagnosis_metrics = {
            "total_diagnoses": 0,
            "successful_diagnoses": 0,
            "failed_diagnoses": 0,
            "deep_research_attempts": 0,
            "deep_research_successes": 0,
            "fallback_to_basic": 0
        }
        
        # Clear operation metrics
        self.operation_metrics.clear()
        
        self.logger.info(f"{Fore.GREEN}✅ Emergency reset completed{Style.RESET_ALL}")
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check"""
        self.logger.info(f"{Fore.CYAN}🏥 Running comprehensive health check{Style.RESET_ALL}")
        
        # Ensure Redis pool is created
        if not self.redis_manager.pool:
            await self.redis_manager.create_pool()
        
        health_results = {
            "timestamp": time.time(),
            "service_name": self.service_name,
            "checks": {}
        }
        
        # Test Redis connectivity
        try:
            await self.enhanced_redis_operation("health_check", self.redis_manager.pool.ping)
            health_results["checks"]["redis"] = {"status": "healthy", "message": "Redis connection OK"}
            self.logger.info(f"{Fore.GREEN}✅ Redis health check passed{Style.RESET_ALL}")
        except Exception as e:
            health_results["checks"]["redis"] = {"status": "unhealthy", "message": str(e)}
            self.logger.error(f"{Fore.RED}❌ Redis health check failed: {str(e)}{Style.RESET_ALL}")
        
        # Test AI service connectivity (mock test)
        try:
            # This would be a lightweight AI service test
            health_results["checks"]["ai_service"] = {"status": "healthy", "message": "AI service accessible"}
            self.logger.info(f"{Fore.GREEN}✅ AI service health check passed{Style.RESET_ALL}")
        except Exception as e:
            health_results["checks"]["ai_service"] = {"status": "unhealthy", "message": str(e)}
            self.logger.error(f"{Fore.RED}❌ AI service health check failed: {str(e)}{Style.RESET_ALL}")
        
        # Get overall health
        overall_health = await self.get_enhanced_diagnosis_health()
        health_results["overall_health"] = overall_health
        
        return health_results