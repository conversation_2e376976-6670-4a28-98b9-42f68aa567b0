"""
分层查询生成器
专注于提升prompt质量和查询生成效率
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
from colorama import Fore

# 导入模板管理器
from .prompt_templates import (
    get_template_manager, 
    get_industry_keywords, 
    get_industry_trending_topics,
    IndustryType
)

logger = logging.getLogger(__name__)

def get_dynamic_year_range() -> Tuple[str, List[str]]:
    """生成动态年份范围，包括当前年份和上一年份"""
    current_year = datetime.now().year
    previous_year = current_year - 1
    
    # 返回当前年份和年份关键词列表
    return str(current_year), [str(current_year), str(previous_year)]

def get_time_related_keywords() -> List[str]:
    """获取时效性相关的关键词"""
    current_year_str, year_keywords = get_dynamic_year_range()
    return year_keywords + ['最新', '热点', '趋势', '前沿', '创新', '突破']

class QueryType(Enum):
    """查询类型枚举"""
    BASIC = "basic"
    INDUSTRY = "industry"
    COMPETITIVE = "competitive"

class AccountStage(Enum):
    """账号发展阶段"""
    STARTUP = "startup"      # 起步阶段：0-1千粉丝
    GROWTH = "growth"        # 成长阶段：1-10千粉丝
    MATURE = "mature"        # 成熟阶段：1万+粉丝

@dataclass
class QueryContext:
    """查询上下文信息"""
    account_name: str
    industry: str
    follower_count: int
    account_stage: AccountStage
    content_themes: List[str]
    recent_performance: Dict
    target_audience: List[str]

class LayeredQueryGenerator:
    """分层查询生成器"""
    
    def __init__(self, config=None):
        # 导入配置
        from .service_config import get_deep_research_config, get_ai_model_config
        
        if config is None:
            config = get_deep_research_config()
        
        self.config = config
        self.gpt_model = config.models.gpt_model
        self.pplx_model = config.models.pplx_model
        self.max_queries_per_layer = config.max_queries_per_layer
        
        # 性能优化配置
        self.enable_caching = config.enable_caching
        self.cache_ttl = config.cache_ttl
        self.max_concurrent_searches = config.max_concurrent_searches
        self.min_search_results = config.min_search_results
        
        # 使用内存智能缓存
        self.cache_manager = None
        logger.info("缓存功能已临时禁用")
        
        # 内存缓存作为后备
        self.query_cache = {}
        
    def _determine_account_stage(self, follower_count: int) -> AccountStage:
        """根据粉丝数确定账号发展阶段"""
        if follower_count < 1000:
            return AccountStage.STARTUP
        elif follower_count < 10000:
            return AccountStage.GROWTH
        else:
            return AccountStage.MATURE
    
    def _extract_query_context(self, account_info: Dict) -> QueryContext:
        """提取查询上下文"""
        try:
            follower_count = account_info.get('follows', 0)
            account_stage = self._determine_account_stage(follower_count)
            
            # 分析最近内容表现
            recent_posts = account_info.get('recent_posts', [])
            recent_performance = {
                'avg_engagement': 0,
                'top_performing_themes': [],
                'engagement_trend': 'stable'
            }
            
            if recent_posts:
                engagements = [post.get('engagement', 0) for post in recent_posts]
                recent_performance['avg_engagement'] = sum(engagements) / len(engagements)
                
                # 简单的主题分析
                if len(recent_posts) > 1:
                    latest_engagement = engagements[-1]
                    prev_engagement = sum(engagements[:-1]) / (len(engagements) - 1)
                    if latest_engagement > prev_engagement * 1.1:
                        recent_performance['engagement_trend'] = 'improving'
                    elif latest_engagement < prev_engagement * 0.9:
                        recent_performance['engagement_trend'] = 'declining'
            
            return QueryContext(
                account_name=account_info.get('account_name', ''),
                industry=account_info.get('industry', ''),
                follower_count=follower_count,
                account_stage=account_stage,
                content_themes=account_info.get('content_themes', []),
                recent_performance=recent_performance,
                target_audience=account_info.get('target_audience', [])
            )
            
        except Exception as e:
            logger.error(f"提取查询上下文失败: {e}")
            # 返回默认上下文
            return QueryContext(
                account_name="未知账号",
                industry="通用",
                follower_count=0,
                account_stage=AccountStage.STARTUP,
                content_themes=[],
                recent_performance={},
                target_audience=[]
            )
    
    async def _call_gpt_for_queries(self, prompt: str, query_type: QueryType) -> List[Dict]:
        """调用GPT生成查询"""
        try:
            # 导入GPT调用函数
            from task import callWattGPT
            
            body = {
                "model": self.gpt_model,
                "messages": [
                    {"role": "user", "content": prompt}
                ],
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": f"{query_type.value}_queries",
                        "schema": {
                            "type": "object",
                            "properties": {
                                "queries": {
                                    "type": "array",
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "content": {"type": "string"},
                                            "focus": {"type": "string"},
                                            "recency_filter": {
                                                "type": "string",
                                                "enum": ["hour", "day", "week", "month", "year"]
                                            },
                                            "priority": {"type": "number", "minimum": 1.0, "maximum": 2.0}
                                        },
                                        "required": ["content", "focus", "recency_filter", "priority"]
                                    }
                                }
                            },
                            "required": ["queries"]
                        }
                    }
                }
            }
            
            gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=60)
            
            if not gpt_status:
                logger.error(f"{query_type.value}查询生成失败: {gpt_result}")
                return []
            
            # 解析GPT响应
            if isinstance(gpt_result, dict) and 'result' in gpt_result:
                result_data = gpt_result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    content = message['content']
                                    queries_data = json.loads(content)
                                    queries = queries_data.get('queries', [])
                                    
                                    # 为每个查询添加类型标识
                                    for query in queries:
                                        query['type'] = query_type.value
                                    
                                    logger.info(f"{query_type.value}查询生成成功，共{len(queries)}个查询")
                                    return queries
            
            logger.error(f"{query_type.value}查询生成失败：响应格式错误")
            return []
            
        except Exception as e:
            logger.error(f"{query_type.value}查询生成异常: {e}")
            return []
    
    def _generate_simplified_queries_prompt(self, context: QueryContext) -> str:
        """生成简化的查询prompt - 依赖Sonar模型智能理解"""
        return f"""
请为{context.industry}行业的账号"{context.account_name}"生成8-12个简洁的搜索查询。

账号信息：
- 行业：{context.industry}
- 粉丝数：{context.follower_count:,}
- 内容主题：{', '.join(context.content_themes)}
- 目标受众：{', '.join(context.target_audience)}

生成要求：
1. 查询要简洁明了，让Sonar模型能够自动理解和扩展
2. 涵盖行业趋势、竞争分析、内容策略等方面
3. 避免过度复杂的术语堆砌
4. 每个查询20-50字符为佳

请按以下JSON格式返回：
{{
    "queries": [
        {{
            "content": "简洁的查询内容",
            "focus": "查询重点",
            "recency_filter": "month",
            "priority": 1.5
        }}
    ]
}}
"""

    def _simple_quality_validation(self, queries: List[Dict], context: QueryContext) -> List[Dict]:
        """简化的质量验证 - 基本检查即可"""
        validated_queries = []
        
        for query in queries:
            # 基本字段检查
            if not all(key in query for key in ['content', 'focus', 'recency_filter', 'priority']):
                continue
            
            content = query['content'].strip()
            
            # 基本长度检查
            if len(content) < 10 or len(content) > 150:
                continue
            
            # 简单的行业相关性检查
            if context.industry.lower() in content.lower() or any(theme.lower() in content.lower() for theme in context.content_themes):
                query['quality_score'] = 0.8  # 高质量
            else:
                query['quality_score'] = 0.6  # 中等质量
            
            validated_queries.append(query)
        
        return validated_queries

    async def _generate_queries_with_dynamic_intelligence(self, context: QueryContext, account_info: Dict) -> List[Dict]:
        """动态查询生成 - 保留PPLX实时信息获取，减少重复调用"""
        try:
            logger.info("使用动态查询生成")
            
            # 1. 生成基础查询（GPT一次调用）
            basic_prompt = self._generate_balanced_queries_prompt(context)
            basic_queries = await self._call_gpt_for_queries(basic_prompt, QueryType.BASIC)

            if not basic_queries:
                logger.warning("基础查询生成失败")
                return []
            print(Fore.GREEN+f"basic_queries: {basic_queries}"+Fore.RESET)
            
            # 2. 核心优化：只进行一次关键的PPLX调用获取实时趋势
            real_time_insights = await self._get_essential_market_insights(context)
            print(Fore.CYAN+f"insights: {real_time_insights}"+Fore.RESET)
            
            # 3. 基于实时信息增强查询
            enhanced_queries = self._enhance_queries_with_real_time_data(basic_queries, real_time_insights, context)
            print(Fore.GREEN+f"enhanced queries: {enhanced_queries}"+Fore.RESET)
            
            # 4. 质量验证
            validated_queries = self._balanced_quality_validation(enhanced_queries, context)
            
            # 5. 按优先级排序
            validated_queries.sort(key=lambda x: x.get('priority', 1.0), reverse=True)
            
            logger.info(f"平衡优化查询生成完成：{len(validated_queries)}个查询（含实时信息）")
            return validated_queries
            
        except Exception as e:
            logger.error(f"平衡优化查询生成失败: {e}")
            return []

    def _generate_balanced_queries_prompt(self, context: QueryContext) -> str:
        """生成平衡的查询prompt - 自适应查询数量和分布"""
        # 根据账号阶段和粉丝数确定查询数量和分布
        query_config = self._get_adaptive_query_config(context)
        
        # 获取行业特定的关键词和模板
        industry_keywords = self._get_industry_specific_keywords(context.industry)
        stage_focus = self._get_stage_specific_focus(context.account_stage)
        
        return f"""
请为{context.industry}行业的账号"{context.account_name}"生成{query_config['total_queries']}个深度研究查询，基于账号发展阶段进行智能分布和优先级调整。

账号信息：
- 行业：{context.industry}
- 粉丝数：{context.follower_count:,}
- 发展阶段：{context.account_stage.value}
- 内容主题：{', '.join(context.content_themes)}
- 目标受众：{', '.join(context.target_audience)}

行业关键词：{', '.join(industry_keywords)}
阶段重点：{stage_focus}

智能查询分布（基于账号发展阶段）：
1. 行业前沿热点（{query_config['hotspot']}个查询）- 关注最新技术、趋势、政策变化
   优先级：{query_config['hotspot_priority']}
2. 营销方法论（{query_config['methodology']}个查询）- 学习成功的营销策略和工具
   优先级：{query_config['methodology_priority']}
3. 头部KOL/竞争对手（{query_config['kol_competitor']}个查询）- 分析成功要素和差异化策略
   优先级：{query_config['kol_priority']}
4. 市场机会挖掘（{query_config['opportunity']}个查询）- 发现细分市场和增长机会
   优先级：{query_config['opportunity_priority']}
5. 案例深度研究（{query_config['case_study']}个查询）- 跨行业成功案例学习
   优先级：{query_config['case_priority']}

生成要求：
1. 查询要具体明确，能够获取实时的行业洞察
2. 重点关注可学习和可复制的成功模式
3. 包含具体的行业关键词和时效性词汇：{', '.join(industry_keywords[:5])}
4. 每个查询15-40字符，确保搜索精准性和覆盖面
5. 基于账号发展阶段调整查询深度和复杂度

质量标准：
- 查询内容包含具体的行业术语和时效性词汇
- 能够获取可操作的实时信息
- 具有明确的分析价值和应用场景
- 适配当前账号发展阶段的实际需求

请按以下JSON格式返回：
{{
    "queries": [
        {{
            "content": "具体的查询内容",
            "focus": "行业前沿热点/营销方法论/头部KOL分析/竞争对手研究/市场机会挖掘/案例深度研究",
            "recency_filter": "month",
            "priority": 1.5,
            "analysis_type": "hotspot_focused/methodology_learning/kol_analysis/competitor_research/opportunity_focused/case_study",
            "needs_real_time": true,
            "specificity_score": 0.8,
            "relevance_score": 0.9
        }}
    ]
}}
"""

    async def _get_essential_market_insights(self, context: QueryContext) -> Dict:
        """获取核心市场洞察 - 优化的单次PPLX调用，重点关注行业前沿热点、营销方法论和KOL分析"""
        try:
            # 构建一个综合性的实时信息查询，涵盖多个关键维度
            current_year, _ = get_dynamic_year_range()
            market_query = f"{context.industry}行业{current_year}年前沿热点 头部KOL成功要素 创新营销方法论 竞争对手分析 市场机会挖掘"
            
            logger.info(f"获取实时市场洞察（涵盖热点、KOL、营销方法论）：{market_query}")
            
            # 使用真实的PPLX API调用
            from task import callWattGPT
            
            # 构建PPLX搜索请求
            search_request = {
                "model": self.pplx_model,
                "messages": [
                    {"role": "user", "content": market_query}
                ],
                "search_recency_filter": "month"
            }
            
            # 添加重试机制
            max_retries = self.config.max_search_retries
            timeouts = self.config.search_timeouts or [30, 45, 60]
            
            for attempt in range(max_retries):
                timeout = timeouts[attempt]
                logger.info(f"PPLX搜索尝试 {attempt + 1}/{max_retries} (超时: {timeout}秒)")
                
                try:
                    # 执行搜索
                    pplx_status, code, pplx_result = callWattGPT.callPplxChannelChatCompletions(
                        search_request, timeout=timeout
                    )
                    
                    if pplx_status:
                        logger.info(f"PPLX搜索成功 (第{attempt + 1}次尝试)")
                        break
                    else:
                        logger.warning(f"PPLX搜索失败 (第{attempt + 1}次尝试): {pplx_result}")
                        if attempt < max_retries - 1:
                            logger.info("将在2秒后重试...")
                            await asyncio.sleep(2)
                            
                except Exception as e:
                    logger.error(f"PPLX搜索异常 (第{attempt + 1}次尝试): {str(e)}")
                    if attempt < max_retries - 1:
                        logger.info("将在3秒后重试...")
                        await asyncio.sleep(3)
            
            if not pplx_status:
                logger.error(f"PPLX搜索失败: {pplx_result}")
                return {}
            
            # 解析搜索结果
            search_content = ""
            search_citations = []
            
            # 根据实际的API返回格式解析
            try:
                if isinstance(pplx_result, dict):
                    # 直接是字典格式
                    result_data = pplx_result
                    choices = result_data.get('result', {}).get('data', {}).get('choices', [])
                    if choices:
                        search_content = choices[0].get('message', {}).get('content', '')
                        search_citations = result_data.get('result', {}).get('data', {}).get('citations', [])
                else:
                    logger.warning("PPLX搜索结果格式异常")
                    return {}
            except (KeyError, IndexError, TypeError) as e:
                logger.error(f"解析PPLX结果失败: {e}")
                return {}
            
            if not search_content:
                logger.warning("PPLX搜索未返回有效内容")
                return {}

            # 使用GPT分析搜索结果，重点提取多维度信息
            analysis_prompt = f"""
请从以下搜索结果中提取{context.industry}行业的关键信息，重点关注多个维度的深度洞察：

搜索内容：
{search_content}

请提取以下信息并以JSON格式返回：
1. industry_hotspots: 3-5个行业前沿热点和新兴趋势
2. marketing_methodologies: 3-5个成功的营销方法论和策略
3. leading_kol_insights: 3-5个头部KOL的成功要素和策略
4. competitor_analysis: 3-5个竞争对手的优势和差异化策略
5. market_opportunities: 3-5个市场机会和增长空间
6. success_patterns: 3-5个成功模式和关键成功因素
7. innovation_strategies: 3-5个创新策略和差异化方法
8. hot_keywords: 8-12个相关热门关键词

JSON格式：
{{
    "industry_hotspots": ["热点1", "热点2", "热点3"],
    "marketing_methodologies": ["方法论1", "方法论2", "方法论3"],
    "leading_kol_insights": ["KOL洞察1", "KOL洞察2", "KOL洞察3"],
    "competitor_analysis": ["竞争分析1", "竞争分析2", "竞争分析3"],
    "market_opportunities": ["机会1", "机会2", "机会3"],
    "success_patterns": ["模式1", "模式2", "模式3"],
    "innovation_strategies": ["创新策略1", "创新策略2", "创新策略3"],
    "hot_keywords": ["关键词1", "关键词2", "关键词3", "关键词4"],
    "timestamp": "{datetime.now().strftime('%Y-%m-%d')}"
}}
"""
            
            # 调用GPT分析，也添加重试机制
            analysis_body = {
                "model": self.gpt_model,
                "messages": [
                    {"role": "user", "content": analysis_prompt}
                ],
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "market_insights",
                        "schema": {
                            "type": "object",
                            "properties": {
                                "industry_hotspots": {"type": "array", "items": {"type": "string"}},
                                "marketing_methodologies": {"type": "array", "items": {"type": "string"}},
                                "leading_kol_insights": {"type": "array", "items": {"type": "string"}},
                                "competitor_analysis": {"type": "array", "items": {"type": "string"}},
                                "market_opportunities": {"type": "array", "items": {"type": "string"}},
                                "success_patterns": {"type": "array", "items": {"type": "string"}},
                                "innovation_strategies": {"type": "array", "items": {"type": "string"}},
                                "hot_keywords": {"type": "array", "items": {"type": "string"}},
                                "timestamp": {"type": "string"}
                            },
                            "required": ["industry_hotspots", "marketing_methodologies", "leading_kol_insights", "competitor_analysis", "market_opportunities", "success_patterns", "innovation_strategies", "hot_keywords"]
                        }
                    }
                }
            }
            
            # GPT分析重试机制
            max_gpt_retries = self.config.max_analysis_retries
            gpt_timeouts = self.config.analysis_timeouts or [15, 30]
            
            for attempt in range(max_gpt_retries):
                timeout = gpt_timeouts[attempt]
                logger.info(f"GPT分析尝试 {attempt + 1}/{max_gpt_retries} (超时: {timeout}秒)")
                
                try:
                    gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(
                        analysis_body, timeout=timeout
                    )
                    
                    if gpt_status:
                        logger.info(f"GPT分析成功 (第{attempt + 1}次尝试)")
                        break
                    else:
                        logger.warning(f"GPT分析失败 (第{attempt + 1}次尝试): {gpt_result}")
                        if attempt < max_gpt_retries - 1:
                            logger.info("将在2秒后重试...")
                            await asyncio.sleep(2)
                            
                except Exception as e:
                    logger.error(f"GPT分析异常 (第{attempt + 1}次尝试): {str(e)}")
                    if attempt < max_gpt_retries - 1:
                        logger.info("将在3秒后重试...")
                        await asyncio.sleep(3)
            
            if not gpt_status:
                logger.error(f"GPT分析失败: {gpt_result}")
                # 返回基础的解析结果
                return {
                    "industry_hotspots": [f"{context.industry}行业前沿热点"],
                    "marketing_methodologies": ["创新营销方法论"],
                    "leading_kol_insights": ["头部KOL成功要素"],
                    "competitor_analysis": ["竞争对手分析"],
                    "market_opportunities": ["市场机会挖掘"],
                    "success_patterns": ["成功模式研究"],
                    "innovation_strategies": ["创新策略分析"],
                    "hot_keywords": [context.industry] + get_dynamic_year_range()[1],
                    "timestamp": datetime.now().strftime('%Y-%m-%d'),
                    "raw_content": search_content[:500]  # 保留原始内容前500字符
                }
            
            # 解析GPT分析结果
            try:
                if isinstance(gpt_result, dict) and 'result' in gpt_result:
                    result_data = gpt_result['result']
                    if isinstance(result_data, dict) and 'data' in result_data:
                        data = result_data['data']
                        if isinstance(data, dict) and 'choices' in data:
                            choices = data['choices']
                            if isinstance(choices, list) and len(choices) > 0:
                                choice = choices[0]
                                if isinstance(choice, dict) and 'message' in choice:
                                    message = choice['message']
                                    if isinstance(message, dict) and 'content' in message:
                                        content = message['content']
                                        insights = json.loads(content)
                                        insights['citations'] = search_citations
                                        insights['raw_content'] = search_content[:500]
                                        
                                        logger.info("实时市场洞察获取成功（涵盖多维度洞察）")
                                        return insights
            except (json.JSONDecodeError, KeyError, TypeError) as e:
                logger.error(f"解析GPT分析结果失败: {e}")
            
            # 如果解析失败，返回基础信息
            return {
                "industry_hotspots": [f"{context.industry}行业发展热点"],
                "marketing_methodologies": ["营销方法论研究"],
                "leading_kol_insights": ["头部KOL洞察"],
                "competitor_analysis": ["竞争对手研究"],
                "market_opportunities": ["市场机会分析"],
                "success_patterns": ["成功模式研究"],
                "innovation_strategies": ["创新策略分析"],
                "hot_keywords": [context.industry] + get_dynamic_year_range()[1],
                "timestamp": datetime.now().strftime('%Y-%m-%d'),
                "raw_content": search_content[:500]
            }
            
        except Exception as e:
            logger.error(f"获取实时市场洞察失败: {e}")
            return {}

    def _enhance_queries_with_real_time_data(self, basic_queries: List[Dict], insights: Dict, context: QueryContext) -> List[Dict]:
        """基于实时数据增强查询"""
        if not insights:
            logger.warning("无实时数据，返回原始查询")
            return basic_queries
        
        enhanced_queries = []
        industry_hotspots = insights.get("industry_hotspots", [])
        marketing_methodologies = insights.get("marketing_methodologies", [])
        leading_kol_insights = insights.get("leading_kol_insights", [])
        competitor_analysis = insights.get("competitor_analysis", [])
        market_opportunities = insights.get("market_opportunities", [])
        success_patterns = insights.get("success_patterns", [])
        innovation_strategies = insights.get("innovation_strategies", [])
        hot_keywords = insights.get("hot_keywords", [])
        
        for query in basic_queries:
            enhanced_query = query.copy()
            original_content = query["content"]
            
            # 基于查询焦点进行智能增强
            if query["focus"] == "行业前沿热点":
                # 使用行业热点信息增强
                if industry_hotspots:
                    hotspot = industry_hotspots[0]
                    if hotspot not in original_content:
                        enhanced_query["content"] = f"{original_content} {hotspot}"
                        enhanced_query["enhanced_with"] = "industry_hotspot"
                        
            elif query["focus"] == "营销方法论":
                # 使用营销方法论信息增强
                if marketing_methodologies:
                    methodology = marketing_methodologies[0]
                    if methodology not in original_content:
                        enhanced_query["content"] = f"{original_content} {methodology}"
                        enhanced_query["enhanced_with"] = "marketing_methodology"
                        
            elif query["focus"] == "头部KOL分析":
                # 使用头部KOL洞察增强
                if leading_kol_insights:
                    kol_insight = leading_kol_insights[0]
                    if kol_insight not in original_content:
                        enhanced_query["content"] = f"{original_content} {kol_insight}"
                        enhanced_query["enhanced_with"] = "kol_insight"
                        
            elif query["focus"] == "竞争对手研究":
                # 使用竞争对手分析增强
                if competitor_analysis:
                    competitor = competitor_analysis[0]
                    if competitor not in original_content:
                        enhanced_query["content"] = f"{original_content} {competitor}"
                        enhanced_query["enhanced_with"] = "competitor_analysis"
                        
            elif query["focus"] == "市场机会挖掘":
                # 使用市场机会信息增强
                if market_opportunities:
                    opportunity = market_opportunities[0]
                    if opportunity not in original_content:
                        enhanced_query["content"] = f"{original_content} {opportunity}"
                        enhanced_query["enhanced_with"] = "market_opportunity"
                        
            elif query["focus"] == "案例深度研究":
                # 使用成功模式增强
                if success_patterns:
                    pattern = success_patterns[0]
                    if pattern not in original_content:
                        enhanced_query["content"] = f"{original_content} {pattern}"
                        enhanced_query["enhanced_with"] = "success_pattern"
                        
            elif hot_keywords:
                # 通用关键词增强
                keyword = hot_keywords[0]
                if keyword and keyword not in original_content:
                    enhanced_query["content"] = f"{original_content} {keyword}"
                    enhanced_query["enhanced_with"] = "hot_keyword"
            
            # 提高有实时数据增强的查询的优先级
            if "enhanced_with" in enhanced_query:
                enhanced_query["priority"] = min(2.0, enhanced_query["priority"] + 0.3)
                enhanced_query["has_real_time_data"] = True
            
            enhanced_queries.append(enhanced_query)
        
        # 基于实时洞察添加额外的查询
        if industry_hotspots:
            extra_query_1 = {
                "content": f"{context.industry} {industry_hotspots[0]} 深度分析 前沿趋势",
                "focus": "行业前沿热点",
                "recency_filter": "week",
                "priority": 1.9,
                "type": "real_time_generated",
                "analysis_type": "hotspot_focused",
                "has_real_time_data": True
            }
            enhanced_queries.append(extra_query_1)
        
        if marketing_methodologies:
            extra_query_2 = {
                "content": f"{context.industry} {marketing_methodologies[0]} 营销策略实践",
                "focus": "营销方法论",
                "recency_filter": "week",
                "priority": 1.8,
                "type": "real_time_generated",
                "analysis_type": "methodology_learning",
                "has_real_time_data": True
            }
            enhanced_queries.append(extra_query_2)
        
        if leading_kol_insights:
            extra_query_3 = {
                "content": f"{context.industry} {leading_kol_insights[0]} 头部KOL成功要素",
                "focus": "头部KOL分析",
                "recency_filter": "month",
                "priority": 1.7,
                "type": "real_time_generated",
                "analysis_type": "kol_analysis",
                "has_real_time_data": True
            }
            enhanced_queries.append(extra_query_3)
        
        logger.info(f"查询增强完成：{len(enhanced_queries)}个查询，{sum(1 for q in enhanced_queries if q.get('has_real_time_data'))}个包含实时数据")
        return enhanced_queries

    def _balanced_quality_validation(self, queries: List[Dict], context: QueryContext) -> List[Dict]:
        """智能质量验证 - 基于多维度评分和阶段适配"""
        validated_queries = []
        industry_keywords = self._get_industry_specific_keywords(context.industry)
        
        for query in queries:
            # 基本字段检查
            if not all(key in query for key in ['content', 'focus', 'priority']):
                continue
            
            content = query['content'].strip()
            
            # 长度检查
            min_length = 10 if context.account_stage == AccountStage.STARTUP else 15
            max_length = 200 if context.account_stage == AccountStage.MATURE else 150
            
            if len(content) < min_length or len(content) > max_length:
                continue
            
            # 多维度质量评分
            quality_score = self._calculate_query_quality_score(query, context, industry_keywords)
            
            # 应用质量阈值
            min_score = 0.5 if context.account_stage == AccountStage.STARTUP else 0.6
            if quality_score < min_score:
                continue
            
            query['quality_score'] = quality_score
            validated_queries.append(query)
        
        # 按质量分数排序，保留最佳查询
        validated_queries.sort(key=lambda x: x.get('quality_score', 0), reverse=True)
        
        return validated_queries

    def _calculate_query_quality_score(self, query: Dict, context: QueryContext, industry_keywords: List[str]) -> float:
        """计算查询质量评分"""
        content = query['content'].strip().lower()
        base_score = 0.4
        
        # 1. 实时数据增强加分
        if query.get('has_real_time_data'):
            base_score += 0.25
        
        # 2. 行业相关性评分
        if context.industry.lower() in content:
            base_score += 0.15
        
        # 3. 行业关键词匹配
        keyword_matches = sum(1 for keyword in industry_keywords if keyword.lower() in content)
        base_score += min(0.2, keyword_matches * 0.05)
        
        # 4. 内容主题相关性
        theme_matches = sum(1 for theme in context.content_themes if theme.lower() in content)
        base_score += min(0.15, theme_matches * 0.05)
        
        # 5. 查询特异性评分（包含具体术语）
        specificity_score = query.get('specificity_score', 0.7)
        base_score += specificity_score * 0.1
        
        # 6. 相关性评分
        relevance_score = query.get('relevance_score', 0.8)
        base_score += relevance_score * 0.1
        
        # 7. 时效性词汇检查
        time_keywords = get_time_related_keywords()
        if any(keyword in content for keyword in time_keywords):
            base_score += 0.1
        
        return min(1.0, base_score)

    def _get_adaptive_query_config(self, context: QueryContext) -> Dict:
        """根据账号阶段和粉丝数确定查询数量和分布"""
        follower_count = context.follower_count
        stage = context.account_stage
        
        # 基础配置
        if stage == AccountStage.STARTUP:
            # 起步阶段：重点关注营销方法论和市场机会
            return {
                'total_queries': 8,
                'hotspot': 1,
                'methodology': 3,
                'kol_competitor': 2,
                'opportunity': 2,
                'case_study': 0,
                'hotspot_priority': 1.2,
                'methodology_priority': 1.8,
                'kol_priority': 1.5,
                'opportunity_priority': 1.7,
                'case_priority': 1.0
            }
        elif stage == AccountStage.GROWTH:
            # 成长阶段：平衡分布，重点关注竞争对手和热点
            return {
                'total_queries': 12,
                'hotspot': 3,
                'methodology': 3,
                'kol_competitor': 3,
                'opportunity': 2,
                'case_study': 1,
                'hotspot_priority': 1.6,
                'methodology_priority': 1.4,
                'kol_priority': 1.7,
                'opportunity_priority': 1.3,
                'case_priority': 1.1
            }
        else:  # MATURE
            # 成熟阶段：重点关注创新和深度案例学习
            return {
                'total_queries': 16,
                'hotspot': 4,
                'methodology': 3,
                'kol_competitor': 4,
                'opportunity': 3,
                'case_study': 2,
                'hotspot_priority': 1.8,
                'methodology_priority': 1.2,
                'kol_priority': 1.6,
                'opportunity_priority': 1.4,
                'case_priority': 1.5
            }

    def _get_industry_specific_keywords(self, industry: str) -> List[str]:
        """获取行业特定关键词"""
        base_keywords = ['数字化', '创新', '用户体验', '市场营销', '品牌建设', '技术升级', '行业趋势', '竞争分析']
        if industry:
            base_keywords.extend([f'{industry}行业', f'{industry}发展', f'{industry}市场'])
        return base_keywords

    def _get_stage_specific_focus(self, stage: AccountStage) -> str:
        """获取阶段特定重点"""
        stage_focus = {
            AccountStage.STARTUP: "基础建设和快速获客，重点关注营销方法论和市场机会识别",
            AccountStage.GROWTH: "扩大影响力和用户增长，重点关注竞争对手分析和行业热点把握",
            AccountStage.MATURE: "创新突破和深度价值创造，重点关注前沿趋势和成功案例学习"
        }
        return stage_focus.get(stage, "全面发展和持续优化")

    async def generate_layered_queries(self, account_info: Dict) -> List[Dict]:
        """生成简化查询（优化版本，减少token消耗）"""
        try:
            logger.info("开始生成简化查询（依赖Sonar智能理解）")
            
            # 1. 提取查询上下文
            context = self._extract_query_context(account_info)
            logger.info(f"账号分析：{context.account_name}，{context.industry}行业，{context.account_stage.value}阶段")
            
            # 2. 直接使用简化的查询生成
            simplified_queries = await self._generate_queries_with_dynamic_intelligence(context, account_info)
            if simplified_queries:
                logger.info(f"简化查询生成成功：{len(simplified_queries)}个查询")
                return simplified_queries
            
            # 3. 如果失败，使用最基本的回退方案
            logger.warning("简化查询生成失败，使用基本回退方案")
            
            basic_prompt = self._generate_simplified_queries_prompt(context)
            basic_queries = await self._call_gpt_for_queries(basic_prompt, QueryType.BASIC)
            
            if basic_queries:
                validated_queries = self._simple_quality_validation(basic_queries, context)
                validated_queries.sort(key=lambda x: x.get('priority', 1.0), reverse=True)
                logger.info(f"基本回退方案成功：{len(validated_queries)}个查询")
                return validated_queries
            
            logger.error("所有查询生成方案都失败")
            return []
            
        except Exception as e:
            logger.error(f"查询生成失败: {e}")
            return []

# 全局实例
_layered_query_generator = None

def get_layered_query_generator() -> LayeredQueryGenerator:
    """获取分层查询生成器实例（单例模式）"""
    global _layered_query_generator
    if _layered_query_generator is None:
        _layered_query_generator = LayeredQueryGenerator()
    return _layered_query_generator

async def generate_smart_queries(account_info: Dict) -> List[Dict]:
    """智能查询生成入口函数"""
    generator = get_layered_query_generator()
    return await generator.generate_layered_queries(account_info)

if __name__ == "__main__":
    # 测试代码
    async def test_layered_query_generation():
        test_account = {
            'account_name': '小美的护肤日记',
            'industry': '美妆护肤',
            'follows': 25000,
            'content_themes': ['护肤知识', '产品评测', '美妆教程'],
            'target_audience': ['18-35岁女性', '护肤爱好者'],
            'recent_posts': [
                {'title': '秋季护肤指南', 'engagement': 1500},
                {'title': '平价精华推荐', 'engagement': 2000},
                {'title': '敏感肌护理', 'engagement': 1800}
            ]
        }
        
        queries = await generate_smart_queries(test_account)
        print(f"生成了 {len(queries)} 个查询:")
        for i, query in enumerate(queries, 1):
            print(f"{i}. [{query['type']}] {query['content']}")
            print(f"   重点：{query['focus']}, 优先级：{query['priority']}")
            print()
    
    # 运行测试
    # asyncio.run(test_layered_query_generation())