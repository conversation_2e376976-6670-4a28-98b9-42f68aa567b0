#!/usr/bin/env python3
"""
新版诊断服务脚本
使用统一的异步服务框架 - 定时任务模式
适用于 Argo Cron 定时执行
"""

import argparse
import os
import sys

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import DiagnosisService
from task.server.account_diagnosis.base_async_service import create_service_main

def create_main(mode: bool = False):
    """创建服务主函数
    
    Args:
        mode: True代表deep_research, False代表basic，默认False
    """
    # 根据mode参数确定模式字符串
    mode_str = "deep_research" if mode else "basic"
    
    # 创建服务主函数 - 定时任务模式
    # run_mode="cron": 定时任务模式，会在指定时间后自动退出
    # timeout=5950: 运行约99分钟后自动退出，配合100分钟cron间隔（激进方案，只留50秒缓冲）
    # max_concurrent_tasks=8: 最大并发任务数，可同时处理多个任务
    return create_service_main(
        DiagnosisService,
        run_mode="cron",     # 定时任务模式
        timeout=7080,  # 118分钟，激进设置，锁过期时间112.1分钟，缓冲7.9分钟
        use_service_lock=False,  # 禁用服务锁，允许随时重启
        max_concurrent_tasks=8,
        redis_connection_timeout=10,  # 增加Redis连接超时
        redis_socket_timeout=10,      # 增加Redis socket超时
        default_mode=mode_str         # 设置默认模式
    )

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="诊断服务脚本")
    parser.add_argument(
        "--mode", 
        action="store_true", 
        help="启用深度研究模式 (true=deep_research, false=basic, 默认false)"
    )
    
    args = parser.parse_args()
    main = create_main(args.mode)
    main() 