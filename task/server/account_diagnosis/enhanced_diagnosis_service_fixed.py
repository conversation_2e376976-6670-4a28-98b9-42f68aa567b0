"""
Enhanced Diagnosis Service with Robust Error Handling (Fixed Version)
增强的诊断服务
"""

import asyncio
import time
import json
from typing import Dict, Tuple, Optional, Any, Callable
from colorama import Fore, Style

from enhanced_base_service_fixed import EnhancedBaseAsyncService
from enhanced_error_handling import CircuitBreakerConfig, ErrorCategory, ErrorSeverity

# Mock diagnosis function for testing
async def diagnosis_process_task(input_data, **kwargs):
    """Mock diagnosis process task"""
    await asyncio.sleep(0.1)  # Simulate processing time
    
    account_info = input_data.get("accountInfo", {})
    note_list = input_data.get("noteList", [])
    marketing_goal = input_data.get("marketingGoal", "")
    
    # Mock diagnosis result
    result = {
        "diagnosis": f"账号 {account_info.get('nickname', 'Unknown')} 诊断完成",
        "recommendations": [
            "建议增加内容发布频率",
            "优化内容标题吸引力",
            "提升用户互动率"
        ],
        "score": 85,
        "marketing_goal": marketing_goal,
        "note_count": len(note_list)
    }
    
    return True, result

class EnhancedDiagnosisService(EnhancedBaseAsyncService):
    """Enhanced Diagnosis Service with comprehensive error handling"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", 
                 timeout: int = 300, use_service_lock: bool = True, 
                 redis_connection_timeout: int = 5, redis_socket_timeout: int = 5):
        
        # Get environment
        env = self._get_env()
        
        # Initialize parent with enhanced error handling
        super().__init__(
            "diagnosis", 
            f"{env.lower()}:q:diagnosis:request",
            f"{env.lower()}:q:diagnosis:response",
            max_concurrent_tasks, 
            run_mode, 
            timeout, 
            use_service_lock, 
            redis_connection_timeout, 
            redis_socket_timeout
        )
        
        # Setup diagnosis-specific circuit breakers
        self._setup_diagnosis_circuit_breakers()
        
        # Performance tracking for diagnosis-specific operations
        self.diagnosis_metrics = {
            "total_diagnoses": 0,
            "successful_diagnoses": 0,
            "failed_diagnoses": 0,
            "deep_research_attempts": 0,
            "deep_research_successes": 0,
            "fallback_to_basic": 0
        }
        
        self.logger.info(f"{Fore.MAGENTA}🏥 Enhanced Diagnosis Service initialized with advanced error handling{Style.RESET_ALL}")
    
    def _get_env(self):
        """Get environment variable"""
        try:
            from task import ENV
            return ENV
        except ImportError:
            return "test"
    
    def _setup_diagnosis_circuit_breakers(self):
        """Setup diagnosis-specific circuit breakers"""
        self.logger.info(f"{Fore.CYAN}🔧 Setting up diagnosis-specific circuit breakers{Style.RESET_ALL}")
        
        # Deep research circuit breaker
        deep_research_config = CircuitBreakerConfig(
            failure_threshold=2,
            recovery_timeout=45,
            timeout=180.0,
            success_threshold=2
        )
        self.error_handler.add_circuit_breaker("deep_research", deep_research_config)
        
        # HTML generation circuit breaker
        html_gen_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=90.0
        )
        self.error_handler.add_circuit_breaker("html_generation", html_gen_config)
        
        self.logger.info(f"{Fore.GREEN}✅ Diagnosis circuit breakers configured{Style.RESET_ALL}")
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """Enhanced diagnosis task processing with comprehensive error handling"""
        start_time = time.time()
        
        try:
            # Validate input data
            validation_result = await self._validate_diagnosis_input(input_data)
            if not validation_result["valid"]:
                self.diagnosis_metrics["failed_diagnoses"] += 1
                return False, {"error": f"Input validation failed: {validation_result['error']}"}
            
            # Log diagnosis start
            account_info = input_data.get("accountInfo", {})
            account_name = account_info.get("nickname", "Unknown Account")
            marketing_goal = input_data.get("marketingGoal", "Unknown Goal")
            
            self.logger.info(f"{Fore.MAGENTA}🏥 Starting diagnosis for account: {account_name} | Goal: {marketing_goal}{Style.RESET_ALL}")
            
            # Enhanced task processing
            success, results = await self.enhanced_task_processing(
                self._enhanced_diagnosis_process,
                input_data
            )
            
            duration = time.time() - start_time
            
            if success:
                self.diagnosis_metrics["successful_diagnoses"] += 1
                self.log_performance_metric("diagnosis_duration", duration, "s")
                
                if isinstance(results, dict):
                    generated_reports = [key for key in results.keys() if results[key]]
                    self.logger.info(f"{Fore.GREEN}📋 Diagnosis completed successfully - Generated: {generated_reports}{Style.RESET_ALL}")
                
                return True, results
            else:
                self.diagnosis_metrics["failed_diagnoses"] += 1
                self.log_operation_error("Diagnosis", results.get('error', 'Unknown error'))
                return False, results
                
        except Exception as e:
            duration = time.time() - start_time
            self.diagnosis_metrics["failed_diagnoses"] += 1
            self.log_operation_error("Diagnosis Task", str(e))
            self.log_performance_metric("failed_diagnosis_duration", duration, "s")
            
            return False, {"error": f"Diagnosis task failed: {str(e)}"}
        finally:
            self.diagnosis_metrics["total_diagnoses"] += 1
    
    async def _validate_diagnosis_input(self, input_data: Dict) -> Dict[str, Any]:
        """Validate diagnosis input data with detailed feedback"""
        try:
            self.log_operation_start("Input Validation")
            
            required_fields = ["accountInfo", "noteList", "marketingGoal"]
            missing_fields = []
            
            for field in required_fields:
                if field not in input_data:
                    missing_fields.append(field)
            
            if missing_fields:
                error_msg = f"Missing required fields: {missing_fields}"
                self.log_operation_error("Input Validation", error_msg)
                return {"valid": False, "error": error_msg}
            
            # Validate account info
            account_info = input_data["accountInfo"]
            if not isinstance(account_info, dict) or not account_info.get("nickname"):
                error_msg = "Invalid accountInfo: missing nickname"
                self.log_operation_error("Input Validation", error_msg)
                return {"valid": False, "error": error_msg}
            
            # Validate note list
            note_list = input_data["noteList"]
            if not isinstance(note_list, list):
                error_msg = "Invalid noteList: must be a list"
                self.log_operation_error("Input Validation", error_msg)
                return {"valid": False, "error": error_msg}
            
            if len(note_list) == 0:
                self.log_operation_warning("Input Validation", "Empty note list - diagnosis quality may be limited")
            
            self.log_operation_success("Input Validation")
            
            return {
                "valid": True,
                "account_name": account_info.get("nickname"),
                "note_count": len(note_list),
                "marketing_goal": input_data["marketingGoal"]
            }
            
        except Exception as e:
            error_msg = f"Validation exception: {str(e)}"
            self.log_operation_error("Input Validation", error_msg)
            return {"valid": False, "error": error_msg}
    
    async def _enhanced_diagnosis_process(self, input_data: Dict) -> Tuple[bool, Dict]:
        """Enhanced diagnosis process with intelligent fallback"""
        try:
            # Check if deep research should be attempted
            deep_research_enabled = True
            
            # Check circuit breaker status
            deep_research_cb = self.error_handler.circuit_breakers.get("deep_research")
            if deep_research_cb and deep_research_cb.state.value == "open":
                self.logger.warning(f"{Fore.YELLOW}🚫 Deep research circuit breaker is OPEN - using basic mode{Style.RESET_ALL}")
                deep_research_enabled = False
                self.diagnosis_metrics["fallback_to_basic"] += 1
            
            if deep_research_enabled:
                try:
                    self.diagnosis_metrics["deep_research_attempts"] += 1
                    self.logger.info(f"{Fore.CYAN}🔍 Attempting diagnosis with deep research{Style.RESET_ALL}")
                    
                    # Use deep research with circuit breaker
                    success, results = await self.enhanced_deep_research_call(
                        diagnosis_process_task,
                        input_data,
                        enable_deep_research=True
                    )
                    
                    if success:
                        self.diagnosis_metrics["deep_research_successes"] += 1
                        self.logger.info(f"{Fore.GREEN}🎯 Deep research diagnosis completed successfully{Style.RESET_ALL}")
                        return success, results
                    else:
                        self.logger.warning(f"{Fore.YELLOW}⚠️  Deep research failed, falling back to basic mode{Style.RESET_ALL}")
                        deep_research_enabled = False
                        self.diagnosis_metrics["fallback_to_basic"] += 1
                        
                except Exception as e:
                    self.logger.warning(f"{Fore.YELLOW}⚠️  Deep research exception, falling back to basic mode: {str(e)}{Style.RESET_ALL}")
                    deep_research_enabled = False
                    self.diagnosis_metrics["fallback_to_basic"] += 1
            
            # Basic mode fallback
            if not deep_research_enabled:
                self.logger.info(f"{Fore.BLUE}🔧 Using basic diagnosis mode{Style.RESET_ALL}")
                
                success, results = await self.enhanced_task_processing(
                    diagnosis_process_task,
                    {**input_data, "enable_deep_research": False}
                )
                
                if success:
                    self.logger.info(f"{Fore.GREEN}✅ Basic diagnosis completed successfully{Style.RESET_ALL}")
                else:
                    self.logger.error(f"{Fore.RED}❌ Basic diagnosis also failed: {results}{Style.RESET_ALL}")
                
                return success, results
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}💥 Diagnosis process failed completely: {str(e)}{Style.RESET_ALL}")
            return False, {"error": f"Diagnosis process failed: {str(e)}"}
    
    async def enhanced_deep_research_call(self, research_func: Callable, *args, **kwargs):
        """Enhanced deep research call with specialized error handling"""
        start_time = time.time()
        research_name = getattr(research_func, '__name__', 'unknown_research')
        
        try:
            self.log_operation_start(f"Deep Research {research_name}", {
                "function": research_name,
                "args_count": len(args),
                "kwargs_keys": list(kwargs.keys())
            })
            
            result = await self.error_handler.handle_with_circuit_breaker(
                research_func,
                "deep_research",
                f"research_{research_name}",
                {
                    "service": self.service_name,
                    "research_function": research_name
                },
                *args, **kwargs
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"Deep Research {research_name}", duration)
            self._record_operation_metric(f"research_{research_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"Deep Research {research_name}", str(e))
            self._record_operation_metric(f"research_{research_name}", duration, False)
            raise
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str = ""):
        """Log performance metrics with color"""
        self.logger.info(f"{Fore.BLUE}📊 {metric_name}: {value:.2f}{unit}{Style.RESET_ALL}")
    
    async def get_diagnosis_metrics(self) -> Dict[str, Any]:
        """Get diagnosis-specific metrics"""
        base_metrics = await self.get_operation_metrics()
        
        # Calculate success rates
        total_diagnoses = self.diagnosis_metrics["total_diagnoses"]
        success_rate = (self.diagnosis_metrics["successful_diagnoses"] / total_diagnoses * 100) if total_diagnoses > 0 else 0
        
        deep_research_attempts = self.diagnosis_metrics["deep_research_attempts"]
        deep_research_success_rate = (self.diagnosis_metrics["deep_research_successes"] / deep_research_attempts * 100) if deep_research_attempts > 0 else 0
        
        fallback_rate = (self.diagnosis_metrics["fallback_to_basic"] / total_diagnoses * 100) if total_diagnoses > 0 else 0
        
        diagnosis_specific = {
            "diagnosis_metrics": self.diagnosis_metrics.copy(),
            "success_rate": success_rate,
            "deep_research_success_rate": deep_research_success_rate,
            "fallback_rate": fallback_rate,
            "recommendations": self._get_performance_recommendations()
        }
        
        return {**base_metrics, **diagnosis_specific}
    
    def _get_performance_recommendations(self) -> list:
        """Get performance recommendations based on metrics"""
        recommendations = []
        
        total_diagnoses = self.diagnosis_metrics["total_diagnoses"]
        if total_diagnoses == 0:
            return ["No diagnoses processed yet"]
        
        success_rate = (self.diagnosis_metrics["successful_diagnoses"] / total_diagnoses * 100)
        fallback_rate = (self.diagnosis_metrics["fallback_to_basic"] / total_diagnoses * 100)
        
        if success_rate < 80:
            recommendations.append("🔴 Low success rate - investigate error patterns")
        
        if fallback_rate > 30:
            recommendations.append("🟡 High fallback rate - check deep research service health")
        
        # Check circuit breaker states
        for name, cb in self.error_handler.circuit_breakers.items():
            if cb.state.value == "open":
                recommendations.append(f"🔴 Circuit breaker '{name}' is OPEN - investigate underlying issues")
        
        if not recommendations:
            recommendations.append("🟢 All metrics look healthy")
        
        return recommendations
    
    async def get_enhanced_diagnosis_health(self) -> Dict[str, Any]:
        """Get comprehensive diagnosis service health"""
        base_health = await self.get_enhanced_service_health()
        diagnosis_metrics = await self.get_diagnosis_metrics()
        
        return {
            **base_health,
            "diagnosis_metrics": diagnosis_metrics["diagnosis_metrics"],
            "performance_recommendations": diagnosis_metrics["recommendations"]
        }
    
    async def run_health_check(self) -> Dict[str, Any]:
        """Run comprehensive health check"""
        self.logger.info(f"{Fore.CYAN}🏥 Running comprehensive health check{Style.RESET_ALL}")
        
        health_results = {
            "timestamp": time.time(),
            "service_name": self.service_name,
            "checks": {}
        }
        
        # Test Redis connectivity (mock)
        try:
            await self.enhanced_redis_operation("health_check", lambda: True)
            health_results["checks"]["redis"] = {"status": "healthy", "message": "Redis connection OK (mock)"}
            self.logger.info(f"{Fore.GREEN}✅ Redis health check passed{Style.RESET_ALL}")
        except Exception as e:
            health_results["checks"]["redis"] = {"status": "unhealthy", "message": str(e)}
            self.logger.error(f"{Fore.RED}❌ Redis health check failed: {str(e)}{Style.RESET_ALL}")
        
        # Test AI service connectivity (mock)
        try:
            health_results["checks"]["ai_service"] = {"status": "healthy", "message": "AI service accessible (mock)"}
            self.logger.info(f"{Fore.GREEN}✅ AI service health check passed{Style.RESET_ALL}")
        except Exception as e:
            health_results["checks"]["ai_service"] = {"status": "unhealthy", "message": str(e)}
            self.logger.error(f"{Fore.RED}❌ AI service health check failed: {str(e)}{Style.RESET_ALL}")
        
        # Get overall health
        overall_health = await self.get_enhanced_diagnosis_health()
        health_results["overall_health"] = overall_health
        
        return health_results