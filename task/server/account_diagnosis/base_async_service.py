import asyncio
import json
import time
import os
import sys
import ssl
import logging
import contextvars
from abc import ABC, abstractmethod
from typing import Dict, Tuple, Optional, Any, Callable
from logging.handlers import RotatingFileHandler

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task.lib.logging_utils import log_task_start, log_task_complete, log_progress
from task.lib.json_utils_enhanced import (
    detect_json_issues,
    safe_redis_serialize_with_validation,
    create_error_response
)
import redis.asyncio as redis
from contextlib import asynccontextmanager
from core.optimized_redis_manager import get_optimized_redis_manager


@asynccontextmanager
async def get_redis_connection():
    """获取Redis连接的兼容函数 - 使用优化的Redis管理器"""
    from simple_redis_manager import get_simple_redis_connection
    async with get_simple_redis_connection() as connection:
        yield connection


class TaskIdFilter(logging.Filter):
    """自定义日志过滤器以添加任务ID"""
    def __init__(self, task_id_var):
        super().__init__()
        self.task_id_var = task_id_var
    
    def filter(self, record):
        record.task_id = self.task_id_var.get()
        return True


class BaseAsyncService(ABC):
    """异步服务基类"""
    
    def __init__(self, service_name: str, input_queue: str, output_queue: str, 
                 max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300, 
                 use_service_lock: bool = True, redis_connection_timeout: int = 30, 
                 redis_socket_timeout: int = 30):
        self.service_name = service_name
        self.input_queue = input_queue
        self.output_queue = output_queue
        self.max_concurrent_tasks = max_concurrent_tasks
        self.run_mode = run_mode  # "daemon" 或 "cron"
        self.timeout = timeout  # 定时任务模式下的超时时间（秒）
        self.use_service_lock = use_service_lock  # 是否使用服务锁
        self.redis_connection_timeout = redis_connection_timeout
        self.redis_socket_timeout = redis_socket_timeout
        
        # 创建任务ID上下文变量
        self.task_id_var = contextvars.ContextVar('task_id', default='N/A')
        
        # 初始化组件
        self.logger = self._setup_logging()
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._shutdown_event = asyncio.Event()
        
        # 添加锁管理状态
        self._acquired_lock = False  # 标记是否成功获取了锁
        self._lock_value = None      # 存储获取到的锁值
        self._heartbeat_task = None  # 心跳任务
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志系统"""
        os.makedirs('logs', exist_ok=True)
        
        logger = logging.getLogger(f'async_{self.service_name}_service')
        logger.setLevel(logging.INFO)
        logger.handlers.clear()
        
        file_handler = RotatingFileHandler(
            f'logs/async_{self.service_name}_service.log',
            maxBytes=10*1024*1024,
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - [task_id:%(task_id)s] - %(message)s'
        )
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # 添加任务ID过滤器
        task_id_filter = TaskIdFilter(self.task_id_var)
        file_handler.addFilter(task_id_filter)
        console_handler.addFilter(task_id_filter)
        
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    async def write_to_queue(self, queue_name: str, data: str, logger: logging.Logger) -> bool:
        """写入Redis队列"""
        max_retries = 2  # 增加重试次数
        for attempt in range(max_retries):
            try:
                logger.debug(f"尝试写入队列 {queue_name} (尝试 {attempt + 1}/{max_retries})")
                async with get_redis_connection() as redis_conn:
                    # 检查数据长度
                    if len(data) > 1024 * 1024:  # 1MB
                        logger.warning(f"写入数据较大: {len(data)} 字符")
                    
                    await redis_conn.rpush(queue_name, data)
                    queue_length = await redis_conn.llen(queue_name)
                    logger.info(f"{queue_name} 队列长度: {queue_length}")
                    return True
            except Exception as e:
                error_type = type(e).__name__
                logger.error(f"写入队列失败 (尝试 {attempt + 1}/{max_retries}) [{error_type}]: {e}")
                
                # 如果是连接问题，等待更长时间
                if "connection" in str(e).lower() or "acquire" in str(e).lower():
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    logger.warning(f"检测到连接问题，等待 {wait_time} 秒后重试")
                    await asyncio.sleep(wait_time)
                elif attempt < max_retries - 1:
                    await asyncio.sleep(0.5)
        
        logger.error(f"写入队列最终失败，队列: {queue_name}, 数据长度: {len(data)}")
        return False
    
    async def get_task_from_queue(self, queue_name: str, logger: logging.Logger, timeout: int = 30) -> Optional[Dict]:
        """从Redis队列获取任务 - 优化版，最小化延迟"""
        # 对于持续监听，使用较长的超时时间减少重连开销
        # 对于定时任务，使用传入的超时时间
        if self.run_mode == "daemon":
            # daemon模式：使用长超时减少重连，提高响应速度
            actual_timeout = 60  # 60秒超时，减少重连频率
        else:
            # cron模式：使用传入的超时时间
            actual_timeout = min(timeout, 30)
        
        try:
            logger.debug(f"监听队列 {queue_name} (超时: {actual_timeout}s)")
            
            async with get_redis_connection() as redis_conn:
                # 使用 blpop 进行阻塞式监听 - 这是最高效的方式
                task_data = await redis_conn.blpop(queue_name, timeout=actual_timeout)
            
            if task_data:
                _, input_json = task_data
                logger.info("=== 收到新任务 ===")
                
                # 优化JSON解析 - 减少不必要的重复解析
                try:
                    if isinstance(input_json, str):
                        input_data = json.loads(input_json)
                        # 检查是否需要二次解析（避免不必要的解析）
                        if isinstance(input_data, str):
                            input_data = json.loads(input_data)
                    else:
                        input_data = input_json
                        
                    if not isinstance(input_data, dict):
                        raise ValueError("数据解析后不是字典格式")
                        
                    return input_data
                except (json.JSONDecodeError, ValueError) as e:
                    logger.error(f"JSON解析失败: {str(e)}")
                    return None
            else:
                # 超时是正常情况，直接返回None继续监听
                logger.debug(f"队列 {queue_name} 监听超时，继续监听...")
                return None
                    
        except asyncio.TimeoutError:
            # 超时是正常情况，直接返回None继续监听
            logger.debug(f"队列 {queue_name} 监听超时，继续监听...")
            return None
                
        except ConnectionError as e:
            logger.warning(f"Redis连接错误: {e}")
            # 连接错误时短暂等待后返回None，让上层循环处理重试
            await asyncio.sleep(1)
            return None
                
        except Exception as e:
            error_type = type(e).__name__
            
            # 对于超时相关错误，降低日志级别
            if any(keyword in str(e).lower() for keyword in ['timeout', 'timed out']):
                logger.debug(f"队列监听超时 [{error_type}]: {e}")
                return None
            else:
                logger.warning(f"从队列获取任务时发生异常 [{error_type}]: {e}")
                
                # 对于网络相关错误，短暂等待
                if any(keyword in str(e).lower() for keyword in ['connection', 'network', 'unreachable']):
                    await asyncio.sleep(1)
                
                return None
    
    async def call_ai_async(self, ai_function: Callable, sys_prompt: str, user_prompt: str, 
                           model_or_schema: Any = None, model_name: Optional[str] = None, 
                           task_name: str = "AI任务", max_retries: int = 2, 
                           timeout: int = 120) -> Tuple[bool, Any]:
        """异步AI调用函数，支持重试和token统计"""
        
        def sync_ai_call():
            start_time = time.time()
            try:
                # 导入必要的模块
                from task.lib.call_claude import gpt, gemini
                
                if ai_function == gpt:
                    # GPT调用
                    if model_or_schema and isinstance(model_or_schema, dict):
                        # 有JSON schema
                        status, result = ai_function(sys_prompt, user_prompt, model_or_schema, model=model_name)
                        return status, result, None, time.time() - start_time
                    else:
                        # 没有JSON schema
                        status, result = ai_function(sys_prompt, user_prompt, model=model_name)
                        return status, result, None, time.time() - start_time
                else:
                    # Gemini调用 - 现在返回三个值：status, result, usage_metadata
                    if model_or_schema:
                        response = ai_function(sys_prompt, user_prompt, json_schema=model_or_schema, model=model_name)
                    else:
                        response = ai_function(sys_prompt, user_prompt, model=model_name)
                    
                    if isinstance(response, tuple) and len(response) == 3:
                        status, result, usage_metadata = response
                        return status, result, usage_metadata, time.time() - start_time
                    elif isinstance(response, tuple) and len(response) == 2:
                        # 兼容旧的返回格式
                        status, result = response
                        return status, result, None, time.time() - start_time
                    else:
                        return False, f"Unexpected response format: {response}", None, time.time() - start_time
            except Exception as e:
                return False, f"调用异常: {str(e)}", None, time.time() - start_time
        
        # 尝试调用AI函数，支持重试
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"开始执行{task_name}，模型：{model_name}")
                self.logger.info(f"{task_name} - 尝试 {attempt + 1}/{max_retries + 1}")
                
                # 在线程池中执行同步调用
                loop = asyncio.get_event_loop()
                status, result, usage_metadata, elapsed_time = await asyncio.wait_for(
                    loop.run_in_executor(None, sync_ai_call),
                    timeout=timeout
                )
                
                if status:
                    self.logger.info(f"{task_name}生成成功，耗时：{elapsed_time:.2f}秒")
                    # 记录token使用量
                    if usage_metadata:
                        prompt_tokens = usage_metadata.get('promptTokenCount', 0)
                        candidates_tokens = usage_metadata.get('candidatesTokenCount', 0)
                        total_tokens = usage_metadata.get('totalTokenCount', 0)
                        self.logger.info(f"{task_name} Token使用量 - Prompt: {prompt_tokens}, Output: {candidates_tokens}, Total: {total_tokens}")
                        
                        # 尝试调用全局的 token 统计函数（如果存在）
                        try:
                            import sys
                            if hasattr(sys.modules.get('__main__'), 'print_token_usage'):
                                main_module = sys.modules['__main__']
                                main_module.print_token_usage(task_name, usage_metadata)
                        except Exception:
                            pass  # 如果调用失败，静默忽略
                    return status, result
                else:
                    self.logger.warning(f"{task_name} - 尝试 {attempt + 1} 失败: {result}")
                    if attempt < max_retries:
                        await asyncio.sleep(2 ** attempt)  # 指数退避
                    else:
                        self.logger.error(f"{task_name}生成失败，所有重试都已用尽: {result}")
                        return status, result
            except asyncio.TimeoutError:
                self.logger.error(f"{task_name} - 尝试 {attempt + 1} 超时")
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)
                else:
                    return False, f"{task_name}调用超时"
            except Exception as e:
                self.logger.error(f"{task_name} - 尝试 {attempt + 1} 异常: {str(e)}")
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)
                else:
                    return False, f"{task_name}异常: {str(e)}"
        
        return False, f"{task_name}失败"
    
    def load_prompt(self, file_name: str, **kwargs) -> str:
        """从prompts文件夹加载指定的prompt文件，支持jinja2模板渲染"""
        prompt_path = os.path.join(os.path.dirname(__file__), 'prompts', file_name)
        try:
            with open(prompt_path, 'r', encoding='utf-8') as f:
                template_content = f.read()
            
            # 如果有模板变量，使用jinja2渲染
            if kwargs:
                try:
                    from jinja2 import Template
                    template = Template(template_content)
                    return template.render(**kwargs)
                except Exception as e:
                    self.logger.error(f"Jinja2模板渲染失败: {str(e)}")
                    return template_content
            else:
                return template_content
                
        except FileNotFoundError:
            self.logger.error(f"Prompt文件未找到: {prompt_path}")
            return ""
        except Exception as e:
            self.logger.error(f"加载prompt文件失败: {str(e)}")
            return ""
    
    @abstractmethod
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """
        抽象方法：具体的任务处理逻辑
        子类必须实现此方法
        """
        pass
    
    def extract_task_info(self, input_data: Dict) -> Tuple[str, str]:
        """
        提取任务信息，子类可以覆盖此方法来自定义提取逻辑
        返回: (task_id, business_id)
        """
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId", "未知任务")
        business_id = task_info.get("disagnosisId") or task_info.get("socialMediaReportId") or task_info.get("reviewId")
        return task_id, business_id
    
    def create_standard_response(self, input_data: Dict, status: str, results: Dict = None, error_msg: str = None) -> Dict:
        """创建标准的响应格式，统一使用aiTaskStatus"""
        task_info = input_data.get("taskInfo", {}).copy()
        
        # 更新taskInfo中的状态信息
        if status == "success":
            task_info.update({
                "aiTaskStatus": "FINISH",
                "aiTaskMsg": "Task completed!",
                "aiTaskMsgCN": "任务完成！",
                "aiTaskProgress": 100
            })
            
            # 构建完整响应（包含业务数据）
            response = {"taskInfo": task_info}
            if isinstance(results, dict):
                self.logger.info(f"🔍 合并业务数据到响应中，业务数据字段: {list(results.keys())}")
                response.update(results)
                self.logger.info(f"🔍 合并后的响应字段: {list(response.keys())}")
            else:
                self.logger.warning(f"🚨 results不是字典类型: {type(results)}, 值: {results}")
            return response
            
        elif status == "failed":
            task_info.update({
                "aiTaskStatus": "FAILED",
                "aiTaskMsg": "Task execution failed",
                "aiTaskMsgCN": error_msg or "任务执行失败",
                "aiTaskProgress": 1
            })
            
            # 失败响应只包含taskInfo
            return {"taskInfo": task_info}
        
        else:
            # 其他状态（如RUNNING）保持原样
            return {"taskInfo": task_info}

    async def process_single_task(self, input_data: Dict):
        """处理单个任务的通用逻辑"""
        task_id, business_id = self.extract_task_info(input_data)
        
        # 设置任务ID上下文
        self.task_id_var.set(task_id)
        
        # 记录任务开始
        log_task_start(self.logger, self.service_name, task_id, business_id)
        start_time = time.time()
        
        try:
            self.logger.info(f"开始处理任务: {task_id}")
            
            # 调用具体的任务处理逻辑
            status, results = await self.process_task_logic(input_data)
            
            if not status:
                error_msg = f"任务 {task_id}: 处理失败: {results.get('error', results) if isinstance(results, dict) else results}"
                self.logger.error(error_msg)
                
                # 使用标准格式创建失败响应
                return_data = self.create_standard_response(
                    input_data, 
                    "failed", 
                    error_msg=results.get('error', results) if isinstance(results, dict) else str(results)
                )
                
                log_task_complete(self.logger, self.service_name, task_id, "failed", time.time() - start_time)
            else:
                # 使用标准格式创建成功响应
                self.logger.info(f"🔍 process_task_logic返回的结果类型: {type(results)}")
                if isinstance(results, dict):
                    self.logger.info(f"🔍 process_task_logic返回的字段: {list(results.keys())}")
                else:
                    self.logger.warning(f"🚨 process_task_logic返回的结果不是字典: {results}")
                
                return_data = self.create_standard_response(input_data, "success", results)
                
                self.logger.info(f"任务 {task_id}: 处理成功")
                log_task_complete(self.logger, self.service_name, task_id, "success", time.time() - start_time)

            # 序列化并写入输出队列
            self.logger.info(f"🔍 准备序列化的数据字段: {list(return_data.keys()) if isinstance(return_data, dict) else 'not_dict'}")
            output_json = safe_redis_serialize_with_validation(return_data)
            
            # 检测序列化后的JSON字符串是否有问题
            json_issues = detect_json_issues(output_json)
            if json_issues:
                self.logger.warning(f"JSON数据问题: {json_issues}")
            
            write_success = await self.write_to_queue(
                self.output_queue, output_json, self.logger
            )
            
            if write_success:
                self.logger.info(f"任务 {task_id}: 结果已写入输出队列")
            else:
                self.logger.error(f"任务 {task_id}: 写入输出队列失败")

        except Exception as e:
            error_msg = f"任务 {task_id}: 处理过程中发生异常: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            
            # 使用标准格式创建异常响应
            return_data = self.create_standard_response(input_data, "failed", error_msg=error_msg)
            
            output_json = safe_redis_serialize_with_validation(return_data)
            await self.write_to_queue(self.output_queue, output_json, self.logger)
            
            log_task_complete(self.logger, self.service_name, task_id, "error", time.time() - start_time)
    
    async def process_with_semaphore(self, input_data: Dict):
        """使用信号量控制并发的任务处理"""
        async with self.semaphore:
            await self.process_single_task(input_data)
    
    async def process_queue(self):
        """处理队列中的任务 - 持续监控版本，优化响应速度"""
        self.logger.info(f"🚀 开始持续监听队列: {self.input_queue}")
        self.logger.info(f"📤 输出队列: {self.output_queue}")
        self.logger.info(f"⚡ 最大并发任务数: {self.max_concurrent_tasks}")
        
        tasks = []
        consecutive_failures = 0
        max_consecutive_failures = 10
        
        while not self._shutdown_event.is_set():
            try:
                # 获取任务 - 使用长超时减少重连开销，提高响应速度
                input_data = await self.get_task_from_queue(
                    self.input_queue, self.logger, timeout=60
                )
                
                if input_data:
                    # 重置失败计数器
                    consecutive_failures = 0
                    
                    # 立即创建任务，无延迟处理
                    task = asyncio.create_task(self.process_with_semaphore(input_data))
                    tasks.append(task)
                    
                    self.logger.info(f"📋 新任务已启动，当前活跃任务数: {len([t for t in tasks if not t.done()])}")
                    
                    # 异步清理已完成的任务，不阻塞新任务处理
                    asyncio.create_task(self._cleanup_completed_tasks(tasks))
                else:
                    # blpop超时是正常情况，立即继续下一次监听，无需额外等待
                    pass
                
                # 非阻塞方式检查已完成的任务
                self._handle_completed_tasks_non_blocking(tasks)
                
            except Exception as e:
                consecutive_failures += 1
                error_msg = str(e)
                
                # 根据错误类型调整日志级别和处理策略
                if any(keyword in error_msg.lower() for keyword in ['timeout', 'connection']):
                    if consecutive_failures <= 3:
                        self.logger.debug(f"网络连接问题 (连续失败 {consecutive_failures}/{max_consecutive_failures}): {error_msg}")
                    else:
                        self.logger.warning(f"持续网络连接问题 (连续失败 {consecutive_failures}/{max_consecutive_failures}): {error_msg}")
                else:
                    self.logger.error(f"队列处理循环异常 (连续失败 {consecutive_failures}/{max_consecutive_failures}): {error_msg}", exc_info=True)
                
                # 智能等待策略
                if consecutive_failures >= max_consecutive_failures:
                    self.logger.error(f"连续失败次数达到上限 ({max_consecutive_failures})，暂停30秒后继续...")
                    await asyncio.sleep(30)
                    consecutive_failures = 0
                else:
                    # 对于网络问题，使用较短的等待时间
                    if any(keyword in error_msg.lower() for keyword in ['timeout', 'connection']):
                        wait_time = min(consecutive_failures * 0.2, 2)  # 最多等待2秒
                    else:
                        wait_time = min(consecutive_failures * 0.5, 5)  # 其他错误最多等待5秒
                    await asyncio.sleep(wait_time)
        
        # 等待所有剩余任务完成
        if tasks:
            active_tasks = [t for t in tasks if not t.done()]
            if active_tasks:
                self.logger.info(f"🔄 等待 {len(active_tasks)} 个活跃任务完成...")
                await asyncio.gather(*active_tasks, return_exceptions=True)
        
        self.logger.info("✅ 队列监听服务已停止")
    
    async def _cleanup_completed_tasks(self, tasks: list):
        """异步清理已完成的任务"""
        try:
            # 移除已完成的任务
            completed_tasks = [t for t in tasks if t.done()]
            for task in completed_tasks:
                if task in tasks:
                    tasks.remove(task)
        except Exception as e:
            self.logger.debug(f"清理已完成任务时出错: {e}")
    
    def _handle_completed_tasks_non_blocking(self, tasks: list):
        """非阻塞方式处理已完成的任务"""
        try:
            done_tasks = [t for t in tasks if t.done()]
            for task in done_tasks:
                try:
                    # 非阻塞获取任务结果，处理异常
                    if task.exception():
                        self.logger.error(f"任务执行异常: {task.exception()}", exc_info=False)
                except Exception as e:
                    self.logger.debug(f"检查任务状态时出错: {e}")
        except Exception as e:
            self.logger.debug(f"处理已完成任务时出错: {e}")
    
    async def process_queue_with_timeout(self):
        """带超时的队列处理，适用于定时任务模式"""
        self.logger.info(f"开始定时任务队列处理: {self.input_queue}")
        self.logger.info(f"输出队列: {self.output_queue}")
        self.logger.info(f"最大并发任务数: {self.max_concurrent_tasks}")
        self.logger.info(f"超时时间: {self.timeout}秒")
        
        start_time = time.time()
        tasks = []
        processed_count = 0
        
        while not self._shutdown_event.is_set():
            try:
                # 检查是否超时
                elapsed_time = time.time() - start_time
                if elapsed_time >= self.timeout:
                    self.logger.info(f"达到超时时间({self.timeout}秒)，准备退出")
                    break
                
                # 计算剩余时间
                remaining_time = self.timeout - elapsed_time
                queue_timeout = min(30, int(remaining_time))  # blpop a float timeout is not supported
                
                # 获取任务
                input_data = await self.get_task_from_queue(
                    self.input_queue, self.logger, timeout=queue_timeout
                )
                
                if input_data:
                    processed_count += 1
                    self.logger.info(f"处理第 {processed_count} 个任务")
                    
                    # 创建任务并添加到任务列表
                    task = asyncio.create_task(self.process_with_semaphore(input_data))
                    tasks.append(task)
                    
                    # 清理已完成的任务
                    tasks = [t for t in tasks if not t.done()]
                
                # 检查是否有任务完成，处理异常
                done_tasks = [t for t in tasks if t.done()]
                for task in done_tasks:
                    try:
                        await task  # 这会抛出任务中的异常（如果有的话）
                    except Exception as e:
                        self.logger.error(f"任务执行异常: {str(e)}", exc_info=True)
                
            except Exception as e:
                self.logger.error(f"队列处理循环异常: {str(e)}", exc_info=True)
                await asyncio.sleep(1)
        
        # 等待所有剩余任务完成
        if tasks:
            self.logger.info(f"等待 {len(tasks)} 个剩余任务完成...")
            await asyncio.gather(*tasks, return_exceptions=True)
        
        total_time = time.time() - start_time
        self.logger.info(f"定时任务完成，共处理 {processed_count} 个任务，总耗时 {total_time:.2f}秒")
    
    async def acquire_service_lock(self, timeout: int = 10) -> bool:
        """获取服务锁，防止多实例同时运行"""
        lock_key = f"service_lock:{self.service_name}"
        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                # 先检查现有锁是否过期或异常
                async with get_redis_connection() as redis_conn:
                    existing_lock = await redis_conn.get(lock_key)
                    if existing_lock:
                        try:
                            # 解析锁的时间戳 - 支持多种格式
                            lock_timestamp = None
                            lock_parts = existing_lock.split('_') # Already a string due to decode_responses=True
                            
                            if len(lock_parts) >= 3:
                                # 新格式: service_timestamp_mode
                                try:
                                    lock_timestamp = float(lock_parts[1])
                                except (ValueError, IndexError):
                                    pass
                            elif len(lock_parts) == 2:
                                # 旧格式: service_timestamp 或 timestamp_mode
                                try:
                                    # 尝试第二部分是否为时间戳
                                    lock_timestamp = float(lock_parts[1])
                                except (ValueError, IndexError):
                                    try:
                                        # 尝试第一部分是否为时间戳（兼容其他格式）
                                        lock_timestamp = float(lock_parts[0])
                                    except (ValueError, IndexError):
                                        pass
                            
                            if lock_timestamp:
                                current_time = time.time()
                                lock_age = current_time - lock_timestamp
                                # 对于cron模式，更激进的清理策略
                                if self.run_mode == "cron":
                                    # cron模式：清理阈值设置为任务时间的80%，快速清理异常锁
                                    # 对于2小时的任务，清理阈值约为96分钟，确保不阻塞下次启动
                                    cleanup_threshold = timeout * 0.9  # 例如：7200秒 * 0.9 = 6480秒（108分钟）
                                else:
                                    # daemon模式：更保守的清理策略
                                    cleanup_threshold = timeout + 120
                                
                                self.logger.info(f"检测到现有锁: {existing_lock}, 年龄: {lock_age:.1f}秒, 清理阈值: {cleanup_threshold}秒")
                                
                                if lock_age > cleanup_threshold:
                                    self.logger.warning(f"检测到过期锁(持续{lock_age:.1f}秒)，强制清理: {lock_key}")
                                    await redis_conn.delete(lock_key)
                                    # 清理后稍等片刻再尝试获取
                                    await asyncio.sleep(1)
                            else:
                                # 无法解析时间戳，检查TTL决定是否清理
                                ttl = await redis_conn.ttl(lock_key)
                                self.logger.warning(f"无法解析锁时间戳: {existing_lock}, TTL: {ttl}秒")
                                
                                # 如果TTL过长或者为-1（永不过期），强制清理
                                # 对于cron模式，更激进的TTL清理策略
                                if self.run_mode == "cron":
                                    # 对于2小时的任务，TTL限制约为114分钟，与expire_time保持一致
                                    max_acceptable_ttl = int(timeout * 0.95)  # 与expire_time保持一致
                                else:
                                    max_acceptable_ttl = cleanup_threshold
                                
                                if ttl == -1 or ttl > max_acceptable_ttl:
                                    self.logger.warning(f"检测到异常锁(TTL: {ttl}，超过{max_acceptable_ttl}秒)，强制清理: {lock_key}")
                                    await redis_conn.delete(lock_key)
                                    await asyncio.sleep(1)
                                
                        except (ValueError, IndexError) as e:
                            # 如果锁格式异常，也删除
                            self.logger.warning(f"检测到格式异常的锁，强制清理: {lock_key}, 错误: {str(e)}")
                            await redis_conn.delete(lock_key)
                            await asyncio.sleep(1)
                
                # 尝试获取锁，设置过期时间
                # 对于cron模式，根据任务运行时间动态调整过期时间
                if self.run_mode == "cron":
                    # cron模式：锁过期时间设置为任务时间的95%，确保下次cron启动时能获取锁
                    # 对于2小时的任务，锁过期时间约为114分钟，留6分钟给下次启动
                    expire_time = int(timeout * 0.98)  # 例如：7200秒 * 0.98 = 7056秒（117.6分钟）
                else:
                    # daemon模式：更长的过期时间
                    expire_time = timeout + 120
                lock_value = f"{self.service_name}_{time.time()}_{self.run_mode}"
                
                lock_acquired = await redis_conn.set(
                    lock_key, 
                    lock_value, 
                    nx=True, 
                    ex=expire_time
                )
                
                if lock_acquired:
                    self.logger.info(f"成功获取服务锁: {lock_key} (模式: {self.run_mode}, 过期时间: {expire_time}秒)")
                    self._acquired_lock = True
                    self._lock_value = lock_value  # 使用相同的时间戳
                    
                    # 对于长时间运行的cron任务，启动心跳机制
                    # 对于2小时这样的长任务，心跳机制是必需的
                    if self.run_mode == "cron" and timeout > 600:  # 超过10分钟的任务启动心跳
                        self._start_heartbeat(lock_key, expire_time)
                    
                    return True
                else:
                    # 如果是第一次尝试失败，检查锁的详细信息
                    if attempt == 0:
                        async with get_redis_connection() as redis_conn:
                            lock_info = await redis_conn.get(lock_key)
                            ttl = await redis_conn.ttl(lock_key)
                        self.logger.warning(f"服务锁已被占用 (尝试 {attempt + 1}/{max_retries})")
                        self.logger.warning(f"锁信息: {lock_info}, TTL: {ttl}秒")
                        
                        # 对于cron模式，如果TTL很短，等待一下再重试
                        if self.run_mode == "cron" and ttl > 0 and ttl <= 60:
                            wait_time = min(ttl + 5, 30)  # 最多等待30秒
                            self.logger.info(f"锁即将过期(TTL: {ttl}秒)，等待 {wait_time} 秒后重试...")
                            await asyncio.sleep(wait_time)
                            continue
                    
                    # 如果不是最后一次尝试，等待后重试
                    if attempt < max_retries - 1:
                        wait_time = 2 ** attempt  # 指数退避
                        self.logger.info(f"等待 {wait_time} 秒后重试...")
                        await asyncio.sleep(wait_time)
                    else:
                        self.logger.warning(f"经过 {max_retries} 次尝试，仍无法获取服务锁，跳过此次执行")
                        return False
                        
            except Exception as e:
                self.logger.error(f"获取服务锁失败 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2)
                else:
                    return False
        
        return False
    
    async def release_service_lock(self):
        """安全释放服务锁 - 只释放自己创建的锁"""
        lock_key = f"service_lock:{self.service_name}"
        try:
            # 检查当前锁是否是自己创建的
            if self._lock_value:
                 async with get_redis_connection() as redis_conn:
                    current_lock = await redis_conn.get(lock_key)
                    if current_lock == self._lock_value:
                        await redis_conn.delete(lock_key)
                        self.logger.info(f"释放服务锁: {lock_key}")
                    else:
                        self.logger.warning(f"锁已被其他实例替换，跳过释放: 期望={self._lock_value}, 实际={current_lock}")
            else:
                # 如果没有记录锁值，直接删除（兼容旧版本）
                async with get_redis_connection() as redis_conn:
                    await redis_conn.delete(lock_key)
                self.logger.info(f"释放服务锁: {lock_key}")
            
            self._acquired_lock = False
            self._lock_value = None
            
            # 停止心跳机制
            await self._stop_heartbeat()
            
        except Exception as e:
            self.logger.error(f"释放服务锁失败: {str(e)}")
    
    async def force_clear_service_lock(self):
        """强制清理服务锁（慎用）"""
        lock_key = f"service_lock:{self.service_name}"
        try:
            # 先检查锁的详细信息
            async with get_redis_connection() as redis_conn:
                lock_info = await redis_conn.get(lock_key)
                ttl = await redis_conn.ttl(lock_key)
                
                if lock_info:
                    self.logger.warning(f"强制清理锁: {lock_info}, TTL: {ttl}秒")
                    deleted = await redis_conn.delete(lock_key)
                    if deleted:
                        self.logger.info(f"强制清理服务锁成功: {lock_key}")
                        return True
                    else:
                        self.logger.error(f"强制清理服务锁失败: {lock_key}")
                        return False
                else:
                    self.logger.info(f"服务锁不存在或已过期: {lock_key}")
                    return True
        except Exception as e:
            self.logger.error(f"强制清理服务锁失败: {str(e)}")
            return False
    
    async def check_service_lock_status(self):
        """检查服务锁状态"""
        lock_key = f"service_lock:{self.service_name}"
        try:
            async with get_redis_connection() as redis_conn:
                lock_info = await redis_conn.get(lock_key)
                ttl = await redis_conn.ttl(lock_key)
            
            if lock_info:
                self.logger.info(f"锁状态 - Key: {lock_key}")
                self.logger.info(f"锁信息: {lock_info}")
                self.logger.info(f"剩余TTL: {ttl}秒")
                
                # 解析时间戳 - 支持多种格式
                try:
                    lock_timestamp = None
                    lock_parts = lock_info.split('_') # Already a string due to decode_responses=True
                    
                    if len(lock_parts) >= 3:
                        # 新格式: service_timestamp_mode
                        try:
                            lock_timestamp = float(lock_parts[1])
                        except (ValueError, IndexError):
                            pass
                    elif len(lock_parts) == 2:
                        # 旧格式: service_timestamp 或 timestamp_mode
                        try:
                            # 尝试第二部分是否为时间戳
                            lock_timestamp = float(lock_parts[1])
                        except (ValueError, IndexError):
                            try:
                                # 尝试第一部分是否为时间戳（兼容其他格式）
                                lock_timestamp = float(lock_parts[0])
                            except (ValueError, IndexError):
                                pass
                    
                    if lock_timestamp:
                        current_time = time.time()
                        elapsed = current_time - lock_timestamp
                        self.logger.info(f"锁创建时间: {time.ctime(lock_timestamp)}")
                        self.logger.info(f"已持续时间: {elapsed:.1f}秒")
                    else:
                        self.logger.warning(f"无法解析锁时间戳: {lock_info}")
                        
                except (ValueError, IndexError) as e:
                    self.logger.warning(f"锁格式异常，无法解析时间戳: {str(e)}")
                    
                return True
            else:
                self.logger.info(f"服务锁不存在: {lock_key}")
                return False
                
        except Exception as e:
            self.logger.error(f"检查服务锁状态失败: {str(e)}")
            return False
    
    def _start_heartbeat(self, lock_key: str, expire_time: int):
        """启动心跳机制，定期续期锁"""
        async def heartbeat():
            try:
                # 根据任务超时时间动态调整心跳间隔
                # 心跳间隔设置为锁过期时间的1/10，确保有足够的续期机会
                if self.timeout > 7200:  # 超过2小时的任务
                    heartbeat_interval = 240  # 4分钟 (114分钟锁过期时间的1/28)
                elif self.timeout > 3600:  # 超过1小时的任务
                    heartbeat_interval = 180  # 3分钟
                elif self.timeout > 1800:  # 超过30分钟的任务
                    heartbeat_interval = 120  # 2分钟
                elif self.timeout > 600:   # 超过10分钟的任务
                    heartbeat_interval = 60   # 1分钟
                else:
                    heartbeat_interval = 30   # 默认30秒
                while self._acquired_lock and not self._shutdown_event.is_set():
                    await asyncio.sleep(heartbeat_interval)
                    
                    if not self._acquired_lock:
                        break
                    
                    try:
                        # 检查锁是否还是自己的
                        async with get_redis_connection() as redis_conn:
                            current_lock = await redis_conn.get(lock_key)
                            if current_lock == self._lock_value:
                                # 续期锁，设置新的过期时间
                                await redis_conn.expire(lock_key, expire_time)
                                self.logger.debug(f"锁心跳续期成功: {lock_key}")
                            else:
                                # 锁被其他实例替换是正常情况，使用INFO级别
                                self.logger.info(f"🔄 锁已被其他实例替换，停止心跳: {lock_key}")
                                self._acquired_lock = False
                                break
                    except Exception as e:
                        self.logger.error(f"锁心跳续期失败: {str(e)}")
                        # 续期失败，可能Redis连接有问题，停止心跳
                        break
                        
            except Exception as e:
                self.logger.error(f"心跳任务异常: {str(e)}")
        
        # 启动心跳任务
        self._heartbeat_task = asyncio.create_task(heartbeat())
        # 计算实际的心跳间隔用于日志显示
        if self.timeout > 7200:
            interval_desc = "每4分钟"
        elif self.timeout > 3600:
            interval_desc = "每3分钟"
        elif self.timeout > 1800:
            interval_desc = "每2分钟"
        elif self.timeout > 600:
            interval_desc = "每1分钟"
        else:
            interval_desc = "每30秒"
        self.logger.info(f"💓 启动锁心跳机制: {lock_key} ({interval_desc}续期)")
    
    async def _stop_heartbeat(self):
        """停止心跳机制"""
        if self._heartbeat_task and not self._heartbeat_task.done():
            self._heartbeat_task.cancel()
            try:
                await self._heartbeat_task
            except asyncio.CancelledError:
                pass
            self.logger.info("💓 锁心跳机制已停止")
    
    async def start(self):
        """启动服务"""
        start_time = time.time()
        try:
            self.logger.info(f"🚀 正在启动 {self.service_name} 服务")
            self.logger.info(f"📋 运行模式: {self.run_mode}")
            self.logger.info(f"⏱️  超时时间: {self.timeout}秒")
            self.logger.info(f"🔄 最大并发: {self.max_concurrent_tasks}")
            self.logger.info(f"🔗 Redis连接超时: {self.redis_connection_timeout}秒")
            self.logger.info(f"🔗 Redis操作超时: {self.redis_socket_timeout}秒")
            
            # 初始化Redis管理器
            redis_manager = get_optimized_redis_manager()
            health_check = await redis_manager.health_check()
            self.logger.info(f"✅ Redis管理器初始化成功: {health_check['status']}")
            
            # 如果是cron模式且启用了服务锁，尝试获取分布式锁
            if self.run_mode == "cron" and self.use_service_lock:
                self.logger.info("🔒 尝试获取分布式锁...")
                if not await self.acquire_service_lock(self.timeout):
                    elapsed = time.time() - start_time
                    self.logger.info(f"❌ 无法获取服务锁，本次执行跳过 (耗时: {elapsed:.1f}秒)")
                    return
                else:
                    self.logger.info("🔓 成功获取分布式锁，开始处理任务")
            elif self.run_mode == "cron" and not self.use_service_lock:
                self.logger.info("⚠️  服务锁已禁用，直接开始处理任务")
            
            # 根据运行模式选择不同的处理方式
            if self.run_mode == "cron":
                await self.process_queue_with_timeout()
            else:
                await self.process_queue()
                
            # 记录总执行时间
            total_time = time.time() - start_time
            self.logger.info(f"✅ 服务执行完成，总耗时: {total_time:.1f}秒")
            
        except KeyboardInterrupt:
            self.logger.info("⚠️  收到中断信号，正在关闭服务...")
        except Exception as e:
            elapsed = time.time() - start_time
            self.logger.error(f"❌ 服务启动失败 (耗时: {elapsed:.1f}秒): {str(e)}", exc_info=True)
            raise
        finally:
            # 只有成功获取锁的实例才应该释放锁
            if self.run_mode == "cron" and self.use_service_lock and self._acquired_lock:
                try:
                    await self.release_service_lock()
                    self.logger.info("🔓 分布式锁已释放")
                except Exception as e:
                    self.logger.error(f"⚠️  释放分布式锁时出错: {str(e)}")
            elif self.run_mode == "cron" and self.use_service_lock and not self._acquired_lock:
                self.logger.info("💡 未获取到锁，跳过锁释放")
            await self.shutdown()
    
    async def shutdown(self):
        """关闭服务"""
        self.logger.info("正在关闭服务...")
        self._shutdown_event.set()
        
        # 停止心跳机制
        await self._stop_heartbeat()
        
        # 关闭Redis管理器
        redis_manager = get_optimized_redis_manager()
        await redis_manager.cleanup()
        self.logger.info("Redis管理器已关闭")
        
        self.logger.info(f"{self.service_name} 服务已关闭")


def create_service_main(service_class, run_mode: str = "daemon", timeout: int = 300, 
                       use_service_lock: bool = True, redis_connection_timeout: int = 30,
                       redis_socket_timeout: int = 30, *args, **kwargs):
    """创建服务主函数的工厂函数"""
    async def main():
        service = service_class(run_mode=run_mode, timeout=timeout, 
                               use_service_lock=use_service_lock,
                               redis_connection_timeout=redis_connection_timeout,
                               redis_socket_timeout=redis_socket_timeout, *args, **kwargs)
        await service.start()
    
    def run_main():
        try:
            asyncio.run(main())
        except KeyboardInterrupt:
            pass
        finally:
            # 确保Redis管理器正确关闭
            try:
                import asyncio as aio
                # Redis连接会自动清理，无需手动cleanup
                pass
            except Exception:
                pass  # 静默处理cleanup错误
    
    return run_main 