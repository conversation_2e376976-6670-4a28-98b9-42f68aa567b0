"""
增强版深度搜索智能体集成模块
集成真正的PPLX搜索和网络爬虫功能到账号诊断系统中
基于company_profile_init.py的搜索能力进行深度研究
集成增强版TaskProgressTracker，支持实时状态推送
"""

import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime
from typing import Dict, Tuple, Optional, List, Callable, Any
from enum import Enum

# 导入动态年份处理函数
from .layered_query_generator import get_dynamic_year_range, get_time_related_keywords

# 设置日志
logger = logging.getLogger(__name__)

# 导入现有的AI调用模块
try:
    from task import callWattGPT
    CALLWATTGPT_AVAILABLE = True
    logger.info("callWattGPT模块导入成功，PPLX搜索功能可用")
except ImportError as e:
    logger.error(f"无法导入callWattGPT模块: {e}")
    CALLWATTGPT_AVAILABLE = False

try:
    from task.lib.call_claude import gemini as _original_gemini
    
    # 创建一个安全的包装函数
    def safe_gemini(sys_prompt, user_prompt, model=None):
        try:
            response = _original_gemini(sys_prompt, user_prompt, model)
            # 处理新的 3 个返回值格式
            if isinstance(response, tuple) and len(response) == 3:
                status, result, usage_metadata = response
                if status and isinstance(result, str) and len(result) > 0:
                    return True, result
                else:
                    return False, f"Gemini调用失败: {result}"
            elif isinstance(response, tuple) and len(response) == 2:
                status, result = response
                if status and isinstance(result, str) and len(result) > 0:
                    return True, result
                else:
                    return False, f"Gemini调用失败: {result}"
            else:
                return False, f"Gemini返回格式异常: {response}"
        except Exception as e:
            return False, f"Gemini函数调用异常: {str(e)}"
    
    gemini = safe_gemini
    GEMINI_AVAILABLE = True
    
except ImportError as e:
    logger.error(f"无法导入gemini函数: {e}")
    GEMINI_AVAILABLE = False
    
    def gemini(sys_prompt, user_prompt, model=None):
        return False, "Gemini函数不可用，请检查配置"


if __name__ == "__main__":
    # The main logic from this file has been refactored into the `core` module.
    # The test functions below are now obsolete as they test the legacy implementation.
    # To test the new implementation, please refer to the unit and integration tests
    # for the `core` modules, such as `tests/unit/test_diagnosis_engine.py`.
    
    # 示例:
    # asyncio.run(test_new_diagnosis_engine())
    pass


# 清理临时文件
def cleanup_temp_files():
    """清理临时文件"""
    import glob
    temp_files = glob.glob("tmp_rovodev_*")
    for file in temp_files:
        try:
            os.remove(file)
            logger.info(f"已删除临时文件: {file}")
        except Exception as e:
            logger.warning(f"删除临时文件失败 {file}: {e}")

# 在模块加载时清理临时文件
# cleanup_temp_files()

async def optimize_industry_terminology(query: str, industry: str = "科技") -> str:
    """
    优化查询中的行业术语
    
    Args:
        query: 原始查询
        industry: 行业类型
        
    Returns:
        str: 优化后的查询
    """
    try:
        # 科技行业术语库
        tech_terms = {
            "通用技术": ["AI", "机器学习", "深度学习", "大数据", "云计算", "区块链", "物联网"],
            "开发技术": ["前端", "后端", "全栈", "DevOps", "微服务", "容器化", "Kubernetes"],
            "新兴技术": ["Web3", "元宇宙", "NFT", "量子计算", "边缘计算", "低代码", "RPA"],
            "平台技术": ["SaaS", "PaaS", "IaaS", "serverless", "云原生", "多云", "混合云"],
            "安全技术": ["网络安全", "数据安全", "隐私保护", "零信任", "身份认证", "加密技术"],
            "数据技术": ["数据科学", "数据分析", "商业智能", "数据可视化", "ETL", "数据仓库"],
            "移动技术": ["移动开发", "跨平台", "原生应用", "PWA", "小程序", "Flutter", "React Native"],
            "运营术语": ["增长黑客", "用户运营", "产品运营", "内容运营", "社群运营", "私域流量"],
            "商业术语": ["MVP", "PMF", "B2B", "B2C", "SaaS", "订阅模式", "freemium", "付费转化"],
            "创业术语": ["Startup", "种子轮", "A轮", "B轮", "独角兽", "风投", "天使投资", "孵化器"]
        }
        
        # 根据查询内容自动添加相关术语
        optimized_query = query
        
        # 检查查询中是否包含足够的行业术语
        term_count = 0
        for category, terms in tech_terms.items():
            for term in terms:
                if term in query:
                    term_count += 1
        
        # 如果术语不足，智能添加相关术语
        if term_count < 3:
            # 根据查询内容添加相关术语
            if "账号" in query and "增长" in query:
                optimized_query = query.replace("科技账号", "AI/SaaS/区块链/物联网科技账号")
                optimized_query = optimized_query.replace("增长策略", "增长黑客方法论、社群运营、KOL合作策略")
            
            if "头部账号" in query:
                optimized_query = query.replace("头部账号", "AI/深度学习/云原生/Web3头部技术账号")
                optimized_query = optimized_query.replace("成功要素", "技术深度内容、开源贡献、技术栈选型、架构设计分享等核心竞争力")
            
            if "政策" in query and "合规" in query:
                optimized_query = query.replace("科技账号", "AI/大数据/云计算Startup")
                optimized_query = optimized_query.replace("合规要求", "SDK集成、用户画像、行为分析、精准营销等技术实现的合规改造")
            
            if "细分市场" in query:
                optimized_query = query.replace("科技细分市场", "边缘计算/低代码/RPA/DevOps/云安全等新兴技术细分赛道")
                optimized_query = optimized_query.replace("内容机会", "技术入门教程、实战案例、工具对比、最佳实践等长尾需求")
            
            if "运营策略" in query:
                optimized_query = query.replace("运营策略", "技术博客、GitHub开源项目、技术视频教程、线上技术分享、Hackathon参与等多维度内容运营模式")
        
        logger.info(f"查询术语优化：{term_count}个术语 -> 优化后查询")
        return optimized_query
        
    except Exception as e:
        logger.error(f"优化行业术语失败: {e}")
        return query

def analyze_query_terminology(query: str) -> Dict:
    """
    分析查询中的行业术语使用情况
    
    Args:
        query: 待分析的查询
        
    Returns:
        Dict: 分析结果
    """
    try:
        # 科技行业关键术语列表
        tech_keywords = [
            "AI", "机器学习", "深度学习", "大数据", "云计算", "区块链", "物联网",
            "前端", "后端", "全栈", "DevOps", "微服务", "容器化", "Kubernetes",
            "Web3", "元宇宙", "NFT", "量子计算", "边缘计算", "低代码", "RPA",
            "SaaS", "PaaS", "IaaS", "serverless", "云原生", "多云", "混合云",
            "网络安全", "数据安全", "隐私保护", "零信任", "身份认证", "加密技术",
            "数据科学", "数据分析", "商业智能", "数据可视化", "ETL", "数据仓库",
            "移动开发", "跨平台", "原生应用", "PWA", "小程序", "Flutter", "React Native",
            "增长黑客", "用户运营", "产品运营", "内容运营", "社群运营", "私域流量",
            "MVP", "PMF", "B2B", "B2C", "订阅模式", "freemium", "付费转化",
            "Startup", "种子轮", "A轮", "B轮", "独角兽", "风投", "天使投资", "孵化器"
        ]
        
        # 统计术语使用情况
        found_terms = []
        for term in tech_keywords:
            if term in query:
                found_terms.append(term)
        
        # 分析结果
        analysis = {
            "total_terms": len(found_terms),
            "found_terms": found_terms,
            "is_sufficient": len(found_terms) >= 2,  # 至少需要2个行业术语
            "recommendations": []
        }
        
        # 生成建议
        if len(found_terms) < 2:
            analysis["recommendations"].append("建议添加更多具体的技术领域术语")
            analysis["recommendations"].append("建议使用具体的技术栈或平台名称")
            analysis["recommendations"].append("建议加入当前热门的技术关键词")
        
        return analysis
        
    except Exception as e:
        logger.error(f"分析查询术语失败: {e}")
        return {"error": str(e)}

# 批量优化查询术语
async def batch_optimize_queries(queries: List[str], industry: str = "科技") -> List[str]:
    """
    批量优化查询中的行业术语
    
    Args:
        queries: 查询列表
        industry: 行业类型
        
    Returns:
        List[str]: 优化后的查询列表
    """
    optimized_queries = []
    
    for query in queries:
        optimized_query = await optimize_industry_terminology(query, industry)
        optimized_queries.append(optimized_query)
    
    return optimized_queries
