# 统一异步服务框架

## 概述

这是一个为Watt AI项目设计的统一异步任务处理框架，用于重构和管理多个诊断相关的服务脚本。框架提供了统一的Redis连接管理、AI调用、日志记录、错误处理等功能，同时保持了各个服务脚本的独立性。

## 架构设计

### 核心组件

1. **BaseAsyncService**: 异步服务基类，提供通用功能
2. **AsyncRedisManager**: Redis连接和队列操作管理器
3. **ServiceImplementations**: 具体服务实现类
4. **ServiceConfig**: 基本服务配置管理

### 设计原则

- **组件分离**: Redis操作、AI调用、业务逻辑独立
- **统一抽象**: 所有服务使用相同的基础框架
- **独立运行**: 每个脚本可以独立启动和监控
- **配置简单**: 最小化配置，专注核心功能
- **错误隔离**: 进程级别的故障隔离

## 文件结构

```
task/server/account_diagnosis/
├── base_async_service.py          # 基础异步服务框架
├── service_implementations.py     # 具体服务实现
├── service_config.py             # 基本服务配置
├── new_script_for_diagnosis.py    # 新版诊断脚本
├── new_script_for_strategy.py     # 新版策略脚本
├── new_script_for_review.py       # 新版复盘脚本
├── new_script_for_cover_gen.py    # 新版封面生成脚本
└── README.md                      # 本文档
```

## 服务列表

| 服务名称 | 功能描述 | 输入队列 | 输出队列 |
|---------|---------|---------|---------|
| diagnosis | 账户诊断 | `{env}:q:diagnosis:request` | `{env}:q:diagnosis:response` |
| strategy | 策略报告生成 | `{env}:q:generate:report:socialmedia:request` | `{env}:q:generate:report:socialmedia:response` |
| review | 运营复盘报告 | `{env}:q:generate:report:operation:request` | `{env}:q:generate:report:operation:response` |
| cover | 封面生成 | `{env}:q:generate:cover:request` | `{env}:q:generate:cover:response` |

## 使用方法

### 直接运行脚本

```bash
# 运行诊断服务
python -m task.server.account_diagnosis.new_script_for_diagnosis

# 运行策略服务
python -m task.server.account_diagnosis.new_script_for_strategy

# 运行复盘服务
python -m task.server.account_diagnosis.new_script_for_review

# 运行封面生成服务
python -m task.server.account_diagnosis.new_script_for_cover_gen
```

### 配置环境变量

```bash
# 必需的环境变量
export ENV=PRODUCTION
export REDIS_HOST=your-redis-host
export REDIS_PASSWORD=your-redis-password

# 可选的环境变量
export MAX_CONCURRENT_TASKS=8
export LOG_LEVEL=INFO
```

## 配置说明

### 服务配置

每个服务的配置在 `service_config.py` 中定义：

```python
ServiceConfig(
    name="diagnosis",
    input_queue=f"{env}:q:diagnosis:request",
    output_queue=f"{env}:q:diagnosis:response",
    max_concurrent_tasks=8,
    ai_timeout=180
)
```

## 监控和日志

### 日志文件

每个服务生成独立的日志文件：
- `logs/async_diagnosis_service.log`
- `logs/async_strategy_service.log`
- `logs/async_review_service.log`
- `logs/async_cover_service.log`

### 日志格式

```
2024-01-01 12:00:00 - async_diagnosis_service - INFO - [script.py:100] - [task_id:task-123] - 任务处理完成
```

## 扩展新服务

### 1. 创建服务实现类

```python
class NewService(BaseAsyncService):
    def __init__(self, max_concurrent_tasks: int = 3):
        input_queue = ENV.lower() + ":q:new:request"
        output_queue = ENV.lower() + ":q:new:response"
        super().__init__("new", input_queue, output_queue, max_concurrent_tasks)
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        # 实现具体的业务逻辑
        return True, {"result": "success"}
```

### 2. 创建独立脚本

```python
from .service_implementations import NewService
from .base_async_service import create_service_main

main = create_service_main(NewService, max_concurrent_tasks=8)

if __name__ == "__main__":
    main()
```

### 3. 更新配置

在 `service_config.py` 中添加新服务的配置。

## 故障排查

### 常见问题

1. **Redis连接失败**
   - 检查Redis配置和网络连接
   - 确认密码和SSL设置正确

2. **AI调用超时**
   - 增加 `ai_timeout` 配置
   - 检查API密钥和网络状况

3. **任务处理失败**
   - 查看详细日志文件
   - 检查输入数据格式

4. **内存不足**
   - 减少 `max_concurrent_tasks`
   - 增加容器内存限制

### 调试模式

设置环境变量启用调试模式：

```bash
export LOG_LEVEL=DEBUG
export AI_TIMEOUT=300  # 增加超时时间
```

## 性能优化

### 并发控制

- 根据资源情况调整 `max_concurrent_tasks`（默认为8）
- 监控Redis连接池使用情况
- 观察AI API调用频率限制

### 资源配置

- CPU: 根据AI调用频率配置
- 内存: 根据任务数据大小配置
- 网络: 确保Redis和AI API连接稳定

## 版本迁移

### 从旧版本迁移

1. 保留原有脚本作为备份
2. 逐步切换到新版本脚本
3. 验证队列和数据格式兼容性
4. 更新监控和告警配置

### 兼容性说明

- Redis队列名称保持不变
- 输入输出数据格式兼容
- 日志格式有所改进
- 错误处理更加健壮

## 核心优势

1. **统一抽象**: 所有服务共享相同的基础设施代码
2. **独立部署**: 每个脚本可以独立运行和监控
3. **易于维护**: 通用逻辑集中管理，业务逻辑分离
4. **生产就绪**: 包含完整的错误处理、日志记录、连接管理
5. **简单配置**: 最小化配置，专注核心功能 