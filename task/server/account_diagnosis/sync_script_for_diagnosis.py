import json
import redis
import time
import ssl
from typing import Dict
import os, sys, re
import threading
import concurrent.futures
import logging
from logging.handlers import RotatingFileHandler
from jinja2 import Environment, FileSystemLoader
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..','..')))
from task import REDIS_CLUSTER_CONFIG, ENV, GEMINI_PRO_MODEL, GEMINI_FLASH_MODEL
from task.lib.call_claude import claude, gemini, gpt

# 导入或定义Redis序列化函数
try:
    from task.lib.json_utils import safe_redis_serialize_with_validation
except ImportError:
    # 如果导入失败，使用本地实现
    def safe_redis_serialize_with_validation(data: dict) -> str:
        """双重JSON序列化函数 - 确保与Java后端兼容"""
        try:
            # 第一次序列化：Python对象 -> JSON字符串
            first_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            # 第二次序列化：JSON字符串 -> 转义的JSON字符串
            result = json.dumps(first_json, ensure_ascii=False)
            return result
        except Exception as e:
            raise ValueError(f"Failed to serialize data to double JSON format: {e}")

# 配置日志系统
def setup_logging():
    """设置日志系统"""
    # 确保logs目录存在
    os.makedirs('logs', exist_ok=True)
    
    # 创建logger
    logger = logging.getLogger('diagnosis_service')
    logger.setLevel(logging.INFO)
    
    # 清除已有的handlers
    logger.handlers.clear()
    
    # 创建文件handler（带轮转）
    file_handler = RotatingFileHandler(
        'logs/diagnosis_service.log',
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    # 创建控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    # 创建formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    # 添加handlers
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    return logger

# 初始化日志系统
logger = setup_logging()


REDIS_CLUSTER_CONFIG.update({
    'ssl_cert_reqs': ssl.CERT_NONE,
    'ssl_ca_certs': None,
})
INPUT_QUEUE = ENV.lower() + ":q:diagnosis:request"
OUTPUT_QUEUE = ENV.lower() + ":q:diagnosis:response"

def create_redis_client():
    """创建Redis连接，包含重试机制"""
    max_retries = 3
    for attempt in range(max_retries):
        try:
            client = redis.RedisCluster(
                host=REDIS_CLUSTER_CONFIG['host'],
                port=REDIS_CLUSTER_CONFIG['port'],
                password=REDIS_CLUSTER_CONFIG['password'],
                ssl=REDIS_CLUSTER_CONFIG['ssl'],
                ssl_cert_reqs=REDIS_CLUSTER_CONFIG['ssl_cert_reqs'],
                ssl_ca_certs=REDIS_CLUSTER_CONFIG['ssl_ca_certs'],
                decode_responses=REDIS_CLUSTER_CONFIG['decode_responses'],
                skip_full_coverage_check=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                max_connections=20
            )
            # 测试连接
            client.ping()
            return client
        except Exception as e:
            logger.warning(f"Redis连接失败，尝试 {attempt + 1}/{max_retries}: {str(e)}")
            if attempt == max_retries - 1:
                raise
            time.sleep(2 ** attempt)  # 指数退避

redis_client = create_redis_client()


# 全局缓存Jinja2环境，避免重复创建
_jinja_env = None

def get_jinja_env():
    """获取或创建Jinja2环境，使用单例模式"""
    global _jinja_env
    if _jinja_env is None:
        _jinja_env = Environment(
            loader=FileSystemLoader('task/server/account_diagnosis/prompts'), 
            lstrip_blocks=True, 
            trim_blocks=True
        )
        _jinja_env.filters['tojson'] = lambda obj: json.dumps(obj, ensure_ascii=False, indent=2)
    return _jinja_env

def get_prompt(prompt_dir, data):
    """获取渲染后的提示词"""
    try:
        env = get_jinja_env()
        prompt_template = env.get_template(prompt_dir)
        return prompt_template.render(data)
    except Exception as e:
        logger.error(f"模板渲染错误: {str(e)}")
        raise


def conduct_diagnosis(input_data: Dict) -> (bool, str):
    """执行诊断，包含完整的错误处理和重试机制"""
    try:
        # 验证输入数据
        required_fields = ["accountInfo", "noteList", "marketingGoal"]
        for field in required_fields:
            if field not in input_data:
                return False, f"缺少必需字段: {field}"
        
        goal_dict = {
            "引流私域": 1,
            "带货变现": 2,
            "品牌曝光": 3,
            "涨粉提升": 4,
        }
        
        account_data = {
            "account": input_data["accountInfo"],
            "noteList": input_data["noteList"],
            "industry": None if input_data.get("industry") == 'empty' else input_data.get("industry"),
            "marketingGoal": goal_dict.get(input_data["marketingGoal"], 1)
        }
        
        sys_prompt = "你是一位资深的小红书运营策略专家，拥有多年针对不同行业账号进行诊断和优化的实战经验。请对目标账号进行深度分析并提供具有高度可操作性的优化方案。回复只能包含方案本身，不要有任何介绍性的开场白"
        
        try:
            from task.lib.prompt_utils import get_diagnosis_prompt
            user_prompt = get_diagnosis_prompt(account_data)
        except Exception as e:
            return False, f"模板渲染失败: {str(e)}"
        
        now = time.time()
        max_retries = 2
        
        for attempt in range(max_retries):
            try:
                status, result = gemini(sys_prompt, user_prompt, GEMINI_FLASH_MODEL)
                if status:
                    logger.info(f"诊断时间: {time.time()-now:.2f}秒")
                    return True, result
                else:
                    logger.warning(f"AI调用失败，尝试 {attempt + 1}/{max_retries}: {str(result)}")
                    if attempt == max_retries - 1:
                        return False, f"AI调用最终失败: {str(result)}"
                    time.sleep(2)
                    
            except Exception as e:
                logger.error(f"AI调用异常，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    return False, f"AI调用异常: {str(e)}"
                time.sleep(2)
                
    except Exception as e:
        return False, f"诊断过程发生异常: {str(e)}"
    
    return False, "未知错误"


def call_ai_with_retry(ai_function, sys_prompt, user_prompt, model_or_schema=None, task_name="AI任务", max_retries=2):
    """
    通用AI调用函数，包含重试机制和错误处理
    
    Args:
        ai_function: AI调用函数 (gemini, gpt等)
        sys_prompt: 系统提示词
        user_prompt: 用户提示词  
        model_or_schema: 模型名称或JSON schema
        task_name: 任务名称，用于日志记录
        max_retries: 最大重试次数
        
    Returns:
        tuple: (成功状态, 结果)
    """
    start_time = time.time()
    
    for attempt in range(max_retries):
        try:
            if model_or_schema:
                status, result = ai_function(sys_prompt, user_prompt, model_or_schema)
            else:
                status, result = ai_function(sys_prompt, user_prompt)
                
            if status:
                logger.info(f"{task_name}生成时间：{time.time() - start_time:.2f}秒")
                return True, result
            else:
                logger.warning(f"{task_name}调用失败，尝试 {attempt + 1}/{max_retries}: {str(result)}")
                if attempt == max_retries - 1:
                    return False, f"{task_name}调用最终失败: {str(result)}"
                time.sleep(2)
                
        except Exception as e:
            logger.error(f"{task_name}调用异常，尝试 {attempt + 1}/{max_retries}: {str(e)}")
            if attempt == max_retries - 1:
                return False, f"{task_name}调用异常: {str(e)}"
            time.sleep(2)
    
    return False, f"{task_name}未知错误"


def generate_html(diagnosis_result: str) -> (bool,  str):
    """生成HTML报告"""
    from task.lib.prompt_utils import get_html_generation_prompts, clean_html_output
    
    try:
        sys_prompt, user_prompt = get_html_generation_prompts(diagnosis_result)
        status, result = call_ai_with_retry(gemini, sys_prompt, user_prompt, GEMINI_PRO_MODEL, "HTML报告")
        
        if status:
            # 清理 HTML 输出，移除可能的 markdown 代码块标记
            cleaned_result = clean_html_output(result)
            return True, cleaned_result
        else:
            return False, result
            
    except Exception as e:
        logger.error(f"获取HTML生成提示词失败: {str(e)}")
        return False, f"模板渲染失败: {str(e)}"


def generate_json(diagnosis_result: str) -> (bool, dict):
    """生成JSON格式的诊断报告"""
    from task.lib.prompt_utils import get_json_generation_prompts
    
    try:
        sys_prompt, user_prompt = get_json_generation_prompts(diagnosis_result)
        return call_ai_with_retry(gpt, sys_prompt, user_prompt, json_schema_gpt, "JSON诊断报告")
    except Exception as e:
        logger.error(f"获取JSON生成提示词失败: {str(e)}")
        return False, f"模板渲染失败: {str(e)}"


def generate_sales_proposal(diagnosis_result: str) -> (bool, dict):
    """生成销售提案"""
    from task.lib.prompt_utils import get_sales_proposal_prompts, clean_markdown_output
    
    try:
        sys_prompt, user_prompt = get_sales_proposal_prompts(diagnosis_result)
        status, result = call_ai_with_retry(gpt, sys_prompt, user_prompt, None, "销售提案")
        
        if status:
            # 清理 Markdown 输出，移除可能的代码块标记
            cleaned_result = clean_markdown_output(result)
            return True, cleaned_result
        else:
            return False, result
            
    except Exception as e:
        logger.error(f"获取销售提案生成提示词失败: {str(e)}")
        return False, f"模板渲染失败: {str(e)}"

def write_to_redis(redis_client: redis.RedisCluster, queue_name: str, data) -> bool:
    """
    写入数据到Redis队列，包含重试机制
    
    Args:
        redis_client: Redis客户端
        queue_name: 队列名称
        data: 要写入的数据
        
    Returns:
        bool: 写入是否成功
    """
    max_retries = 3
    for attempt in range(max_retries):
        try:
            # 将数据写入队列
            redis_client.rpush(queue_name, data)
            
            # 验证数据是否写入成功
            queue_length = redis_client.llen(queue_name)
            logger.info(f"{queue_name} 队列长度: {queue_length}")
            return True
            
        except redis.ConnectionError as e:
            logger.warning(f"Redis连接错误，尝试 {attempt + 1}/{max_retries}: {str(e)}")
            if attempt == max_retries - 1:
                return False
            # 重新创建连接
            try:
                redis_client = create_redis_client()
                time.sleep(1)
            except Exception:
                pass
                
        except Exception as e:
            logger.error(f"写入数据时发生错误，尝试 {attempt + 1}/{max_retries}: {str(e)}")
            if attempt == max_retries - 1:
                return False
            time.sleep(1)
    
    return False


# 添加文件锁
file_locks = {
    'diagnosis': threading.Lock(),
    'html': threading.Lock(),
    'json': threading.Lock(),
    'sales': threading.Lock()
}


def write_to_text(tmp_data, task):
    """把数据写入当前路径下的txt文件中，使用线程锁保证安全"""
    max_retries = 3
    
    # 确保logs目录存在
    try:
        os.makedirs('logs', exist_ok=True)
    except Exception as e:
        logger.error(f"创建logs目录失败: {str(e)}")
        return
    
    file_mapping = {
        1: ('diagnosis_false.txt', 'diagnosis'),
        2: ('html_false.txt', 'html'),
        3: ('json_false.txt', 'json'),
        4: ('sales_false.txt', 'sales')
    }
    
    if task not in file_mapping:
        logger.error(f"未知的任务类型: {task}")
        return
        
    filename, lock_key = file_mapping[task]
    file_path = os.path.join('logs', filename)
    
    # 使用对应的锁来保护文件写入
    with file_locks[lock_key]:
        for attempt in range(max_retries):
            try:
                # 添加时间戳和分隔符
                timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
                separator = "=" * 50
                
                with open(file_path, 'a', encoding='utf-8') as f:
                    f.write(f"\n{separator}\n")
                    f.write(f"时间: {timestamp}\n")
                    f.write(f"任务类型: {task}\n")
                    f.write(f"数据: {str(tmp_data)}\n")
                    f.write(f"{separator}\n")
                    f.flush()  # 确保数据写入磁盘
                break
                
            except PermissionError as e:
                logger.warning(f"文件权限错误，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"文件写入最终失败: {str(e)}")
                time.sleep(0.1)
                
            except Exception as e:
                logger.error(f"写入文件时发生错误，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    logger.error(f"文件写入最终失败: {str(e)}")
                time.sleep(0.1)


def process_single_task(input_data):
    """处理单个任务的函数，包含完整的错误处理"""
    task_id = input_data.get("taskInfo", {}).get("taskId", "未知任务")
    
    try:
        logger.info(f"开始处理任务: {task_id}")
        
        # 生成报告
        status, diagnosis_result = conduct_diagnosis(input_data)
        if not status:
            error_msg = f"生成报告失败: {diagnosis_result}"
            logger.error(error_msg)
            write_to_text({"error": error_msg, "input": input_data}, 1)
            return

        html, json_report, sales_proposal = None, None, None
        
        # 并行处理三个生成任务，带超时控制
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            future_html = executor.submit(generate_html, diagnosis_result)
            future_json = executor.submit(generate_json, diagnosis_result)
            future_sales = executor.submit(generate_sales_proposal, diagnosis_result)
            
            # 等待结果，设置超时
            timeout = 300  # 5分钟超时
            
            try:
                status_html, html_result = future_html.result(timeout=timeout)
                if not status_html:
                    error_msg = f"HTML生成失败: {html_result}"
                    logger.error(error_msg)
                    write_to_text({"error": error_msg, "diagnosis": diagnosis_result}, 2)
                else:
                    html = extract_full_html(html_result)
            except concurrent.futures.TimeoutError:
                logger.warning("HTML生成超时")
                future_html.cancel()
            except Exception as e:
                logger.error(f"HTML生成异常: {str(e)}")

            try:
                status_json, json_result = future_json.result(timeout=timeout)
                if not status_json:
                    error_msg = f"JSON生成失败: {json_result}"
                    logger.error(error_msg)
                    write_to_text({"error": error_msg, "diagnosis": diagnosis_result}, 3)
                else:
                    json_report = json_result
            except concurrent.futures.TimeoutError:
                logger.warning("JSON生成超时")
                future_json.cancel()
            except Exception as e:
                logger.error(f"JSON生成异常: {str(e)}")
            
            try:
                status_sales, sales_result = future_sales.result(timeout=timeout)
                if not status_sales:
                    error_msg = f"销售提案生成失败: {sales_result}"
                    logger.error(error_msg)
                    write_to_text({"error": error_msg, "diagnosis": diagnosis_result}, 4)
                else:
                    sales_proposal = sales_result
            except concurrent.futures.TimeoutError:
                logger.warning("销售提案生成超时")
                future_sales.cancel()
            except Exception as e:
                logger.error(f"销售提案生成异常: {str(e)}")

        # 构建返回数据
        return_data = {
            "taskInfo": input_data.get("taskInfo", {}),
            "diagnosisResult": diagnosis_result,
            "diagnosisHtml": html,
            "diagnosisReport": json.dumps(json_report, ensure_ascii=False, indent=2),
            "salesProposal": sales_proposal
        }
        
        # 写入结果到Redis
        try:
            # 使用双重序列化确保与Java后端兼容
            result_json = safe_redis_serialize_with_validation(return_data)
            if write_to_redis(redis_client, OUTPUT_QUEUE, result_json):
                logger.info(f"任务处理完成: {task_id}")
                logger.info("=== 任务处理完成 ===\n")
            else:
                logger.error(f"任务 {task_id} 写入结果失败")
        except Exception as e:
            logger.error(f"序列化结果数据失败: {str(e)}")
            
    except KeyError as e:
        error_msg = f"输入数据缺少必需字段: {str(e)}"
        logger.error(error_msg)
        write_to_text({"error": error_msg, "input": input_data}, 1)
    except Exception as e:
        error_msg = f"处理任务时发生严重错误: {str(e)}"
        logger.error(error_msg)
        write_to_text({"error": error_msg, "input": input_data, "task_id": task_id}, 1)


def process_queue():
    """使用线程池处理队列中的任务"""
    global redis_client
    max_workers = 5
    batch_timeout = 30  # 批处理超时时间
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        start_time = time.time()
        
        # 动态获取任务，避免创建空的future
        while len(futures) < max_workers and (time.time() - start_time) < batch_timeout:
            try:
                task_data = redis_client.blpop(INPUT_QUEUE, timeout=1)
                if task_data:
                    _, input_json = task_data
                    logger.info("\n=== 收到新任务 ===")
                    
                    try:
                        # 解析JSON数据
                        input_data = parse_input_data(input_json)
                        
                        # 提交任务到线程池
                        future = executor.submit(process_single_task, input_data)
                        futures.append(future)
                        
                    except (json.JSONDecodeError, ValueError) as e:
                        logger.error(f"数据解析错误: {str(e)}")
                    except Exception as e:
                        logger.error(f"处理任务时发生错误: {str(e)}")
                else:
                    # 没有任务时短暂等待
                    break
                    
            except redis.ConnectionError as e:
                logger.error(f"Redis连接错误: {str(e)}")
                # 尝试重新连接
                try:
                    redis_client = create_redis_client()
                except Exception:
                    break
            except Exception as e:
                logger.error(f"获取任务时发生错误: {str(e)}")
                break
        
        # 等待所有任务完成
        if futures:
            concurrent.futures.wait(futures, timeout=300)  # 5分钟超时
            
            # 检查未完成的任务
            for future in futures:
                if not future.done():
                    logger.warning("警告: 检测到未完成的任务")
                    future.cancel()


def parse_input_data(input_json):
    """解析输入数据，提取到单独函数便于测试和复用"""
    if isinstance(input_json, str):
        input_data = json.loads(input_json)
        if isinstance(input_data, str):
            input_data = json.loads(input_data)
    else:
        input_data = input_json
        
    if not isinstance(input_data, dict):
        raise ValueError("数据解析后不是字典格式")
        
    return input_data


# 简化的JSON Schema - 减少复杂度和嵌套结构
json_schema_gpt = {
    "name": "generate_diagnosis_report",
    "description": "根据诊断结果生成报告",
    "strict": False,  # 放宽严格模式以减少处理复杂度
    "schema": {
        "type": "object",
        "properties": {
            "summary": {
                "type": "string",
                "description": "当前账号问题和建议的完整概括，50-100字"
            },
            "tags": {
                "type": "array",
                "description": "3个固定维度的账号指标",
                "items": {
                    "type": "object",
                    "properties": {
                        "dimension": {
                            "type": "string",
                            "enum": ["CURRENT STATUS", "GROWTH POTENTIAL", "FOCUS NEEDED"]
                        },
                        "status": {"type": "string"}
                    },
                    "required": ["dimension", "status"]
                },
                "minItems": 3,
                "maxItems": 3
            },
            "bottleneck": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "area": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "title_en": {"type": "string"},
                                "des": {"type": "string"}
                            },
                            "required": ["title", "des"]
                        }
                    }
                },
                "required": ["title", "area"]
            },
            "content_analysis": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "des": {"type": "string", "description": "完整的内容分析，不能有省略号"},
                    "title_en": {"type": "string"}
                },
                "required": ["title", "des"]
            },
            "ip_analysis": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "des": {"type": "string", "description": "完整的IP分析，不能有省略号"},
                    "title_en": {"type": "string"}
                },
                "required": ["title", "des"]
            },
            "optimize_dimension": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "areas": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "question": {"type": "string", "description": "具体问题描述，不能是String等占位符"}
                            },
                            "required": ["name", "question"]
                        }
                    }
                },
                "required": ["title", "areas"]
            },
            "suggestion": {
                "type": "array",
                "description": "优化建议",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string"},
                        "content": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "具体可执行的建议，不能有占位符"
                        }
                    },
                    "required": ["title", "content"]
                }
            }
        },
        "required": ["summary", "tags", "bottleneck", "content_analysis", "ip_analysis", "optimize_dimension", "suggestion"]
    }
}


def extract_full_html(text):
    pattern = r'<!DOCTYPE html.*?>.*?</html>'
    match = re.search(pattern, text, flags=re.DOTALL | re.IGNORECASE)
    if match:
        return match.group()  # 返回匹配到的第一个完整 HTML 文本
    else:
        return text


import signal
import sys

# 全局变量控制服务运行状态
service_running = True

def signal_handler(signum, frame):
    """信号处理器，用于优雅关闭"""
    global service_running
    logger.info(f"\n收到信号 {signum}，准备优雅关闭服务...")
    service_running = False

def main():
    """主函数，包含优雅关闭机制"""
    global service_running
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)   # Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler)  # 终止信号
    
    start_time = time.time()
    
    logger.info("启动诊断服务...")
    logger.info("按 Ctrl+C 优雅关闭服务")
    
    try:
        while service_running:
                
            try:
                process_queue()
                # 短暂休息，避免CPU过度占用
                time.sleep(0.1)
                
            except KeyboardInterrupt:
                logger.info("收到键盘中断信号")
                break
            except Exception as e:
                logger.error(f"处理队列时发生错误: {str(e)}")
                time.sleep(5)  # 发生错误时等待5秒再继续
                
    except Exception as e:
        logger.error(f"服务运行时发生严重错误: {str(e)}")
    finally:
        logger.info("正在关闭服务...")
        # 清理资源
        try:
            if 'redis_client' in globals():
                redis_client.close()
                logger.info("Redis连接已关闭")
        except Exception as e:
            logger.error(f"关闭Redis连接时发生错误: {str(e)}")
        
        logger.info("服务已安全关闭")

if __name__ == "__main__":
    main()

