"""
Unit tests for diagnosis_engine module
"""

import unittest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.diagnosis_engine import (
    DiagnosisEngine, DiagnosisContext, DiagnosisMode, ExecutionStage,
    StageResult, create_diagnosis_engine, quick_diagnose
)
from core.status_manager import UnifiedTaskStatus
from core.config_manager import DiagnosisConfig


class TestDiagnosisMode(unittest.TestCase):
    """测试诊断模式枚举"""
    
    def test_diagnosis_modes(self):
        """测试诊断模式值"""
        self.assertEqual(DiagnosisMode.BASIC.value, "basic")
        self.assertEqual(DiagnosisMode.DEEP_RESEARCH.value, "deep_research")


class TestExecutionStage(unittest.TestCase):
    """测试执行阶段枚举"""
    
    def test_execution_stages(self):
        """测试执行阶段值"""
        self.assertEqual(ExecutionStage.INITIALIZATION.value, "initialization")
        self.assertEqual(ExecutionStage.DATA_EXTRACTION.value, "data_extraction")
        self.assertEqual(ExecutionStage.ANALYSIS.value, "analysis")
        self.assertEqual(ExecutionStage.REPORT_GENERATION.value, "report_generation")
        self.assertEqual(ExecutionStage.FINALIZATION.value, "finalization")


class TestDiagnosisContext(unittest.TestCase):
    """测试诊断上下文"""
    
    def test_initialization(self):
        """测试初始化"""
        context = DiagnosisContext(
            task_id="test_task_001",
            user_id="test_user",
            mode=DiagnosisMode.BASIC
        )
        
        self.assertEqual(context.task_id, "test_task_001")
        self.assertEqual(context.user_id, "test_user")
        self.assertEqual(context.mode, DiagnosisMode.BASIC)
        self.assertEqual(context.error_count, 0)
        self.assertEqual(context.retry_count, 0)
        self.assertIsInstance(context.account_info, dict)
        self.assertIsInstance(context.search_results, list)
        self.assertIsInstance(context.analysis_results, dict)
        self.assertIsInstance(context.report_data, dict)
    
    def test_default_values(self):
        """测试默认值"""
        context = DiagnosisContext(task_id="test")
        
        self.assertEqual(context.mode, DiagnosisMode.BASIC)
        self.assertEqual(context.complexity_factor, 1.0)
        self.assertGreater(context.start_time, 0)


class TestStageResult(unittest.TestCase):
    """测试阶段结果"""
    
    def test_initialization(self):
        """测试初始化"""
        result = StageResult(
            stage=ExecutionStage.INITIALIZATION,
            success=True,
            duration=1.5,
            data={'test': 'data'}
        )
        
        self.assertEqual(result.stage, ExecutionStage.INITIALIZATION)
        self.assertTrue(result.success)
        self.assertEqual(result.duration, 1.5)
        self.assertEqual(result.data, {'test': 'data'})
        self.assertIsNone(result.error_message)


class TestDiagnosisEngine(unittest.IsolatedAsyncioTestCase):
    """测试诊断引擎"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.resource_manager_mock = Mock()
        self.progress_tracker_mock = Mock()
        
        self.engine = DiagnosisEngine(
            config=self.config,
            resource_manager=self.resource_manager_mock,
            progress_tracker=self.progress_tracker_mock
        )
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.engine.config.environment, "test")
        self.assertEqual(self.engine.resource_manager, self.resource_manager_mock)
        self.assertEqual(self.engine.progress_tracker, self.progress_tracker_mock)
        self.assertIsNone(self.engine._execution_context)
        self.assertEqual(len(self.engine._stage_results), 0)
    
    async def test_basic_diagnosis_flow(self):
        """测试基础诊断流程"""
        context = DiagnosisContext(
            task_id="test_basic_001",
            account_info={'industry': 'technology', 'followers': 1000},
            mode=DiagnosisMode.BASIC
        )
        
        result = await self.engine.diagnose(context)
        
        # 验证结果结构
        self.assertIn('task_id', result)
        self.assertIn('status', result)
        self.assertIn('mode', result)
        self.assertIn('account_info', result)
        self.assertIn('search_results', result)
        self.assertIn('analysis_results', result)
        self.assertIn('reports', result)
        self.assertIn('performance_metrics', result)
        
        # 验证执行结果
        self.assertEqual(result['task_id'], "test_basic_001")
        self.assertEqual(result['status'], 'completed')
        self.assertEqual(result['mode'], 'basic')
        self.assertGreater(result['execution_time'], 0)
        
        # 验证阶段执行
        self.assertEqual(len(self.engine._stage_results), 3)  # 3个主要阶段
        self.assertIn(ExecutionStage.INITIALIZATION, self.engine._stage_results)
        self.assertIn(ExecutionStage.ANALYSIS, self.engine._stage_results)
        self.assertIn(ExecutionStage.REPORT_GENERATION, self.engine._stage_results)
    
    async def test_deep_research_diagnosis_flow(self):
        """测试深度研究诊断流程"""
        context = DiagnosisContext(
            task_id="test_deep_001",
            account_info={'industry': 'finance', 'followers': 50000},
            mode=DiagnosisMode.DEEP_RESEARCH
        )
        
        result = await self.engine.diagnose(context)
        
        # 验证深度研究模式特有结果
        self.assertEqual(result['mode'], 'deep_research')
        self.assertIn('sales_proposal_md', result['reports'])
        self.assertIn('sales_proposal_html', result['reports'])
        
        # 验证进度跟踪器调用
        self.progress_tracker_mock.update_status.assert_called()
        calls = self.progress_tracker_mock.update_status.call_args_list
        
        # 验证深度研究特有状态被调用
        called_statuses = [call[0][0] for call in calls]
        self.assertIn(UnifiedTaskStatus.INITIALIZING_RESEARCH, called_statuses)
    
    async def test_stage_1_execution(self):
        """测试阶段1执行"""
        context = DiagnosisContext(
            task_id="test_stage1",
            account_info={'test': 'data'}
        )
        self.engine._execution_context = context
        
        await self.engine._execute_stage_1(context)
        
        # 验证阶段结果
        self.assertIn(ExecutionStage.INITIALIZATION, self.engine._stage_results)
        stage_result = self.engine._stage_results[ExecutionStage.INITIALIZATION]
        self.assertEqual(stage_result.stage, ExecutionStage.INITIALIZATION)
        self.assertGreater(stage_result.duration, 0)
        
        # 验证复杂度计算
        self.assertGreater(context.complexity_factor, 0)
    
    async def test_stage_2_basic_execution(self):
        """测试阶段2基础执行"""
        context = DiagnosisContext(
            task_id="test_stage2_basic",
            mode=DiagnosisMode.BASIC
        )
        self.engine._execution_context = context
        
        await self.engine._execute_stage_2(context)
        
        # 验证阶段结果
        self.assertIn(ExecutionStage.ANALYSIS, self.engine._stage_results)
        stage_result = self.engine._stage_results[ExecutionStage.ANALYSIS]
        self.assertTrue(stage_result.success)
        self.assertEqual(stage_result.data['mode'], 'basic')
        
        # 验证搜索和分析结果
        self.assertGreater(len(context.search_results), 0)
        self.assertIn('basic', context.analysis_results)
    
    async def test_stage_2_deep_research_execution(self):
        """测试阶段2深度研究执行"""
        context = DiagnosisContext(
            task_id="test_stage2_deep",
            mode=DiagnosisMode.DEEP_RESEARCH
        )
        self.engine._execution_context = context
        
        await self.engine._execute_stage_2(context)
        
        # 验证深度研究结果
        stage_result = self.engine._stage_results[ExecutionStage.ANALYSIS]
        self.assertEqual(stage_result.data['mode'], 'deep_research')
        
        # 验证深度搜索和分析结果
        self.assertGreater(len(context.search_results), 0)
        self.assertIn('comprehensive', context.analysis_results)
    
    async def test_stage_3_execution(self):
        """测试阶段3执行"""
        context = DiagnosisContext(
            task_id="test_stage3",
            mode=DiagnosisMode.DEEP_RESEARCH
        )
        self.engine._execution_context = context
        
        await self.engine._execute_stage_3(context)
        
        # 验证阶段结果
        self.assertIn(ExecutionStage.REPORT_GENERATION, self.engine._stage_results)
        stage_result = self.engine._stage_results[ExecutionStage.REPORT_GENERATION]
        self.assertTrue(stage_result.success)
        self.assertGreater(stage_result.data['reports_generated'], 0)
        
        # 验证报告生成
        self.assertIn('html_report', context.report_data)
        self.assertIn('json_report', context.report_data)
        self.assertIn('sales_proposal_md', context.report_data)
        self.assertIn('sales_proposal_html', context.report_data)
    
    async def test_error_handling(self):
        """测试错误处理"""
        context = DiagnosisContext(
            task_id="test_error",
            account_info={}  # 空账号信息应该触发错误
        )
        
        # Mock一个会失败的方法
        original_method = self.engine._extract_account_data
        async def failing_extract(ctx):
            raise ValueError("Test error")
        
        self.engine._extract_account_data = failing_extract
        
        result = await self.engine.diagnose(context)
        
        # 验证错误结果
        self.assertEqual(result['status'], 'failed')
        self.assertIn('error_message', result)
        self.assertGreater(result['error_count'], 0)
        
        # 恢复原方法
        self.engine._extract_account_data = original_method
    
    async def test_performance_metrics_calculation(self):
        """测试性能指标计算"""
        context = DiagnosisContext(
            task_id="test_performance",
            account_info={'test': 'data'},
            mode=DiagnosisMode.BASIC
        )
        
        result = await self.engine.diagnose(context)
        
        # 验证性能指标
        metrics = result['performance_metrics']
        self.assertIn('total_duration', metrics)
        self.assertIn('stage_durations', metrics)
        self.assertIn('parallel_efficiency', metrics)
        
        self.assertGreater(metrics['total_duration'], 0)
        self.assertGreater(len(metrics['stage_durations']), 0)
        self.assertGreaterEqual(metrics['parallel_efficiency'], 1.0)  # 并行应该提高效率
    
    async def test_cancellation(self):
        """测试取消功能"""
        await self.engine.cancel()
        self.assertTrue(self.engine._cancellation_token.is_set())
    
    async def test_get_performance_metrics(self):
        """测试获取性能指标"""
        metrics = self.engine.get_performance_metrics()
        self.assertIsInstance(metrics, dict)
        self.assertIn('total_duration', metrics)
    
    async def test_get_stage_results(self):
        """测试获取阶段结果"""
        # 先执行一个诊断以生成阶段结果
        context = DiagnosisContext(task_id="test", account_info={'test': 'data'})
        await self.engine.diagnose(context)
        
        stage_results = self.engine.get_stage_results()
        self.assertIsInstance(stage_results, dict)
        self.assertGreater(len(stage_results), 0)
        
        for stage, result in stage_results.items():
            self.assertIsInstance(stage, ExecutionStage)
            self.assertIsInstance(result, StageResult)


class TestDiagnosisEngineComplexity(unittest.IsolatedAsyncioTestCase):
    """测试诊断引擎复杂度计算"""
    
    async def test_complexity_calculation_simple(self):
        """测试简单复杂度计算"""
        engine = DiagnosisEngine()
        context = DiagnosisContext(
            task_id="test",
            account_info={'industry': 'retail', 'followers': 100}
        )
        engine._execution_context = context
        
        await engine._calculate_complexity(context)
        
        # 简单账号应该有较低的复杂度因子
        self.assertLessEqual(context.complexity_factor, 1.5)
    
    async def test_complexity_calculation_complex(self):
        """测试复杂复杂度计算"""
        engine = DiagnosisEngine()
        context = DiagnosisContext(
            task_id="test",
            account_info={
                'industry': 'technology',
                'followers': 1000000,
                'posts': ['post1'] * 1000  # 大量内容
            },
            mode=DiagnosisMode.DEEP_RESEARCH
        )
        engine._execution_context = context
        
        await engine._calculate_complexity(context)
        
        # 复杂账号应该有较高的复杂度因子
        self.assertGreater(context.complexity_factor, 1.5)


class TestGlobalFunctions(unittest.IsolatedAsyncioTestCase):
    """测试全局函数"""
    
    @patch('core.diagnosis_engine.get_resource_manager')
    async def test_create_diagnosis_engine(self, mock_get_resource_manager):
        """测试创建诊断引擎"""
        mock_resource_manager = Mock()
        mock_get_resource_manager.return_value = mock_resource_manager
        
        engine = await create_diagnosis_engine()
        
        self.assertIsInstance(engine, DiagnosisEngine)
        self.assertEqual(engine.resource_manager, mock_resource_manager)
    
    @patch('core.diagnosis_engine.create_diagnosis_engine')
    async def test_quick_diagnose(self, mock_create_engine):
        """测试快速诊断"""
        mock_engine = Mock()
        mock_engine.diagnose = AsyncMock(return_value={'status': 'completed'})
        mock_create_engine.return_value = mock_engine
        
        result = await quick_diagnose(
            task_id="quick_test",
            account_info={'test': 'data'},
            mode=DiagnosisMode.BASIC
        )
        
        self.assertEqual(result['status'], 'completed')
        mock_engine.diagnose.assert_called_once()


if __name__ == '__main__':
    unittest.main()