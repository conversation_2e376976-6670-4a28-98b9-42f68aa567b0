"""
Unit tests for search_pipeline module
"""

import unittest
import asyncio
import time
import hashlib
from unittest.mock import Mo<PERSON>, AsyncMock, patch

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.search_pipeline import (
    SearchPipeline, QueryGenerator, SearchCache, SearchExecutor,
    SearchQuery, SearchResult, SearchBatch, QueryType, SearchProvider,
    create_search_pipeline, quick_search
)
from core.config_manager import DiagnosisConfig


class TestQueryType(unittest.TestCase):
    """测试查询类型枚举"""
    
    def test_query_types(self):
        """测试查询类型值"""
        self.assertEqual(QueryType.BASIC.value, "basic")
        self.assertEqual(QueryType.INDUSTRY.value, "industry")
        self.assertEqual(QueryType.COMPETITOR.value, "competitor")
        self.assertEqual(QueryType.TREND.value, "trend")
        self.assertEqual(QueryType.CUSTOM.value, "custom")


class TestSearchProvider(unittest.TestCase):
    """测试搜索提供商枚举"""
    
    def test_search_providers(self):
        """测试搜索提供商值"""
        self.assertEqual(SearchProvider.PPLX.value, "pplx")
        self.assertEqual(SearchProvider.SERPER.value, "serper")
        self.assertEqual(SearchProvider.CUSTOM.value, "custom")


class TestSearchQuery(unittest.TestCase):
    """测试搜索查询对象"""
    
    def test_initialization(self):
        """测试初始化"""
        query = SearchQuery(
            query_id="test_001",
            query_text="test query",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX,
            priority=4,
            timeout=90.0
        )
        
        self.assertEqual(query.query_id, "test_001")
        self.assertEqual(query.query_text, "test query")
        self.assertEqual(query.query_type, QueryType.BASIC)
        self.assertEqual(query.provider, SearchProvider.PPLX)
        self.assertEqual(query.priority, 4)
        self.assertEqual(query.timeout, 90.0)
        self.assertEqual(query.max_results, 10)  # 默认值
        self.assertEqual(query.language, "zh")   # 默认值
    
    def test_default_values(self):
        """测试默认值"""
        query = SearchQuery(
            query_id="test",
            query_text="test",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX
        )
        
        self.assertEqual(query.priority, 1)
        self.assertEqual(query.timeout, 60.0)
        self.assertEqual(query.max_results, 10)
        self.assertEqual(query.language, "zh")
        self.assertIsInstance(query.metadata, dict)
        self.assertGreater(query.created_at, 0)


class TestSearchResult(unittest.TestCase):
    """测试搜索结果对象"""
    
    def test_initialization(self):
        """测试初始化"""
        result = SearchResult(
            query_id="test_001",
            provider=SearchProvider.PPLX,
            results=[{'title': 'test', 'url': 'http://example.com'}],
            total_count=1,
            execution_time=1.5,
            quality_score=0.8
        )
        
        self.assertEqual(result.query_id, "test_001")
        self.assertEqual(result.provider, SearchProvider.PPLX)
        self.assertEqual(len(result.results), 1)
        self.assertEqual(result.total_count, 1)
        self.assertEqual(result.execution_time, 1.5)
        self.assertEqual(result.quality_score, 0.8)
        self.assertFalse(result.cache_hit)
        self.assertIsNone(result.error_message)


class TestQueryGenerator(unittest.IsolatedAsyncioTestCase):
    """测试查询生成器"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.generator = QueryGenerator(self.config)
    
    async def test_generate_basic_queries(self):
        """测试生成基础查询"""
        account_info = {'industry': 'technology'}
        queries = await self.generator.generate_basic_queries(account_info, count=3)
        
        self.assertEqual(len(queries), 3)
        for query in queries:
            self.assertIsInstance(query, SearchQuery)
            self.assertEqual(query.query_type, QueryType.BASIC)
            self.assertEqual(query.provider, SearchProvider.PPLX)
            self.assertIn('technology', query.query_text)
    
    async def test_generate_industry_queries(self):
        """测试生成行业查询"""
        account_info = {'industry': 'finance'}
        queries = await self.generator.generate_industry_queries(account_info, count=5)
        
        self.assertEqual(len(queries), 5)
        for query in queries:
            self.assertEqual(query.query_type, QueryType.INDUSTRY)
            self.assertIn('finance', query.query_text)
            self.assertEqual(query.max_results, 15)
            self.assertEqual(query.timeout, 90.0)
    
    async def test_generate_competitor_queries(self):
        """测试生成竞争对手查询"""
        account_info = {'industry': 'retail'}
        competitors = ['competitor_a', 'competitor_b']
        queries = await self.generator.generate_competitor_queries(
            account_info, competitors, count=2
        )
        
        self.assertEqual(len(queries), 2)
        for i, query in enumerate(queries):
            self.assertEqual(query.query_type, QueryType.COMPETITOR)
            self.assertEqual(query.provider, SearchProvider.SERPER)
            self.assertIn(competitors[i], query.query_text)
    
    async def test_optimize_queries(self):
        """测试查询优化"""
        # 创建包含重复查询的列表
        queries = [
            SearchQuery("1", "duplicate query", QueryType.BASIC, SearchProvider.PPLX, priority=3),
            SearchQuery("2", "duplicate query", QueryType.BASIC, SearchProvider.PPLX, priority=5),  # 更高优先级
            SearchQuery("3", "unique query", QueryType.INDUSTRY, SearchProvider.PPLX, priority=2),
        ]
        
        optimized = await self.generator.optimize_queries(queries)
        
        # 应该去重并保留高优先级的查询
        self.assertEqual(len(optimized), 2)
        
        # 验证按优先级排序
        self.assertGreaterEqual(optimized[0].priority, optimized[1].priority)
        
        # 验证保留了高优先级的重复查询
        duplicate_query = next(q for q in optimized if q.query_text == "duplicate query")
        self.assertEqual(duplicate_query.priority, 5)


class TestSearchCache(unittest.IsolatedAsyncioTestCase):
    """测试搜索缓存"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.cache = SearchCache(ttl=60, max_size=100)
        self.test_query = SearchQuery(
            query_id="test_001",
            query_text="test query",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX
        )
        self.test_result = SearchResult(
            query_id="test_001",
            provider=SearchProvider.PPLX,
            results=[{'title': 'test result'}]
        )
    
    async def test_cache_set_and_get(self):
        """测试缓存设置和获取"""
        # 设置缓存
        await self.cache.set(self.test_query, self.test_result)
        
        # 获取缓存
        cached_result = await self.cache.get(self.test_query)
        
        self.assertIsNotNone(cached_result)
        self.assertTrue(cached_result.cache_hit)
        self.assertEqual(cached_result.query_id, self.test_result.query_id)
    
    async def test_cache_miss(self):
        """测试缓存未命中"""
        different_query = SearchQuery(
            query_id="different",
            query_text="different query",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX
        )
        
        cached_result = await self.cache.get(different_query)
        self.assertIsNone(cached_result)
    
    async def test_cache_expiration(self):
        """测试缓存过期"""
        # 创建短TTL的缓存
        short_ttl_cache = SearchCache(ttl=0.1, max_size=100)
        
        await short_ttl_cache.set(self.test_query, self.test_result)
        
        # 等待缓存过期
        await asyncio.sleep(0.2)
        
        cached_result = await short_ttl_cache.get(self.test_query)
        self.assertIsNone(cached_result)
    
    async def test_cache_size_limit(self):
        """测试缓存大小限制"""
        small_cache = SearchCache(ttl=3600, max_size=2)
        
        # 添加3个缓存项
        for i in range(3):
            query = SearchQuery(
                query_id=f"test_{i}",
                query_text=f"query {i}",
                query_type=QueryType.BASIC,
                provider=SearchProvider.PPLX
            )
            result = SearchResult(query_id=f"test_{i}", provider=SearchProvider.PPLX)
            await small_cache.set(query, result)
        
        # 缓存应该只保留2个项目
        self.assertEqual(len(small_cache._cache), 2)
    
    async def test_clear_expired(self):
        """测试清理过期缓存"""
        # 创建短TTL缓存并添加项目
        short_ttl_cache = SearchCache(ttl=0.1, max_size=100)
        await short_ttl_cache.set(self.test_query, self.test_result)
        
        # 等待过期
        await asyncio.sleep(0.2)
        
        # 清理过期缓存
        await short_ttl_cache.clear_expired()
        
        self.assertEqual(len(short_ttl_cache._cache), 0)
    
    async def test_get_stats(self):
        """测试获取缓存统计"""
        await self.cache.set(self.test_query, self.test_result)
        
        stats = self.cache.get_stats()
        
        self.assertIn('total_entries', stats)
        self.assertIn('max_size', stats)
        self.assertIn('ttl', stats)
        self.assertIn('memory_usage_estimate', stats)
        self.assertEqual(stats['total_entries'], 1)


class TestSearchExecutor(unittest.IsolatedAsyncioTestCase):
    """测试搜索执行器"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.executor = SearchExecutor(self.config)
    
    async def test_execute_pplx_search(self):
        """测试PPLX搜索执行"""
        query = SearchQuery(
            query_id="pplx_test",
            query_text="test query",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX,
            max_results=3
        )
        
        result = await self.executor.execute_search(query)
        
        self.assertEqual(result.query_id, "pplx_test")
        self.assertEqual(result.provider, SearchProvider.PPLX)
        self.assertIsNone(result.error_message)
        self.assertGreater(len(result.results), 0)
        self.assertLessEqual(len(result.results), 3)
        self.assertGreater(result.execution_time, 0)
        self.assertGreater(result.quality_score, 0)
    
    async def test_execute_serper_search(self):
        """测试Serper搜索执行"""
        query = SearchQuery(
            query_id="serper_test",
            query_text="test query",
            query_type=QueryType.COMPETITOR,
            provider=SearchProvider.SERPER,
            max_results=4
        )
        
        result = await self.executor.execute_search(query)
        
        self.assertEqual(result.provider, SearchProvider.SERPER)
        self.assertGreater(len(result.results), 0)
        self.assertLessEqual(len(result.results), 4)
    
    async def test_quality_score_calculation(self):
        """测试质量评分计算"""
        query = SearchQuery(
            query_id="quality_test",
            query_text="test",
            query_type=QueryType.BASIC,
            provider=SearchProvider.PPLX,
            max_results=5
        )
        
        result = await self.executor.execute_search(query)
        
        # 质量评分应该在0-1之间
        self.assertGreaterEqual(result.quality_score, 0.0)
        self.assertLessEqual(result.quality_score, 1.0)


class TestSearchPipeline(unittest.IsolatedAsyncioTestCase):
    """测试搜索管道"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.resource_manager_mock = Mock()
        self.progress_tracker_mock = Mock()
        
        self.pipeline = SearchPipeline(
            config=self.config,
            resource_manager=self.resource_manager_mock,
            progress_tracker=self.progress_tracker_mock
        )
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.pipeline.config.environment, "test")
        self.assertIsInstance(self.pipeline.query_generator, QueryGenerator)
        self.assertIsInstance(self.pipeline.search_cache, SearchCache)
        self.assertIsInstance(self.pipeline.search_executor, SearchExecutor)
        self.assertIsNotNone(self.pipeline._search_semaphore)
    
    async def test_basic_search_pipeline(self):
        """测试基础搜索管道"""
        account_info = {
            'industry': 'technology',
            'followers_count': 1000
        }
        
        result = await self.pipeline.execute_search_pipeline(
            account_info, 
            mode="basic"
        )
        
        # 验证结果结构
        self.assertIn('results', result)
        self.assertIn('queries_executed', result)
        self.assertIn('total_results', result)
        self.assertIn('execution_time', result)
        self.assertIn('performance_stats', result)
        self.assertIn('cache_stats', result)
        
        # 验证执行结果
        self.assertGreater(result['queries_executed'], 0)
        self.assertGreater(result['execution_time'], 0)
        
        # 验证结果结构
        results = result['results']
        self.assertIn('all_results', results)
        self.assertIn('results_by_type', results)
        self.assertIn('total_count', results)
        self.assertIn('average_quality', results)
    
    async def test_deep_search_pipeline(self):
        """测试深度搜索管道"""
        account_info = {
            'industry': 'finance',
            'competitors': ['competitor_a', 'competitor_b'],
            'followers_count': 50000
        }
        
        result = await self.pipeline.execute_search_pipeline(
            account_info,
            mode="deep"
        )
        
        # 深度模式应该执行更多查询
        self.assertGreater(result['queries_executed'], 3)
        
        # 验证包含不同类型的查询结果
        results_by_type = result['results']['results_by_type']
        self.assertIn('basic', results_by_type)
        self.assertIn('industry', results_by_type)
    
    async def test_custom_queries(self):
        """测试自定义查询"""
        account_info = {'industry': 'retail'}
        custom_queries = ['custom query 1', 'custom query 2']
        
        result = await self.pipeline.execute_search_pipeline(
            account_info,
            mode="basic",
            custom_queries=custom_queries
        )
        
        # 应该包含自定义查询的结果
        results_by_type = result['results']['results_by_type']
        self.assertIn('custom', results_by_type)
    
    async def test_performance_stats(self):
        """测试性能统计"""
        account_info = {'industry': 'technology'}
        
        result = await self.pipeline.execute_search_pipeline(account_info)
        
        stats = result['performance_stats']
        self.assertIn('total_queries', stats)
        self.assertIn('cache_hits', stats)
        self.assertIn('cache_misses', stats)
        self.assertIn('failed_queries', stats)
        
        self.assertGreater(stats['total_queries'], 0)
        self.assertGreaterEqual(stats['cache_misses'], 0)
    
    async def test_cache_functionality(self):
        """测试缓存功能"""
        account_info = {'industry': 'technology'}
        
        # 第一次执行
        result1 = await self.pipeline.execute_search_pipeline(account_info)
        stats1 = result1['performance_stats']
        
        # 第二次执行（应该有缓存命中）
        result2 = await self.pipeline.execute_search_pipeline(account_info)
        stats2 = result2['performance_stats']
        
        # 第二次执行应该有缓存命中
        self.assertGreater(stats2['cache_hits'], stats1['cache_hits'])
    
    async def test_cleanup(self):
        """测试清理"""
        await self.pipeline.cleanup()
        # 清理不应该抛出异常
    
    async def test_get_performance_stats(self):
        """测试获取性能统计"""
        stats = self.pipeline.get_performance_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn('total_queries', stats)


class TestSearchPipelineErrorHandling(unittest.IsolatedAsyncioTestCase):
    """测试搜索管道错误处理"""
    
    async def test_search_timeout_handling(self):
        """测试搜索超时处理"""
        config = DiagnosisConfig(environment="test")
        pipeline = SearchPipeline(config=config)
        
        # Mock一个会超时的搜索执行器
        async def timeout_search(query):
            await asyncio.sleep(2.0)  # 模拟超时
            return SearchResult(query_id=query.query_id, provider=query.provider)
        
        pipeline.search_executor.execute_search = timeout_search
        
        account_info = {'industry': 'technology'}
        
        # 执行应该能处理超时但不应该完全失败
        result = await pipeline.execute_search_pipeline(account_info)
        
        # 验证部分结果可能失败但管道仍然返回结果
        self.assertIn('results', result)
        self.assertIn('performance_stats', result)


class TestGlobalFunctions(unittest.IsolatedAsyncioTestCase):
    """测试全局函数"""
    
    @patch('core.search_pipeline.get_resource_manager')
    async def test_create_search_pipeline(self, mock_get_resource_manager):
        """测试创建搜索管道"""
        mock_resource_manager = Mock()
        mock_get_resource_manager.return_value = mock_resource_manager
        
        pipeline = await create_search_pipeline()
        
        self.assertIsInstance(pipeline, SearchPipeline)
        self.assertEqual(pipeline.resource_manager, mock_resource_manager)
    
    @patch('core.search_pipeline.create_search_pipeline')
    async def test_quick_search(self, mock_create_pipeline):
        """测试快速搜索"""
        mock_pipeline = Mock()
        mock_pipeline.execute_search_pipeline = AsyncMock(
            return_value={'status': 'completed', 'results': {}}
        )
        mock_pipeline.cleanup = AsyncMock()
        mock_create_pipeline.return_value = mock_pipeline
        
        result = await quick_search(
            account_info={'industry': 'test'},
            mode="basic"
        )
        
        self.assertEqual(result['status'], 'completed')
        mock_pipeline.execute_search_pipeline.assert_called_once()
        mock_pipeline.cleanup.assert_called_once()


if __name__ == '__main__':
    unittest.main()