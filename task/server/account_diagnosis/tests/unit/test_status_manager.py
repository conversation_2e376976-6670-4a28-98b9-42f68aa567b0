"""
Unit tests for status_manager module
"""

import unittest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.status_manager import (
    UnifiedTaskStatus, StatusManager, StatusMessageManager,
    UnifiedProgressTracker, TaskProgress, StatusTransition
)


class TestUnifiedTaskStatus(unittest.TestCase):
    """测试统一任务状态枚举"""
    
    def test_status_values(self):
        """测试状态值"""
        self.assertEqual(UnifiedTaskStatus.TASK_QUEUED.value, "TASK_QUEUED")
        self.assertEqual(UnifiedTaskStatus.PLANNING.value, "PLANNING")
        self.assertEqual(UnifiedTaskStatus.TASK_COMPLETED.value, "TASK_COMPLETED")
        
    def test_deep_research_statuses(self):
        """测试深度研究状态"""
        self.assertEqual(UnifiedTaskStatus.INITIALIZING_RESEARCH.value, "INITIALIZING_RESEARCH")
        self.assertEqual(UnifiedTaskStatus.GENERATING_QUERIES.value, "GENERATING_QUERIES")
        self.assertEqual(UnifiedTaskStatus.FINALIZING_REPORT.value, "FINALIZING_REPORT")


class TestStatusManager(unittest.TestCase):
    """测试状态管理器"""
    
    def test_get_progress_percentage(self):
        """测试获取进度百分比"""
        progress = StatusManager.get_progress_percentage(UnifiedTaskStatus.TASK_QUEUED)
        self.assertEqual(progress, 5.0)
        
        progress = StatusManager.get_progress_percentage(UnifiedTaskStatus.PERFORMING_DIAGNOSIS)
        self.assertEqual(progress, 80.0)
        
        progress = StatusManager.get_progress_percentage(UnifiedTaskStatus.TASK_COMPLETED)
        self.assertEqual(progress, 100.0)
    
    def test_get_next_status(self):
        """测试获取下一个状态"""
        next_status = StatusManager.get_next_status(UnifiedTaskStatus.TASK_QUEUED)
        self.assertEqual(next_status, UnifiedTaskStatus.PLANNING)
        
        next_status = StatusManager.get_next_status(UnifiedTaskStatus.GENERATING_REPORT)
        self.assertEqual(next_status, UnifiedTaskStatus.PAGE_RENDERING)
        
        # 最终状态应该返回None
        next_status = StatusManager.get_next_status(UnifiedTaskStatus.TASK_COMPLETED)
        self.assertIsNone(next_status)
    
    def test_get_estimated_duration(self):
        """测试获取预估耗时"""
        duration = StatusManager.get_estimated_duration(UnifiedTaskStatus.SEARCHING)
        self.assertEqual(duration, 30.0)
        
        duration = StatusManager.get_estimated_duration(UnifiedTaskStatus.PERFORMING_DIAGNOSIS)
        self.assertEqual(duration, 20.0)


class TestStatusMessageManager(unittest.TestCase):
    """测试状态消息管理器"""
    
    def test_get_message_chinese(self):
        """测试获取中文消息"""
        message = StatusMessageManager.get_message(UnifiedTaskStatus.TASK_QUEUED, "cn")
        self.assertIn("任务已创建", message)
        
        message = StatusMessageManager.get_message(UnifiedTaskStatus.PERFORMING_DIAGNOSIS, "cn")
        self.assertIn("多维度账号比对", message)
    
    def test_get_message_english(self):
        """测试获取英文消息"""
        message = StatusMessageManager.get_message(UnifiedTaskStatus.TASK_QUEUED, "en")
        self.assertIn("Task created", message)
        
        message = StatusMessageManager.get_message(UnifiedTaskStatus.PERFORMING_DIAGNOSIS, "en")
        self.assertIn("multi-dimensional", message)
    
    def test_get_message_verbose(self):
        """测试获取详细消息"""
        message = StatusMessageManager.get_message(
            UnifiedTaskStatus.PLANNING, "cn", verbose=True
        )
        self.assertIn("个性化", message)
        
        message = StatusMessageManager.get_message(
            UnifiedTaskStatus.PLANNING, "en", verbose=True
        )
        self.assertIn("personalized", message)
    
    def test_get_message_custom(self):
        """测试自定义消息"""
        custom_msg = "自定义测试消息"
        message = StatusMessageManager.get_message(
            UnifiedTaskStatus.TASK_QUEUED, "cn", custom_message=custom_msg
        )
        self.assertEqual(message, custom_msg)
    
    def test_deep_research_messages(self):
        """测试深度研究消息"""
        message = StatusMessageManager.get_message(
            UnifiedTaskStatus.GENERATING_QUERIES, "cn"
        )
        self.assertIn("智能搜索查询", message)
        
        message = StatusMessageManager.get_message(
            UnifiedTaskStatus.EXECUTING_SEARCHES, "en"
        )
        self.assertIn("large-scale", message)


class TestUnifiedProgressTracker(unittest.IsolatedAsyncioTestCase):
    """测试统一进度跟踪器"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.task_id = "test_task_001"
        self.callback_mock = Mock()
        self.redis_mock = Mock()
        self.redis_mock.lpush = Mock()
        
        self.tracker = UnifiedProgressTracker(
            task_id=self.task_id,
            user_id="test_user",
            diagnosis_id=123,
            callback=self.callback_mock,
            redis_manager=self.redis_mock,
            verbose=True
        )
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.tracker.task_id, self.task_id)
        self.assertEqual(self.tracker.user_id, "test_user")
        self.assertEqual(self.tracker.diagnosis_id, 123)
        self.assertEqual(self.tracker.progress.current_status, UnifiedTaskStatus.TASK_QUEUED)
        self.assertEqual(self.tracker.progress.progress_percentage, 0.0)
    
    async def test_update_status(self):
        """测试状态更新"""
        self.tracker.update_status(UnifiedTaskStatus.PLANNING)
        
        self.assertEqual(self.tracker.progress.current_status, UnifiedTaskStatus.PLANNING)
        self.assertEqual(self.tracker.progress.progress_percentage, 10.0)
        self.assertGreater(self.tracker.progress.last_update_time, 0)
        
        # 验证回调被调用
        self.callback_mock.assert_called()
        call_args = self.callback_mock.call_args[0][0]
        self.assertEqual(call_args["status"], "PLANNING")
        self.assertEqual(call_args["progress"], 10.0)
    
    async def test_update_status_with_custom_message(self):
        """测试带自定义消息的状态更新"""
        custom_msg = "正在处理特殊任务"
        self.tracker.update_status(UnifiedTaskStatus.SEARCHING, custom_msg)
        
        self.assertEqual(self.tracker.progress.custom_message, custom_msg)
        
        # 验证回调中包含自定义消息
        call_args = self.callback_mock.call_args[0][0]
        self.assertEqual(call_args["message"], custom_msg)
    
    async def test_update_step_progress(self):
        """测试步骤进度更新"""
        self.tracker.update_status(UnifiedTaskStatus.SEARCHING)
        self.tracker.update_step_progress("搜索进度", 5, 10)
        
        # 进度应该在搜索状态(40%)和下一个状态(60%)之间
        self.assertGreater(self.tracker.progress.progress_percentage, 40.0)
        self.assertLess(self.tracker.progress.progress_percentage, 60.0)
    
    async def test_mark_completed(self):
        """测试标记完成"""
        self.tracker.mark_completed("任务成功完成")
        
        self.assertEqual(self.tracker.progress.current_status, UnifiedTaskStatus.TASK_COMPLETED)
        self.assertEqual(self.tracker.progress.progress_percentage, 100.0)
        self.assertEqual(self.tracker.progress.custom_message, "任务成功完成")
    
    async def test_mark_failed(self):
        """测试标记失败"""
        error_msg = "任务执行失败"
        self.tracker.mark_failed(error_msg)
        
        self.assertEqual(self.tracker.progress.current_status, UnifiedTaskStatus.TASK_FAILED)
        self.assertEqual(self.tracker.progress.error_message, error_msg)
    
    async def test_performance_tracking(self):
        """测试性能统计"""
        self.tracker.update_status(UnifiedTaskStatus.PLANNING)
        await asyncio.sleep(0.1)  # 模拟一些耗时
        self.tracker.update_status(UnifiedTaskStatus.SEARCHING)
        
        stats = self.tracker.progress.performance_stats
        self.assertIn('total_duration', stats)
        self.assertIn('step_timings', stats)
        self.assertIn('status_transitions', stats)
        self.assertGreater(stats['total_duration'], 0)
        self.assertEqual(stats['status_transitions'], 2)
    
    async def test_get_current_progress(self):
        """测试获取当前进度"""
        self.tracker.update_status(UnifiedTaskStatus.ANALYZING_INDUSTRY)
        
        progress_info = self.tracker.get_current_progress()
        
        self.assertEqual(progress_info["task_id"], self.task_id)
        self.assertEqual(progress_info["status"], "ANALYZING_INDUSTRY")
        self.assertEqual(progress_info["progress"], 60.0)
        self.assertIn("message", progress_info)
        self.assertIn("performance_stats", progress_info)
    
    async def test_language_switching(self):
        """测试语言切换"""
        # 中文模式
        tracker_cn = UnifiedProgressTracker(
            task_id="test_cn",
            language="cn"
        )
        tracker_cn.update_status(UnifiedTaskStatus.PLANNING)
        progress_cn = tracker_cn.get_current_progress()
        self.assertIn("规划", progress_cn["message"])
        
        # 英文模式
        tracker_en = UnifiedProgressTracker(
            task_id="test_en",
            language="en"
        )
        tracker_en.update_status(UnifiedTaskStatus.PLANNING)
        progress_en = tracker_en.get_current_progress()
        self.assertIn("Planning", progress_en["message"])
    
    async def test_estimated_completion_time(self):
        """测试预估完成时间"""
        self.tracker.update_status(UnifiedTaskStatus.SEARCHING)
        
        # 应该有预估完成时间
        self.assertIsNotNone(self.tracker.progress.estimated_completion_time)
        
        # 预估时间应该大于当前时间
        current_time = time.time()
        self.assertGreater(self.tracker.progress.estimated_completion_time, current_time)
        
        # 完成任务后不应该有预估时间
        self.tracker.mark_completed()
        # 注意：完成状态下estimated_completion_time可能仍存在（未重置）


class TestTaskProgress(unittest.TestCase):
    """测试任务进度数据类"""
    
    def test_initialization(self):
        """测试初始化"""
        progress = TaskProgress(
            task_id="test_001",
            current_status=UnifiedTaskStatus.PLANNING,
            progress_percentage=15.0,
            start_time=time.time(),
            last_update_time=time.time()
        )
        
        self.assertEqual(progress.task_id, "test_001")
        self.assertEqual(progress.current_status, UnifiedTaskStatus.PLANNING)
        self.assertEqual(progress.progress_percentage, 15.0)
        self.assertIsInstance(progress.performance_stats, dict)


if __name__ == '__main__':
    unittest.main()