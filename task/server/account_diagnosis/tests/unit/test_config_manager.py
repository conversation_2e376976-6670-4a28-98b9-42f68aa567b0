"""
Unit tests for config_manager module
"""

import unittest
import os
import json
import tempfile
from unittest.mock import patch, Mock

import sys
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.config_manager import (
    ConfigManager, DiagnosisConfig, AIModelConfig, ConcurrencyConfig,
    TimeoutConfig, CacheConfig, RedisConfig, DeepResearchConfig,
    DynamicTimeoutCalculator, ConfigSource, get_config_manager,
    get_diagnosis_config, calculate_dynamic_timeout
)


class TestAIModelConfig(unittest.TestCase):
    """测试AI模型配置"""
    
    def test_default_values(self):
        """测试默认值"""
        config = AIModelConfig()
        self.assertEqual(config.gpt_model, "gpt-4.1")
        self.assertEqual(config.gemini_model, "gemini-2.5-flash")
        self.assertEqual(config.claude_model, "claude-sonnet-4-20250514")
        self.assertIn("gemini", config.fallback_models)
        self.assertIn("gpt", config.fallback_models)
    
    def test_model_timeouts(self):
        """测试模型超时配置"""
        config = AIModelConfig()
        self.assertEqual(config.model_timeouts["gpt"], 90)
        self.assertEqual(config.model_timeouts["gemini"], 120)
        self.assertEqual(config.model_timeouts["claude"], 120)


class TestConcurrencyConfig(unittest.TestCase):
    """测试并发配置"""
    
    def test_default_values(self):
        """测试默认值"""
        config = ConcurrencyConfig()
        self.assertEqual(config.max_concurrent_tasks, 8)
        self.assertEqual(config.max_concurrent_searches, 3)
        self.assertEqual(config.max_concurrent_reports, 4)
        self.assertEqual(config.task_retry_limit, 3)


class TestTimeoutConfig(unittest.TestCase):
    """测试超时配置"""
    
    def test_default_values(self):
        """测试默认值"""
        config = TimeoutConfig()
        self.assertEqual(config.connection_timeout, 30.0)
        self.assertEqual(config.ai_base_timeout, 120)
        self.assertEqual(config.search_timeout, 60)
        self.assertEqual(config.analysis_timeout, 180)


class TestDiagnosisConfig(unittest.TestCase):
    """测试主诊断配置"""
    
    def test_initialization(self):
        """测试初始化"""
        config = DiagnosisConfig(environment="test")
        self.assertEqual(config.environment, "test")
        self.assertEqual(config.service_name, "diagnosis")
        self.assertIsInstance(config.ai_models, AIModelConfig)
        self.assertIsInstance(config.concurrency, ConcurrencyConfig)
        self.assertIsInstance(config.timeouts, TimeoutConfig)
    
    def test_post_init_queue_setup(self):
        """测试初始化后队列设置"""
        config = DiagnosisConfig(environment="test")
        self.assertEqual(config.input_queue, "test:q:diagnosis:request")
        self.assertEqual(config.output_queue, "test:q:diagnosis:response")
        self.assertEqual(config.redis.queue_prefix, "test:q:")
    
    def test_custom_queue_names(self):
        """测试自定义队列名称"""
        config = DiagnosisConfig(
            environment="prod",
            input_queue="custom:input",
            output_queue="custom:output"
        )
        self.assertEqual(config.input_queue, "custom:input")
        self.assertEqual(config.output_queue, "custom:output")


class TestDynamicTimeoutCalculator(unittest.TestCase):
    """测试动态超时计算器"""
    
    def test_calculate_ai_timeout_basic(self):
        """测试基础AI超时计算"""
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(120)
        self.assertEqual(timeout, 120)
    
    def test_calculate_ai_timeout_with_complexity(self):
        """测试带复杂度的AI超时计算"""
        # 复杂度系数2.0，应该使超时时间翻倍
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(120, complexity_factor=2.0)
        self.assertEqual(timeout, 240)
        
        # 复杂度系数0.5，应该减半
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(120, complexity_factor=0.5)
        self.assertEqual(timeout, 60)
    
    def test_calculate_ai_timeout_with_retries(self):
        """测试带重试的AI超时计算"""
        # 第一次重试应该增加50%
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(120, retry_count=1)
        self.assertEqual(timeout, 180)  # 120 * 1.5
        
        # 第二次重试应该是1.5的平方倍
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(120, retry_count=2)
        self.assertEqual(timeout, 270)  # 120 * 1.5^2
    
    def test_calculate_ai_timeout_model_specific(self):
        """测试模型特定的超时计算"""
        base_timeout = 120
        
        # Gemini应该更快
        gemini_timeout = DynamicTimeoutCalculator.calculate_ai_timeout(
            base_timeout, model_type="gemini"
        )
        self.assertEqual(gemini_timeout, 96)  # 120 * 0.8
        
        # Claude应该更慢
        claude_timeout = DynamicTimeoutCalculator.calculate_ai_timeout(
            base_timeout, model_type="claude"
        )
        self.assertEqual(claude_timeout, 144)  # 120 * 1.2
    
    def test_calculate_ai_timeout_bounds(self):
        """测试超时时间边界"""
        # 测试最小值
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(10, complexity_factor=0.1)
        self.assertGreaterEqual(timeout, 30)
        
        # 测试最大值
        timeout = DynamicTimeoutCalculator.calculate_ai_timeout(200, complexity_factor=5.0)
        self.assertLessEqual(timeout, 600)
    
    def test_calculate_complexity_factor(self):
        """测试复杂度系数计算"""
        # 基础情况
        factor = DynamicTimeoutCalculator.calculate_complexity_factor(1024)
        self.assertEqual(factor, 1.01)  # 1.0 + (1024/10240)*0.1
        
        # 带搜索查询
        factor = DynamicTimeoutCalculator.calculate_complexity_factor(1024, search_queries=5)
        expected = 1.01 * (1.0 + 5 * 0.15)  # 1.01 * 1.75
        self.assertAlmostEqual(factor, expected, places=2)
        
        # 复杂行业
        factor = DynamicTimeoutCalculator.calculate_complexity_factor(
            1024, industry_complexity="complex"
        )
        expected = 1.01 * 1.3  # data_factor * industry_factor
        self.assertAlmostEqual(factor, expected, places=2)
    
    def test_calculate_complexity_factor_bounds(self):
        """测试复杂度系数边界"""
        # 测试最小值
        factor = DynamicTimeoutCalculator.calculate_complexity_factor(
            0, search_queries=0, industry_complexity="simple"
        )
        self.assertGreaterEqual(factor, 0.5)
        
        # 测试最大值
        factor = DynamicTimeoutCalculator.calculate_complexity_factor(
            100000, search_queries=20, industry_complexity="complex"
        )
        self.assertLessEqual(factor, 3.0)


class TestConfigManager(unittest.TestCase):
    """测试配置管理器"""
    
    def setUp(self):
        """测试设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = os.path.join(self.temp_dir, "test_config.json")
    
    def tearDown(self):
        """测试清理"""
        if os.path.exists(self.config_file):
            os.remove(self.config_file)
        os.rmdir(self.temp_dir)
    
    def test_initialization_default(self):
        """测试默认初始化"""
        manager = ConfigManager(environment="test")
        config = manager.get_config()
        
        self.assertEqual(config.environment, "test")
        self.assertEqual(config.service_name, "diagnosis")
        self.assertFalse(config.debug_mode)  # test环境默认为False
    
    def test_initialization_dev_environment(self):
        """测试开发环境初始化"""
        manager = ConfigManager(environment="dev")
        config = manager.get_config()
        
        self.assertTrue(config.debug_mode)
        self.assertEqual(config.concurrency.max_concurrent_tasks, 6)
    
    def test_initialization_prod_environment(self):
        """测试生产环境初始化"""
        manager = ConfigManager(environment="prod")
        config = manager.get_config()
        
        self.assertFalse(config.debug_mode)
        self.assertEqual(config.concurrency.max_concurrent_tasks, 12)
        self.assertEqual(config.timeouts.ai_base_timeout, 150)
    
    def test_load_from_file(self):
        """测试从文件加载配置"""
        # 创建测试配置文件
        test_config = {
            "concurrency": {
                "max_concurrent_tasks": 16
            },
            "timeouts": {
                "ai_base_timeout": 200
            },
            "debug_mode": True
        }
        
        with open(self.config_file, 'w') as f:
            json.dump(test_config, f)
        
        manager = ConfigManager(config_file=self.config_file, environment="test")
        config = manager.get_config()
        
        self.assertEqual(config.concurrency.max_concurrent_tasks, 16)
        self.assertEqual(config.timeouts.ai_base_timeout, 200)
        self.assertTrue(config.debug_mode)
    
    @patch.dict(os.environ, {
        'DIAGNOSIS_MAX_CONCURRENT_TASKS': '20',
        'DIAGNOSIS_AI_TIMEOUT': '180',
        'DIAGNOSIS_ENABLE_CACHE': 'true',
        'DIAGNOSIS_DEBUG_MODE': 'false'
    })
    def test_load_from_env(self):
        """测试从环境变量加载配置"""
        manager = ConfigManager(environment="test")
        config = manager.get_config()
        
        self.assertEqual(config.concurrency.max_concurrent_tasks, 20)
        self.assertEqual(config.timeouts.ai_base_timeout, 180)
        self.assertTrue(config.cache.enable_caching)
        self.assertFalse(config.debug_mode)
    
    def test_update_config_runtime(self):
        """测试运行时更新配置"""
        manager = ConfigManager(environment="test")
        
        updates = {
            "concurrency": {
                "max_concurrent_tasks": 25
            },
            "enable_deep_research": True
        }
        
        manager.update_config(updates)
        config = manager.get_config()
        
        self.assertEqual(config.concurrency.max_concurrent_tasks, 25)
        self.assertTrue(config.enable_deep_research)
    
    def test_get_ai_timeout(self):
        """测试获取AI超时时间"""
        manager = ConfigManager(environment="test")
        
        # 基础超时
        timeout = manager.get_ai_timeout()
        self.assertGreater(timeout, 0)
        
        # 带复杂度的超时
        timeout = manager.get_ai_timeout(complexity_factor=2.0)
        self.assertGreater(timeout, manager.get_ai_timeout())
        
        # 带重试的超时
        timeout = manager.get_ai_timeout(retry_count=1)
        self.assertGreater(timeout, manager.get_ai_timeout())
    
    def test_validate_config(self):
        """测试配置验证"""
        manager = ConfigManager(environment="test")
        errors = manager.validate_config()
        
        # 默认配置应该没有错误
        self.assertEqual(len(errors), 0)
        
        # 测试无效配置
        manager.update_config({
            "concurrency": {"max_concurrent_tasks": -1}
        })
        errors = manager.validate_config()
        self.assertGreater(len(errors), 0)
        self.assertIn("max_concurrent_tasks must be positive", errors)
    
    def test_save_config(self):
        """测试保存配置"""
        manager = ConfigManager(environment="test")
        manager.save_config(self.config_file)
        
        # 验证文件被创建
        self.assertTrue(os.path.exists(self.config_file))
        
        # 验证文件内容
        with open(self.config_file, 'r') as f:
            saved_config = json.load(f)
        
        self.assertEqual(saved_config["environment"], "test")
        self.assertEqual(saved_config["service_name"], "diagnosis")
    
    def test_get_config_summary(self):
        """测试获取配置摘要"""
        manager = ConfigManager(environment="test")
        summary = manager.get_config_summary()
        
        self.assertIn("environment", summary)
        self.assertIn("service_name", summary)
        self.assertIn("max_concurrent_tasks", summary)
        self.assertIn("config_sources", summary)
        
        self.assertEqual(summary["environment"], "test")


class TestGlobalFunctions(unittest.TestCase):
    """测试全局函数"""
    
    def test_get_config_manager_singleton(self):
        """测试配置管理器单例"""
        manager1 = get_config_manager("test")
        manager2 = get_config_manager("test")
        
        # 应该返回同一个实例
        self.assertIs(manager1, manager2)
        
        # 不同环境应该返回不同实例
        manager3 = get_config_manager("prod")
        self.assertIsNot(manager1, manager3)
    
    def test_get_diagnosis_config(self):
        """测试获取诊断配置"""
        config = get_diagnosis_config("test")
        self.assertIsInstance(config, DiagnosisConfig)
        self.assertEqual(config.environment, "test")
    
    def test_calculate_dynamic_timeout_convenience(self):
        """测试动态超时计算便捷函数"""
        timeout = calculate_dynamic_timeout(
            base_timeout=120,
            data_size=5120,
            search_queries=3,
            industry_complexity="complex",
            model_type="gemini",
            retry_count=1
        )
        
        self.assertIsInstance(timeout, int)
        self.assertGreater(timeout, 120)  # 应该比基础超时大


if __name__ == '__main__':
    unittest.main()