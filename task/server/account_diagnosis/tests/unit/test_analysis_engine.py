"""
Unit tests for analysis_engine module
"""

import unittest
import asyncio
import time
from unittest.mock import Mock, AsyncMock, patch

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.analysis_engine import (
    AnalysisEngine, AnalysisProcessor, ComplexityCalculator, AIModelSelector,
    AnalysisTask, AnalysisResult, AnalysisContext, AnalysisType, AnalysisComplexity,
    create_analysis_engine, quick_analysis
)
from core.config_manager import DiagnosisConfig


class TestAnalysisType(unittest.TestCase):
    """测试分析类型枚举"""
    
    def test_analysis_types(self):
        """测试分析类型值"""
        self.assertEqual(AnalysisType.BASIC_ACCOUNT.value, "basic_account")
        self.assertEqual(AnalysisType.INDUSTRY_TRENDS.value, "industry_trends")
        self.assertEqual(AnalysisType.COMPETITIVE_LANDSCAPE.value, "competitive_landscape")
        self.assertEqual(AnalysisType.CONTENT_STRATEGY.value, "content_strategy")
        self.assertEqual(AnalysisType.AUDIENCE_INSIGHTS.value, "audience_insights")
        self.assertEqual(AnalysisType.PERFORMANCE_METRICS.value, "performance_metrics")
        self.assertEqual(AnalysisType.COMPREHENSIVE.value, "comprehensive")


class TestAnalysisComplexity(unittest.TestCase):
    """测试分析复杂度枚举"""
    
    def test_complexity_levels(self):
        """测试复杂度级别值"""
        self.assertEqual(AnalysisComplexity.SIMPLE.value, "simple")
        self.assertEqual(AnalysisComplexity.MEDIUM.value, "medium")
        self.assertEqual(AnalysisComplexity.COMPLEX.value, "complex")
        self.assertEqual(AnalysisComplexity.VERY_COMPLEX.value, "very_complex")


class TestAnalysisTask(unittest.TestCase):
    """测试分析任务对象"""
    
    def test_initialization(self):
        """测试初始化"""
        task = AnalysisTask(
            task_id="test_task_001",
            analysis_type=AnalysisType.BASIC_ACCOUNT,
            input_data={'account_info': {'industry': 'tech'}},
            complexity=AnalysisComplexity.MEDIUM,
            priority=4,
            timeout=120.0
        )
        
        self.assertEqual(task.task_id, "test_task_001")
        self.assertEqual(task.analysis_type, AnalysisType.BASIC_ACCOUNT)
        self.assertEqual(task.complexity, AnalysisComplexity.MEDIUM)
        self.assertEqual(task.priority, 4)
        self.assertEqual(task.timeout, 120.0)
        self.assertEqual(task.retry_count, 0)
        self.assertEqual(task.max_retries, 2)
    
    def test_default_values(self):
        """测试默认值"""
        task = AnalysisTask(
            task_id="test",
            analysis_type=AnalysisType.BASIC_ACCOUNT,
            input_data={}
        )
        
        self.assertEqual(task.complexity, AnalysisComplexity.MEDIUM)
        self.assertEqual(task.priority, 3)
        self.assertEqual(task.timeout, 180.0)
        self.assertEqual(task.ai_model, "gpt")
        self.assertIsInstance(task.metadata, dict)
        self.assertGreater(task.created_at, 0)


class TestAnalysisResult(unittest.TestCase):
    """测试分析结果对象"""
    
    def test_initialization(self):
        """测试初始化"""
        result = AnalysisResult(
            task_id="test_task_001",
            analysis_type=AnalysisType.INDUSTRY_TRENDS,
            result_data={'trends': ['ai', 'sustainability']},
            confidence_score=0.85,
            execution_time=45.0,
            ai_model_used="claude"
        )
        
        self.assertEqual(result.task_id, "test_task_001")
        self.assertEqual(result.analysis_type, AnalysisType.INDUSTRY_TRENDS)
        self.assertEqual(result.confidence_score, 0.85)
        self.assertEqual(result.execution_time, 45.0)
        self.assertEqual(result.ai_model_used, "claude")
        self.assertIsInstance(result.result_data, dict)
        self.assertIsInstance(result.token_usage, dict)
        self.assertIsInstance(result.quality_metrics, dict)


class TestAnalysisContext(unittest.TestCase):
    """测试分析上下文"""
    
    def test_initialization(self):
        """测试初始化"""
        context = AnalysisContext(
            account_info={'industry': 'technology', 'followers': 10000},
            search_results=[{'title': 'result1'}, {'title': 'result2'}],
            target_audience="business_professionals",
            industry_context="b2b_software"
        )
        
        self.assertEqual(context.account_info['industry'], 'technology')
        self.assertEqual(len(context.search_results), 2)
        self.assertEqual(context.target_audience, "business_professionals")
        self.assertEqual(context.industry_context, "b2b_software")
        self.assertIsInstance(context.previous_results, dict)
        self.assertIsInstance(context.analysis_goals, list)
        self.assertIsInstance(context.constraints, dict)


class TestComplexityCalculator(unittest.TestCase):
    """测试复杂度计算器"""
    
    def test_simple_complexity_calculation(self):
        """测试简单复杂度计算"""
        account_info = {'industry': 'retail', 'followers': 100}
        search_results = [{'title': 'result1'}]
        
        complexity = ComplexityCalculator.calculate_analysis_complexity(
            account_info, search_results, AnalysisType.BASIC_ACCOUNT
        )
        
        # 基础账号分析应该是简单或中等复杂度
        self.assertIn(complexity, [AnalysisComplexity.SIMPLE, AnalysisComplexity.MEDIUM])
    
    def test_complex_complexity_calculation(self):
        """测试复杂复杂度计算"""
        # 大量数据的复杂账号
        account_info = {
            'industry': 'technology',  # 复杂行业
            'followers': 1500000,      # 大量粉丝
            'content': 'x' * 30000     # 大量内容
        }
        search_results = [{'title': f'result{i}', 'content': 'x' * 1000} for i in range(50)]
        
        complexity = ComplexityCalculator.calculate_analysis_complexity(
            account_info, search_results, AnalysisType.COMPREHENSIVE
        )
        
        # 应该是复杂或非常复杂
        self.assertIn(complexity, [AnalysisComplexity.COMPLEX, AnalysisComplexity.VERY_COMPLEX])
    
    def test_timeout_calculation(self):
        """测试超时计算"""
        base_timeout = 120.0
        
        # 简单任务超时
        simple_timeout = ComplexityCalculator.calculate_timeout(
            base_timeout, AnalysisComplexity.SIMPLE
        )
        self.assertLess(simple_timeout, base_timeout)
        
        # 复杂任务超时
        complex_timeout = ComplexityCalculator.calculate_timeout(
            base_timeout, AnalysisComplexity.VERY_COMPLEX
        )
        self.assertGreater(complex_timeout, base_timeout)
        
        # 重试时超时增加
        retry_timeout = ComplexityCalculator.calculate_timeout(
            base_timeout, AnalysisComplexity.MEDIUM, retry_count=1
        )
        normal_timeout = ComplexityCalculator.calculate_timeout(
            base_timeout, AnalysisComplexity.MEDIUM, retry_count=0
        )
        self.assertGreater(retry_timeout, normal_timeout)
    
    def test_timeout_bounds(self):
        """测试超时边界"""
        # 最小超时
        min_timeout = ComplexityCalculator.calculate_timeout(
            10.0, AnalysisComplexity.SIMPLE
        )
        self.assertGreaterEqual(min_timeout, 60.0)
        
        # 最大超时
        max_timeout = ComplexityCalculator.calculate_timeout(
            1000.0, AnalysisComplexity.VERY_COMPLEX, retry_count=5
        )
        self.assertLessEqual(max_timeout, 600.0)


class TestAIModelSelector(unittest.TestCase):
    """测试AI模型选择器"""
    
    def setUp(self):
        """测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.selector = AIModelSelector(self.config)
    
    def test_model_selection_by_type(self):
        """测试按分析类型选择模型"""
        # 行业趋势分析应该优先选择Claude
        model = self.selector.select_model(
            AnalysisType.INDUSTRY_TRENDS, 
            AnalysisComplexity.MEDIUM
        )
        self.assertEqual(model, "claude")
        
        # 基础账号分析应该选择GPT
        model = self.selector.select_model(
            AnalysisType.BASIC_ACCOUNT,
            AnalysisComplexity.SIMPLE
        )
        self.assertEqual(model, "gpt")
    
    def test_model_selection_by_complexity(self):
        """测试按复杂度选择模型"""
        # 复杂任务应该优先选择Claude
        model = self.selector.select_model(
            AnalysisType.PERFORMANCE_METRICS,
            AnalysisComplexity.VERY_COMPLEX
        )
        self.assertEqual(model, "claude")
    
    def test_retry_model_selection(self):
        """测试重试时的模型选择"""
        # 测试重试时使用备选模型的逻辑
        # 对于INDUSTRY_TRENDS，首选是claude，重试1次应该是gemini（fallback_models[1]）
        original_model = self.selector.select_model(
            AnalysisType.INDUSTRY_TRENDS,
            AnalysisComplexity.MEDIUM,
            retry_count=0
        )
        
        retry_model = self.selector.select_model(
            AnalysisType.INDUSTRY_TRENDS,
            AnalysisComplexity.MEDIUM,
            retry_count=1
        )
        
        # 验证重试时使用了fallback模型
        fallback_models = self.config.ai_models.fallback_models
        expected_retry_model = fallback_models[1] if len(fallback_models) > 1 else fallback_models[0]
        
        # 验证原始模型和重试模型
        self.assertEqual(original_model, "claude")  # INDUSTRY_TRENDS首选claude
        self.assertEqual(retry_model, expected_retry_model)  # 重试时使用fallback_models[1]
        
        # 如果有足够的备选模型，重试时应该不同
        if len(fallback_models) > 1 and expected_retry_model != "claude":
            self.assertNotEqual(original_model, retry_model)


class TestAnalysisProcessor(unittest.IsolatedAsyncioTestCase):
    """测试分析处理器"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.processor = AnalysisProcessor(self.config)
        
        self.context = AnalysisContext(
            account_info={'industry': 'technology', 'followers': 5000},
            search_results=[{'title': 'tech trend 1'}, {'title': 'tech trend 2'}]
        )
    
    async def test_basic_account_analysis(self):
        """测试基础账号分析"""
        task = AnalysisTask(
            task_id="basic_test",
            analysis_type=AnalysisType.BASIC_ACCOUNT,
            input_data={'account_info': self.context.account_info}
        )
        
        result = await self.processor.process_analysis(task, self.context)
        
        self.assertEqual(result.task_id, "basic_test")
        self.assertEqual(result.analysis_type, AnalysisType.BASIC_ACCOUNT)
        self.assertIsNone(result.error_message)
        self.assertGreater(result.execution_time, 0)
        self.assertGreater(result.confidence_score, 0)
        
        # 验证基础分析结果结构
        self.assertIn('account_type', result.result_data)
        self.assertIn('content_themes', result.result_data)
        self.assertIn('engagement_rate', result.result_data)
    
    async def test_industry_trends_analysis(self):
        """测试行业趋势分析"""
        task = AnalysisTask(
            task_id="industry_test",
            analysis_type=AnalysisType.INDUSTRY_TRENDS,
            input_data={'search_results': self.context.search_results}
        )
        
        result = await self.processor.process_analysis(task, self.context)
        
        self.assertEqual(result.analysis_type, AnalysisType.INDUSTRY_TRENDS)
        self.assertIsNone(result.error_message)
        
        # 验证行业趋势分析结果结构
        self.assertIn('trending_topics', result.result_data)
        self.assertIn('market_opportunities', result.result_data)
        self.assertIn('industry_challenges', result.result_data)
    
    async def test_comprehensive_analysis(self):
        """测试综合分析"""
        task = AnalysisTask(
            task_id="comprehensive_test",
            analysis_type=AnalysisType.COMPREHENSIVE,
            input_data={'account_info': self.context.account_info}
        )
        
        result = await self.processor.process_analysis(task, self.context)
        
        self.assertEqual(result.analysis_type, AnalysisType.COMPREHENSIVE)
        self.assertIsNone(result.error_message)
        
        # 综合分析应该花费更多时间
        self.assertGreater(result.execution_time, 0.5)
        
        # 验证综合分析结果结构
        self.assertIn('overall_assessment', result.result_data)
        self.assertIn('strategic_recommendations', result.result_data)
        self.assertIn('performance_forecast', result.result_data)
    
    async def test_confidence_score_calculation(self):
        """测试置信度评分计算"""
        # 丰富的结果数据应该有较高的置信度
        rich_result = {
            'field1': 'value1',
            'field2': 'value2',
            'field3': 'value3',
            'field4': 'value4',
            'field5': 'value5'
        }
        
        confidence = self.processor._calculate_confidence_score(
            rich_result, AnalysisComplexity.SIMPLE
        )
        
        self.assertGreater(confidence, 0.4)
        self.assertLessEqual(confidence, 1.0)
        
        # 空结果应该有零置信度
        empty_confidence = self.processor._calculate_confidence_score(
            {}, AnalysisComplexity.SIMPLE
        )
        self.assertEqual(empty_confidence, 0.0)
    
    async def test_quality_metrics_calculation(self):
        """测试质量指标计算"""
        result_data = {'analysis': 'detailed analysis result'}
        execution_time = 30.0
        
        metrics = self.processor._calculate_quality_metrics(result_data, execution_time)
        
        self.assertIn('data_richness', metrics)
        self.assertIn('processing_efficiency', metrics)
        self.assertIn('structure_quality', metrics)
        self.assertIn('completeness', metrics)
        
        # 所有指标应该在合理范围内
        for metric_value in metrics.values():
            self.assertGreaterEqual(metric_value, 0.0)
            self.assertLessEqual(metric_value, 1.0)


class TestAnalysisEngine(unittest.IsolatedAsyncioTestCase):
    """测试分析引擎"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.config = DiagnosisConfig(environment="test")
        self.resource_manager_mock = Mock()
        self.progress_tracker_mock = Mock()
        
        self.engine = AnalysisEngine(
            config=self.config,
            resource_manager=self.resource_manager_mock,
            progress_tracker=self.progress_tracker_mock
        )
        
        self.context = AnalysisContext(
            account_info={'industry': 'technology', 'followers': 10000},
            search_results=[
                {'title': 'tech trend 1', 'content': 'content 1'},
                {'title': 'tech trend 2', 'content': 'content 2'}
            ]
        )
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.engine.config.environment, "test")
        self.assertIsInstance(self.engine.processor, AnalysisProcessor)
        self.assertIsNotNone(self.engine._analysis_semaphore)
        self.assertIsInstance(self.engine._performance_stats, dict)
    
    async def test_basic_analysis_pipeline(self):
        """测试基础分析管道"""
        analysis_types = [
            AnalysisType.BASIC_ACCOUNT,
            AnalysisType.PERFORMANCE_METRICS
        ]
        
        result = await self.engine.execute_analysis_pipeline(
            self.context, 
            analysis_types,
            mode="parallel"
        )
        
        # 验证结果结构
        self.assertIn('results', result)
        self.assertIn('execution_time', result)
        self.assertIn('performance_stats', result)
        self.assertIn('analysis_count', result)
        self.assertIn('successful_count', result)
        
        # 验证执行结果
        self.assertGreater(result['execution_time'], 0)
        self.assertEqual(result['analysis_count'], 2)
        self.assertGreaterEqual(result['successful_count'], 0)
        
        # 验证分析结果
        results = result['results']
        self.assertIn('basic_account', results)
        self.assertIn('performance_metrics', results)
        self.assertIn('_meta', results)
    
    async def test_comprehensive_analysis_pipeline(self):
        """测试综合分析管道"""
        analysis_types = [
            AnalysisType.BASIC_ACCOUNT,
            AnalysisType.INDUSTRY_TRENDS,
            AnalysisType.COMPETITIVE_LANDSCAPE,
            AnalysisType.COMPREHENSIVE
        ]
        
        result = await self.engine.execute_analysis_pipeline(
            self.context,
            analysis_types,
            mode="parallel"
        )
        
        # 综合分析应该执行更多分析
        self.assertEqual(result['analysis_count'], 4)
        
        # 验证各种分析类型的结果
        results = result['results']
        self.assertIn('basic_account', results)
        self.assertIn('industry_trends', results)
        self.assertIn('competitive_landscape', results)
        self.assertIn('comprehensive', results)
    
    async def test_sequential_analysis_mode(self):
        """测试串行分析模式"""
        analysis_types = [
            AnalysisType.BASIC_ACCOUNT,
            AnalysisType.INDUSTRY_TRENDS
        ]
        
        result = await self.engine.execute_analysis_pipeline(
            self.context,
            analysis_types,
            mode="sequential"
        )
        
        # 串行模式也应该成功完成
        self.assertGreater(result['successful_count'], 0)
        self.assertIn('basic_account', result['results'])
    
    async def test_default_analysis_types(self):
        """测试默认分析类型"""
        result = await self.engine.execute_analysis_pipeline(self.context)
        
        # 应该执行默认的分析类型
        results = result['results']
        self.assertIn('basic_account', results)
        self.assertIn('industry_trends', results)
        self.assertIn('performance_metrics', results)
    
    async def test_task_creation_and_prioritization(self):
        """测试任务创建和优先级"""
        analysis_types = [
            AnalysisType.COMPREHENSIVE,  # 低优先级
            AnalysisType.BASIC_ACCOUNT,  # 高优先级
            AnalysisType.INDUSTRY_TRENDS  # 中优先级
        ]
        
        tasks = await self.engine._create_analysis_tasks(self.context, analysis_types)
        
        # 验证任务数量
        self.assertEqual(len(tasks), 3)
        
        # 验证任务按优先级排序（高优先级在前）
        self.assertGreaterEqual(tasks[0].priority, tasks[1].priority)
        self.assertGreaterEqual(tasks[1].priority, tasks[2].priority)
        
        # 验证复杂度计算
        for task in tasks:
            self.assertIsInstance(task.complexity, AnalysisComplexity)
            self.assertGreater(task.timeout, 60.0)
    
    async def test_parallel_execution_with_semaphore(self):
        """测试带信号量的并行执行"""
        # 设置小的信号量限制
        self.engine._analysis_semaphore = asyncio.Semaphore(1)
        
        analysis_types = [
            AnalysisType.BASIC_ACCOUNT,
            AnalysisType.INDUSTRY_TRENDS,
            AnalysisType.PERFORMANCE_METRICS
        ]
        
        start_time = time.time()
        result = await self.engine.execute_analysis_pipeline(
            self.context, 
            analysis_types,
            mode="parallel"
        )
        execution_time = time.time() - start_time
        
        # 信号量限制应该使并行执行变成类似串行
        self.assertGreater(execution_time, 0.3)  # 至少要一些时间
        self.assertEqual(result['analysis_count'], 3)
    
    async def test_performance_stats_update(self):
        """测试性能统计更新"""
        initial_stats = self.engine.get_performance_stats()
        
        await self.engine.execute_analysis_pipeline(self.context)
        
        updated_stats = self.engine.get_performance_stats()
        
        # 统计应该被更新
        self.assertGreater(updated_stats['total_analyses'], initial_stats['total_analyses'])
        self.assertGreater(updated_stats['successful_analyses'], initial_stats['successful_analyses'])
        self.assertGreater(updated_stats['average_execution_time'], 0)
    
    async def test_error_handling_and_retry(self):
        """测试错误处理和重试"""
        # Mock一个会失败的处理器
        failing_processor = Mock()
        failing_processor.process_analysis = AsyncMock(
            side_effect=[
                Exception("First attempt failed"),
                Exception("Second attempt failed"),
                AnalysisResult(
                    task_id="test",
                    analysis_type=AnalysisType.BASIC_ACCOUNT,
                    result_data={'success': True}
                )
            ]
        )
        
        self.engine.processor = failing_processor
        
        # 创建一个会重试的任务
        task = AnalysisTask(
            task_id="retry_test",
            analysis_type=AnalysisType.BASIC_ACCOUNT,
            input_data={},
            max_retries=2
        )
        
        result = await self.engine._execute_single_analysis(task, self.context)
        
        # 第三次尝试应该成功
        self.assertIsNone(result.error_message)
        self.assertEqual(result.result_data['success'], True)
        
        # 验证重试次数
        self.assertEqual(failing_processor.process_analysis.call_count, 3)


class TestAnalysisEngineIntegration(unittest.IsolatedAsyncioTestCase):
    """分析引擎集成测试"""
    
    async def test_full_analysis_workflow(self):
        """测试完整分析工作流"""
        engine = AnalysisEngine()
        
        # 复杂的分析上下文
        context = AnalysisContext(
            account_info={
                'industry': 'technology',
                'followers_count': 50000,
                'engagement_rate': 0.05,
                'content_types': ['articles', 'videos'],
                'posting_frequency': 'daily'
            },
            search_results=[
                {'title': 'Tech Trends 2024', 'content': 'AI and machine learning...'},
                {'title': 'Industry Report', 'content': 'Market analysis shows...'},
                {'title': 'Competitor Analysis', 'content': 'Leading companies are...'}
            ],
            target_audience="tech_professionals",
            industry_context="b2b_saas",
            analysis_goals=['improve_engagement', 'content_optimization']
        )
        
        # 执行完整分析
        result = await engine.execute_analysis_pipeline(
            context,
            analysis_types=[
                AnalysisType.BASIC_ACCOUNT,
                AnalysisType.INDUSTRY_TRENDS,
                AnalysisType.CONTENT_STRATEGY,
                AnalysisType.AUDIENCE_INSIGHTS,
                AnalysisType.COMPREHENSIVE
            ],
            mode="parallel"
        )
        
        # 验证完整结果
        self.assertEqual(result['analysis_count'], 5)
        self.assertGreater(result['successful_count'], 3)  # 至少大部分成功
        self.assertGreater(result['execution_time'], 0)
        
        # 验证所有分析类型的结果
        results = result['results']
        expected_analyses = [
            'basic_account', 'industry_trends', 'content_strategy',
            'audience_insights', 'comprehensive'
        ]
        
        for analysis_type in expected_analyses:
            if analysis_type in results:
                self.assertIsInstance(results[analysis_type], dict)
                self.assertGreater(len(results[analysis_type]), 0)
        
        # 验证元数据
        meta = results['_meta']
        self.assertIn('total_analyses', meta)
        self.assertIn('average_confidence', meta)
        self.assertIn('analysis_timestamp', meta)


class TestGlobalFunctions(unittest.IsolatedAsyncioTestCase):
    """测试全局函数"""
    
    @patch('core.analysis_engine.get_resource_manager')
    async def test_create_analysis_engine(self, mock_get_resource_manager):
        """测试创建分析引擎"""
        mock_resource_manager = Mock()
        mock_get_resource_manager.return_value = mock_resource_manager
        
        engine = await create_analysis_engine()
        
        self.assertIsInstance(engine, AnalysisEngine)
        self.assertEqual(engine.resource_manager, mock_resource_manager)
    
    @patch('core.analysis_engine.create_analysis_engine')
    async def test_quick_analysis(self, mock_create_engine):
        """测试快速分析"""
        mock_engine = Mock()
        mock_engine.execute_analysis_pipeline = AsyncMock(
            return_value={'status': 'completed', 'results': {}}
        )
        mock_create_engine.return_value = mock_engine
        
        result = await quick_analysis(
            account_info={'industry': 'test'},
            search_results=[{'title': 'test result'}],
            analysis_types=[AnalysisType.BASIC_ACCOUNT]
        )
        
        self.assertEqual(result['status'], 'completed')
        mock_engine.execute_analysis_pipeline.assert_called_once()


if __name__ == '__main__':
    unittest.main()