"""
Unit tests for resource_manager module
"""

import unittest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..')))

from core.resource_manager import (
    ResourceType, ResourceStatus, ResourceConfig, ResourceMetrics,
    ManagedResource, ResourcePool, UnifiedResourceManager,
    get_resource_manager, get_redis_connection, get_http_client, get_thread_pool
)


class TestResourceConfig(unittest.TestCase):
    """测试资源配置"""
    
    def test_default_values(self):
        """测试默认值"""
        config = ResourceConfig()
        self.assertEqual(config.max_instances, 10)
        self.assertEqual(config.min_instances, 1)
        self.assertEqual(config.idle_timeout, 300.0)
        self.assertEqual(config.connection_timeout, 30.0)
        self.assertTrue(config.cleanup_on_exit)


class TestResourceMetrics(unittest.TestCase):
    """测试资源指标"""
    
    def test_initialization(self):
        """测试初始化"""
        metrics = ResourceMetrics()
        self.assertGreater(metrics.created_at, 0)
        self.assertGreater(metrics.last_used_at, 0)
        self.assertEqual(metrics.usage_count, 0)
        self.assertEqual(metrics.error_count, 0)


class TestManagedResource(unittest.IsolatedAsyncioTestCase):
    """测试托管资源"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.mock_resource = Mock()
        self.cleanup_func = AsyncMock()
        self.config = ResourceConfig(idle_timeout=60.0)
        
        self.managed_resource = ManagedResource(
            resource_id="test_resource_001",
            resource_type=ResourceType.REDIS,
            resource_obj=self.mock_resource,
            cleanup_func=self.cleanup_func,
            config=self.config
        )
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.managed_resource.resource_id, "test_resource_001")
        self.assertEqual(self.managed_resource.resource_type, ResourceType.REDIS)
        self.assertEqual(self.managed_resource.resource_obj, self.mock_resource)
        self.assertEqual(self.managed_resource.status, ResourceStatus.INITIALIZING)
        self.assertIsInstance(self.managed_resource.metrics, ResourceMetrics)
    
    async def test_mark_active(self):
        """测试标记为活跃"""
        initial_usage = self.managed_resource.metrics.usage_count
        initial_time = self.managed_resource.metrics.last_used_at
        
        await asyncio.sleep(0.01)  # 确保时间差异
        await self.managed_resource.mark_active()
        
        self.assertEqual(self.managed_resource.status, ResourceStatus.ACTIVE)
        self.assertEqual(self.managed_resource.metrics.usage_count, initial_usage + 1)
        self.assertGreater(self.managed_resource.metrics.last_used_at, initial_time)
    
    async def test_mark_idle(self):
        """测试标记为空闲"""
        await self.managed_resource.mark_active()
        await self.managed_resource.mark_idle()
        
        self.assertEqual(self.managed_resource.status, ResourceStatus.IDLE)
    
    async def test_mark_error(self):
        """测试标记错误"""
        initial_error_count = self.managed_resource.metrics.error_count
        
        await self.managed_resource.mark_error("Test error")
        
        self.assertEqual(self.managed_resource.status, ResourceStatus.ERROR)
        self.assertEqual(self.managed_resource.metrics.error_count, initial_error_count + 1)
    
    async def test_is_healthy_basic(self):
        """测试基础健康检查"""
        # 初始状态应该是健康的
        await self.managed_resource.mark_active()
        self.assertTrue(await self.managed_resource.is_healthy())
        
        # 错误状态应该不健康
        await self.managed_resource.mark_error()
        self.assertFalse(await self.managed_resource.is_healthy())
        
        # 关闭状态应该不健康
        self.managed_resource.status = ResourceStatus.CLOSED
        self.assertFalse(await self.managed_resource.is_healthy())
    
    async def test_is_healthy_idle_timeout(self):
        """测试空闲超时健康检查"""
        # 设置一个很短的空闲超时
        self.managed_resource.config.idle_timeout = 0.01
        await self.managed_resource.mark_active()
        
        # 等待超过空闲超时时间
        await asyncio.sleep(0.02)
        
        # 应该被认为不健康
        self.assertFalse(await self.managed_resource.is_healthy())
    
    async def test_redis_health_check(self):
        """测试Redis健康检查"""
        # Mock Redis连接
        redis_mock = AsyncMock()
        redis_mock.ping = AsyncMock(return_value=True)
        
        resource = ManagedResource(
            resource_id="redis_test",
            resource_type=ResourceType.REDIS,
            resource_obj=redis_mock,
            config=self.config
        )
        
        await resource.mark_active()
        self.assertTrue(await resource.is_healthy())
        
        # 模拟ping失败
        redis_mock.ping.side_effect = Exception("Connection lost")
        self.assertFalse(await resource.is_healthy())
    
    async def test_http_client_health_check(self):
        """测试HTTP客户端健康检查"""
        # Mock HTTP客户端
        http_mock = Mock()
        http_mock.closed = False
        
        resource = ManagedResource(
            resource_id="http_test",
            resource_type=ResourceType.HTTP_CLIENT,
            resource_obj=http_mock,
            config=self.config
        )
        
        await resource.mark_active()
        self.assertTrue(await resource.is_healthy())
        
        # 模拟连接关闭
        http_mock.closed = True
        self.assertFalse(await resource.is_healthy())
    
    async def test_cleanup(self):
        """测试清理"""
        await self.managed_resource.cleanup()
        
        self.assertEqual(self.managed_resource.status, ResourceStatus.CLOSED)
        self.cleanup_func.assert_called_once_with(self.mock_resource)
    
    async def test_cleanup_with_redis(self):
        """测试Redis特定清理"""
        redis_mock = AsyncMock()
        redis_mock.close = AsyncMock()
        
        resource = ManagedResource(
            resource_id="redis_cleanup_test",
            resource_type=ResourceType.REDIS,
            resource_obj=redis_mock
        )
        
        await resource.cleanup()
        redis_mock.close.assert_called_once()
        self.assertEqual(resource.status, ResourceStatus.CLOSED)


class TestResourcePool(unittest.IsolatedAsyncioTestCase):
    """测试资源池"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.factory_call_count = 0
        
        def mock_factory():
            self.factory_call_count += 1
            return Mock(name=f"resource_{self.factory_call_count}")
        
        self.factory_func = mock_factory
        self.config = ResourceConfig(max_instances=3, min_instances=1)
        
        self.pool = ResourcePool(
            resource_type=ResourceType.REDIS,
            factory_func=self.factory_func,
            config=self.config
        )
    
    async def test_acquire_new_resource(self):
        """测试获取新资源"""
        resource = await self.pool.acquire()
        
        self.assertIsNotNone(resource)
        self.assertEqual(resource.status, ResourceStatus.ACTIVE)
        self.assertEqual(resource.resource_type, ResourceType.REDIS)
        self.assertEqual(len(self.pool._pool), 1)
        self.assertEqual(self.factory_call_count, 1)
    
    async def test_acquire_multiple_resources(self):
        """测试获取多个资源"""
        resource1 = await self.pool.acquire()
        resource2 = await self.pool.acquire()
        resource3 = await self.pool.acquire()
        
        self.assertIsNotNone(resource1)
        self.assertIsNotNone(resource2)
        self.assertIsNotNone(resource3)
        self.assertEqual(len(self.pool._pool), 3)
        self.assertEqual(self.factory_call_count, 3)
    
    async def test_acquire_beyond_limit(self):
        """测试超出限制的获取"""
        # 获取最大数量的资源
        for _ in range(self.config.max_instances):
            resource = await self.pool.acquire()
            self.assertIsNotNone(resource)
        
        # 再次获取应该返回None
        resource = await self.pool.acquire()
        self.assertIsNone(resource)
    
    async def test_release_and_reuse(self):
        """测试释放和重用"""
        resource1 = await self.pool.acquire()
        self.assertIsNotNone(resource1)
        
        # 释放资源
        await self.pool.release(resource1)
        self.assertEqual(resource1.status, ResourceStatus.IDLE)
        
        # 再次获取应该重用同一个资源
        resource2 = await self.pool.acquire()
        self.assertIs(resource1, resource2)
        self.assertEqual(resource2.status, ResourceStatus.ACTIVE)
        self.assertEqual(self.factory_call_count, 1)  # 只创建了一次
    
    async def test_health_check(self):
        """测试健康检查"""
        resource = await self.pool.acquire()
        await self.pool.release(resource)
        
        # 模拟资源变得不健康
        resource.status = ResourceStatus.ERROR
        
        initial_pool_size = len(self.pool._pool)
        await self.pool.health_check()
        
        # 不健康的资源应该被移除
        self.assertEqual(len(self.pool._pool), initial_pool_size - 1)
    
    async def test_cleanup_all(self):
        """测试清理所有资源"""
        # 创建几个资源
        for _ in range(2):
            await self.pool.acquire()
        
        self.assertEqual(len(self.pool._pool), 2)
        
        await self.pool.cleanup_all()
        
        self.assertEqual(len(self.pool._pool), 0)


class TestUnifiedResourceManager(unittest.IsolatedAsyncioTestCase):
    """测试统一资源管理器"""
    
    async def asyncSetUp(self):
        """异步测试设置"""
        self.manager = UnifiedResourceManager()
        await self.manager.initialize()
        
        # Mock工厂函数
        self.factory_call_count = 0
        
        def mock_factory():
            self.factory_call_count += 1
            return Mock(name=f"mock_resource_{self.factory_call_count}")
        
        self.mock_factory = mock_factory
    
    async def asyncTearDown(self):
        """异步测试清理"""
        await self.manager.cleanup()
    
    async def test_initialization(self):
        """测试初始化"""
        self.assertIsNotNone(self.manager._health_check_task)
        self.assertEqual(len(self.manager._pools), 0)
        self.assertEqual(len(self.manager._singletons), 0)
    
    async def test_register_pool(self):
        """测试注册资源池"""
        config = ResourceConfig(max_instances=5)
        
        await self.manager.register_pool(
            "test_pool",
            ResourceType.REDIS,
            self.mock_factory,
            config
        )
        
        self.assertIn("test_pool", self.manager._pools)
        pool = self.manager._pools["test_pool"]
        self.assertEqual(pool.resource_type, ResourceType.REDIS)
        self.assertEqual(pool.config.max_instances, 5)
    
    async def test_register_singleton(self):
        """测试注册单例资源"""
        mock_resource = Mock()
        cleanup_func = Mock()
        
        await self.manager.register_singleton(
            "test_singleton",
            ResourceType.CACHE,
            mock_resource,
            cleanup_func
        )
        
        self.assertIn("test_singleton", self.manager._singletons)
        singleton = self.manager._singletons["test_singleton"]
        self.assertEqual(singleton.resource_obj, mock_resource)
        self.assertEqual(singleton.cleanup_func, cleanup_func)
    
    async def test_acquire_from_pool(self):
        """测试从资源池获取资源"""
        config = ResourceConfig(max_instances=2)
        
        await self.manager.register_pool(
            "test_pool",
            ResourceType.REDIS,
            self.mock_factory,
            config
        )
        
        async with self.manager.acquire_from_pool("test_pool") as resource:
            self.assertIsNotNone(resource)
            self.assertEqual(self.factory_call_count, 1)
    
    async def test_acquire_from_nonexistent_pool(self):
        """测试从不存在的资源池获取资源"""
        with self.assertRaises(ValueError):
            async with self.manager.acquire_from_pool("nonexistent_pool"):
                pass
    
    async def test_get_singleton(self):
        """测试获取单例资源"""
        mock_resource = Mock()
        
        await self.manager.register_singleton(
            "test_singleton",
            ResourceType.CACHE,
            mock_resource
        )
        
        resource = await self.manager.get_singleton("test_singleton")
        self.assertEqual(resource, mock_resource)
    
    async def test_get_nonexistent_singleton(self):
        """测试获取不存在的单例资源"""
        with self.assertRaises(ValueError):
            await self.manager.get_singleton("nonexistent_singleton")
    
    async def test_get_metrics(self):
        """测试获取指标"""
        # 注册一些资源
        await self.manager.register_pool(
            "test_pool",
            ResourceType.REDIS,
            self.mock_factory,
            ResourceConfig(max_instances=2)
        )
        
        await self.manager.register_singleton(
            "test_singleton",
            ResourceType.CACHE,
            Mock()
        )
        
        # 创建一些资源
        async with self.manager.acquire_from_pool("test_pool"):
            pass
        
        metrics = await self.manager.get_metrics()
        
        self.assertIn("pools", metrics)
        self.assertIn("singletons", metrics)
        self.assertIn("total_resources", metrics)
        self.assertIn("test_pool", metrics["pools"])
        self.assertIn("test_singleton", metrics["singletons"])
        self.assertGreater(metrics["total_resources"], 0)
    
    async def test_cleanup(self):
        """测试清理"""
        # 注册一些资源
        await self.manager.register_pool(
            "test_pool",
            ResourceType.REDIS,
            self.mock_factory,
            ResourceConfig()
        )
        
        await self.manager.register_singleton(
            "test_singleton",
            ResourceType.CACHE,
            Mock()
        )
        
        await self.manager.cleanup()
        
        # 验证清理后状态
        self.assertEqual(len(self.manager._pools), 0)
        self.assertEqual(len(self.manager._singletons), 0)
        self.assertTrue(self.manager._shutdown_event.is_set())


class TestGlobalFunctions(unittest.IsolatedAsyncioTestCase):
    """测试全局函数"""
    
    async def test_get_resource_manager_singleton(self):
        """测试资源管理器单例"""
        manager1 = await get_resource_manager()
        manager2 = await get_resource_manager()
        
        # 应该返回同一个实例
        self.assertIs(manager1, manager2)
        
        # 清理
        await manager1.cleanup()
    
    @patch('core.resource_manager.create_redis_connection')
    async def test_get_redis_connection(self, mock_create_redis):
        """测试获取Redis连接"""
        mock_redis = AsyncMock()
        mock_create_redis.return_value = mock_redis
        
        async with get_redis_connection() as connection:
            self.assertIsNotNone(connection)
        
        # 清理全局管理器
        manager = await get_resource_manager()
        await manager.cleanup()
    
    @patch('core.resource_manager.create_http_client')
    async def test_get_http_client(self, mock_create_http):
        """测试获取HTTP客户端"""
        mock_client = AsyncMock()
        mock_create_http.return_value = mock_client
        
        async with get_http_client() as client:
            self.assertIsNotNone(client)
        
        # 清理全局管理器
        manager = await get_resource_manager()
        await manager.cleanup()
    
    async def test_get_thread_pool(self):
        """测试获取线程池"""
        thread_pool = await get_thread_pool()
        self.assertIsNotNone(thread_pool)
        
        # 清理全局管理器
        manager = await get_resource_manager()
        await manager.cleanup()


# 集成测试
class TestResourceManagerIntegration(unittest.IsolatedAsyncioTestCase):
    """资源管理器集成测试"""
    
    async def test_full_workflow(self):
        """测试完整工作流程"""
        manager = UnifiedResourceManager()
        await manager.initialize()
        
        try:
            # 注册资源池
            def simple_factory():
                return Mock(name="simple_resource")
            
            await manager.register_pool(
                "simple_pool",
                ResourceType.CUSTOM,
                simple_factory,
                ResourceConfig(max_instances=2)
            )
            
            # 注册单例
            singleton_obj = Mock(name="singleton_resource")
            await manager.register_singleton(
                "simple_singleton",
                ResourceType.CUSTOM,
                singleton_obj
            )
            
            # 使用资源池
            async with manager.acquire_from_pool("simple_pool") as resource1:
                self.assertIsNotNone(resource1)
                
                async with manager.acquire_from_pool("simple_pool") as resource2:
                    self.assertIsNotNone(resource2)
                    self.assertIsNot(resource1, resource2)
            
            # 使用单例
            singleton = await manager.get_singleton("simple_singleton")
            self.assertEqual(singleton, singleton_obj)
            
            # 获取指标
            metrics = await manager.get_metrics()
            self.assertEqual(metrics["pools"]["simple_pool"]["total_resources"], 2)
            self.assertEqual(metrics["total_resources"], 3)  # 2个池资源 + 1个单例
            
        finally:
            await manager.cleanup()


if __name__ == '__main__':
    unittest.main()