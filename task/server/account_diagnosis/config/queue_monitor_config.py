"""
队列监控配置 - 针对不同场景的优化参数
根据你的具体需求调整这些参数以获得最佳性能
"""

from dataclasses import dataclass
from typing import Dict, Any


@dataclass
class QueueMonitorConfig:
    """队列监控配置类"""
    
    # BLPOP相关配置
    blpop_timeout: int = 60  # BLPOP超时时间（秒）
    max_consecutive_errors: int = 5  # 最大连续错误次数
    error_backoff_base: float = 0.5  # 错误退避基础时间（秒）
    error_backoff_max: float = 30.0  # 错误退避最大时间（秒）
    
    # 连接池配置
    min_connections: int = 5  # 最小连接数
    max_connections: int = 20  # 最大连接数
    connection_timeout: float = 10.0  # 连接超时（秒）
    socket_timeout: float = 60.0  # Socket超时（秒）
    idle_timeout: float = 600.0  # 空闲超时（秒）
    
    # 性能优化配置
    enable_fast_json_parsing: bool = True  # 启用快速JSON解析
    enable_connection_pooling: bool = True  # 启用连接池
    enable_async_task_cleanup: bool = True  # 启用异步任务清理
    
    # 监控和统计配置
    enable_stats_collection: bool = True  # 启用统计收集
    stats_report_interval: int = 300  # 统计报告间隔（秒）
    enable_health_check: bool = True  # 启用健康检查
    health_check_interval: int = 180  # 健康检查间隔（秒）
    
    # 日志配置
    log_level: str = "INFO"  # 日志级别
    log_message_details: bool = False  # 是否记录消息详情
    log_performance_metrics: bool = True  # 是否记录性能指标


# 预定义配置模板
class ConfigTemplates:
    """预定义的配置模板"""
    
    @staticmethod
    def high_throughput() -> QueueMonitorConfig:
        """高吞吐量配置 - 适用于大量消息处理"""
        return QueueMonitorConfig(
            blpop_timeout=120,  # 更长的超时，减少重连
            max_consecutive_errors=10,
            min_connections=10,  # 更多连接
            max_connections=50,
            connection_timeout=15.0,
            socket_timeout=120.0,  # 更长的socket超时
            idle_timeout=900.0,  # 15分钟空闲超时
            enable_async_task_cleanup=True,
            stats_report_interval=60,  # 更频繁的统计报告
        )
    
    @staticmethod
    def low_latency() -> QueueMonitorConfig:
        """低延迟配置 - 适用于实时响应要求高的场景"""
        return QueueMonitorConfig(
            blpop_timeout=30,  # 较短超时，快速响应
            max_consecutive_errors=3,
            error_backoff_base=0.1,  # 更快的错误恢复
            error_backoff_max=5.0,
            min_connections=8,
            max_connections=25,
            connection_timeout=5.0,  # 快速连接超时
            socket_timeout=30.0,
            enable_fast_json_parsing=True,
            enable_async_task_cleanup=True,
            log_performance_metrics=True,
        )
    
    @staticmethod
    def resource_conservative() -> QueueMonitorConfig:
        """资源保守配置 - 适用于资源受限的环境"""
        return QueueMonitorConfig(
            blpop_timeout=90,
            max_consecutive_errors=5,
            min_connections=2,  # 较少连接
            max_connections=8,
            connection_timeout=20.0,
            socket_timeout=60.0,
            idle_timeout=300.0,  # 较短空闲超时
            enable_stats_collection=False,  # 关闭统计收集
            stats_report_interval=600,
            health_check_interval=300,
            log_message_details=False,
        )
    
    @staticmethod
    def development() -> QueueMonitorConfig:
        """开发环境配置 - 适用于开发和调试"""
        return QueueMonitorConfig(
            blpop_timeout=10,  # 短超时，便于调试
            max_consecutive_errors=2,
            min_connections=1,
            max_connections=5,
            connection_timeout=5.0,
            socket_timeout=15.0,
            log_level="DEBUG",
            log_message_details=True,  # 记录详细消息
            log_performance_metrics=True,
            stats_report_interval=30,  # 频繁统计报告
        )
    
    @staticmethod
    def production_stable() -> QueueMonitorConfig:
        """生产环境稳定配置 - 平衡性能和稳定性"""
        return QueueMonitorConfig(
            blpop_timeout=60,
            max_consecutive_errors=5,
            error_backoff_base=1.0,
            error_backoff_max=60.0,
            min_connections=5,
            max_connections=20,
            connection_timeout=10.0,
            socket_timeout=60.0,
            idle_timeout=600.0,
            enable_stats_collection=True,
            stats_report_interval=300,
            health_check_interval=180,
            log_level="INFO",
            log_performance_metrics=True,
        )


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config: QueueMonitorConfig = None):
        self.config = config or ConfigTemplates.production_stable()
    
    def load_from_template(self, template_name: str) -> QueueMonitorConfig:
        """从模板加载配置"""
        templates = {
            'high_throughput': ConfigTemplates.high_throughput,
            'low_latency': ConfigTemplates.low_latency,
            'resource_conservative': ConfigTemplates.resource_conservative,
            'development': ConfigTemplates.development,
            'production_stable': ConfigTemplates.production_stable,
        }
        
        if template_name not in templates:
            raise ValueError(f"未知的配置模板: {template_name}")
        
        self.config = templates[template_name]()
        return self.config
    
    def load_from_dict(self, config_dict: Dict[str, Any]) -> QueueMonitorConfig:
        """从字典加载配置"""
        # 使用当前配置作为基础，更新指定的参数
        current_dict = self.config.__dict__.copy()
        current_dict.update(config_dict)
        
        self.config = QueueMonitorConfig(**current_dict)
        return self.config
    
    def get_redis_pool_config(self) -> Dict[str, Any]:
        """获取Redis连接池配置"""
        return {
            'min_connections': self.config.min_connections,
            'max_connections': self.config.max_connections,
            'connection_timeout': self.config.connection_timeout,
            'socket_timeout': self.config.socket_timeout,
            'idle_timeout': self.config.idle_timeout,
        }
    
    def get_monitor_config(self) -> Dict[str, Any]:
        """获取监控器配置"""
        return {
            'blpop_timeout': self.config.blpop_timeout,
            'max_consecutive_errors': self.config.max_consecutive_errors,
            'error_backoff_base': self.config.error_backoff_base,
            'error_backoff_max': self.config.error_backoff_max,
        }
    
    def validate_config(self) -> bool:
        """验证配置的合理性"""
        issues = []
        
        # 检查超时配置
        if self.config.blpop_timeout <= 0:
            issues.append("blpop_timeout必须大于0")
        
        if self.config.connection_timeout <= 0:
            issues.append("connection_timeout必须大于0")
        
        if self.config.socket_timeout < self.config.blpop_timeout:
            issues.append("socket_timeout应该大于等于blpop_timeout")
        
        # 检查连接池配置
        if self.config.min_connections <= 0:
            issues.append("min_connections必须大于0")
        
        if self.config.max_connections < self.config.min_connections:
            issues.append("max_connections必须大于等于min_connections")
        
        # 检查错误处理配置
        if self.config.max_consecutive_errors <= 0:
            issues.append("max_consecutive_errors必须大于0")
        
        if self.config.error_backoff_base <= 0:
            issues.append("error_backoff_base必须大于0")
        
        if self.config.error_backoff_max < self.config.error_backoff_base:
            issues.append("error_backoff_max必须大于等于error_backoff_base")
        
        if issues:
            raise ValueError(f"配置验证失败: {'; '.join(issues)}")
        
        return True
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("=== 队列监控配置摘要 ===")
        print(f"BLPOP超时: {self.config.blpop_timeout}秒")
        print(f"连接池: {self.config.min_connections}-{self.config.max_connections}个连接")
        print(f"连接超时: {self.config.connection_timeout}秒")
        print(f"Socket超时: {self.config.socket_timeout}秒")
        print(f"最大连续错误: {self.config.max_consecutive_errors}次")
        print(f"统计收集: {'启用' if self.config.enable_stats_collection else '禁用'}")
        print(f"健康检查: {'启用' if self.config.enable_health_check else '禁用'}")
        print(f"日志级别: {self.config.log_level}")


# 全局配置实例
_global_config_manager = None


def get_config_manager() -> ConfigManager:
    """获取全局配置管理器"""
    global _global_config_manager
    if _global_config_manager is None:
        _global_config_manager = ConfigManager()
    return _global_config_manager


def set_global_config(config: QueueMonitorConfig):
    """设置全局配置"""
    global _global_config_manager
    _global_config_manager = ConfigManager(config)


def load_config_template(template_name: str) -> ConfigManager:
    """加载配置模板"""
    manager = ConfigManager()
    manager.load_from_template(template_name)
    set_global_config(manager.config)
    return manager


# 使用示例
if __name__ == "__main__":
    # 示例1: 使用预定义模板
    print("=== 示例1: 低延迟配置 ===")
    low_latency_manager = load_config_template('low_latency')
    low_latency_manager.print_config_summary()
    
    print("\n=== 示例2: 高吞吐量配置 ===")
    high_throughput_manager = load_config_template('high_throughput')
    high_throughput_manager.print_config_summary()
    
    # 示例3: 自定义配置
    print("\n=== 示例3: 自定义配置 ===")
    custom_manager = ConfigManager()
    custom_config = {
        'blpop_timeout': 45,
        'min_connections': 3,
        'max_connections': 15,
        'log_level': 'DEBUG'
    }
    custom_manager.load_from_dict(custom_config)
    custom_manager.print_config_summary()
    
    # 验证配置
    try:
        custom_manager.validate_config()
        print("✅ 配置验证通过")
    except ValueError as e:
        print(f"❌ 配置验证失败: {e}")