#!/usr/bin/env python3
"""
紧急超时修复脚本 - 针对深度搜索超时问题
简化深度搜索过程，添加更好的错误处理和进度提示
"""

import asyncio
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# 添加 colorama 支持用于彩色输出
try:
    from colorama import Fore, Style, init
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    # 如果没有安装 colorama，创建空的占位符
    COLORAMA_AVAILABLE = False
    
# 确保 Fore 和 Style 在全局范围内可用
if not COLORAMA_AVAILABLE:
    class MockFore:
        RED = YELLOW = GREEN = BLUE = CYAN = MAGENTA = WHITE = ""
    class MockStyle:
        RESET_ALL = ""
    Fore = MockFore()
    Style = MockStyle()

logger = logging.getLogger(__name__)

class QuickDeepResearchFix:
    """快速深度搜索修复器"""
    
    def __init__(self):
        self.search_timeout = 45  # 减少到45秒
        self.max_queries = 2      # 只执行2个查询
        self.gpt_timeout = 60     # GPT分析超时60秒
        
    async def conduct_simplified_deep_research(self, input_data: Dict) -> Tuple[bool, str]:
        """
        简化版深度研究分析
        """
        start_time = time.time()
        
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}🚀 紧急备用方案：简化版深度研究分析")
        print(f"{Fore.CYAN}{'='*80}")
        
        logger.info("🚀 开始简化版深度研究分析")
        
        try:
            # 1. 快速提取账号信息
            print(f"{Fore.BLUE}📊 第一阶段：提取账号信息...")
            account_info = self._extract_account_info_fast(input_data)
            if not account_info:
                print(f"{Fore.RED}❌ 账号信息提取失败")
                logger.error("账号信息提取失败")
                return False, "账号信息提取失败"
            
            print(f"{Fore.GREEN}✅ 账号信息提取成功:")
            print(f"{Fore.YELLOW}   - 账号名称: {account_info.get('account_name', 'N/A')}")
            print(f"{Fore.YELLOW}   - 粉丝数量: {account_info.get('followers', 'N/A')}")
            print(f"{Fore.YELLOW}   - 行业: {account_info.get('industry', 'N/A')}")
            logger.info(f"✅ 账号: {account_info.get('account_name', 'N/A')}")
            
            # 2. 生成简化查询
            print(f"\n{Fore.BLUE}🔍 第二阶段：生成搜索查询...")
            search_queries = self._generate_simple_queries(account_info)
            
            print(f"{Fore.GREEN}✅ 生成了 {len(search_queries)} 个简化查询:")
            for i, query in enumerate(search_queries[:3]):  # 显示前3个查询
                query_content = query.get('content', '') if isinstance(query, dict) else str(query)
                print(f"{Fore.YELLOW}   {i+1}. {query_content[:80]}...")
            logger.info(f"📝 生成了 {len(search_queries)} 个简化查询")
            
            # 3. 执行快速搜索
            print(f"\n{Fore.BLUE}🚀 第三阶段：执行快速搜索...")
            logger.info("🔍 开始快速搜索...")
            search_results = await self._execute_quick_search(search_queries)
            
            if not search_results:
                print(f"{Fore.YELLOW}⚠️ 搜索无结果，生成基础报告")
                logger.warning("搜索无结果，生成基础报告")
                return await self._generate_basic_report(account_info)
            
            print(f"{Fore.GREEN}✅ 搜索完成，获得 {len(search_results)} 个结果")
            print(f"{Fore.YELLOW}📊 搜索质量评估:")
            
            # 显示搜索结果质量
            for i, result in enumerate(search_results[:3]):  # 显示前3个结果
                content_length = len(result.get('content', ''))
                relevance = result.get('relevance_score', 0.8)  # 模拟相关性分数
                color = Fore.GREEN if relevance > 0.8 else Fore.YELLOW if relevance > 0.6 else Fore.RED
                print(f"{color}   - 结果 {i+1}: 内容长度 {content_length}, 相关性 {relevance:.2f}")
                
            logger.info(f"✅ 搜索完成，获得 {len(search_results)} 个结果")
            
            # 4. 简化分析
            print(f"\n{Fore.MAGENTA}🧠 第四阶段：执行智能分析...")
            logger.info("🧠 开始简化分析...")
            analysis_result = await self._analyze_results_simple(account_info, search_results)
            
            if analysis_result and len(analysis_result.strip()) > 100:
                print(f"{Fore.GREEN}✅ 分析完成")
                print(f"{Fore.YELLOW}📊 分析质量评估:")
                print(f"{Fore.YELLOW}   - 报告长度: {len(analysis_result)} 字符")
                print(f"{Fore.YELLOW}   - 包含搜索结果: {len(search_results)} 个来源")
                print(f"{Fore.CYAN}   - 报告摘要: {analysis_result[:150]}...")
            else:
                print(f"{Fore.RED}⚠️ 分析结果质量较低，长度: {len(analysis_result) if analysis_result else 0}")
            
            total_time = time.time() - start_time
            print(f"\n{Fore.GREEN}🎉 简化深度研究完成")
            print(f"{Fore.CYAN}⏱️ 总耗时: {total_time:.2f}秒")
            print(f"{Fore.CYAN}{'='*80}")
            
            logger.info(f"🎉 简化深度研究完成，耗时 {total_time:.2f}秒")
            
            return True, analysis_result
            
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"💥 简化深度研究失败 (耗时: {total_time:.2f}秒): {str(e)}")
            
            # 回退到最基础的报告
            try:
                account_info = self._extract_account_info_fast(input_data)
                if account_info is None:
                    return False, "深度研究分析失败，无法提取账号信息"
                return await self._generate_basic_report(account_info)
            except:
                return False, "深度研究分析失败，无法生成基础报告"
    
    def _extract_account_info_fast(self, input_data: Dict) -> Optional[Dict]:
        """快速提取账号信息"""
        try:
            account_info_nested = input_data.get('accountInfo', {})
            
            return {
                'account_name': account_info_nested.get('nickname', '未知账号'),
                'industry': input_data.get('industry', '未知行业'),
                'follows': account_info_nested.get('followers', 0),
                'posts_count': account_info_nested.get('posts', 0),
                'description': account_info_nested.get('desc', ''),
                'recent_posts': input_data.get('noteList', [])[:5] if isinstance(input_data.get('noteList'), list) else []  # 只取前5篇
            }
        except Exception as e:
            logger.error(f"提取账号信息失败: {e}")
            return None
    
    def _generate_simple_queries(self, account_info: Dict) -> List[Dict]:
        """生成简化的搜索查询"""
        industry = account_info.get('industry', '通用')
        account_name = account_info.get('account_name', '账号')
        
        queries = [
            {
                'content': f'{industry}行业{datetime.now().year}年发展趋势 营销策略',
                'recency_filter': 'month'
            },
            {
                'content': f'{industry}领域头部账号成功案例分析',
                'recency_filter': 'month'
            }
        ]
        
        return queries
    
    async def _execute_quick_search(self, queries: List[Dict]) -> List[Dict]:
        """执行快速搜索"""
        try:
            # 导入搜索功能
            import sys
            import os
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))
            from task import callWattGPT
            
            # 构建搜索请求
            search_requests = []
            for query in queries:
                search_requests.append({
                    "model": "sonar",
                    "messages": [{"role": "user", "content": query['content']}],
                    "search_recency_filter": query.get('recency_filter', 'month')
                })
            
            logger.info(f"📡 执行 {len(search_requests)} 个搜索请求 (超时: {self.search_timeout}秒)")
            
            # 执行搜索
            status, code, results = callWattGPT.gcallPplxChannelChatCompletions(
                search_requests, timeout=self.search_timeout
            )
            
            if not status:
                logger.error(f"搜索失败: {results}")
                return []
            
            # 解析结果
            search_results = []
            for i, result in enumerate(results):
                try:
                    if isinstance(result, tuple) and len(result) >= 3:
                        result_status, result_code, result_data = result
                        if result_status and result_data:
                            search_results.append({
                                'query': queries[i]['content'],
                                'result': str(result_data)
                            })
                except Exception as e:
                    logger.warning(f"解析结果 {i} 失败: {e}")
                    continue
            
            return search_results
            
        except Exception as e:
            logger.error(f"快速搜索失败: {e}")
            return []
    
    async def _analyze_results_simple(self, account_info: Dict, search_results: List[Dict]) -> str:
        """简化分析结果"""
        try:
            # 导入GPT功能
            import sys
            import os
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))
            from task import callWattGPT
            
            # 合并搜索结果
            combined_content = ""
            for i, result in enumerate(search_results[:2]):  # 只分析前2个结果
                content = result.get('result', '')[:800]  # 每个结果限制800字符
                combined_content += f"\n--- 搜索结果 {i+1} ---\n{content}\n"
            
            # 构建简化的分析提示
            analysis_prompt = f"""
基于搜索结果，为账号"{account_info.get('account_name', 'N/A')}"生成简化的诊断报告：

账号信息：
- 行业：{account_info.get('industry', 'N/A')}
- 粉丝数：{account_info.get('follows', 0)}

搜索结果：
{combined_content}

请生成一个简洁的诊断报告，包含：
1. 行业趋势分析（2-3条要点）
2. 账号发展建议（3-4条具体建议）
3. 内容策略建议（2-3条要点）

要求：
- 中文回复
- 每个部分都要具体可操作
- 总长度控制在500字以内
"""
            
            # 调用GPT进行分析
            gpt_request = {
                "model": "gpt-4o-mini",
                "messages": [
                    {"role": "system", "content": "你是一个专业的社交媒体分析师，擅长简洁明了的账号诊断。"},
                    {"role": "user", "content": analysis_prompt}
                ],
                "max_tokens": 800,
                "temperature": 0.7
            }
            
            logger.info("🤖 开始GPT简化分析...")
            
            status, code, result = callWattGPT.callOpenaiChannelChatCompletions(
                gpt_request, timeout=self.gpt_timeout
            )
            
            if status and result:
                logger.info("✅ GPT分析完成")
                # 从原始响应中提取纯净的内容
                try:
                    if isinstance(result, dict) and 'result' in result:
                        result_data = result['result']
                        if isinstance(result_data, dict) and 'data' in result_data:
                            data = result_data['data']
                            if isinstance(data, dict) and 'choices' in data:
                                choices = data['choices']
                                if isinstance(choices, list) and len(choices) > 0:
                                    choice = choices[0]
                                    if isinstance(choice, dict) and 'message' in choice:
                                        message = choice['message']
                                        if isinstance(message, dict) and 'content' in message:
                                            content = message['content']
                                            logger.info("✅ 成功提取GPT分析内容")
                                            return content
                    
                    # 如果解析失败，尝试直接使用result作为字符串
                    logger.warning("GPT响应格式异常，尝试直接使用结果")
                    return str(result)
                except Exception as e:
                    logger.error(f"解析GPT响应失败: {e}")
                    return str(result)
            else:
                logger.error(f"GPT分析失败: {result}")
                basic_status, basic_result = await self._generate_basic_report(account_info)
                return basic_result if basic_status else "无法生成诊断报告"
                
        except Exception as e:
            logger.error(f"简化分析失败: {e}")
            basic_status, basic_result = await self._generate_basic_report(account_info)
            return basic_result if basic_status else "无法生成诊断报告"
    
    async def _generate_basic_report(self, account_info: Dict) -> Tuple[bool, str]:
        """生成基础报告（无搜索结果的情况）"""
        try:
            industry = account_info.get('industry', '未知行业')
            account_name = account_info.get('account_name', '账号')
            followers = account_info.get('follows', 0)
            
            basic_report = f"""
# {account_name} 账号诊断报告

## 基本信息
- 账号名称：{account_name}
- 所属行业：{industry}
- 粉丝数量：{followers:,}

## 行业趋势分析
1. {industry}行业正处于快速发展阶段，内容创作需要紧跟行业动态
2. 优质原创内容仍是获得用户关注的关键因素
3. 行业垂直化和专业化趋势明显，需要深耕细分领域

## 发展建议
1. **内容策略**：专注于{industry}领域的专业内容创作，提高内容的深度和价值
2. **互动策略**：积极与粉丝互动，建立稳定的用户关系
3. **发布频率**：保持稳定的发布节奏，建议每周3-5次更新
4. **话题热度**：关注行业热点话题，及时跟进讨论

## 内容策略建议
1. 结合{industry}行业特点，制作教程类、经验分享类内容
2. 定期发布行业观点和见解，建立专业权威形象
3. 适当加入生活化元素，增强内容的亲和力

## 总结
建议继续深耕{industry}领域，通过持续的优质内容输出和用户互动，提升账号影响力。
"""
            
            logger.info("📄 生成基础报告完成")
            return True, basic_report.strip()
            
        except Exception as e:
            logger.error(f"生成基础报告失败: {e}")
            return False, "无法生成诊断报告"


# 全局实例
_quick_fix_instance = None

def get_quick_deep_research_fix():
    """获取快速深度研究修复器实例"""
    global _quick_fix_instance
    if _quick_fix_instance is None:
        _quick_fix_instance = QuickDeepResearchFix()
    return _quick_fix_instance

async def enhanced_diagnosis_with_quick_fix(input_data: Dict, use_deep_research: bool = True) -> Tuple[bool, str]:
    """
    使用快速修复版本的深度研究分析
    """
    if not use_deep_research:
        return False, "深度研究功能已禁用"
    
    fixer = get_quick_deep_research_fix()
    return await fixer.conduct_simplified_deep_research(input_data)

if __name__ == "__main__":
    # 测试代码
    async def test_quick_fix():
        test_data = {
            'accountInfo': {
                'nickname': '测试科技博主',
                'followers': 10000,
                'posts': 150,
                'desc': '专注于AI技术分享'
            },
            'industry': '科技',
            'noteList': [
                {'title': 'AI发展趋势', 'engagement': 800}
            ]
        }
        
        status, result = await enhanced_diagnosis_with_quick_fix(test_data)
        print(f"Status: {status}")
        print(f"Result: {result}")
    
    # 运行测试
    asyncio.run(test_quick_fix())