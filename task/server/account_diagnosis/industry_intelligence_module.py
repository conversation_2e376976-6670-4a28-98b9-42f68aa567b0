"""
行业智能模块 - 专注于行业前沿热点、营销方法论和头部KOL/竞争对手分析
基于company_profile_init.py的搜索思路，结合深度研究需求
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from colorama import Fore

logger = logging.getLogger(__name__)

class IndustrySearchType(Enum):
    """行业搜索类型"""
    HOTSPOT = "hotspot"           # 行业热点
    METHODOLOGY = "methodology"   # 营销方法论
    KOL_ANALYSIS = "kol_analysis" # KOL分析
    COMPETITOR = "competitor"     # 竞争对手
    MARKET_TREND = "market_trend" # 市场趋势
    CASE_STUDY = "case_study"     # 案例研究

@dataclass
class IndustrySearchQuery:
    """行业搜索查询"""
    content: str
    search_type: IndustrySearchType
    focus: str
    recency_filter: str
    priority: float
    keywords: List[str]
    expected_results: int = 3

class IndustryIntelligenceModule:
    """行业智能模块"""
    
    def __init__(self, config=None):
        # 导入配置
        from .service_config import get_deep_research_config
        
        if config is None:
            config = get_deep_research_config()
            
        self.config = config
        self.gpt_model = config.models.gpt_model
        self.pplx_model = config.models.pplx_model
        self.search_timeout = config.search_timeout
        self.analysis_timeout = config.analysis_timeout
        
        # 导入PPLX调用模块
        try:
            from task import callWattGPT
            self.callWattGPT = callWattGPT
            self.pplx_available = True
            logger.info("PPLX搜索模块导入成功")
        except ImportError as e:
            logger.error(f"无法导入PPLX模块: {e}")
            self.pplx_available = False
            
        # 导入GPT调用模块
        try:
            from task.lib.call_claude import gemini
            self.gemini = gemini
            self.gemini_available = True
            logger.info("Gemini模块导入成功")
        except ImportError as e:
            logger.error(f"无法导入Gemini模块: {e}")
            self.gemini_available = False
            
        # 使用动态关键词生成器
        try:
            from .dynamic_keyword_generator import get_dynamic_keyword_generator
            self.keyword_generator = get_dynamic_keyword_generator()
            self.use_dynamic_keywords = True
            logger.info("动态关键词生成器初始化成功")
        except ImportError as e:
            logger.warning(f"动态关键词生成器不可用，使用基础关键词: {e}")
            self.keyword_generator = None
            self.use_dynamic_keywords = False
            
        # 基础关键词映射（作为后备方案）
        self.fallback_keywords = {
            "美妆护肤": ["美妆", "护肤", "彩妆", "护肤品", "美容", "化妆品", "面膜", "精华", "防晒"],
            "科技数码": ["科技", "数码", "手机", "电脑", "AI", "人工智能", "智能硬件", "软件", "APP"],
            "时尚穿搭": ["时尚", "穿搭", "服装", "搭配", "时装", "潮流", "品牌", "风格", "造型"],
            "健康养生": ["健康", "养生", "保健", "营养", "运动", "健身", "减肥", "瑜伽", "医疗"],
            "美食料理": ["美食", "料理", "菜谱", "烹饪", "食谱", "餐厅", "小吃", "饮食", "厨艺"],
            "旅行摄影": ["旅行", "摄影", "旅游", "拍照", "风景", "攻略", "酒店", "景点", "游记"],
            "母婴育儿": ["母婴", "育儿", "宝宝", "婴儿", "孕妇", "亲子", "早教", "辅食", "玩具"],
            "宠物": ["宠物", "狗狗", "猫咪", "宠物用品", "宠物护理", "宠物训练", "宠物食品"],
            "家居装修": ["家居", "装修", "家装", "设计", "软装", "家具", "建材", "收纳", "装饰"],
            "教育培训": ["教育", "培训", "学习", "课程", "技能", "考试", "职业", "英语", "知识"]
        }
        
    async def _get_industry_keywords(self, industry: str, account_info: Dict) -> List[str]:
        """获取行业相关关键词（支持动态生成）"""
        try:
            if self.use_dynamic_keywords and self.keyword_generator:
                # 使用动态关键词生成器
                logger.info(f"使用动态关键词生成器获取关键词: {industry}")
                enhanced_keywords = await self.keyword_generator.get_enhanced_keywords(industry, account_info)
                if enhanced_keywords and len(enhanced_keywords) > 1:
                    logger.info(f"动态生成关键词成功: {len(enhanced_keywords)} 个关键词")
                    return enhanced_keywords
                else:
                    logger.warning(f"动态生成关键词返回结果不理想，回退到基础关键词")
            
            # 回退到基础关键词映射
            fallback_keywords = self.fallback_keywords.get(industry, [industry])
            logger.info(f"使用基础关键词: {industry} -> {len(fallback_keywords)} 个关键词")
            return fallback_keywords
            
        except Exception as e:
            logger.error(f"获取行业关键词失败: {e}")
            # 最后的后备方案
            return [industry]
        
    async def _generate_industry_hotspot_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成行业热点查询"""
        industry = account_info.get('industry', '')
        keywords = await self._get_industry_keywords(industry, account_info)
        
        queries = []
        
        # 1. 行业最新热点趋势
        queries.append(IndustrySearchQuery(
            content=f"{industry}行业{datetime.now().year}年最新热点趋势 市场变化 发展机会",
            search_type=IndustrySearchType.HOTSPOT,
            focus="行业最新热点",
            recency_filter="month",
            priority=1.9,
            keywords=keywords + [str(datetime.now().year), "热点", "趋势", "最新"]
        ))
        
        # 2. 新兴技术应用
        queries.append(IndustrySearchQuery(
            content=f"{industry}新兴技术应用 创新产品 技术革命 数字化转型",
            search_type=IndustrySearchType.HOTSPOT,
            focus="新兴技术应用",
            recency_filter="month",
            priority=1.8,
            keywords=keywords + ["新兴技术", "创新", "数字化"]
        ))
        
        # 3. 消费者行为变化
        queries.append(IndustrySearchQuery(
            content=f"{industry}消费者行为变化 购买偏好 用户需求 消费习惯",
            search_type=IndustrySearchType.HOTSPOT,
            focus="消费者行为变化",
            recency_filter="month",
            priority=1.7,
            keywords=keywords + ["消费者", "行为", "需求", "习惯"]
        ))
        
        # 4. 政策法规影响
        queries.append(IndustrySearchQuery(
            content=f"{industry}政策法规影响 合规要求 监管变化 行业标准",
            search_type=IndustrySearchType.HOTSPOT,
            focus="政策法规影响",
            recency_filter="month",
            priority=1.6,
            keywords=keywords + ["政策", "法规", "监管", "合规"]
        ))
        
        return queries
    
    async def _generate_marketing_methodology_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成营销方法论查询"""
        industry = account_info.get('industry', '')
        keywords = await self._get_industry_keywords(industry, account_info)
        
        queries = []
        
        # 1. 头部品牌营销策略
        queries.append(IndustrySearchQuery(
            content=f"{industry}头部品牌营销策略 成功案例 营销创新 品牌传播",
            search_type=IndustrySearchType.METHODOLOGY,
            focus="头部品牌营销策略",
            recency_filter="month",
            priority=1.9,
            keywords=keywords + ["营销策略", "品牌", "传播", "创新"]
        ))
        
        # 2. 内容营销最佳实践
        queries.append(IndustrySearchQuery(
            content=f"{industry}内容营销最佳实践 爆款内容 用户增长 互动策略",
            search_type=IndustrySearchType.METHODOLOGY,
            focus="内容营销实践",
            recency_filter="month",
            priority=1.8,
            keywords=keywords + ["内容营销", "爆款", "增长", "互动"]
        ))
        
        # 3. 社交媒体运营
        queries.append(IndustrySearchQuery(
            content=f"{industry}社交媒体运营 小红书运营 抖音营销 私域流量",
            search_type=IndustrySearchType.METHODOLOGY,
            focus="社交媒体运营",
            recency_filter="month",
            priority=1.7,
            keywords=keywords + ["社交媒体", "小红书", "抖音", "私域"]
        ))
        
        # 4. 数字营销工具
        queries.append(IndustrySearchQuery(
            content=f"{industry}数字营销工具 营销技术 数据分析 精准营销",
            search_type=IndustrySearchType.METHODOLOGY,
            focus="数字营销工具",
            recency_filter="month",
            priority=1.6,
            keywords=keywords + ["数字营销", "工具", "数据", "精准"]
        ))
        
        return queries
    
    async def _generate_kol_competitor_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成KOL/竞争对手查询"""
        industry = account_info.get('industry', '')
        keywords = await self._get_industry_keywords(industry, account_info)
        follower_count = account_info.get('follows', 0)
        
        queries = []
        
        # 1. 行业头部KOL分析
        queries.append(IndustrySearchQuery(
            content=f"{industry}行业头部KOL 影响力排名 成功要素 内容策略",
            search_type=IndustrySearchType.KOL_ANALYSIS,
            focus="头部KOL分析",
            recency_filter="month",
            priority=1.9,
            keywords=keywords + ["头部KOL", "影响力", "排名", "成功"]
        ))
        
        # 2. 竞争对手分析
        if follower_count > 0:
            follower_range = self._get_follower_range(follower_count)
            queries.append(IndustrySearchQuery(
                content=f"{industry}优质账号 {follower_range} 竞争对手分析 差异化策略",
                search_type=IndustrySearchType.COMPETITOR,
                focus="竞争对手分析",
                recency_filter="month",
                priority=1.8,
                keywords=keywords + ["竞争对手", "差异化", "策略"]
            ))
        
        # 3. 新兴KOL崛起
        queries.append(IndustrySearchQuery(
            content=f"{industry}新兴KOL崛起 黑马账号 增长策略 突破方法",
            search_type=IndustrySearchType.KOL_ANALYSIS,
            focus="新兴KOL分析",
            recency_filter="week",
            priority=1.7,
            keywords=keywords + ["新兴KOL", "黑马", "增长", "突破"]
        ))
        
        # 4. 合作机会分析
        queries.append(IndustrySearchQuery(
            content=f"{industry}KOL合作机会 品牌联名 跨界合作 商业化模式",
            search_type=IndustrySearchType.KOL_ANALYSIS,
            focus="合作机会分析",
            recency_filter="month",
            priority=1.6,
            keywords=keywords + ["合作", "联名", "跨界", "商业化"]
        ))
        
        return queries
    
    def _get_follower_range(self, follower_count: int) -> str:
        """获取粉丝数范围描述"""
        if follower_count < 1000:
            return "0-1K粉丝"
        elif follower_count < 10000:
            return "1K-1W粉丝"
        elif follower_count < 100000:
            return "1W-10W粉丝"
        elif follower_count < 1000000:
            return "10W-100W粉丝"
        else:
            return "100W+粉丝"
    
    async def _generate_market_trend_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成市场趋势查询"""
        industry = account_info.get('industry', '')
        keywords = await self._get_industry_keywords(industry, account_info)
        
        queries = []
        
        # 1. 细分市场机会
        queries.append(IndustrySearchQuery(
            content=f"{industry}细分市场机会 蓝海市场 利基市场 增长空间",
            search_type=IndustrySearchType.MARKET_TREND,
            focus="细分市场机会",
            recency_filter="month",
            priority=1.8,
            keywords=keywords + ["细分市场", "蓝海", "利基", "增长"]
        ))
        
        # 2. 用户画像变化
        queries.append(IndustrySearchQuery(
            content=f"{industry}用户画像变化 目标用户 消费群体 代际差异",
            search_type=IndustrySearchType.MARKET_TREND,
            focus="用户画像变化",
            recency_filter="month",
            priority=1.7,
            keywords=keywords + ["用户画像", "消费群体", "代际"]
        ))
        
        # 3. 商业模式创新
        queries.append(IndustrySearchQuery(
            content=f"{industry}商业模式创新 盈利模式 变现方式 收入来源",
            search_type=IndustrySearchType.MARKET_TREND,
            focus="商业模式创新",
            recency_filter="month",
            priority=1.6,
            keywords=keywords + ["商业模式", "盈利", "变现", "收入"]
        ))
        
        return queries
    
    async def _generate_case_study_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成案例研究查询"""
        industry = account_info.get('industry', '')
        keywords = await self._get_industry_keywords(industry, account_info)
        
        queries = []
        
        # 1. 成功案例分析
        queries.append(IndustrySearchQuery(
            content=f"{industry}成功案例分析 爆款案例 病毒传播 营销奇迹",
            search_type=IndustrySearchType.CASE_STUDY,
            focus="成功案例分析",
            recency_filter="month",
            priority=1.8,
            keywords=keywords + ["成功案例", "爆款", "病毒", "营销"]
        ))
        
        # 2. 失败案例教训
        queries.append(IndustrySearchQuery(
            content=f"{industry}失败案例教训 营销失误 品牌危机 经验总结",
            search_type=IndustrySearchType.CASE_STUDY,
            focus="失败案例教训",
            recency_filter="month",
            priority=1.7,
            keywords=keywords + ["失败案例", "教训", "危机", "经验"]
        ))
        
        return queries
    
    async def generate_comprehensive_industry_queries(self, account_info: Dict) -> List[IndustrySearchQuery]:
        """生成综合行业查询"""
        try:
            logger.info("开始生成综合行业智能查询")
            
            all_queries = []
            
            # 1. 行业热点查询
            hotspot_queries = await self._generate_industry_hotspot_queries(account_info)
            all_queries.extend(hotspot_queries)
            print(Fore.YELLOW + f"hotspots: {hotspot_queries}"+Fore.RESET)
            
            # 2. 营销方法论查询
            methodology_queries = await self._generate_marketing_methodology_queries(account_info)
            all_queries.extend(methodology_queries)
            print(Fore.YELLOW + f"methodologies: {methodology_queries}" + Fore.RESET)
            
            # 3. KOL/竞争对手查询
            kol_competitor_queries = await self._generate_kol_competitor_queries(account_info)
            all_queries.extend(kol_competitor_queries)
            print(Fore.BLUE + f"kol_competitors: {kol_competitor_queries}"+Fore.RESET)
            
            # 4. 市场趋势查询
            market_trend_queries = await self._generate_market_trend_queries(account_info)
            all_queries.extend(market_trend_queries)
            print(Fore.BLUE + f"market_trends: {market_trend_queries}" + Fore.RESET)

            # 5. 案例研究查询
            case_study_queries = await self._generate_case_study_queries(account_info)
            all_queries.extend(case_study_queries)
            print(Fore.BLUE + f"case_studies: {case_study_queries}" + Fore.RESET)
            
            # 按优先级排序
            all_queries.sort(key=lambda x: x.priority, reverse=True)
            
            # 限制查询数量
            max_queries = 15
            limited_queries = all_queries[:max_queries]
            
            logger.info(f"综合行业查询生成完成：{len(limited_queries)}个查询")
            
            return limited_queries
            
        except Exception as e:
            logger.error(f"生成综合行业查询失败: {e}")
            return []
    
    async def execute_industry_search(self, queries: List[IndustrySearchQuery]) -> List[Dict]:
        """执行行业智能搜索"""
        if not self.pplx_available:
            logger.error("PPLX搜索功能不可用")
            return []
        
        try:
            logger.info(f"开始执行行业智能搜索：{len(queries)}个查询")
            
            # 构建PPLX搜索请求列表
            pplx_search_list = []
            for query in queries:
                search_request = {
                    "model": self.pplx_model,
                    "messages": [
                        {"role": "user", "content": query.content}
                    ],
                    "search_recency_filter": query.recency_filter
                }
                pplx_search_list.append(search_request)
            
            # 执行批量搜索
            logger.info(f"执行{len(pplx_search_list)}个PPLX搜索（超时{self.search_timeout}秒）")
            
            pplx_search_status, code, pplx_search_result = self.callWattGPT.gcallPplxChannelChatCompletions(
                pplx_search_list, timeout=self.search_timeout
            )
            
            if not pplx_search_status:
                logger.error(f"PPLX批量搜索失败: {pplx_search_result}")
                return []
            
            # 解析搜索结果
            search_results = []
            for i, pplx_result in enumerate(pplx_search_result):
                try:
                    # 修复: 安全解包pplx_result，处理可能的多个返回值
                    if isinstance(pplx_result, (list, tuple)) and len(pplx_result) >= 3:
                        status, result_code, result = pplx_result[0], pplx_result[1], pplx_result[2]
                    elif isinstance(pplx_result, (list, tuple)) and len(pplx_result) == 2:
                        status, result = pplx_result[0], pplx_result[1]
                        result_code = None
                    else:
                        logger.error(f"搜索结果 {i+1} 格式异常: {type(pplx_result)}, {pplx_result}")
                        continue
                    
                    if not status:
                        logger.warning(f"搜索查询 {i+1} 失败: {result}")
                        continue
                    
                    search_content = result['result']['data']['choices'][0]['message']['content']
                    search_citations = result['result']['data']['citations']
                    
                    if search_content and len(search_content.strip()) > 50:
                        search_result = {
                            "query": queries[i].content,
                            "search_type": queries[i].search_type.value,
                            "focus": queries[i].focus,
                            "result": search_content,
                            "citations": search_citations,
                            "keywords": queries[i].keywords,
                            "priority": queries[i].priority
                        }
                        search_results.append(search_result)
                        logger.info(f"搜索查询 {i+1} 成功: {len(search_content)} 字符")
                    
                except Exception as e:
                    logger.error(f"解析搜索结果 {i+1} 失败: {e}")
                    continue
            
            logger.info(f"行业智能搜索完成：{len(search_results)}个有效结果")
            return search_results
            
        except Exception as e:
            logger.error(f"执行行业智能搜索失败: {e}")
            return []
    
    async def analyze_industry_intelligence(self, search_results: List[Dict], account_info: Dict) -> Dict:
        """分析行业智能信息"""
        try:
            logger.info("开始分析行业智能信息")
            
            # 按搜索类型分类结果
            categorized_results = {}
            for result in search_results:
                search_type = result.get('search_type', 'unknown')
                if search_type not in categorized_results:
                    categorized_results[search_type] = []
                categorized_results[search_type].append(result)
            
            # 构建分析提示词
            industry = account_info.get('industry', '')
            account_name = account_info.get('account_name', '')
            
            analysis_prompt = f"""
基于以下搜索结果，对{industry}行业进行深度智能分析，为账号"{account_name}"提供策略洞察：

搜索结果分类：
"""
            
            for search_type, results in categorized_results.items():
                analysis_prompt += f"\n{search_type.upper()}相关信息：\n"
                for i, result in enumerate(results[:2]):  # 每类最多取2个结果
                    analysis_prompt += f"- {result['focus']}: {result['result'][:300]}...\n"
            
            analysis_prompt += f"""

请从以下维度进行分析：

1. 行业热点洞察
- 当前最重要的3个行业热点趋势
- 每个热点的商业机会和风险
- 对该账号的具体影响和建议

2. 营销方法论分析
- 行业内最有效的3种营销策略
- 成功案例的关键成功因素
- 可复制的营销模式和工具

3. KOL竞争格局
- 头部KOL的核心竞争优势
- 新兴KOL的崛起策略
- 竞争对手的差异化定位

4. 市场机会挖掘
- 未被充分开发的细分市场
- 用户需求的变化趋势
- 商业模式创新机会

5. 行动建议
- 基于分析的具体行动建议
- 短期策略（1-3个月）
- 中期策略（3-6个月）

请以JSON格式返回分析结果。
"""
            
            # 调用Gemini进行分析
            sys_prompt = """
你是一位资深的行业分析专家，专注于社交媒体和数字营销领域。
请基于搜索结果提供专业、实用的行业洞察和策略建议。
"""
            
            # 修复：gemini 函数返回 3 个值 (status, result, usage_metadata)
            status, result, usage_metadata = self.gemini(sys_prompt, analysis_prompt)
            
            if not status:
                logger.error(f"行业智能分析失败: {result}")
                return {}
            
            # 尝试解析JSON结果
            try:
                if isinstance(result, str):
                    # 提取JSON部分
                    json_start = result.find('{')
                    json_end = result.rfind('}') + 1
                    if json_start != -1 and json_end != -1:
                        json_str = result[json_start:json_end]
                        intelligence_data = json.loads(json_str)
                    else:
                        # 如果无法提取JSON，返回原始文本
                        intelligence_data = {"raw_analysis": result}
                else:
                    intelligence_data = result
                
                # 确保 intelligence_data 是字典类型
                if not isinstance(intelligence_data, dict):
                    intelligence_data = {"raw_analysis": str(intelligence_data)}
                
                # 添加元数据
                intelligence_data['metadata'] = {
                    'analysis_time': datetime.now().isoformat(),
                    'industry': industry,
                    'account_name': account_name,
                    'search_results_count': len(search_results),
                    'categorized_results': {k: len(v) for k, v in categorized_results.items()}
                }
                
                logger.info("行业智能分析完成")
                return intelligence_data
                
            except json.JSONDecodeError:
                logger.warning("无法解析JSON结果，返回原始文本")
                return {
                    "raw_analysis": result,
                    "metadata": {
                        'analysis_time': datetime.now().isoformat(),
                        'industry': industry,
                        'account_name': account_name,
                        'search_results_count': len(search_results)
                    }
                }
                
        except Exception as e:
            logger.error(f"分析行业智能信息失败: {e}")
            return {}
    
    async def conduct_comprehensive_industry_research(self, account_info: Dict) -> Tuple[bool, Dict]:
        """执行综合行业研究"""
        try:
            logger.info("开始综合行业研究")
            
            # 1. 生成查询
            queries = await self.generate_comprehensive_industry_queries(account_info)
            if not queries:
                return False, {}
            
            # 2. 执行搜索
            search_results = await self.execute_industry_search(queries)
            if not search_results:
                return False, {}
            
            # 3. 分析结果
            intelligence_data = await self.analyze_industry_intelligence(search_results, account_info)
            if not intelligence_data:
                return False, {}
            
            # 4. 构建最终结果
            final_result = {
                "industry_hotspots": intelligence_data.get("行业热点洞察", {}),
                "marketing_methodologies": intelligence_data.get("营销方法论分析", {}),
                "kol_competitor_analysis": intelligence_data.get("KOL竞争格局", {}),
                "market_opportunities": intelligence_data.get("市场机会挖掘", {}),
                "strategic_recommendations": intelligence_data.get("行动建议", {}),
                "raw_intelligence": intelligence_data,
                "search_summary": {
                    "total_queries": len(queries),
                    "successful_searches": len(search_results),
                    "analysis_timestamp": datetime.now().isoformat()
                }
            }
            
            logger.info("综合行业研究完成")
            return True, final_result
            
        except Exception as e:
            logger.error(f"综合行业研究失败: {e}")
            return False, {}

# 全局实例
_industry_intelligence_module = None

def get_industry_intelligence_module() -> IndustryIntelligenceModule:
    """获取行业智能模块实例（单例模式）"""
    global _industry_intelligence_module
    if _industry_intelligence_module is None:
        _industry_intelligence_module = IndustryIntelligenceModule()
    return _industry_intelligence_module

async def conduct_industry_research(account_info: Dict) -> Tuple[bool, Dict]:
    """行业研究入口函数"""
    module = get_industry_intelligence_module()
    return await module.conduct_comprehensive_industry_research(account_info)

if __name__ == "__main__":
    # 测试代码
    async def test_industry_research():
        test_account = {
            'account_name': '小美的护肤日记',
            'industry': '美妆护肤',
            'follows': 25000,
            'content_themes': ['护肤知识', '产品评测', '美妆教程'],
            'target_audience': ['18-35岁女性', '护肤爱好者']
        }
        
        success, result = await conduct_industry_research(test_account)
        print(f"研究结果: {success}")
        if success:
            print(f"行业热点: {result.get('industry_hotspots', {})}")
            print(f"营销方法论: {result.get('marketing_methodologies', {})}")
        else:
            print(f"失败原因: {result}")
    
    # 运行测试
    # asyncio.run(test_industry_research())