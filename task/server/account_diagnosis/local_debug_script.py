import asyncio
import json
import time
import os
import sys
import logging
from logging.handlers import RotatingFileHandler
from typing import Dict, Any, <PERSON><PERSON>
import random
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

# 导入核心处理函数
from task.server.account_diagnosis.diagnosis_core import process_task_async as process_diagnosis_task
from task.server.account_diagnosis.service_implementations import StrategyService, ReviewService, CoverService

# 导入 json_utils 用于后处理
from task.lib.json_utils_enhanced import (
    safe_redis_serialize_with_validation,
    detect_json_issues,
    enhanced_validate_and_fix_ai_json,
    create_error_response
)

# 配置日志系统
def setup_logging():
    """设置日志系统"""
    os.makedirs('logs', exist_ok=True)
    
    logger = logging.getLogger('local_diagnosis_debug')
    logger.setLevel(logging.INFO)
    logger.handlers.clear()
    
    file_handler = RotatingFileHandler(
        'logs/local_diagnosis_debug.log',
        maxBytes=10*1024*1024,
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)
    
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    # 为核心模块也配置日志
    core_logger_diag = logging.getLogger('task.server.account_diagnosis.diagnosis_core')
    core_logger_diag.setLevel(logging.INFO)
    core_logger_diag.handlers.clear()
    core_logger_diag.addHandler(file_handler)
    core_logger_diag.addHandler(console_handler)
    
    strat_logger = logging.getLogger('strategy_service')
    strat_logger.setLevel(logging.INFO)
    strat_logger.handlers.clear()
    strat_logger.addHandler(file_handler)
    strat_logger.addHandler(console_handler)

    review_logger = logging.getLogger('review_service')
    review_logger.setLevel(logging.INFO)
    review_logger.handlers.clear()
    review_logger.addHandler(file_handler)
    review_logger.addHandler(console_handler)

    cover_logger = logging.getLogger('async_cover_service')
    cover_logger.setLevel(logging.INFO)
    cover_logger.handlers.clear()
    cover_logger.addHandler(file_handler)
    cover_logger.addHandler(console_handler)

    return logger

logger = setup_logging()

# 全局 token 统计
token_stats = {
    "total_prompt_tokens": 0,
    "total_output_tokens": 0,
    "total_tokens": 0,
    "model_usage": {},
    "task_breakdown": {}
}

def print_token_usage(task_name: str, usage_metadata: dict):
    """打印 token 使用量信息"""
    if not usage_metadata:
        return
    
    prompt_tokens = usage_metadata.get('promptTokenCount', 0)
    output_tokens = usage_metadata.get('candidatesTokenCount', 0)
    total_tokens = usage_metadata.get('totalTokenCount', 0)
    
    # 更新全局统计
    token_stats["total_prompt_tokens"] += prompt_tokens
    token_stats["total_output_tokens"] += output_tokens
    token_stats["total_tokens"] += total_tokens
    
    # 按任务统计
    if task_name not in token_stats["task_breakdown"]:
        token_stats["task_breakdown"][task_name] = {
            "prompt_tokens": 0,
            "output_tokens": 0,
            "total_tokens": 0,
            "calls": 0
        }
    
    task_stats = token_stats["task_breakdown"][task_name]
    task_stats["prompt_tokens"] += prompt_tokens
    task_stats["output_tokens"] += output_tokens
    task_stats["total_tokens"] += total_tokens
    task_stats["calls"] += 1
    
    print(f"{Fore.YELLOW}📊 {task_name} Token使用量:")
    print(f"{Fore.YELLOW}   Prompt: {prompt_tokens:,}, Output: {output_tokens:,}, Total: {total_tokens:,}")

def print_final_token_stats():
    """打印最终的 token 统计信息"""
    print(f"\n{Fore.CYAN}{'='*80}")
    print(f"{Fore.CYAN}📊 TOKEN 使用量统计报告")
    print(f"{Fore.CYAN}{'='*80}")
    
    print(f"{Fore.GREEN}🔢 总计使用量:")
    print(f"{Fore.GREEN}   Prompt Tokens: {token_stats['total_prompt_tokens']:,}")
    print(f"{Fore.GREEN}   Output Tokens: {token_stats['total_output_tokens']:,}")
    print(f"{Fore.GREEN}   Total Tokens: {token_stats['total_tokens']:,}")
    
    print(f"\n{Fore.YELLOW}📋 按任务分类:")
    for task_name, stats in token_stats["task_breakdown"].items():
        print(f"{Fore.YELLOW}   {task_name}:")
        print(f"{Fore.YELLOW}     调用次数: {stats['calls']}")
        print(f"{Fore.YELLOW}     Prompt: {stats['prompt_tokens']:,}")
        print(f"{Fore.YELLOW}     Output: {stats['output_tokens']:,}")
        print(f"{Fore.YELLOW}     Total: {stats['total_tokens']:,}")
        if stats['calls'] > 0:
            avg_total = stats['total_tokens'] / stats['calls']
            print(f"{Fore.YELLOW}     平均每次: {avg_total:.1f} tokens")
    
    print(f"{Fore.CYAN}{'='*80}")

async def compare_deep_research_usage():
    """对比启用和禁用深度搜索的 token 使用量"""
    print(f"\n{Fore.MAGENTA}🔬 开始对比深度搜索 vs 基础模式的 Token 使用量...")
    
    # 测试数据
    test_data = {
        "accountInfo": {
            "nickname": "测试账号",
            "followers": 28500,
            "posts": 8,
            "description": "在线教育账号测试"
        },
        "noteList": [
            {
                "title": "Python学习指南",
                "likes": 1200,
                "collected": 300,
                "comments_count": 50,
                "content": "分享Python学习经验"
            }
        ],
        "marketingGoal": "引流私域",
        "industry": "在线教育"
    }
    
    # 1. 测试禁用深度搜索
    print(f"\n{Fore.BLUE}🔍 测试 1: 禁用深度搜索 (基础模式)")
    token_stats_basic = {
        "total_prompt_tokens": 0,
        "total_output_tokens": 0,
        "total_tokens": 0,
        "task_breakdown": {}
    }
    
    # 重置统计
    global token_stats
    token_stats = token_stats_basic.copy()
    
    start_time = time.time()
    basic_status, basic_results = await process_diagnosis_task(test_data, enable_deep_research=False)
    basic_time = time.time() - start_time
    
    basic_stats = token_stats.copy()
    
    print(f"{Fore.BLUE}   基础模式完成，耗时: {basic_time:.2f}秒")
    print(f"{Fore.BLUE}   Total Tokens: {basic_stats['total_tokens']:,}")
    
    # 2. 测试启用深度搜索
    print(f"\n{Fore.GREEN}🔍 测试 2: 启用深度搜索 (深度模式)")
    token_stats_deep = {
        "total_prompt_tokens": 0,
        "total_output_tokens": 0,
        "total_tokens": 0,
        "task_breakdown": {}
    }
    
    # 重置统计
    token_stats = token_stats_deep.copy()
    
    start_time = time.time()
    deep_status, deep_results = await process_diagnosis_task(test_data, enable_deep_research=True)
    deep_time = time.time() - start_time
    
    deep_stats = token_stats.copy()
    
    print(f"{Fore.GREEN}   深度模式完成，耗时: {deep_time:.2f}秒")
    print(f"{Fore.GREEN}   Total Tokens: {deep_stats['total_tokens']:,}")
    
    # 3. 对比分析
    print(f"\n{Fore.MAGENTA}📊 对比分析:")
    print(f"{Fore.MAGENTA}{'='*60}")
    
    token_diff = deep_stats['total_tokens'] - basic_stats['total_tokens']
    time_diff = deep_time - basic_time
    
    print(f"{Fore.CYAN}基础模式:")
    print(f"{Fore.CYAN}   耗时: {basic_time:.2f}秒")
    print(f"{Fore.CYAN}   Token使用: {basic_stats['total_tokens']:,}")
    print(f"{Fore.CYAN}   任务数: {len(basic_stats['task_breakdown'])}")
    
    print(f"\n{Fore.CYAN}深度模式:")
    print(f"{Fore.CYAN}   耗时: {deep_time:.2f}秒")
    print(f"{Fore.CYAN}   Token使用: {deep_stats['total_tokens']:,}")
    print(f"{Fore.CYAN}   任务数: {len(deep_stats['task_breakdown'])}")
    
    print(f"\n{Fore.YELLOW}差异:")
    print(f"{Fore.YELLOW}   额外耗时: {time_diff:.2f}秒 ({(time_diff/basic_time)*100:.1f}%)")
    
    if basic_stats['total_tokens'] > 0:
        token_percentage = (token_diff/basic_stats['total_tokens'])*100
        print(f"{Fore.YELLOW}   额外Token: {token_diff:,} ({token_percentage:.1f}%)")
        efficiency = deep_stats['total_tokens'] / basic_stats['total_tokens']
        print(f"{Fore.YELLOW}   Token效率比: {efficiency:.2f}x")
    else:
        print(f"{Fore.YELLOW}   额外Token: {token_diff:,} (基础模式无token使用)")
        if deep_stats['total_tokens'] > 0:
            print(f"{Fore.YELLOW}   Token效率比: ∞ (基础模式失败)")
        else:
            print(f"{Fore.YELLOW}   Token效率比: N/A (两种模式都失败)")
    
    print(f"{Fore.MAGENTA}{'='*60}")
    
    return basic_stats, deep_stats

# 本地任务处理函数
async def process_task_locally_async(input_data: Dict):
    """异步处理单个本地任务，并输出结果"""
    task_id = input_data.get("taskInfo", {}).get("taskId", "local-debug")
    logger.info(f"--- 开始处理本地任务: {task_id} ---")

    try:
        is_strategy_task = "socialMediaReportData" in input_data
        is_review_task = "scheduleInfo" in input_data
        is_cover_task = "style" in input_data and "content" in input_data
        
        if is_cover_task:
            logger.info("调用封面生成任务...")
            cover_service = CoverService()
            status, results = await cover_service.process_task_logic(input_data)
            if status:
                # 转换为与原来兼容的格式
                results = {
                    "coverResult": results.get("coverResult"),
                    "style": results.get("style"),
                    "content": results.get("content")
                }
            else:
                results = {"error": results.get("error", "封面生成失败")}
        elif is_strategy_task:
            logger.info("调用策略生成任务...")
            strategy_service = StrategyService()
            status, results = await strategy_service.process_task_logic(input_data)
        elif is_review_task:
            logger.info("调用小红书运营复盘任务...")
            review_service = ReviewService()
            status, results = await review_service.process_task_logic(input_data)
        else:
            logger.info("调用账号诊断任务...")
            status, results = await process_diagnosis_task(input_data, enable_deep_research=True)

        if is_cover_task:
            # 处理封面生成结果 - 与CoverService返回结构一致
            if not status or "error" in results:
                logger.error(f"❌ 封面生成失败: {results.get('error')}")
                return False, {"error": results.get('error')}
            
            logger.info("✅ 封面生成成功")
            
            # 输出封面生成信息
            style_used = results.get("style", "未知样式")
            content_used = results.get("content", "")
            logger.info(f"使用样式: {style_used}")
            
            # 安全地处理 content_used，可能为 None
            if content_used and len(content_used) > 100:
                logger.info(f"封面内容: {content_used[:100]}...")
            elif content_used:
                logger.info(f"封面内容: {content_used}")
            else:
                logger.info("封面内容: (空)")
            
            # 保存HTML文件
            cover_html = results.get("coverResult")
            if cover_html:
                filename = f"cover_result_{task_id}.html"
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(cover_html)
                    logger.info(f"✅ 封面HTML已保存到: {os.path.abspath(filename)}")
                    
                    # 显示HTML文件大小
                    file_size = len(cover_html.encode('utf-8'))
                    logger.info(f"📄 HTML文件大小: {file_size} 字节")
                    
                except Exception as e:
                    logger.error(f"❌ 保存封面HTML失败: {e}")
                    print("\n--- 封面HTML 内容 ---\n")
                    print(cover_html[:2000] + "..." if len(cover_html) > 2000 else cover_html)
            else:
                logger.warning("⚠️  封面生成结果为空")
            
            # 构建返回数据结构（模拟真实服务的逻辑）
            return_data = {
                "taskInfo": input_data.get("taskInfo", {}),
                "coverResult": cover_html,
                "style": style_used,
                "content": content_used
            }
            
            # JSON序列化测试
            print("\n" + "="*30 + " 封面生成结果JSON序列化测试 " + "="*30)
            try:
                redis_json = safe_redis_serialize_with_validation(return_data)
                logger.info(f"✅ 封面结果序列化成功，长度: {len(redis_json)} 字符")
                
                # 保存序列化结果
                json_filename = f"cover_serialized_{task_id}.json"
                with open(json_filename, 'w', encoding='utf-8') as f:
                    f.write(redis_json)
                logger.info(f"✅ 序列化结果已保存到: {os.path.abspath(json_filename)}")
                
            except Exception as e:
                logger.error(f"❌ 封面结果序列化失败: {e}")
            
            print("="*80 + "\n")
            return True, return_data

        if not status:
            logger.error(f"❌ 任务处理失败: {results.get('error')}")
            return False, results

        # 构建最终返回数据结构（模拟真实服务的逻辑）
        return_data = {"taskInfo": input_data.get("taskInfo", {})}
        
        if is_strategy_task:
            logger.info("✅ 策略报告生成成功")
            return_data["status"] = "success"
            return_data.update(results)
            
            # 输出JSON报告
            strategy_report = results.get("reportJson")
            if strategy_report:
                print("\n" + "="*20 + " 策略报告 (JSON) " + "="*20)
                # 如果是字符串，需要解析后再格式化输出
                if isinstance(strategy_report, str):
                    try:
                        strategy_json = json.loads(strategy_report)
                        print(json.dumps(strategy_json, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError:
                        print(strategy_report)
                else:
                    print(json.dumps(strategy_report, ensure_ascii=False, indent=2))
                print("="*50 + "\n")
            
            # 保存HTML报告
            strategy_html = results.get("reportHtml")
            if strategy_html:
                filename = f"strategy_report_{task_id}.html"
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(strategy_html)
                    logger.info(f"✅ 策略HTML报告已保存到: {os.path.abspath(filename)}")
                except Exception as e:
                    logger.error(f"❌ 保存策略HTML报告失败: {e}")
                    print("\n--- 策略HTML 内容 ---\n")
                    print(strategy_html[:1000] + "..." if len(strategy_html) > 1000 else strategy_html)
            elif "error" in results:
                 logger.warning(f"⚠️  生成策略HTML报告失败: {results.get('error')}")

        elif is_review_task:
            logger.info("✅ 运营复盘报告生成成功")
            return_data["status"] = "success"
            return_data.update(results)
            
            # 输出JSON报告
            review_report = results.get("reportJson")
            if review_report:
                print("\n" + "="*20 + " 运营复盘报告 (JSON) " + "="*20)
                # 如果是字符串，需要解析后再格式化输出
                if isinstance(review_report, str):
                    try:
                        review_json = json.loads(review_report)
                        print(json.dumps(review_json, ensure_ascii=False, indent=2))
                    except json.JSONDecodeError:
                        print(review_report)
                else:
                    print(json.dumps(review_report, ensure_ascii=False, indent=2))
                print("="*55 + "\n")
            
            # 保存HTML报告
            review_html = results.get("reportHtml")
            if review_html:
                filename = f"review_report_{task_id}.html"
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(review_html)
                    logger.info(f"✅ 运营复盘HTML报告已保存到: {os.path.abspath(filename)}")
                except Exception as e:
                    logger.error(f"❌ 保存运营复盘HTML报告失败: {e}")
                    print("\n--- 运营复盘HTML 内容 ---\n")
                    print(review_html[:1000] + "..." if len(review_html) > 1000 else review_html)
            elif "error" in results:
                 logger.warning(f"⚠️  生成运营复盘HTML报告失败: {results.get('error')}")

        else: # 诊断任务
            logger.info("✅ 诊断报告主体生成成功.")
            return_data["status"] = "success"
            return_data.update(results)
            
            print("\n" + "="*20 + " 诊断报告 " + "="*20)
            print(results.get("diagnosisResult"))
            print("="*50 + "\n")
            
            # 处理 HTML
            diagnosis_html = results.get("diagnosisHtml")
            if diagnosis_html:
                filename = f"diagnosis_report_{task_id}.html"
                try:
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(diagnosis_html)
                    logger.info(f"✅ HTML报告已保存到: {os.path.abspath(filename)}")
                except Exception as e:
                    logger.error(f"❌ 保存HTML报告失败: {e}")
                    print("\n--- HTML 内容 ---\n")
                    print(diagnosis_html[:1000] + "..." if len(diagnosis_html) > 1000 else diagnosis_html)
            else:
                logger.error(f"❌ 生成HTML报告失败")

            # 处理 JSON
            diagnosis_report = results.get("diagnosisReport")
            if diagnosis_report:
                logger.info("✅ JSON报告生成成功:")
                print("\n" + "="*20 + " JSON 报告 " + "="*20)
                print(json.dumps(diagnosis_report, ensure_ascii=False, indent=2))
                print("="*50 + "\n")
            else:
                logger.error(f"❌ 生成JSON报告失败")

            # 处理销售提案 (Markdown)
            sales_proposal = results.get("marketingProposal")
            if sales_proposal:
                logger.info("✅ 销售提案(Markdown)生成成功:")
                
                # 显示原始 Markdown 内容（转义前）
                print("\n" + "="*20 + " 销售提案 (原始 Markdown) " + "="*20)
                print(sales_proposal)
                print("="*60 + "\n")
                
                # 显示 JSON 序列化后的内容（转义后）
                serialized_proposal = json.dumps(sales_proposal, ensure_ascii=False)
                print("="*20 + " 销售提案 (JSON 序列化后) " + "="*20)
                print(f"JSON 序列化结果: {serialized_proposal}")
                print("="*60 + "\n")
                
                # 对比信息
                original_lines = len(sales_proposal.split('\n'))
                original_chars = len(sales_proposal)
                serialized_chars = len(serialized_proposal)
                escaped_newlines = serialized_proposal.count('\\n')
                
                print("="*20 + " 转义前后对比信息 " + "="*20)
                print(f"📝 原始内容行数: {original_lines}")
                print(f"📏 原始内容字符数: {original_chars}")
                print(f"📦 序列化后字符数: {serialized_chars}")
                print(f"🔄 转义的换行符数量: {escaped_newlines}")
                print(f"📈 序列化膨胀率: {((serialized_chars - original_chars) / original_chars * 100):.1f}%")
                
                # 显示转义字符示例
                if '\\n' in serialized_proposal:
                    print(f"🔍 转义示例: 原始换行符 → JSON中的'\\\\n'")
                if '\\"' in serialized_proposal:
                    print(f"🔍 转义示例: 原始双引号'\"' → JSON中的'\\\\\"'")
                
                print("="*60 + "\n")
                
                # 前端解决方案提示
                print("💡 前端反转义解决方案:")
                print("JavaScript: unescapedMarkdown = jsonData.marketingProposal.replace(/\\\\n/g, '\\n').replace(/\\\\\"/g, '\"')")
                print("="*80 + "\n")
                
            else:
                logger.error(f"❌ 生成销售提案(Markdown)失败")

            # 处理销售提案 HTML
            sales_proposal_html = results.get("marketingProposalHtml")
            if sales_proposal_html:
                logger.info("✅ 销售提案(HTML)生成成功:")
                
                # 保存HTML文件
                sales_html_filename = f"sales_proposal_{task_id}.html"
                try:
                    with open(sales_html_filename, 'w', encoding='utf-8') as f:
                        f.write(sales_proposal_html)
                    logger.info(f"✅ 销售提案HTML已保存到: {os.path.abspath(sales_html_filename)}")
                    
                    # 显示HTML文件大小和基本信息
                    file_size = len(sales_proposal_html.encode('utf-8'))
                    logger.info(f"📄 销售提案HTML文件大小: {file_size} 字节")
                    
                    # 统计HTML标签数量
                    html_lower = sales_proposal_html.lower()
                    tag_counts = {
                        'div': html_lower.count('<div'),
                        'section': html_lower.count('<section'),
                        'h1-h6': sum(html_lower.count(f'<h{i}') for i in range(1, 7)),
                        'p': html_lower.count('<p'),
                        'img': html_lower.count('<img'),
                        'link': html_lower.count('<link'),
                        'script': html_lower.count('<script')
                    }
                    
                    print("\n" + "="*20 + " 销售提案HTML统计信息 " + "="*20)
                    print(f"📄 文件大小: {file_size:,} 字节")
                    print(f"📝 总字符数: {len(sales_proposal_html):,}")
                    print(f"📊 HTML标签统计:")
                    for tag, count in tag_counts.items():
                        if count > 0:
                            print(f"   {tag}: {count}")
                    print("="*60 + "\n")
                    
                except Exception as e:
                    logger.error(f"❌ 保存销售提案HTML失败: {e}")
                    print("\n--- 销售提案HTML 内容 (前1000字符) ---\n")
                    print(sales_proposal_html[:1000] + "..." if len(sales_proposal_html) > 1000 else sales_proposal_html)
            else:
                logger.error(f"❌ 生成销售提案(HTML)失败")

        # === 新增：JSON 序列化测试和问题检测 ===
        print("\n" + "="*30 + " JSON 序列化测试 " + "="*30)
        
        try:
            # 1. 检测返回数据中的潜在问题
            logger.info("🔍 检测返回数据中的潜在问题...")
            if isinstance(return_data, dict):
                for key, value in return_data.items():
                    if isinstance(value, str) and (value.startswith('{') or value.startswith('[')):
                        issues = detect_json_issues(value)
                        if issues:
                            logger.warning(f"⚠️  检测到{key}字段的JSON问题: {issues}")
                        else:
                            logger.info(f"✅ {key}字段JSON格式正常")
            
            # 2. 使用 safe_redis_serialize_with_validation 进行序列化
            logger.info("🔄 使用 safe_redis_serialize_with_validation 进行序列化...")
            redis_json = safe_redis_serialize_with_validation(return_data)
            logger.info(f"✅ 序列化成功，长度: {len(redis_json)} 字符")
            
            # 3. 输出序列化后的JSON字符串（截断显示）
            print("\n--- 序列化后的 Redis JSON 字符串 (前1000字符) ---")
            print(redis_json[:1000])
            if len(redis_json) > 1000:
                print("... (内容被截断)")
            print("\n--- 序列化后的 Redis JSON 字符串 (后500字符) ---")
            print(redis_json[-500:] if len(redis_json) > 500 else redis_json)
            
            # 4. 验证双重反序列化
            logger.info("🔍 验证双重反序列化...")
            try:
                step1 = json.loads(redis_json)
                logger.info(f"✅ 第一次反序列化成功，类型: {type(step1)}")
                
                if isinstance(step1, str):
                    step2 = json.loads(step1)
                    logger.info(f"✅ 第二次反序列化成功，类型: {type(step2)}")
                    logger.info("✅ 双重序列化格式验证通过")
                else:
                    logger.warning("⚠️  第一次反序列化结果不是字符串，可能不是双重序列化格式")
                    
            except json.JSONDecodeError as e:
                logger.error(f"❌ 反序列化失败: {e}")
                logger.error(f"❌ 失败位置附近的内容: {redis_json[max(0, e.pos-50):e.pos+50]}")
            
            # 5. 保存序列化结果到文件
            json_filename = f"serialized_result_{task_id}.json"
            try:
                with open(json_filename, 'w', encoding='utf-8') as f:
                    f.write(redis_json)
                logger.info(f"✅ 序列化结果已保存到: {os.path.abspath(json_filename)}")
            except Exception as e:
                logger.error(f"❌ 保存序列化结果失败: {e}")
                
        except Exception as e:
            logger.error(f"❌ JSON序列化测试失败: {e}", exc_info=True)
            
            # 创建错误响应进行测试
            logger.info("🔄 测试错误响应的序列化...")
            try:
                error_data = create_error_response(str(e), input_data.get("taskInfo", {}))
                error_redis_json = safe_redis_serialize_with_validation(error_data)
                logger.info(f"✅ 错误响应序列化成功，长度: {len(error_redis_json)} 字符")
                print("\n--- 错误响应序列化结果 ---")
                print(error_redis_json)
            except Exception as error_e:
                logger.error(f"❌ 错误响应序列化也失败: {error_e}")
        
        print("="*80 + "\n")
        
        return True, return_data

    except Exception as e:
        logger.error(f"处理本地任务时发生严重错误: {e}", exc_info=True)
        return False, {"error": str(e)}


# 主函数
async def main():
    """主函数"""
    logger.info("启动本地诊断脚本...")
    
    # 检查是否需要进行 token 对比测试
    if len(sys.argv) > 1 and sys.argv[1] == "--compare-tokens":
        await compare_deep_research_usage()
        return
    
    # 配置封面生成模型
    # 注意：现在使用 service_implementations.py 中的 CoverService，模型配置在服务内部
    logger.info("封面生成将使用 CoverService 中配置的模型")
    
    # 生成任务ID
    task_id = f"local_diagnosis_{int(time.time())}"
    
    # 读取输入数据
    input_data_path = "test/sample_data/diagnosis_sample.json"
    
    if not os.path.exists(input_data_path):
        logger.error(f"输入数据文件不存在: {input_data_path}")
        return
    
    with open(input_data_path, 'r', encoding='utf-8') as f:
        diagnosis_input_data = json.load(f)
    
    # 添加任务信息
    diagnosis_input_data["taskInfo"] = {"taskId": task_id}
    
    # 处理任务
    status, results = await process_task_locally_async(diagnosis_input_data)
    
    if status:
        logger.info("✅ 诊断报告主体生成成功.")
        
        # 输出诊断报告 - 添加安全的键检查
        diagnosis_result = results.get("diagnosisResult")
        if diagnosis_result:
            print("\n" + "="*50)
            print("诊断报告")
            print("="*50)
            print(diagnosis_result)
            print("="*50)
        else:
            logger.warning("⚠️ diagnosisResult 字段缺失或为空")
            print("\n" + "="*50)
            print("诊断报告 - 未生成或为空")
            print("="*50)
            print("诊断结果不可用")
            print("="*50)
        
        # 保存HTML报告
        diagnosis_html = results.get("diagnosisHtml")
        if diagnosis_html:
            html_file_path = f"diagnosis_report_{task_id}.html"
            with open(html_file_path, 'w', encoding='utf-8') as f:
                f.write(diagnosis_html)
            logger.info(f"✅ HTML报告已保存到: {os.path.abspath(html_file_path)}")
        else:
            logger.warning("⚠️ diagnosisHtml 字段缺失或为空")
        
        # 输出JSON报告
        diagnosis_report = results.get("diagnosisReport")
        if diagnosis_report:
            logger.info("✅ JSON报告生成成功:")
            print("\n" + "="*50)
            print("JSON 报告")
            print("="*50)
            print(json.dumps(diagnosis_report, ensure_ascii=False, indent=2))
            print("="*50)
        else:
            logger.warning("⚠️ diagnosisReport 字段缺失或为空")
        
        # 输出销售提案 (Markdown)
        marketing_proposal = results.get("marketingProposal")
        if marketing_proposal:
            logger.info("✅ 销售提案(Markdown)生成成功:")
            print("\n" + "="*50)
            print("销售提案 (原始 Markdown)")
            print("="*50)
            print(marketing_proposal)
            print("="*50)
        else:
            logger.warning("⚠️ marketingProposal 字段缺失或为空")
        
        # 输出和保存销售提案HTML
        marketing_proposal_html = results.get("marketingProposalHtml")
        if marketing_proposal_html:
            logger.info("✅ 销售提案(HTML)生成成功:")
            
            # 保存销售提案HTML文件
            sales_html_file_path = f"sales_proposal_{task_id}.html"
            try:
                with open(sales_html_file_path, 'w', encoding='utf-8') as f:
                    f.write(marketing_proposal_html)
                logger.info(f"✅ 销售提案HTML已保存到: {os.path.abspath(sales_html_file_path)}")
                
                # 显示HTML文件统计信息
                file_size = len(marketing_proposal_html.encode('utf-8'))
                logger.info(f"📄 销售提案HTML文件大小: {file_size:,} 字节")
                
                # 简单的HTML标签统计
                html_lower = marketing_proposal_html.lower()
                has_html_structure = any(tag in html_lower for tag in ['<html', '<body', '<head'])
                has_responsive = 'viewport' in html_lower or 'media' in html_lower
                has_css = '<style' in html_lower or 'css' in html_lower
                has_js = '<script' in html_lower
                
                print(f"\n📊 销售提案HTML特征:")
                print(f"   完整HTML结构: {'✅' if has_html_structure else '❌'}")
                print(f"   响应式设计: {'✅' if has_responsive else '❌'}")
                print(f"   包含样式: {'✅' if has_css else '❌'}")
                print(f"   包含脚本: {'✅' if has_js else '❌'}")
                print(f"   文件大小: {file_size:,} 字节\n")
                
            except Exception as e:
                logger.error(f"❌ 保存销售提案HTML失败: {e}")
                print("\n--- 销售提案HTML 内容 (前1000字符) ---\n")
                print(marketing_proposal_html[:1000] + "..." if len(marketing_proposal_html) > 1000 else marketing_proposal_html)
        else:
            logger.warning("⚠️ marketingProposalHtml 字段缺失或为空")
        
        # 检查数据结构
        if diagnosis_result:
            logger.info("✅ diagnosisResult 存在且不为空")
        else:
            logger.warning("⚠️ diagnosisResult 缺失或为空")
            
        # 输出可用的字段信息
        available_fields = list(results.keys())
        logger.info(f"📋 返回结果中可用的字段: {available_fields}")
        
        # 如果主要字段都缺失，显示完整的 results 内容以便调试
        if not diagnosis_result and not diagnosis_html and not diagnosis_report:
            logger.warning("⚠️ 主要诊断字段都缺失，显示完整结果以便调试:")
            print("\n" + "="*50)
            print("完整返回结果 (调试信息)")
            print("="*50)
            print(json.dumps(results, ensure_ascii=False, indent=2))
            print("="*50)
        
        logger.info("🔄 使用 safe_redis_serialize_with_validation 进行序列化...")
        try:
            serialized_data = safe_redis_serialize_with_validation(results)
            logger.info(f"✅ 序列化成功，长度: {len(serialized_data)} 字符")
            
            # 验证双重反序列化
            logger.info("🔍 验证双重反序列化...")
            try:
                first_deserialize = json.loads(serialized_data)
                logger.info(f"✅ 第一次反序列化成功，类型: {type(first_deserialize)}")
                
                second_deserialize = json.loads(first_deserialize)
                logger.info(f"✅ 第二次反序列化成功，类型: {type(second_deserialize)}")
                logger.info("✅ 双重序列化格式验证通过")
            except Exception as e:
                logger.error(f"❌ 双重反序列化验证失败: {e}")
            
            # 保存序列化结果
            serialized_file_path = f"serialized_result_{task_id}.json"
            with open(serialized_file_path, 'w', encoding='utf-8') as f:
                f.write(serialized_data)
            logger.info(f"✅ 序列化结果已保存到: {os.path.abspath(serialized_file_path)}")
        except Exception as e:
            logger.error(f"❌ 序列化失败: {e}")
        
        print("="*80)
        
    else:
        logger.error(f"❌ 诊断任务失败: {results}")
    
    logger.info(f"\n--- 本地任务处理完成: {task_id} ---")
    
    # 打印最终的 token 统计
    print_final_token_stats()


if __name__ == "__main__":
    logger.info("本地诊断脚本启动...")
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--help":
            print("使用方法:")
            print("  python local_debug_script.py                    # 运行标准诊断")
            print("  python local_debug_script.py --compare-tokens   # 对比深度搜索 vs 基础模式的 token 使用量")
            sys.exit(0)
    
    asyncio.run(main())
    logger.info("本地诊断脚本运行结束.") 