"""
Resource Management Dashboard - 资源管理仪表板
提供可视化的资源生命周期管理和超时配置界面
"""

import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from dataclasses import asdict
from colorama import Fore, Style, init
from resource_lifecycle_manager import (
    ResourceLifecycleManager, ResourceType, ResourceStatus, TimeoutConfig,
    get_resource_manager, shutdown_resource_manager
)

# 初始化colorama
init(autoreset=True)

class ResourceManagementDashboard:
    """资源管理仪表板"""
    
    def __init__(self):
        self.resource_manager = get_resource_manager()
        self.running = False
    
    def print_header(self, title: str):
        """打印标题"""
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}{title:^80}")
        print(f"{Fore.CYAN}{'='*80}{Style.RESET_ALL}")
    
    def print_section(self, title: str):
        """打印章节标题"""
        print(f"\n{Fore.YELLOW}{'─'*60}")
        print(f"{Fore.YELLOW}{title}")
        print(f"{Fore.YELLOW}{'─'*60}{Style.RESET_ALL}")
    
    async def show_resource_overview(self):
        """显示资源概览"""
        self.print_section("📊 资源概览 (Resource Overview)")
        
        metrics = await self.resource_manager.get_resource_metrics()
        
        print(f"{Fore.GREEN}总资源数量: {metrics['total_resources']}{Style.RESET_ALL}")
        
        # 按类型显示
        print(f"\n{Fore.CYAN}📦 按类型分布:{Style.RESET_ALL}")
        for resource_type, count in metrics['resources_by_type'].items():
            print(f"  • {resource_type}: {count}")
        
        # 按状态显示
        print(f"\n{Fore.CYAN}🔄 按状态分布:{Style.RESET_ALL}")
        for status, count in metrics['resources_by_status'].items():
            status_color = self._get_status_color(status)
            print(f"  • {status_color}{status}: {count}{Style.RESET_ALL}")
    
    def _get_status_color(self, status: str) -> str:
        """获取状态对应的颜色"""
        color_map = {
            'active': Fore.GREEN,
            'idle': Fore.YELLOW,
            'error': Fore.RED,
            'closing': Fore.MAGENTA,
            'closed': Fore.BLUE,
            'initializing': Fore.CYAN
        }
        return color_map.get(status, Fore.WHITE)
    
    async def show_resource_details(self):
        """显示资源详情"""
        self.print_section("🔍 资源详情 (Resource Details)")
        
        metrics = await self.resource_manager.get_resource_metrics()
        
        if not metrics['resource_details']:
            print(f"{Fore.YELLOW}暂无活跃资源{Style.RESET_ALL}")
            return
        
        for resource_id, details in metrics['resource_details'].items():
            status_color = self._get_status_color(details['status'])
            expired_indicator = "⚠️ " if details['is_expired'] else ""
            
            print(f"\n{Fore.CYAN}🔹 {resource_id}{Style.RESET_ALL}")
            print(f"  类型: {details['type']}")
            print(f"  状态: {status_color}{details['status']}{Style.RESET_ALL} {expired_indicator}")
            print(f"  创建时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(details['created_at']))}")
            print(f"  最后使用: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(details['last_used_at']))}")
            print(f"  使用次数: {details['usage_count']}")
            print(f"  错误次数: {details['error_count']}")
    
    def show_timeout_configurations(self):
        """显示超时配置"""
        self.print_section("⏰ 超时配置 (Timeout Configurations)")
        
        for resource_type in ResourceType:
            config = self.resource_manager.get_timeout_config(resource_type)
            print(f"\n{Fore.CYAN}🔧 {resource_type.value.upper()}{Style.RESET_ALL}")
            print(f"  连接超时: {config.connection_timeout}s")
            print(f"  操作超时: {config.operation_timeout}s")
            print(f"  空闲超时: {config.idle_timeout}s")
            print(f"  总超时: {config.total_timeout}s")
            print(f"  重试超时: {config.retry_timeout}s")
            print(f"  清理超时: {config.cleanup_timeout}s")
    
    async def interactive_timeout_config(self):
        """交互式超时配置"""
        self.print_section("⚙️ 交互式超时配置 (Interactive Timeout Configuration)")
        
        print("选择要配置的资源类型:")
        resource_types = list(ResourceType)
        for i, rt in enumerate(resource_types, 1):
            print(f"  {i}. {rt.value}")
        
        try:
            choice = int(input(f"\n{Fore.YELLOW}请输入选择 (1-{len(resource_types)}): {Style.RESET_ALL}"))
            if 1 <= choice <= len(resource_types):
                selected_type = resource_types[choice - 1]
                await self._configure_resource_timeout(selected_type)
            else:
                print(f"{Fore.RED}无效选择{Style.RESET_ALL}")
        except ValueError:
            print(f"{Fore.RED}请输入有效数字{Style.RESET_ALL}")
    
    async def _configure_resource_timeout(self, resource_type: ResourceType):
        """配置特定资源类型的超时"""
        current_config = self.resource_manager.get_timeout_config(resource_type)
        
        print(f"\n{Fore.CYAN}配置 {resource_type.value} 的超时设置:{Style.RESET_ALL}")
        print(f"当前配置: {asdict(current_config)}")
        
        new_config = TimeoutConfig()
        
        # 逐个配置超时参数
        timeout_params = [
            ('connection_timeout', '连接超时'),
            ('operation_timeout', '操作超时'),
            ('idle_timeout', '空闲超时'),
            ('total_timeout', '总超时'),
            ('retry_timeout', '重试超时'),
            ('cleanup_timeout', '清理超时')
        ]
        
        for param_name, param_desc in timeout_params:
            current_value = getattr(current_config, param_name)
            try:
                new_value = input(f"{param_desc} (当前: {current_value}s, 回车保持不变): ")
                if new_value.strip():
                    setattr(new_config, param_name, float(new_value))
                else:
                    setattr(new_config, param_name, current_value)
            except ValueError:
                print(f"{Fore.YELLOW}使用默认值: {current_value}s{Style.RESET_ALL}")
                setattr(new_config, param_name, current_value)
        
        # 应用新配置
        self.resource_manager.configure_timeout(resource_type, new_config)
        print(f"{Fore.GREEN}✅ 配置已更新{Style.RESET_ALL}")
    
    async def cleanup_expired_resources(self):
        """清理过期资源"""
        self.print_section("🧹 清理过期资源 (Cleanup Expired Resources)")
        
        print(f"{Fore.YELLOW}正在清理过期资源...{Style.RESET_ALL}")
        await self.resource_manager.cleanup_expired_resources()
        print(f"{Fore.GREEN}✅ 清理完成{Style.RESET_ALL}")
    
    async def force_cleanup_all(self):
        """强制清理所有资源"""
        self.print_section("🗑️ 强制清理所有资源 (Force Cleanup All)")
        
        confirm = input(f"{Fore.RED}⚠️  这将清理所有资源，确认吗? (y/N): {Style.RESET_ALL}")
        if confirm.lower() == 'y':
            await self.resource_manager.force_cleanup_all()
            print(f"{Fore.GREEN}✅ 所有资源已清理{Style.RESET_ALL}")
        else:
            print(f"{Fore.YELLOW}操作已取消{Style.RESET_ALL}")
    
    async def export_configuration(self):
        """导出配置"""
        self.print_section("📤 导出配置 (Export Configuration)")
        
        config_data = {}
        for resource_type in ResourceType:
            config = self.resource_manager.get_timeout_config(resource_type)
            config_data[resource_type.value] = asdict(config)
        
        filename = f"resource_config_{int(time.time())}.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        print(f"{Fore.GREEN}✅ 配置已导出到: {filename}{Style.RESET_ALL}")
    
    async def import_configuration(self):
        """导入配置"""
        self.print_section("📥 导入配置 (Import Configuration)")
        
        filename = input(f"{Fore.YELLOW}请输入配置文件名: {Style.RESET_ALL}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            for resource_type_str, config_dict in config_data.items():
                try:
                    resource_type = ResourceType(resource_type_str)
                    timeout_config = TimeoutConfig(**config_dict)
                    self.resource_manager.configure_timeout(resource_type, timeout_config)
                    print(f"{Fore.GREEN}✅ 已导入 {resource_type_str} 配置{Style.RESET_ALL}")
                except (ValueError, TypeError) as e:
                    print(f"{Fore.RED}❌ 导入 {resource_type_str} 配置失败: {e}{Style.RESET_ALL}")
            
            print(f"{Fore.GREEN}✅ 配置导入完成{Style.RESET_ALL}")
            
        except FileNotFoundError:
            print(f"{Fore.RED}❌ 文件不存在: {filename}{Style.RESET_ALL}")
        except json.JSONDecodeError:
            print(f"{Fore.RED}❌ 无效的JSON文件{Style.RESET_ALL}")
    
    def show_menu(self):
        """显示菜单"""
        print(f"\n{Fore.CYAN}📋 操作菜单:{Style.RESET_ALL}")
        print("  1. 📊 显示资源概览")
        print("  2. 🔍 显示资源详情")
        print("  3. ⏰ 显示超时配置")
        print("  4. ⚙️ 交互式超时配置")
        print("  5. 🧹 清理过期资源")
        print("  6. 🗑️ 强制清理所有资源")
        print("  7. 📤 导出配置")
        print("  8. 📥 导入配置")
        print("  9. 🔄 刷新显示")
        print("  0. 🚪 退出")
    
    async def run_interactive(self):
        """运行交互式界面"""
        self.running = True
        
        while self.running:
            self.print_header("🎛️ 资源管理仪表板 (Resource Management Dashboard)")
            
            # 显示概览
            await self.show_resource_overview()
            
            # 显示菜单
            self.show_menu()
            
            try:
                choice = input(f"\n{Fore.YELLOW}请选择操作 (0-9): {Style.RESET_ALL}")
                
                if choice == '1':
                    await self.show_resource_overview()
                elif choice == '2':
                    await self.show_resource_details()
                elif choice == '3':
                    self.show_timeout_configurations()
                elif choice == '4':
                    await self.interactive_timeout_config()
                elif choice == '5':
                    await self.cleanup_expired_resources()
                elif choice == '6':
                    await self.force_cleanup_all()
                elif choice == '7':
                    await self.export_configuration()
                elif choice == '8':
                    await self.import_configuration()
                elif choice == '9':
                    continue  # 刷新
                elif choice == '0':
                    self.running = False
                    break
                else:
                    print(f"{Fore.RED}无效选择，请重试{Style.RESET_ALL}")
                
                if choice != '9':
                    input(f"\n{Fore.CYAN}按回车键继续...{Style.RESET_ALL}")
                    
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                print(f"{Fore.RED}❌ 操作失败: {e}{Style.RESET_ALL}")
                input(f"\n{Fore.CYAN}按回车键继续...{Style.RESET_ALL}")
        
        print(f"\n{Fore.GREEN}👋 感谢使用资源管理仪表板{Style.RESET_ALL}")

async def main():
    """主函数"""
    dashboard = ResourceManagementDashboard()
    
    try:
        await dashboard.run_interactive()
    finally:
        # 清理资源管理器
        await shutdown_resource_manager()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print(f"\n{Fore.YELLOW}👋 程序已退出{Style.RESET_ALL}")