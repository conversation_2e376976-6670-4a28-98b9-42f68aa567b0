# 增强版TaskProgressTracker使用说明

## 概述

重构后的 `EnhancedTaskProgressTracker` 实现了与现有的 `AsyncRedisManager` 的完全解耦，通过依赖注入的方式使用 Redis 集群连接，避免了本地 Redis 连接的复杂性。

## 主要改进

### 1. 架构解耦
- **移除了本地Redis连接**：不再直接创建Redis连接
- **依赖注入**：通过构造函数注入 `AsyncRedisManager` 实例
- **优雅降级**：当没有提供Redis管理器时，状态更新仅记录到日志

### 2. 与现有系统集成
- **使用AsyncRedisManager**：复用现有的Redis集群连接管理
- **保持接口兼容性**：原有的API接口保持不变
- **统一的队列管理**：使用统一的Redis队列格式

## 使用方法

### 1. 在BaseAsyncService中使用

```python
from .base_async_service import BaseAsyncService, AsyncRedisManager
from .deep_research_integration import diagnosis_with_deep_research

class DiagnosisService(BaseAsyncService):
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        # 提取任务信息
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId")
        user_id = task_info.get("userId")
        diagnosis_id = task_info.get("diagnosisId")
        
        # 使用深度研究功能，传递Redis管理器
        success, result = await diagnosis_with_deep_research(
            input_data,
            use_deep_research=True,
            task_id=task_id,
            user_id=user_id,
            diagnosis_id=diagnosis_id,
            env="production",
            redis_manager=self.redis_manager  # 传递现有的Redis管理器
        )
        
        return success, {"diagnosis_result": result}
```

### 2. 独立使用

```python
from .base_async_service import AsyncRedisManager
from .deep_research_integration import create_progress_tracker

async def standalone_usage():
    # 创建Redis管理器
    redis_manager = AsyncRedisManager()
    await redis_manager.create_pool()
    
    # 创建进度跟踪器
    tracker = create_progress_tracker(
        task_id="unique_task_id",
        user_id="user_123",
        diagnosis_id=456,
        env="production",
        redis_manager=redis_manager
    )
    
    # 使用进度跟踪器
    await tracker.update_status(TaskStatus.PLANNING, "开始规划任务...")
    await tracker.update_step_progress("数据搜索", 30, "正在搜索行业热点...")
    await tracker.complete_step("数据搜索", {"results_count": 15})
    
    # 关闭连接
    await tracker.close()
    await redis_manager.close()
```

### 3. 兼容性使用（无Redis）

```python
from .deep_research_integration import create_progress_tracker

async def fallback_usage():
    # 不提供Redis管理器，状态更新仅记录到日志
    tracker = create_progress_tracker(
        task_id="unique_task_id",
        user_id="user_123",
        diagnosis_id=456,
        env="production"
        # redis_manager=None  # 默认为None
    )
    
    # 状态更新会记录到日志，但不会推送到Redis
    await tracker.update_status(TaskStatus.PLANNING, "开始规划任务...")
    await tracker.close()
```

## 状态推送格式

推送到Redis队列的消息格式：

```json
{
  "taskInfo": {
    "env": "production",
    "taskId": "unique_task_id",
    "userId": "user_123",
    "diagnosisId": 456,
    "aiTaskStatus": "EXECUTING_SEARCHES",
    "aiTaskMsg": "Executing multi-dimensional searches…",
    "aiTaskMsgCN": "正在执行多维度搜索…",
    "aiTaskProgress": 35,
    "timestamp": "2024-01-20T10:30:00.123Z",
    "elapsed_time": 45.67,
    "step_details": {
      "step_name": "数据搜索",
      "step_progress": 70,
      "step_message": "正在搜索竞争对手...",
      "step_elapsed": 12.34
    }
  }
}
```

## 支持的状态

### 基础状态
- `TASK_QUEUED`: 任务已创建
- `PLANNING`: 正在规划任务
- `CRAWLING_ACCOUNT`: 正在分析账号信息
- `SEARCHING`: 正在搜索数据
- `ANALYZING_INDUSTRY`: 正在分析行业
- `PERFORMING_DIAGNOSIS`: 正在进行诊断
- `GENERATING_REPORT`: 正在生成报告
- `PAGE_RENDERING`: 正在渲染页面
- `TASK_COMPLETED`: 任务完成
- `TASK_FAILED`: 任务失败

### 深度研究特有状态
- `INITIALIZING_RESEARCH`: 正在初始化深度研究
- `GENERATING_QUERIES`: 正在生成智能搜索查询
- `OPTIMIZING_QUERIES`: 正在优化查询策略
- `EXECUTING_SEARCHES`: 正在执行多维度搜索
- `PROCESSING_RESULTS`: 正在处理搜索结果
- `INDUSTRY_ANALYSIS`: 正在进行行业智能分析
- `COMPREHENSIVE_ANALYSIS`: 正在进行综合深度分析
- `FINALIZING_REPORT`: 正在最终化报告

## 性能监控

```python
# 获取性能统计
stats = tracker.get_performance_stats()
print(f"性能统计: {stats}")

# 输出示例：
# {
#   "total_elapsed_time": 120.45,
#   "total_steps": 5,
#   "completed_steps": 5,
#   "average_step_time": 24.09,
#   "redis_available": True,
#   "status_updates_sent": 12
# }
```

## 错误处理

系统具有完善的错误处理机制：

1. **Redis连接失败**：自动降级到日志记录模式
2. **序列化错误**：记录错误并继续执行
3. **网络超时**：使用AsyncRedisManager的重试机制
4. **状态推送失败**：记录警告但不影响主要业务逻辑

## 最佳实践

1. **在服务中使用**：推荐在BaseAsyncService子类中使用，自动获得Redis管理器
2. **任务ID管理**：确保任务ID的唯一性
3. **状态粒度**：合理控制状态更新频率，避免过度推送
4. **错误处理**：始终在finally块中调用close()方法
5. **性能监控**：定期检查性能统计，优化执行效率

## 前端集成

前端可以通过Redis队列 `{env}:q:diagnosis:response` 监听状态更新：

```javascript
// 示例：监听状态更新
const redisClient = redis.createClient();
redisClient.subscribe('production:q:diagnosis:response');

redisClient.on('message', (channel, message) => {
  const statusUpdate = JSON.parse(message);
  const taskInfo = statusUpdate.taskInfo;
  
  updateProgress(taskInfo.taskId, taskInfo.aiTaskProgress);
  showStatusMessage(taskInfo.aiTaskMsgCN);
  
  if (taskInfo.step_details) {
    updateStepProgress(taskInfo.step_details);
  }
});
```

## 迁移指南

从旧的进度跟踪器迁移到新版本：

1. **更新调用方式**：添加 `redis_manager` 参数
2. **检查依赖**：确保使用的是 `AsyncRedisManager` 实例
3. **测试兼容性**：验证状态推送格式是否符合前端期望
4. **性能测试**：确认新版本不会影响现有性能

这个重构后的系统提供了更好的架构分离和更强的可扩展性，同时保持了与现有系统的完全兼容性。