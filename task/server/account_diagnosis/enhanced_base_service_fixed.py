"""
Enhanced Base Async Service with Robust Error Handling (Fixed Version)
增强的基础异步服务 - 修复版本，避免导入问题
"""

import asyncio
import time
import logging
from typing import Dict, Tuple, Optional, Any, Callable
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 导入错误处理模块
from enhanced_error_handling import (
    EnhancedErrorHandler, 
    CircuitBreakerConfig,
    ErrorCategory,
    ErrorSeverity
)

# Mock BaseAsyncService for testing
class BaseAsyncService:
    """Mock Base Async Service for testing"""
    
    def __init__(self, service_name, request_queue, response_queue, 
                 max_concurrent_tasks=3, run_mode="daemon", timeout=300,
                 use_service_lock=True, redis_connection_timeout=5, 
                 redis_socket_timeout=5):
        self.service_name = service_name
        self.request_queue = request_queue
        self.response_queue = response_queue
        self.max_concurrent_tasks = max_concurrent_tasks
        self.run_mode = run_mode
        self.timeout = timeout
        self.use_service_lock = use_service_lock
        self.redis_connection_timeout = redis_connection_timeout
        self.redis_socket_timeout = redis_socket_timeout
        
        # Mock redis manager
        class MockRedisManager:
            def __init__(self):
                self.pool = None
            async def setex(self, key, ttl, value): return True
            async def get(self, key): return None
            async def lpush(self, key, value): return 1
            async def ltrim(self, key, start, end): return True
            async def expire(self, key, ttl): return True
        
        self.redis_manager = MockRedisManager()
        
        # Setup logger
        self.logger = logging.getLogger(service_name)
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    async def start(self):
        print(f"Mock service {self.service_name} started")
    
    async def check_service_lock_status(self):
        return {"status": "mock", "locked": False}

class EnhancedBaseAsyncService(BaseAsyncService):
    """Enhanced Base Async Service with robust error handling"""
    
    def __init__(self, service_name, request_queue, response_queue,
                 max_concurrent_tasks=3, run_mode="daemon", timeout=300,
                 use_service_lock=True, redis_connection_timeout=5, 
                 redis_socket_timeout=5):
        
        # Initialize parent
        super().__init__(
            service_name, request_queue, response_queue,
            max_concurrent_tasks, run_mode, timeout,
            use_service_lock, redis_connection_timeout, redis_socket_timeout
        )
        
        # Initialize enhanced error handler
        self.error_handler = EnhancedErrorHandler(self.redis_manager)
        
        # Add service-specific circuit breakers
        self._setup_circuit_breakers()
        
        # Enhanced logging setup
        self._setup_enhanced_logging()
        
        # Performance tracking
        self.operation_metrics = {}
        
        self.logger.info(f"{Fore.GREEN}🚀 Enhanced {self.service_name} service initialized with robust error handling{Style.RESET_ALL}")
    
    def _setup_circuit_breakers(self):
        """Setup service-specific circuit breakers"""
        self.logger.info(f"{Fore.CYAN}🔧 Setting up circuit breakers for {self.service_name}{Style.RESET_ALL}")
        
        # Redis operations circuit breaker
        redis_config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=60,
            timeout=30.0
        )
        self.error_handler.add_circuit_breaker("redis_ops", redis_config)
        
        # AI service circuit breaker
        ai_config = CircuitBreakerConfig(
            failure_threshold=3,
            recovery_timeout=30,
            timeout=180.0
        )
        self.error_handler.add_circuit_breaker("ai_service", ai_config)
        
        # Task processing circuit breaker
        task_config = CircuitBreakerConfig(
            failure_threshold=4,
            recovery_timeout=45,
            timeout=300.0
        )
        self.error_handler.add_circuit_breaker("task_processing", task_config)
        
        self.logger.info(f"{Fore.GREEN}✅ Circuit breakers configured successfully{Style.RESET_ALL}")
    
    def _setup_enhanced_logging(self):
        """Setup enhanced colorful logging"""
        # Create a custom formatter with colors
        class ColoredFormatter(logging.Formatter):
            def format(self, record):
                # Color mapping for log levels
                level_colors = {
                    'DEBUG': Fore.BLUE,
                    'INFO': Fore.GREEN,
                    'WARNING': Fore.YELLOW,
                    'ERROR': Fore.RED,
                    'CRITICAL': Fore.MAGENTA
                }
                
                level_color = level_colors.get(record.levelname, Fore.WHITE)
                
                # Format the message
                formatted = super().format(record)
                
                # Add service name prefix with color
                service_prefix = f"{Fore.CYAN}[{self.service_name.upper()}]{Style.RESET_ALL}"
                
                return f"{service_prefix} {formatted}"
        
        # Apply the colored formatter to all handlers
        formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        for handler in self.logger.handlers:
            handler.setFormatter(formatter)
    
    async def enhanced_redis_operation(self, operation_name: str, func: Callable, *args, **kwargs):
        """Enhanced Redis operation with circuit breaker and metrics"""
        start_time = time.time()
        
        try:
            self.log_operation_start(f"Redis {operation_name}", {"args_count": len(args), "kwargs_count": len(kwargs)})
            
            result = await self.error_handler.handle_with_circuit_breaker(
                func,
                "redis_ops",
                f"redis_{operation_name}",
                {
                    "service": self.service_name, 
                    "operation": operation_name
                },
                *args, **kwargs
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"Redis {operation_name}", duration)
            self._record_operation_metric(f"redis_{operation_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"Redis {operation_name}", str(e))
            self._record_operation_metric(f"redis_{operation_name}", duration, False)
            raise
    
    async def enhanced_ai_call(self, ai_function: Callable, *args, **kwargs):
        """Enhanced AI call with circuit breaker, retry and detailed logging"""
        start_time = time.time()
        ai_name = getattr(ai_function, '__name__', 'unknown_ai')
        
        try:
            self.log_operation_start(f"AI Call {ai_name}", {
                "function": ai_name,
                "args_count": len(args),
                "has_model": 'model' in kwargs
            })
            
            result = await self.error_handler.handle_with_circuit_breaker(
                ai_function,
                "ai_service", 
                f"ai_call_{ai_name}",
                {
                    "service": self.service_name, 
                    "ai_function": ai_name,
                    "model": kwargs.get('model', 'unknown')
                },
                *args, **kwargs
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"AI Call {ai_name}", duration)
            self._record_operation_metric(f"ai_{ai_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"AI Call {ai_name}", str(e))
            self._record_operation_metric(f"ai_{ai_name}", duration, False)
            raise
    
    async def enhanced_task_processing(self, task_func: Callable, input_data: Dict):
        """Enhanced task processing with comprehensive error handling"""
        start_time = time.time()
        task_name = getattr(task_func, '__name__', 'unknown_task')
        
        try:
            task_metadata = {
                "service": self.service_name,
                "task_function": task_name,
                "input_keys": list(input_data.keys()),
                "input_size": len(str(input_data))
            }
            
            self.log_operation_start(f"Task Processing {task_name}", task_metadata)
            
            result = await self.error_handler.handle_with_circuit_breaker(
                task_func,
                "task_processing",
                f"task_{task_name}",
                task_metadata,
                input_data
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"Task Processing {task_name}", duration)
            self._record_operation_metric(f"task_{task_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"Task Processing {task_name}", str(e))
            self._record_operation_metric(f"task_{task_name}", duration, False)
            raise
    
    def _record_operation_metric(self, operation: str, duration: float, success: bool):
        """Record operation metrics for monitoring"""
        if operation not in self.operation_metrics:
            self.operation_metrics[operation] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0,
                "total_duration": 0.0,
                "avg_duration": 0.0
            }
        
        metrics = self.operation_metrics[operation]
        metrics["total_calls"] += 1
        metrics["total_duration"] += duration
        
        if success:
            metrics["successful_calls"] += 1
        else:
            metrics["failed_calls"] += 1
        
        metrics["avg_duration"] = metrics["total_duration"] / metrics["total_calls"]
    
    async def get_enhanced_service_health(self) -> Dict[str, Any]:
        """Get comprehensive service health status"""
        try:
            # Get error handler health
            error_handler_health = self.error_handler.get_system_health()
            
            # Get operation metrics
            operation_summary = {}
            for op, metrics in self.operation_metrics.items():
                success_rate = metrics["successful_calls"] / metrics["total_calls"] if metrics["total_calls"] > 0 else 0
                operation_summary[op] = {
                    "success_rate": success_rate,
                    "avg_duration": metrics["avg_duration"],
                    "total_calls": metrics["total_calls"]
                }
            
            health_status = {
                "service_name": self.service_name,
                "error_handling": error_handler_health,
                "operation_metrics": operation_summary,
                "timestamp": error_handler_health["timestamp"]
            }
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Failed to get service health: {str(e)}{Style.RESET_ALL}")
            return {
                "service_name": self.service_name,
                "error": str(e),
                "timestamp": time.time()
            }
    
    def log_operation_start(self, operation: str, context: Dict = None):
        """Log operation start with color and context"""
        context_str = ""
        if context:
            context_items = []
            for key, value in context.items():
                if isinstance(value, (list, dict)):
                    context_items.append(f"{key}={len(value)}")
                else:
                    context_items.append(f"{key}={value}")
            context_str = f" | {', '.join(context_items)}"
        
        self.logger.info(f"{Fore.GREEN}🚀 Starting {operation}{context_str}{Style.RESET_ALL}")
    
    def log_operation_success(self, operation: str, duration: float = None):
        """Log operation success with color and timing"""
        duration_str = f" | ⏱️  {duration:.2f}s" if duration else ""
        self.logger.info(f"{Fore.GREEN}✅ Completed {operation}{duration_str}{Style.RESET_ALL}")
    
    def log_operation_warning(self, operation: str, message: str):
        """Log operation warning with color"""
        self.logger.warning(f"{Fore.YELLOW}⚠️  {operation}: {message}{Style.RESET_ALL}")
    
    def log_operation_error(self, operation: str, error: str):
        """Log operation error with color"""
        self.logger.error(f"{Fore.RED}❌ {operation} failed: {error}{Style.RESET_ALL}")
    
    async def reset_circuit_breakers(self):
        """Reset all circuit breakers"""
        self.logger.warning(f"{Fore.YELLOW}🔄 Resetting all circuit breakers for {self.service_name}{Style.RESET_ALL}")
        
        for name in self.error_handler.circuit_breakers.keys():
            await self.error_handler.reset_circuit_breaker(name)
        
        self.logger.info(f"{Fore.GREEN}✅ All circuit breakers reset successfully{Style.RESET_ALL}")
    
    async def get_operation_metrics(self) -> Dict[str, Any]:
        """Get detailed operation metrics"""
        return {
            "service_name": self.service_name,
            "metrics": self.operation_metrics.copy(),
            "timestamp": time.time()
        }