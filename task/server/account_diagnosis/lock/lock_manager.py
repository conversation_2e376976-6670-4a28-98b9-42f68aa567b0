#!/usr/bin/env python3
"""
服务锁管理工具
用于检查、清理和管理各种服务的分布式锁
"""

import os
import sys
import asyncio
import argparse
import time

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import (
    DiagnosisService, StrategyService, ReviewService, CoverService
)


class LockManager:
    """服务锁管理器"""
    
    def __init__(self):
        self.services = {
            'diagnosis': DiagnosisService,
            'strategy': StrategyService, 
            'review': ReviewService,
            'cover': CoverService
        }
    
    async def check_lock(self, service_name: str):
        """检查指定服务的锁状态"""
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            print(f"可用服务: {', '.join(self.services.keys())}")
            return
        
        service_class = self.services[service_name]
        service = service_class(run_mode="daemon")  # 创建服务实例
        
        try:
            await service.redis_manager.create_pool()
            print(f"🔍 检查 {service_name} 服务锁状态...")
            await service.check_service_lock_status()
        except Exception as e:
            print(f"❌ 检查锁状态失败: {str(e)}")
        finally:
            await service.redis_manager.close()
    
    async def clear_lock(self, service_name: str, force: bool = False):
        """清理指定服务的锁"""
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            print(f"可用服务: {', '.join(self.services.keys())}")
            return
        
        if not force:
            confirm = input(f"⚠️  确定要清理 {service_name} 服务锁吗？(y/N): ")
            if confirm.lower() != 'y':
                print("❌ 操作已取消")
                return
        
        service_class = self.services[service_name]
        service = service_class(run_mode="daemon")
        
        try:
            await service.redis_manager.create_pool()
            print(f"🧹 清理 {service_name} 服务锁...")
            success = await service.force_clear_service_lock()
            if success:
                print(f"✅ {service_name} 服务锁清理成功")
            else:
                print(f"❌ {service_name} 服务锁清理失败")
        except Exception as e:
            print(f"❌ 清理锁失败: {str(e)}")
        finally:
            await service.redis_manager.close()
    
    async def check_all_locks(self):
        """检查所有服务的锁状态"""
        print("🔍 检查所有服务锁状态...")
        print("=" * 60)
        
        for service_name in self.services.keys():
            print(f"\n📋 {service_name.upper()} 服务:")
            print("-" * 30)
            await self.check_lock(service_name)
    
    async def clear_all_locks(self, force: bool = False):
        """清理所有服务锁"""
        if not force:
            confirm = input("⚠️  确定要清理所有服务锁吗？(y/N): ")
            if confirm.lower() != 'y':
                print("❌ 操作已取消")
                return
        
        print("🧹 清理所有服务锁...")
        print("=" * 60)
        
        for service_name in self.services.keys():
            print(f"\n📋 清理 {service_name.upper()} 服务锁:")
            print("-" * 30)
            await self.clear_lock(service_name, force=True)
    
    def print_help(self):
        """打印帮助信息"""
        print("""
🔧 服务锁管理工具

用法:
  python lock_manager.py <command> [options]

命令:
  check <service>     检查指定服务的锁状态
  check-all          检查所有服务的锁状态
  clear <service>     清理指定服务的锁
  clear-all          清理所有服务的锁
  help               显示此帮助信息

可用服务:
  - diagnosis        诊断服务
  - strategy         策略服务
  - review           复盘服务
  - cover            封面服务

选项:
  --force            强制执行，不询问确认

示例:
  python lock_manager.py check diagnosis
  python lock_manager.py clear strategy
  python lock_manager.py clear-all --force
  python lock_manager.py check-all
        """)


async def main():
    manager = LockManager()
    
    parser = argparse.ArgumentParser(description='服务锁管理工具')
    parser.add_argument('command', nargs='?', help='命令')
    parser.add_argument('service', nargs='?', help='服务名称')
    parser.add_argument('--force', action='store_true', help='强制执行')
    
    args = parser.parse_args()
    
    if not args.command or args.command == 'help':
        manager.print_help()
        return
    
    try:
        if args.command == 'check':
            if not args.service:
                print("❌ 请指定服务名称")
                return
            await manager.check_lock(args.service)
            
        elif args.command == 'check-all':
            await manager.check_all_locks()
            
        elif args.command == 'clear':
            if not args.service:
                print("❌ 请指定服务名称")
                return
            await manager.clear_lock(args.service, args.force)
            
        elif args.command == 'clear-all':
            await manager.clear_all_locks(args.force)
            
        else:
            print(f"❌ 未知命令: {args.command}")
            manager.print_help()
            
    except KeyboardInterrupt:
        print("\n❌ 操作被中断")
    except Exception as e:
        print(f"❌ 执行失败: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main()) 