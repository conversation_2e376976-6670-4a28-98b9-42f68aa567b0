apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: diagnosis-service-cron
  namespace: default
spec:
  schedule: "*/10 * * * *"  # 每10分钟执行一次
  timezone: "Asia/Shanghai"
  concurrencyPolicy: "Forbid"  # 禁止并发执行
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  workflowSpec:
    entrypoint: diagnosis-with-health-check
    templates:
    - name: diagnosis-with-health-check
      steps:
      # 第一步：健康检查
      - - name: health-check
          template: health-check-step
      # 第二步：执行实际任务（只有健康检查通过才执行）
      - - name: run-diagnosis
          template: diagnosis-step
          when: "{{steps.health-check.outputs.result}} == 'healthy'"
    
    - name: health-check-step
      container:
        image: your-python-image:latest
        command: [python]
        args: 
          - /app/task/server/account_diagnosis/lock/cron_health_check.py
          - --service
          - diagnosis
          - --max-age
          - "600"
          - --exit-on-error
        workingDir: /app
        env:
        - name: PYTHONPATH
          value: "/app"
        # 添加必要的环境变量
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
      outputs:
        result: "healthy"  # 如果脚本成功退出，输出healthy
    
    - name: diagnosis-step
      container:
        image: your-python-image:latest
        command: [python]
        args: [/app/task/server/account_diagnosis/script_for_diagnosis.py]
        workingDir: /app
        env:
        - name: PYTHONPATH
          value: "/app"
        # 添加必要的环境变量
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: strategy-service-cron
  namespace: default
spec:
  schedule: "*/10 * * * *"  # 每10分钟执行一次
  timezone: "Asia/Shanghai"
  concurrencyPolicy: "Forbid"
  successfulJobsHistoryLimit: 3
  failedJobsHistoryLimit: 3
  workflowSpec:
    entrypoint: strategy-with-health-check
    templates:
    - name: strategy-with-health-check
      steps:
      - - name: health-check
          template: health-check-step
      - - name: run-strategy
          template: strategy-step
          when: "{{steps.health-check.outputs.result}} == 'healthy'"
    
    - name: health-check-step
      container:
        image: your-python-image:latest
        command: [python]
        args: 
          - /app/task/server/account_diagnosis/lock/cron_health_check.py
          - --service
          - strategy
          - --max-age
          - "600"
          - --exit-on-error
        workingDir: /app
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
      outputs:
        result: "healthy"
    
    - name: strategy-step
      container:
        image: your-python-image:latest
        command: [python]
        args: [/app/task/server/account_diagnosis/script_for_strategy.py]
        workingDir: /app
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "2Gi"
            cpu: "1000m"

---
# 全局健康检查任务，每5分钟运行一次，清理所有异常锁
apiVersion: argoproj.io/v1alpha1
kind: CronWorkflow
metadata:
  name: global-health-check-cron
  namespace: default
spec:
  schedule: "*/5 * * * *"  # 每5分钟执行一次
  timezone: "Asia/Shanghai"
  concurrencyPolicy: "Replace"  # 允许替换正在运行的任务
  successfulJobsHistoryLimit: 2
  failedJobsHistoryLimit: 2
  workflowSpec:
    entrypoint: global-health-check
    templates:
    - name: global-health-check
      container:
        image: your-python-image:latest
        command: [python]
        args: 
          - /app/task/server/account_diagnosis/lock/cron_health_check.py
          - --max-age
          - "300"  # 5分钟阈值，更激进的清理
        workingDir: /app
        env:
        - name: PYTHONPATH
          value: "/app"
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: host
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-secret
              key: password
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "250m" 