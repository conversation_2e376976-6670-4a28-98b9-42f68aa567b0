#!/usr/bin/env python3
"""
Argo Cron 健康检查脚本
在每次cron执行前自动检查和清理异常的服务锁
确保服务能够正常启动
"""

import os
import sys
import asyncio
import time
import argparse

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import (
    DiagnosisService, StrategyService, ReviewService, CoverService
)


class CronHealthChecker:
    """Cron健康检查器"""
    
    def __init__(self):
        self.services = {
            'diagnosis': DiagnosisService,
            'strategy': StrategyService,
            'review': ReviewService,
            'cover': CoverService
        }
    
    async def check_and_clean_lock(self, service_name: str, max_age_seconds: int = 600) -> bool:
        """
        检查并清理指定服务的异常锁
        
        Args:
            service_name: 服务名称
            max_age_seconds: 锁的最大允许存在时间（秒），默认10分钟
            
        Returns:
            bool: True表示锁状态正常（不存在或已清理），False表示存在问题
        """
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            return False
        
        service_class = self.services[service_name]
        service = service_class(run_mode="cron", timeout=550)  # 与实际cron配置保持一致
        
        try:
            await service.redis_manager.create_pool()
            lock_key = f"service_lock:{service_name}"
            
            # 检查锁是否存在
            existing_lock = await service.redis_manager.pool.get(lock_key)
            if not existing_lock:
                print(f"✅ {service_name}: 锁状态正常（不存在）")
                return True
            
            # 解析锁信息
            try:
                lock_parts = existing_lock.split('_')
                if len(lock_parts) >= 2:
                    lock_timestamp = float(lock_parts[-1])
                    current_time = time.time()
                    lock_age = current_time - lock_timestamp
                    
                    # 获取TTL
                    ttl = await service.redis_manager.pool.ttl(lock_key)
                    
                    print(f"🔍 {service_name}: 发现锁 (年龄: {lock_age:.1f}秒, TTL: {ttl}秒)")
                    
                    # 如果锁年龄超过阈值，清理它
                    if lock_age > max_age_seconds:
                        print(f"⚠️  {service_name}: 锁年龄超过阈值({max_age_seconds}秒)，执行清理")
                        await service.redis_manager.pool.delete(lock_key)
                        print(f"🧹 {service_name}: 异常锁已清理")
                        return True
                    
                    # 如果TTL很短（小于30秒），等待其自然过期
                    elif ttl > 0 and ttl <= 30:
                        print(f"⏳ {service_name}: 锁即将过期(TTL: {ttl}秒)，等待自然过期")
                        await asyncio.sleep(ttl + 2)
                        
                        # 再次检查锁是否已过期
                        still_exists = await service.redis_manager.pool.get(lock_key)
                        if not still_exists:
                            print(f"✅ {service_name}: 锁已自然过期")
                            return True
                        else:
                            print(f"⚠️  {service_name}: 锁未按预期过期，强制清理")
                            await service.redis_manager.pool.delete(lock_key)
                            return True
                    
                    else:
                        print(f"⚠️  {service_name}: 锁仍在有效期内，可能有其他实例正在运行")
                        return False
                        
                else:
                    # 锁格式异常，清理它
                    print(f"⚠️  {service_name}: 锁格式异常，执行清理")
                    await service.redis_manager.pool.delete(lock_key)
                    print(f"🧹 {service_name}: 异常锁已清理")
                    return True
                    
            except (ValueError, IndexError):
                # 解析失败，清理异常锁
                print(f"⚠️  {service_name}: 锁数据解析失败，执行清理")
                await service.redis_manager.pool.delete(lock_key)
                print(f"🧹 {service_name}: 异常锁已清理")
                return True
                
        except Exception as e:
            print(f"❌ {service_name}: 健康检查失败: {str(e)}")
            return False
        finally:
            await service.redis_manager.close()
    
    async def health_check_all(self, max_age_seconds: int = 600) -> bool:
        """
        检查所有服务的健康状态
        
        Returns:
            bool: True表示所有服务状态正常，False表示存在问题
        """
        print(f"🏥 开始Argo Cron健康检查 (锁最大年龄: {max_age_seconds}秒)")
        print("=" * 60)
        
        all_healthy = True
        
        for service_name in self.services.keys():
            healthy = await self.check_and_clean_lock(service_name, max_age_seconds)
            if not healthy:
                all_healthy = False
            print()  # 空行分隔
        
        if all_healthy:
            print("✅ 所有服务锁状态正常，可以安全启动Cron任务")
        else:
            print("⚠️  部分服务存在锁问题，建议检查是否有其他实例正在运行")
        
        return all_healthy
    
    async def health_check_service(self, service_name: str, max_age_seconds: int = 600) -> bool:
        """检查单个服务的健康状态"""
        print(f"🏥 检查 {service_name} 服务健康状态 (锁最大年龄: {max_age_seconds}秒)")
        print("=" * 60)
        
        healthy = await self.check_and_clean_lock(service_name, max_age_seconds)
        
        if healthy:
            print(f"✅ {service_name} 服务状态正常，可以安全启动")
        else:
            print(f"⚠️  {service_name} 服务存在锁问题")
        
        return healthy


async def main():
    parser = argparse.ArgumentParser(description='Argo Cron健康检查工具')
    parser.add_argument('--service', help='检查指定服务（不指定则检查所有服务）')
    parser.add_argument('--max-age', type=int, default=600, 
                       help='锁的最大允许年龄（秒），默认600秒（10分钟）')
    parser.add_argument('--exit-on-error', action='store_true',
                       help='如果发现问题则以非零状态码退出')
    
    args = parser.parse_args()
    
    checker = CronHealthChecker()
    
    try:
        if args.service:
            healthy = await checker.health_check_service(args.service, args.max_age)
        else:
            healthy = await checker.health_check_all(args.max_age)
        
        if args.exit_on_error and not healthy:
            print("❌ 健康检查发现问题，以错误状态退出")
            sys.exit(1)
        else:
            print("✅ 健康检查完成")
            sys.exit(0)
            
    except KeyboardInterrupt:
        print("\n❌ 健康检查被中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 健康检查失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main()) 