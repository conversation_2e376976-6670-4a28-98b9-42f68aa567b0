"""
Timeout Configuration Manager - 超时配置管理器
提供统一的超时配置管理界面和API
"""

import json
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, asdict
from pathlib import Path
from colorama import Fore, Style
import logging

from resource_lifecycle_manager import (
    ResourceType, TimeoutConfig, get_resource_manager
)

@dataclass
class TimeoutProfile:
    """超时配置档案"""
    name: str
    description: str
    configs: Dict[ResourceType, TimeoutConfig]
    created_at: float
    updated_at: float
    is_active: bool = False

class TimeoutConfigManager:
    """超时配置管理器"""
    
    def __init__(self, config_file: str = "timeout_configs.json"):
        self.logger = logging.getLogger("timeout_config_manager")
        self.config_file = Path(config_file)
        self._profiles: Dict[str, TimeoutProfile] = {}
        self._active_profile: Optional[str] = None
        
        # 加载配置
        self._load_configs()
        
        # 如果没有配置，创建默认配置
        if not self._profiles:
            self._create_default_profiles()
    
    def _load_configs(self):
        """加载配置文件"""
        if not self.config_file.exists():
            return
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            for profile_name, profile_data in data.get('profiles', {}).items():
                # 重建TimeoutConfig对象
                configs = {}
                for resource_type_str, config_data in profile_data.get('configs', {}).items():
                    resource_type = ResourceType(resource_type_str)
                    configs[resource_type] = TimeoutConfig(**config_data)
                
                profile = TimeoutProfile(
                    name=profile_data['name'],
                    description=profile_data['description'],
                    configs=configs,
                    created_at=profile_data['created_at'],
                    updated_at=profile_data['updated_at'],
                    is_active=profile_data.get('is_active', False)
                )
                
                self._profiles[profile_name] = profile
                
                if profile.is_active:
                    self._active_profile = profile_name
            
            self.logger.info(f"{Fore.GREEN}📁 Loaded {len(self._profiles)} timeout profiles{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Error loading timeout configs: {e}{Style.RESET_ALL}")
    
    def _save_configs(self):
        """保存配置文件"""
        try:
            data = {
                'profiles': {},
                'last_updated': time.time()
            }
            
            for profile_name, profile in self._profiles.items():
                # 序列化TimeoutConfig对象
                configs_data = {}
                for resource_type, config in profile.configs.items():
                    configs_data[resource_type.value] = asdict(config)
                
                data['profiles'][profile_name] = {
                    'name': profile.name,
                    'description': profile.description,
                    'configs': configs_data,
                    'created_at': profile.created_at,
                    'updated_at': profile.updated_at,
                    'is_active': profile.is_active
                }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"{Fore.GREEN}💾 Saved timeout configurations{Style.RESET_ALL}")
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Error saving timeout configs: {e}{Style.RESET_ALL}")
    
    def _create_default_profiles(self):
        """创建默认配置档案"""
        now = time.time()
        
        # 开发环境配置 - 较短超时
        dev_configs = {
            ResourceType.REDIS: TimeoutConfig(
                connection_timeout=5.0,
                operation_timeout=15.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            ),
            ResourceType.AI_CLIENT: TimeoutConfig(
                connection_timeout=15.0,
                operation_timeout=60.0,
                idle_timeout=180.0,
                total_timeout=900.0
            ),
            ResourceType.DATABASE: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=30.0,
                idle_timeout=600.0,
                total_timeout=1800.0
            ),
            ResourceType.HTTP_CLIENT: TimeoutConfig(
                connection_timeout=5.0,
                operation_timeout=15.0,
                idle_timeout=120.0,
                total_timeout=600.0
            )
        }
        
        # 生产环境配置 - 较长超时
        prod_configs = {
            ResourceType.REDIS: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=30.0,
                idle_timeout=600.0,
                total_timeout=3600.0
            ),
            ResourceType.AI_CLIENT: TimeoutConfig(
                connection_timeout=30.0,
                operation_timeout=120.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            ),
            ResourceType.DATABASE: TimeoutConfig(
                connection_timeout=15.0,
                operation_timeout=60.0,
                idle_timeout=900.0,
                total_timeout=3600.0
            ),
            ResourceType.HTTP_CLIENT: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=30.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            )
        }
        
        # 高性能配置 - 更短超时，快速失败
        performance_configs = {
            ResourceType.REDIS: TimeoutConfig(
                connection_timeout=3.0,
                operation_timeout=10.0,
                idle_timeout=180.0,
                total_timeout=900.0
            ),
            ResourceType.AI_CLIENT: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=45.0,
                idle_timeout=120.0,
                total_timeout=600.0
            ),
            ResourceType.DATABASE: TimeoutConfig(
                connection_timeout=5.0,
                operation_timeout=20.0,
                idle_timeout=300.0,
                total_timeout=900.0
            ),
            ResourceType.HTTP_CLIENT: TimeoutConfig(
                connection_timeout=3.0,
                operation_timeout=10.0,
                idle_timeout=60.0,
                total_timeout=300.0
            )
        }
        
        self._profiles = {
            'development': TimeoutProfile(
                name='development',
                description='开发环境配置 - 适中的超时设置，便于调试',
                configs=dev_configs,
                created_at=now,
                updated_at=now,
                is_active=True
            ),
            'production': TimeoutProfile(
                name='production',
                description='生产环境配置 - 较长的超时设置，保证稳定性',
                configs=prod_configs,
                created_at=now,
                updated_at=now
            ),
            'performance': TimeoutProfile(
                name='performance',
                description='高性能配置 - 较短的超时设置，快速失败',
                configs=performance_configs,
                created_at=now,
                updated_at=now
            )
        }
        
        self._active_profile = 'development'
        self._save_configs()
        
        self.logger.info(f"{Fore.GREEN}🎯 Created default timeout profiles{Style.RESET_ALL}")
    
    def create_profile(self, name: str, description: str, 
                      base_profile: Optional[str] = None) -> TimeoutProfile:
        """创建新的配置档案"""
        if name in self._profiles:
            raise ValueError(f"Profile '{name}' already exists")
        
        now = time.time()
        
        # 如果指定了基础档案，复制其配置
        if base_profile and base_profile in self._profiles:
            base_configs = self._profiles[base_profile].configs.copy()
        else:
            # 使用默认配置
            base_configs = {
                resource_type: TimeoutConfig()
                for resource_type in ResourceType
            }
        
        profile = TimeoutProfile(
            name=name,
            description=description,
            configs=base_configs,
            created_at=now,
            updated_at=now
        )
        
        self._profiles[name] = profile
        self._save_configs()
        
        self.logger.info(f"{Fore.GREEN}➕ Created timeout profile: {name}{Style.RESET_ALL}")
        return profile
    
    def update_profile_config(self, profile_name: str, 
                            resource_type: ResourceType,
                            timeout_config: TimeoutConfig):
        """更新档案中的资源配置"""
        if profile_name not in self._profiles:
            raise ValueError(f"Profile '{profile_name}' not found")
        
        profile = self._profiles[profile_name]
        profile.configs[resource_type] = timeout_config
        profile.updated_at = time.time()
        
        self._save_configs()
        
        # 如果是活动档案，立即应用配置
        if profile_name == self._active_profile:
            self._apply_profile_configs(profile)
        
        self.logger.info(f"{Fore.CYAN}🔧 Updated {resource_type.value} config in profile: {profile_name}{Style.RESET_ALL}")
    
    def activate_profile(self, profile_name: str):
        """激活配置档案"""
        if profile_name not in self._profiles:
            raise ValueError(f"Profile '{profile_name}' not found")
        
        # 取消当前活动档案
        if self._active_profile:
            self._profiles[self._active_profile].is_active = False
        
        # 激活新档案
        profile = self._profiles[profile_name]
        profile.is_active = True
        self._active_profile = profile_name
        
        # 应用配置到资源管理器
        self._apply_profile_configs(profile)
        
        self._save_configs()
        
        self.logger.info(f"{Fore.GREEN}🎯 Activated timeout profile: {profile_name}{Style.RESET_ALL}")
    
    def _apply_profile_configs(self, profile: TimeoutProfile):
        """应用档案配置到资源管理器"""
        resource_manager = get_resource_manager()
        
        for resource_type, timeout_config in profile.configs.items():
            resource_manager.configure_timeout(resource_type, timeout_config)
        
        self.logger.info(f"{Fore.CYAN}⚙️  Applied timeout configs from profile: {profile.name}{Style.RESET_ALL}")
    
    def get_profile(self, profile_name: str) -> Optional[TimeoutProfile]:
        """获取配置档案"""
        return self._profiles.get(profile_name)
    
    def list_profiles(self) -> List[TimeoutProfile]:
        """列出所有配置档案"""
        return list(self._profiles.values())
    
    def get_active_profile(self) -> Optional[TimeoutProfile]:
        """获取当前活动的配置档案"""
        if self._active_profile:
            return self._profiles.get(self._active_profile)
        return None
    
    def delete_profile(self, profile_name: str):
        """删除配置档案"""
        if profile_name not in self._profiles:
            raise ValueError(f"Profile '{profile_name}' not found")
        
        if profile_name == self._active_profile:
            raise ValueError("Cannot delete active profile")
        
        del self._profiles[profile_name]
        self._save_configs()
        
        self.logger.info(f"{Fore.YELLOW}🗑️  Deleted timeout profile: {profile_name}{Style.RESET_ALL}")
    
    def export_profile(self, profile_name: str, file_path: str):
        """导出配置档案"""
        if profile_name not in self._profiles:
            raise ValueError(f"Profile '{profile_name}' not found")
        
        profile = self._profiles[profile_name]
        
        # 序列化配置
        export_data = {
            'profile': {
                'name': profile.name,
                'description': profile.description,
                'configs': {
                    resource_type.value: asdict(config)
                    for resource_type, config in profile.configs.items()
                },
                'created_at': profile.created_at,
                'updated_at': profile.updated_at
            },
            'exported_at': time.time(),
            'version': '1.0'
        }
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"{Fore.GREEN}📤 Exported profile '{profile_name}' to {file_path}{Style.RESET_ALL}")
    
    def import_profile(self, file_path: str, new_name: Optional[str] = None):
        """导入配置档案"""
        with open(file_path, 'r', encoding='utf-8') as f:
            import_data = json.load(f)
        
        profile_data = import_data['profile']
        profile_name = new_name or profile_data['name']
        
        if profile_name in self._profiles:
            raise ValueError(f"Profile '{profile_name}' already exists")
        
        # 重建配置对象
        configs = {}
        for resource_type_str, config_data in profile_data['configs'].items():
            resource_type = ResourceType(resource_type_str)
            configs[resource_type] = TimeoutConfig(**config_data)
        
        profile = TimeoutProfile(
            name=profile_name,
            description=profile_data['description'],
            configs=configs,
            created_at=profile_data['created_at'],
            updated_at=time.time()  # 更新导入时间
        )
        
        self._profiles[profile_name] = profile
        self._save_configs()
        
        self.logger.info(f"{Fore.GREEN}📥 Imported profile '{profile_name}' from {file_path}{Style.RESET_ALL}")
    
    def get_timeout_summary(self) -> Dict[str, Any]:
        """获取超时配置摘要"""
        active_profile = self.get_active_profile()
        
        summary = {
            'active_profile': active_profile.name if active_profile else None,
            'total_profiles': len(self._profiles),
            'profiles': []
        }
        
        for profile in self._profiles.values():
            profile_info = {
                'name': profile.name,
                'description': profile.description,
                'is_active': profile.is_active,
                'created_at': profile.created_at,
                'updated_at': profile.updated_at,
                'resource_types': list(profile.configs.keys())
            }
            
            if profile.is_active:
                # 添加详细的超时配置
                profile_info['configs'] = {
                    resource_type.value: asdict(config)
                    for resource_type, config in profile.configs.items()
                }
            
            summary['profiles'].append(profile_info)
        
        return summary

# 全局超时配置管理器实例
_global_timeout_manager: Optional[TimeoutConfigManager] = None

def get_timeout_manager() -> TimeoutConfigManager:
    """获取全局超时配置管理器实例"""
    global _global_timeout_manager
    if _global_timeout_manager is None:
        _global_timeout_manager = TimeoutConfigManager()
    return _global_timeout_manager