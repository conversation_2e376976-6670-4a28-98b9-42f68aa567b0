#!/usr/bin/env python3
"""
紧急锁清理工具
用于快速清理异常的服务锁，适用于紧急情况
"""

import asyncio
import sys
import os
import time
import argparse

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import (
    DiagnosisService, StrategyService, ReviewService, CoverService
)


class EmergencyLockCleanup:
    """紧急锁清理工具"""
    
    def __init__(self):
        self.services = {
            'diagnosis': DiagnosisService,
            'strategy': StrategyService,
            'review': ReviewService,
            'cover': CoverService
        }
    
    async def quick_clean_lock(self, service_name: str, max_age_seconds: int = 300) -> bool:
        """
        快速清理指定服务的锁
        
        Args:
            service_name: 服务名称
            max_age_seconds: 锁的最大允许年龄（秒），默认5分钟
        """
        if service_name not in self.services:
            print(f"❌ 未知服务: {service_name}")
            print(f"可用服务: {', '.join(self.services.keys())}")
            return False
        
        service_class = self.services[service_name]
        service = service_class(run_mode="cron", timeout=300)
        
        try:
            await service.redis_manager.create_pool()
            lock_key = f"service_lock:{service_name}"
            
            # 获取锁信息
            existing_lock = await service.redis_manager.pool.get(lock_key)
            if not existing_lock:
                print(f"✅ {service_name}: 锁不存在，无需清理")
                return True
            
            ttl = await service.redis_manager.pool.ttl(lock_key)
            print(f"🔍 {service_name}: 发现锁 {existing_lock}, TTL: {ttl}秒")
            
            # 尝试解析锁年龄
            lock_age = None
            try:
                lock_parts = existing_lock.split('_')
                if len(lock_parts) >= 2:
                    if len(lock_parts) >= 3:
                        # 新格式: service_timestamp_mode
                        lock_timestamp = float(lock_parts[1])
                    else:
                        # 旧格式: service_timestamp
                        lock_timestamp = float(lock_parts[1])
                    
                    lock_age = time.time() - lock_timestamp
                    print(f"📅 {service_name}: 锁年龄 {lock_age:.1f}秒")
                    
            except (ValueError, IndexError):
                print(f"⚠️  {service_name}: 无法解析锁时间戳")
            
            # 决定是否清理
            should_clean = False
            reason = ""
            
            if lock_age and lock_age > max_age_seconds:
                should_clean = True
                reason = f"锁年龄({lock_age:.1f}秒)超过阈值({max_age_seconds}秒)"
            elif ttl == -1:
                should_clean = True
                reason = "锁永不过期"
            elif ttl > 600:  # TTL超过10分钟
                should_clean = True
                reason = f"TTL过长({ttl}秒)"
            elif lock_age is None and ttl > 300:  # 无法解析时间戳且TTL较长
                should_clean = True
                reason = "无法解析锁格式且TTL较长"
            
            if should_clean:
                print(f"🧹 {service_name}: 清理锁 - {reason}")
                deleted = await service.redis_manager.pool.delete(lock_key)
                if deleted:
                    print(f"✅ {service_name}: 锁清理成功")
                    return True
                else:
                    print(f"❌ {service_name}: 锁清理失败")
                    return False
            else:
                print(f"⏸️  {service_name}: 锁状态正常，不需要清理")
                return True
                
        except Exception as e:
            print(f"❌ {service_name}: 清理失败: {str(e)}")
            return False
        finally:
            await service.redis_manager.close()
    
    async def emergency_clean_all(self, max_age_seconds: int = 300, force: bool = False) -> bool:
        """
        紧急清理所有服务锁
        
        Args:
            max_age_seconds: 锁的最大允许年龄（秒）
            force: 是否强制清理所有锁
        """
        print(f"🚨 紧急清理所有服务锁 (阈值: {max_age_seconds}秒)")
        print("=" * 60)
        
        all_success = True
        
        for service_name in self.services.keys():
            print(f"\n📋 处理 {service_name.upper()} 服务:")
            print("-" * 30)
            
            if force:
                # 强制模式：直接删除锁
                service_class = self.services[service_name]
                service = service_class(run_mode="cron", timeout=300)
                try:
                    await service.redis_manager.create_pool()
                    success = await service.force_clear_service_lock()
                    if not success:
                        all_success = False
                except Exception as e:
                    print(f"❌ {service_name}: 强制清理失败: {str(e)}")
                    all_success = False
                finally:
                    await service.redis_manager.close()
            else:
                # 智能模式：根据条件清理
                success = await self.quick_clean_lock(service_name, max_age_seconds)
                if not success:
                    all_success = False
        
        if all_success:
            print("\n✅ 所有服务锁清理完成")
        else:
            print("\n⚠️  部分服务锁清理失败")
        
        return all_success


async def main():
    parser = argparse.ArgumentParser(description='紧急锁清理工具')
    parser.add_argument('--service', help='指定要清理的服务名称')
    parser.add_argument('--all', action='store_true', help='清理所有服务锁')
    parser.add_argument('--max-age', type=int, default=300, 
                       help='锁的最大允许年龄（秒），默认300秒（5分钟）')
    parser.add_argument('--force', action='store_true',
                       help='强制清理所有锁，不检查年龄')
    parser.add_argument('--yes', action='store_true',
                       help='跳过确认提示')
    
    args = parser.parse_args()
    
    if not args.service and not args.all:
        print("❌ 请指定 --service <服务名> 或 --all")
        print("可用服务: diagnosis, strategy, review, cover")
        return
    
    cleanup = EmergencyLockCleanup()
    
    try:
        if args.all:
            if not args.yes and not args.force:
                confirm = input(f"⚠️  确定要清理所有服务锁吗？(最大年龄: {args.max_age}秒) (y/N): ")
                if confirm.lower() != 'y':
                    print("❌ 操作已取消")
                    return
            
            await cleanup.emergency_clean_all(args.max_age, args.force)
        else:
            if not args.yes:
                confirm = input(f"⚠️  确定要清理 {args.service} 服务锁吗？(最大年龄: {args.max_age}秒) (y/N): ")
                if confirm.lower() != 'y':
                    print("❌ 操作已取消")
                    return
            
            await cleanup.quick_clean_lock(args.service, args.max_age)
            
    except KeyboardInterrupt:
        print("\n❌ 操作被中断")
    except Exception as e:
        print(f"❌ 操作失败: {str(e)}")


if __name__ == "__main__":
    print("🚨 紧急锁清理工具")
    print("=" * 50)
    
    asyncio.run(main()) 