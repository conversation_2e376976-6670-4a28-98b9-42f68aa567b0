"""
智能查询增强系统
基于动态行业发现的查询术语智能增强
"""

import asyncio
import json
import logging
import re
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime

from .dynamic_industry_intelligence import discover_industry_terms, IndustryIntelligence

logger = logging.getLogger(__name__)

class IntelligentQueryEnhancer:
    """智能查询增强器"""
    
    def __init__(self, gpt_model: str = "gpt-4.1"):
        self.gpt_model = gpt_model
        self.enhancement_cache = {}  # 增强结果缓存
        
    async def enhance_query_intelligently(self, query: str, industry: str, account_context: Optional[Dict] = None) -> Dict[str, Any]:
        """
        智能增强查询
        
        Args:
            query: 原始查询
            industry: 行业名称
            account_context: 账号上下文
            
        Returns:
            Dict: 增强结果
        """
        try:
            logger.info(f"开始智能增强查询：{query[:50]}...")
            
            # 1. 动态发现行业智能
            industry_intelligence = await discover_industry_terms(industry, account_context)
            logger.info(f"发现{industry}行业术语：{len(industry_intelligence.keywords)}个关键词")
            
            # 2. 分析原查询的术语覆盖情况
            coverage_analysis = self._analyze_query_coverage(query, industry_intelligence)
            
            # 3. 如果覆盖不足，进行智能增强
            if coverage_analysis['needs_enhancement']:
                enhanced_query = await self._perform_intelligent_enhancement(
                    query, industry_intelligence, coverage_analysis, account_context
                )
            else:
                enhanced_query = query
                logger.info("查询术语覆盖充足，无需增强")
            
            # 4. 验证增强效果
            enhanced_coverage = self._analyze_query_coverage(enhanced_query, industry_intelligence)
            
            return {
                'original_query': query,
                'enhanced_query': enhanced_query,
                'industry_intelligence': industry_intelligence,
                'original_coverage': coverage_analysis,
                'enhanced_coverage': enhanced_coverage,
                'enhancement_applied': enhanced_query != query,
                'improvement_score': enhanced_coverage['total_score'] - coverage_analysis['total_score']
            }
            
        except Exception as e:
            logger.error(f"智能查询增强失败: {e}")
            return {
                'original_query': query,
                'enhanced_query': query,
                'enhancement_applied': False,
                'error': str(e)
            }
    
    def _analyze_query_coverage(self, query: str, intelligence: IndustryIntelligence) -> Dict[str, Any]:
        """分析查询的术语覆盖情况"""
        try:
            query_lower = query.lower()
            this_year = datetime.now().year
            last_year = this_year - 1
            
            # 统计各类术语
            found_keywords = [kw for kw in intelligence.keywords if kw.lower() in query_lower]
            found_topics = [topic for topic in intelligence.trending_topics if topic.lower() in query_lower]
            
            # 检查具体性关键词
            specific_terms = [str(last_year), str(this_year), '最新', '最近', '当前', '案例', '数据', '策略', '方法', '分析', '趋势', '发展']
            found_specific = [term for term in specific_terms if term in query_lower]
            
            # 计算覆盖得分
            keyword_score = min(1.0, len(found_keywords) / 3.0)  # 3个关键词满分
            topic_score = min(1.0, len(found_topics) / 2.0)      # 2个话题满分  
            specific_score = min(1.0, len(found_specific) / 2.0)  # 2个具体词满分
            
            total_score = (keyword_score * 0.5 + topic_score * 0.3 + specific_score * 0.2)
            
            return {
                'total_score': total_score,
                'keyword_count': len(found_keywords),
                'found_keywords': found_keywords,
                'topic_count': len(found_topics),
                'found_topics': found_topics,
                'specific_count': len(found_specific),
                'found_specific': found_specific,
                'needs_enhancement': total_score < 0.6,  # 60%以下需要增强
                'quality_level': self._get_quality_level(total_score)
            }
            
        except Exception as e:
            logger.error(f"分析查询覆盖失败: {e}")
            return {'total_score': 0.0, 'needs_enhancement': True, 'error': str(e)}
    
    def _get_quality_level(self, score: float) -> str:
        """获取质量等级"""
        if score >= 0.8:
            return "优秀"
        elif score >= 0.6:
            return "良好"
        elif score >= 0.4:
            return "一般"
        else:
            return "较差"
    
    async def _perform_intelligent_enhancement(self, query: str, intelligence: IndustryIntelligence, 
                                            coverage: Dict, account_context: Optional[Dict] = None) -> str:
        """执行智能增强"""
        try:
            from task import callWattGPT
            
            # 构建增强prompt
            enhancement_prompt = f"""
你是一位专业的查询优化专家。请基于原始查询进行适度增强，重点是保持原有内容。

原始查询：{query}
行业：{intelligence.industry}

可用术语：
- 关键词：{', '.join(intelligence.keywords[:10])}
- 热门话题：{', '.join(intelligence.trending_topics[:5])}

增强要求：
1. 【核心保持】必须包含原始查询的所有主要词汇
2. 【适度添加】只添加1个最相关的关键词或时间词（如"2025年"）
3. 【自然表达】确保语句通顺，不生硬堆砌
4. 【长度适中】增强后长度不超过原始查询的2倍
5. 【风格一致】保持原始查询的表达风格

示例：
- 原始：美妆护肤产品推荐
- 增强：{datetime.now().year}年美妆护肤产品推荐
- 原始：科技数码评测
- 增强：科技数码产品评测分析

请返回增强后的查询（仅返回查询文本）：
"""

            body = {
                "model": self.gpt_model,
                "messages": [
                    {"role": "user", "content": enhancement_prompt}
                ],
                "temperature": 0.3,  # 降低随机性，保持一致性
                "max_tokens": 200
            }
            
            # 调用GPT增强
            gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=15)
            
            if not gpt_status:
                logger.error(f"GPT查询增强失败: {gpt_result}")
                return self._rule_based_enhancement(query, intelligence, coverage)
            
            # 解析GPT响应
            if isinstance(gpt_result, dict) and 'result' in gpt_result:
                result_data = gpt_result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    enhanced_query = message['content'].strip()
                                    logger.debug(f"GPT返回增强查询: '{enhanced_query}'")
                                    
                                    # 验证增强结果
                                    if await self._validate_enhancement(query, enhanced_query, intelligence):
                                        logger.info(f"GPT增强验证通过: '{query}' -> '{enhanced_query}'")
                                        return enhanced_query
                                    else:
                                        logger.warning(f"GPT增强结果验证失败，使用规则增强: '{enhanced_query}'")
                                        return self._rule_based_enhancement(query, intelligence, coverage)
            
            logger.error("GPT增强响应格式错误，使用规则增强")
            return self._rule_based_enhancement(query, intelligence, coverage)
            
        except Exception as e:
            logger.error(f"智能增强异常: {e}")
            return self._rule_based_enhancement(query, intelligence, coverage)
    
    def _rule_based_enhancement(self, query: str, intelligence: IndustryIntelligence, coverage: Dict) -> str:
        """基于规则的增强（作为后备）"""
        try:
            enhanced_query = query
            
            # 1. 添加时间性关键词
            current_year = str(datetime.now().year)
            next_year = str(datetime.now().year + 1)
            if not any(time_kw in enhanced_query for time_kw in [current_year, next_year, '最新', '最近']):
                enhanced_query = f"{current_year}年{enhanced_query}"
            
            # 2. 添加缺失的关键词
            missing_keywords = [kw for kw in intelligence.keywords[:5] 
                              if kw.lower() not in enhanced_query.lower()]
            
            if missing_keywords and len(coverage['found_keywords']) < 2:
                # 选择最相关的1-2个关键词
                selected_keywords = missing_keywords[:2]
                
                # 智能插入关键词
                if '行业' in enhanced_query:
                    enhanced_query = enhanced_query.replace('行业', f'{selected_keywords[0]}行业')
                elif '发展' in enhanced_query:
                    enhanced_query = enhanced_query.replace('发展', f'{selected_keywords[0]}发展')
                elif '趋势' in enhanced_query:
                    enhanced_query = enhanced_query.replace('趋势', f'{selected_keywords[0]}趋势')
                else:
                    enhanced_query = f"{enhanced_query} {selected_keywords[0]}"
            
            # 3. 添加热门话题
            if not coverage['found_topics'] and intelligence.trending_topics:
                trending_topic = intelligence.trending_topics[0]
                if '创新' in enhanced_query:
                    enhanced_query = enhanced_query.replace('创新', f'{trending_topic}创新')
                elif '变化' in enhanced_query:
                    enhanced_query = enhanced_query.replace('变化', f'{trending_topic}变化')
                else:
                    enhanced_query = f"{enhanced_query} {trending_topic}"
            
            # 4. 确保长度合理
            if len(enhanced_query) > 150:
                enhanced_query = enhanced_query[:150]
            
            return enhanced_query
            
        except Exception as e:
            logger.error(f"规则增强失败: {e}")
            return query
    
    async def _validate_enhancement(self, original: str, enhanced: str, intelligence: IndustryIntelligence) -> bool:
        """验证增强结果"""
        validation_details = {}
        
        try:
            logger.debug(f"开始验证增强结果: 原始='{original}', 增强='{enhanced}'")
            
            # 1. 基本长度验证 - 更宽松的长度要求
            length_valid = 5 <= len(enhanced) <= 300  # 扩大长度范围
            validation_details['length_valid'] = length_valid
            validation_details['enhanced_length'] = len(enhanced)
            
            if not length_valid:
                logger.warning(f"长度验证失败: {len(enhanced)} (要求5-300)")
                validation_details['length_invalid'] = True
                self._record_validation_result(original, enhanced, False, validation_details)
                return False
            
            # 2. 快速检查 - 如果增强查询包含原始查询的主要内容，直接通过
            if self._quick_relevance_check(original, enhanced):
                logger.info(f"快速相关性检查通过: '{original}' -> '{enhanced}'")
                validation_details['quick_check'] = True
                self._record_validation_result(original, enhanced, True, validation_details)
                return True
            
            # 3. 使用GPT-4o-mini进行智能验证
            try:
                gpt_validation = await self._gpt_assisted_validation(original, enhanced, intelligence)
                validation_details.update(gpt_validation)
                
                if gpt_validation.get('valid', False):
                    logger.info(f"GPT辅助验证通过: {gpt_validation.get('reason', '无原因')}")
                    self._record_validation_result(original, enhanced, True, validation_details)
                    return True
                else:
                    logger.warning(f"GPT辅助验证失败: {gpt_validation.get('reason', '无原因')}")
                    
            except Exception as e:
                logger.error(f"GPT辅助验证异常: {e}")
                validation_details['gpt_validation_error'] = str(e)
            
            # 4. 如果GPT验证失败，使用改进的传统验证作为后备
            traditional_valid = self._improved_traditional_validation(original, enhanced, intelligence, validation_details)
            if traditional_valid:
                logger.info("改进的传统验证通过，作为GPT验证的后备")
                self._record_validation_result(original, enhanced, True, validation_details)
                return True
            else:
                logger.warning(f"所有验证方法都失败: {validation_details}")
                self._record_validation_result(original, enhanced, False, validation_details)
                return False
            
        except Exception as e:
            logger.error(f"验证增强结果失败: {e}")
            validation_details['exception'] = str(e)
            self._record_validation_result(original, enhanced, False, validation_details)
            return False
    
    def _quick_relevance_check(self, original: str, enhanced: str) -> bool:
        """快速相关性检查 - 检查增强查询是否包含原始查询的核心内容"""
        try:
            # 移除标点符号和空格，转换为小写
            original_clean = ''.join(c.lower() for c in original if c.isalnum())
            enhanced_clean = ''.join(c.lower() for c in enhanced if c.isalnum())
            
            # 如果原始查询很短（<=6个字符），检查是否完全包含
            if len(original_clean) <= 6:
                return original_clean in enhanced_clean
            
            # 对于较长的查询，检查主要词汇是否包含
            original_words = [w for w in original.split() if len(w) >= 2]
            enhanced_lower = enhanced.lower()
            
            # 至少50%的原始词汇应该出现在增强查询中
            found_words = sum(1 for word in original_words if word.lower() in enhanced_lower)
            coverage_ratio = found_words / len(original_words) if original_words else 0
            
            logger.debug(f"快速检查: 原始词汇{len(original_words)}个, 找到{found_words}个, 覆盖率{coverage_ratio:.2f}")
            return coverage_ratio >= 0.5
            
        except Exception as e:
            logger.error(f"快速相关性检查失败: {e}")
            return False
    
    def _improved_traditional_validation(self, original: str, enhanced: str, intelligence: IndustryIntelligence, validation_details: dict) -> bool:
        """改进的传统验证方法"""
        try:
            # 1. 使用改进的语义相似度计算
            semantic_similarity = self._calculate_improved_semantic_similarity(original, enhanced)
            validation_details['semantic_similarity'] = semantic_similarity
            
            # 大幅降低语义相似度要求
            if semantic_similarity < 0.2:  # 从0.3降低到0.2
                logger.warning(f"语义相似度验证失败: {semantic_similarity:.3f} < 0.2")
                validation_details['semantic_low'] = True
                # 不直接返回False，继续其他检查
            
            # 2. 检查是否真的增加了有用信息
            enhanced_coverage = self._analyze_query_coverage(enhanced, intelligence)
            original_coverage = self._analyze_query_coverage(original, intelligence)
            
            improvement = enhanced_coverage['total_score'] - original_coverage['total_score']
            validation_details['improvement'] = improvement
            validation_details['original_score'] = original_coverage['total_score']
            validation_details['enhanced_score'] = enhanced_coverage['total_score']
            
            # 3. 多维度验证 - 只要满足一个条件就通过
            validation_passed = False
            
            # 条件1: 语义相似度足够且有改进
            if semantic_similarity >= 0.2 and improvement >= -0.1:
                validation_passed = True
                validation_details['pass_reason'] = 'semantic_similarity_and_improvement'
                
            # 条件2: 语义相似度一般但改进明显
            elif semantic_similarity >= 0.15 and improvement >= 0.1:
                validation_passed = True
                validation_details['pass_reason'] = 'moderate_similarity_good_improvement'
                
            # 条件3: 长度合理且包含关键词
            elif self._contains_key_enhancements(original, enhanced):
                validation_passed = True
                validation_details['pass_reason'] = 'contains_key_enhancements'
                
            # 条件4: 基本相关性检查
            elif self._basic_relevance_check(original, enhanced):
                validation_passed = True
                validation_details['pass_reason'] = 'basic_relevance'
            
            if validation_passed:
                logger.info(f"改进的传统验证通过: {validation_details['pass_reason']}")
                return True
            else:
                logger.warning(f"改进的传统验证失败: 语义相似度{semantic_similarity:.3f}, 改进{improvement:.3f}")
                return False
            
        except Exception as e:
            logger.error(f"改进的传统验证失败: {e}")
            validation_details['improved_traditional_validation_error'] = str(e)
            return False
    
    def _calculate_improved_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算改进的语义相似度"""
        try:
            # 方法1: 基于字符级别的相似度
            char_similarity = self._calculate_character_similarity(text1, text2)
            
            # 方法2: 基于词汇级别的相似度
            word_similarity = self._calculate_word_similarity(text1, text2)
            
            # 方法3: 基于概念级别的相似度
            concept_similarity = self._calculate_semantic_similarity(text1, text2)
            
            # 综合计算，取最高值
            final_similarity = max(char_similarity, word_similarity, concept_similarity)
            
            logger.debug(f"相似度计算: 字符={char_similarity:.3f}, 词汇={word_similarity:.3f}, 概念={concept_similarity:.3f}, 最终={final_similarity:.3f}")
            return final_similarity
            
        except Exception as e:
            logger.error(f"计算改进语义相似度失败: {e}")
            return 0.0
    
    def _calculate_character_similarity(self, text1: str, text2: str) -> float:
        """计算字符级别相似度"""
        try:
            # 移除空格和标点，转换为小写
            clean1 = ''.join(c.lower() for c in text1 if c.isalnum())
            clean2 = ''.join(c.lower() for c in text2 if c.isalnum())
            
            if not clean1 or not clean2:
                return 0.0
            
            # 计算最长公共子序列
            common_chars = sum(1 for c in clean1 if c in clean2)
            return common_chars / max(len(clean1), len(clean2))
            
        except Exception as e:
            logger.error(f"计算字符相似度失败: {e}")
            return 0.0
    
    def _calculate_word_similarity(self, text1: str, text2: str) -> float:
        """计算词汇级别相似度"""
        try:
            words1 = set(w.lower() for w in text1.split() if len(w) >= 2)
            words2 = set(w.lower() for w in text2.split() if len(w) >= 2)
            
            if not words1 or not words2:
                return 0.0
            
            common_words = len(words1 & words2)
            total_words = len(words1 | words2)
            
            return common_words / total_words if total_words > 0 else 0.0
            
        except Exception as e:
            logger.error(f"计算词汇相似度失败: {e}")
            return 0.0
    
    def _contains_key_enhancements(self, original: str, enhanced: str) -> bool:
        """检查是否包含关键增强元素"""
        try:
            # 检查是否添加了时间信息
            current_year = str(datetime.now().year)
            next_year = str(datetime.now().year + 1)
            time_keywords = [current_year, next_year, '最新', '最近', '当前', '今年']
            has_time = any(keyword in enhanced for keyword in time_keywords)
            
            # 检查是否添加了具体性词汇
            specific_keywords = ['分析', '评测', '方法', '技巧', '策略', '案例', '数据', '趋势']
            has_specific = any(keyword in enhanced for keyword in specific_keywords)
            
            # 检查长度是否合理增长
            length_growth = len(enhanced) / len(original) if len(original) > 0 else 0
            reasonable_length = 1.2 <= length_growth <= 3.0
            
            logger.debug(f"关键增强检查: 时间={has_time}, 具体={has_specific}, 长度增长={length_growth:.2f}")
            
            # 至少满足两个条件
            conditions_met = sum([has_time, has_specific, reasonable_length])
            return conditions_met >= 2
            
        except Exception as e:
            logger.error(f"检查关键增强失败: {e}")
            return False
    
    def _basic_relevance_check(self, original: str, enhanced: str) -> bool:
        """基本相关性检查"""
        try:
            # 检查增强查询是否包含原始查询的主要内容
            original_words = [w for w in original.split() if len(w) >= 2]
            if not original_words:
                return True  # 如果原始查询为空或很短，认为相关
            
            enhanced_lower = enhanced.lower()
            found_words = sum(1 for word in original_words if word.lower() in enhanced_lower)
            
            # 至少包含40%的原始词汇
            coverage = found_words / len(original_words)
            
            logger.debug(f"基本相关性: 原始词汇{len(original_words)}个, 找到{found_words}个, 覆盖率{coverage:.2f}")
            return coverage >= 0.4
            
        except Exception as e:
            logger.error(f"基本相关性检查失败: {e}")
            return False
    
    async def _gpt_assisted_validation(self, original: str, enhanced: str, intelligence: IndustryIntelligence) -> dict:
        """使用GPT-4o-mini辅助验证"""
        try:
            from task import callWattGPT
            
            validation_prompt = f"""
你是一位专业的查询优化评估专家。请评估以下查询增强是否合理：

原始查询：{original}
增强查询：{enhanced}
目标行业：{intelligence.industry}

评估标准：
1. 增强查询是否保持了原始查询的核心意图和主题？
2. 增强查询是否添加了相关的行业术语和专业词汇？
3. 增强查询是否比原始查询更具体、更有针对性？
4. 增强查询的长度是否合理（不过度冗长）？
5. 增强查询是否自然流畅，没有生硬堆砌关键词？

请返回JSON格式的评估结果：
{{
    "valid": true/false,
    "score": 0-10,
    "reason": "详细说明原因",
    "core_intent_preserved": true/false,
    "industry_terms_added": true/false,
    "specificity_improved": true/false,
    "length_reasonable": true/false,
    "natural_flow": true/false
}}
"""

            body = {
                "model": "gpt-4o-mini",
                "messages": [
                    {"role": "user", "content": validation_prompt}
                ],
                "temperature": 0.1,
                "max_tokens": 300
            }
            
            gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=10)
            
            if not gpt_status:
                logger.error(f"GPT辅助验证调用失败: {gpt_result}")
                return {'valid': False, 'reason': 'GPT调用失败', 'gpt_error': str(gpt_result)}
            
            # 解析GPT响应
            if isinstance(gpt_result, dict) and 'result' in gpt_result:
                result_data = gpt_result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    content = message['content'].strip()
                                    
                                    # 尝试解析JSON
                                    try:
                                        import json
                                        validation_result = json.loads(content)
                                        return validation_result
                                    except json.JSONDecodeError:
                                        # 如果JSON解析失败，尝试简单的文本分析
                                        if 'true' in content.lower() or '通过' in content or '合理' in content:
                                            return {'valid': True, 'reason': '基于文本分析判断为有效', 'gpt_response': content}
                                        else:
                                            return {'valid': False, 'reason': '基于文本分析判断为无效', 'gpt_response': content}
            
            return {'valid': False, 'reason': 'GPT响应解析失败', 'gpt_result': str(gpt_result)}
            
        except Exception as e:
            logger.error(f"GPT辅助验证异常: {e}")
            return {'valid': False, 'reason': f'GPT辅助验证异常: {str(e)}'}
    
    def _calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """计算语义相似度（简化版本）"""
        try:
            # 简化的语义相似度计算
            # 基于共同的关键概念而不是确切的词汇匹配
            
            # 提取关键概念
            concepts1 = self._extract_key_concepts(text1)
            concepts2 = self._extract_key_concepts(text2)
            
            if not concepts1 or not concepts2:
                return 0.0
            
            # 计算概念重叠
            common_concepts = len(set(concepts1) & set(concepts2))
            total_concepts = len(set(concepts1) | set(concepts2))
            
            if total_concepts == 0:
                return 0.0
            
            return common_concepts / total_concepts
            
        except Exception as e:
            logger.error(f"计算语义相似度失败: {e}")
            return 0.0
    
    def _extract_key_concepts(self, text: str) -> list:
        """提取关键概念"""
        try:
            # 移除常见停用词
            stop_words = {'的', '在', '和', '与', '或', '是', '有', '了', '着', '过', '也', '都', '将', '及', '等', '如何', '什么', '怎样', '年'}
            
            # 提取2字以上的词汇作为概念
            words = text.split()
            concepts = []
            
            for word in words:
                if len(word) >= 2 and word not in stop_words:
                    concepts.append(word.lower())
            
            return concepts
            
        except Exception as e:
            logger.error(f"提取关键概念失败: {e}")
            return []
    
    def _record_validation_result(self, original: str, enhanced: str, result: bool, details: dict):
        """记录验证结果到监控系统"""
        try:
            from .enhancement_monitor import record_validation
            record_validation(original, enhanced, result, **details)
        except Exception as e:
            logger.debug(f"记录验证结果失败: {e}")  # 不影响主流程
    
    def _check_relevance(self, original: str, enhanced: str) -> bool:
        """检查增强后的查询是否与原始查询相关"""
        try:
            # 检查核心主题词是否保留
            original_lower = original.lower()
            enhanced_lower = enhanced.lower()
            
            # 提取可能的核心词汇
            core_terms = []
            for word in original.split():
                if len(word) > 2 and word not in ['的', '在', '和', '与', '或', '是', '有', '了', '着', '过']:
                    core_terms.append(word.lower())
            
            # 至少保留一个核心词汇
            preserved_terms = [term for term in core_terms if term in enhanced_lower]
            
            if not preserved_terms and len(core_terms) > 0:
                logger.warning(f"核心词汇未保留: {core_terms}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"相关性检查失败: {e}")
            return True  # 检查失败时默认通过

    async def batch_enhance_queries(self, queries: List[str], industry: str, 
                                  account_context: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """批量增强查询"""
        try:
            logger.info(f"开始批量增强{len(queries)}个查询")
            
            # 并发处理查询增强
            tasks = [
                self.enhance_query_intelligently(query, industry, account_context)
                for query in queries
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            enhanced_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"查询{i}增强失败: {result}")
                    enhanced_results.append({
                        'original_query': queries[i],
                        'enhanced_query': queries[i],
                        'enhancement_applied': False,
                        'error': str(result)
                    })
                else:
                    enhanced_results.append(result)
            
            # 统计增强效果
            enhanced_count = sum(1 for r in enhanced_results if r.get('enhancement_applied', False))
            avg_improvement = sum(r.get('improvement_score', 0) for r in enhanced_results) / len(enhanced_results)
            
            logger.info(f"批量增强完成：{enhanced_count}/{len(queries)}个查询被增强，平均改进：{avg_improvement:.3f}")
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"批量增强查询失败: {e}")
            return []

# 全局实例
_intelligent_query_enhancer = None

def get_intelligent_query_enhancer() -> IntelligentQueryEnhancer:
    """获取智能查询增强器实例"""
    global _intelligent_query_enhancer
    if _intelligent_query_enhancer is None:
        _intelligent_query_enhancer = IntelligentQueryEnhancer()
    return _intelligent_query_enhancer

async def enhance_query_with_intelligence(query: str, industry: str, 
                                        account_context: Optional[Dict] = None) -> Dict[str, Any]:
    """
    智能增强查询的便捷函数
    
    Args:
        query: 原始查询
        industry: 行业名称
        account_context: 账号上下文
        
    Returns:
        Dict: 增强结果
    """
    enhancer = get_intelligent_query_enhancer()
    return await enhancer.enhance_query_intelligently(query, industry, account_context)