#!/usr/bin/env python3
"""
测试 prompt 模板系统
验证新的 Jinja2 模板是否能正确渲染
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

def test_prompt_templates():
    """测试所有 prompt 模板"""
    print("开始测试 prompt 模板系统...")
    
    try:
        from task.lib.prompt_utils import (
            get_diagnosis_prompt,
            get_html_generation_prompts,
            get_json_generation_prompts,
            get_sales_proposal_prompts
        )
        
        # 测试数据
        account_data = {
            "account": {
                "name": "测试账号",
                "id": "test_account_123",
                "description": "这是一个用于测试的账号，主要分享一些日常生活和美食。",
                "followers": 1024,
                "following": 100,
                "posts": 50
            },
            "noteList": [
                {
                    "title": "我的第一篇笔记",
                    "content": "这是笔记的内容，关于上海的周末去处",
                    "likes": 150,
                    "comments": 30,
                    "favorites": 50
                }
            ],
            "industry": "生活分享",
            "marketingGoal": 1
        }
        
        diagnosis_result = "这是一个测试诊断结果，用于验证模板系统是否正常工作。"
        
        # 测试诊断模板
        print("\n1. 测试诊断模板...")
        try:
            diagnosis_prompt = get_diagnosis_prompt(account_data)
            print("✅ 诊断模板渲染成功")
            print(f"   提示词长度: {len(diagnosis_prompt)} 字符")
        except Exception as e:
            print(f"❌ 诊断模板渲染失败: {e}")
        
        # 测试 HTML 生成模板
        print("\n2. 测试 HTML 生成模板...")
        try:
            sys_prompt, user_prompt = get_html_generation_prompts(diagnosis_result)
            print("✅ HTML 生成模板渲染成功")
            print(f"   系统提示词长度: {len(sys_prompt)} 字符")
            print(f"   用户提示词长度: {len(user_prompt)} 字符")
        except Exception as e:
            print(f"❌ HTML 生成模板渲染失败: {e}")
        
        # 测试 JSON 生成模板
        print("\n3. 测试 JSON 生成模板...")
        try:
            sys_prompt, user_prompt = get_json_generation_prompts(diagnosis_result)
            print("✅ JSON 生成模板渲染成功")
            print(f"   系统提示词长度: {len(sys_prompt)} 字符")
            print(f"   用户提示词长度: {len(user_prompt)} 字符")
        except Exception as e:
            print(f"❌ JSON 生成模板渲染失败: {e}")
        
        # 测试销售提案生成模板
        print("\n4. 测试销售提案生成模板...")
        try:
            sys_prompt, user_prompt = get_sales_proposal_prompts(diagnosis_result)
            print("✅ 销售提案生成模板渲染成功")
            print(f"   系统提示词长度: {len(sys_prompt)} 字符")
            print(f"   用户提示词长度: {len(user_prompt)} 字符")
        except Exception as e:
            print(f"❌ 销售提案生成模板渲染失败: {e}")
        
        print("\n✅ 所有模板测试完成！")
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保项目路径配置正确")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

def show_template_content():
    """显示模板内容示例"""
    print("\n" + "="*50)
    print("模板内容示例")
    print("="*50)
    
    try:
        from task.lib.prompt_utils import get_html_generation_prompts
        
        diagnosis_result = "测试诊断结果"
        sys_prompt, user_prompt = get_html_generation_prompts(diagnosis_result)
        
        print("\n【HTML 生成 - 系统提示词】:")
        print("-" * 30)
        print(sys_prompt[:200] + "..." if len(sys_prompt) > 200 else sys_prompt)
        
        print("\n【HTML 生成 - 用户提示词】:")
        print("-" * 30)
        print(user_prompt[:300] + "..." if len(user_prompt) > 300 else user_prompt)
        
    except Exception as e:
        print(f"显示模板内容失败: {e}")

if __name__ == "__main__":
    test_prompt_templates()
    show_template_content() 