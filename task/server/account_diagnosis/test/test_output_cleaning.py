#!/usr/bin/env python3
"""
测试输出清理功能
验证 HTML、JSON 和 Markdown 输出清理是否正常工作
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

def test_html_cleaning():
    """测试 HTML 清理功能"""
    print("测试 HTML 清理功能...")
    
    from task.lib.prompt_utils import clean_html_output
    
    # 测试用例 1: 带有 markdown 代码块标记的 HTML
    test_html_1 = """```html
<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>
```"""
    
    cleaned_1 = clean_html_output(test_html_1)
    print(f"✅ 测试用例 1: 移除了 markdown 标记")
    print(f"   原始长度: {len(test_html_1)}, 清理后长度: {len(cleaned_1)}")
    assert not cleaned_1.startswith('```')
    assert not cleaned_1.endswith('```')
    assert cleaned_1.startswith('<!DOCTYPE html')
    
    # 测试用例 2: 带有额外文本的 HTML
    test_html_2 = """这是一些额外的文本
```html
<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
</head>
<body>
    <h1>Hello World</h1>
</body>
</html>
```
这是结尾的额外文本"""
    
    cleaned_2 = clean_html_output(test_html_2)
    print(f"✅ 测试用例 2: 提取了完整的 HTML 文档")
    assert cleaned_2.startswith('<!DOCTYPE html')
    assert cleaned_2.endswith('</html>')
    
    print("HTML 清理功能测试通过！\n")

def test_json_cleaning():
    """测试 JSON 清理功能"""
    print("测试 JSON 清理功能...")
    
    from task.lib.prompt_utils import clean_json_output
    
    # 测试用例 1: 带有 markdown 代码块标记的 JSON
    test_json_1 = """```json
{
    "name": "test",
    "value": 123
}
```"""
    
    cleaned_1 = clean_json_output(test_json_1)
    print(f"✅ 测试用例 1: 移除了 markdown 标记")
    print(f"   原始长度: {len(test_json_1)}, 清理后长度: {len(cleaned_1)}")
    assert not cleaned_1.startswith('```')
    assert not cleaned_1.endswith('```')
    assert cleaned_1.startswith('{')
    
    # 测试用例 2: 带有额外文本的 JSON
    test_json_2 = """这是一些解释文本
{
    "name": "test",
    "value": 123,
    "nested": {
        "key": "value"
    }
}
这是结尾文本"""
    
    cleaned_2 = clean_json_output(test_json_2)
    print(f"✅ 测试用例 2: 提取了完整的 JSON 对象")
    assert cleaned_2.startswith('{')
    assert cleaned_2.endswith('}')
    
    print("JSON 清理功能测试通过！\n")

def test_markdown_cleaning():
    """测试 Markdown 清理功能"""
    print("测试 Markdown 清理功能...")
    
    from task.lib.prompt_utils import clean_markdown_output
    
    # 测试用例 1: 带有 markdown 代码块标记的 Markdown
    test_md_1 = """```markdown
# 标题

这是一些内容

## 子标题

- 列表项 1
- 列表项 2
```"""
    
    cleaned_1 = clean_markdown_output(test_md_1)
    print(f"✅ 测试用例 1: 移除了 markdown 代码块标记")
    print(f"   原始长度: {len(test_md_1)}, 清理后长度: {len(cleaned_1)}")
    assert not cleaned_1.startswith('```')
    assert not cleaned_1.endswith('```')
    assert cleaned_1.startswith('# 标题')
    
    # 测试用例 2: 带有普通代码块标记的 Markdown
    test_md_2 = """```
# 销售提案

## 概述

这是一个销售提案的内容。

### 主要优势

1. 优势一
2. 优势二
```"""
    
    cleaned_2 = clean_markdown_output(test_md_2)
    print(f"✅ 测试用例 2: 移除了普通代码块标记")
    assert not cleaned_2.startswith('```')
    assert not cleaned_2.endswith('```')
    assert cleaned_2.startswith('# 销售提案')
    
    print("Markdown 清理功能测试通过！\n")

def test_edge_cases():
    """测试边界情况"""
    print("测试边界情况...")
    
    from task.lib.prompt_utils import (
        clean_html_output, 
        clean_json_output, 
        clean_markdown_output
    )
    
    # 测试空字符串
    assert clean_html_output("") == ""
    assert clean_json_output("") == ""
    assert clean_markdown_output("") == ""
    print("✅ 空字符串测试通过")
    
    # 测试 None
    assert clean_html_output(None) == None
    assert clean_json_output(None) == None
    assert clean_markdown_output(None) == None
    print("✅ None 值测试通过")
    
    # 测试没有代码块标记的内容
    normal_html = "<!DOCTYPE html><html><body>Test</body></html>"
    assert clean_html_output(normal_html) == normal_html
    print("✅ 正常 HTML 测试通过")
    
    normal_json = '{"key": "value"}'
    assert clean_json_output(normal_json) == normal_json
    print("✅ 正常 JSON 测试通过")
    
    normal_md = "# Title\n\nContent"
    assert clean_markdown_output(normal_md) == normal_md
    print("✅ 正常 Markdown 测试通过")
    
    print("边界情况测试通过！\n")

if __name__ == "__main__":
    print("开始测试输出清理功能...\n")
    
    try:
        test_html_cleaning()
        test_json_cleaning()
        test_markdown_cleaning()
        test_edge_cases()
        
        print("🎉 所有测试通过！输出清理功能正常工作。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc() 