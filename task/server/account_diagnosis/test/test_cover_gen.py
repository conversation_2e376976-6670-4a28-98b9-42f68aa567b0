import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..','..')))

from script_for_cover_gen import call_anthropic_api, extract_html

async def test_cover_generation():
    """测试封面生成功能"""
    
    # 测试数据
    test_cases = [
        {
            "style": "工业极简",
            "content": "空运全流程",
            "mediaList": [
                {
                    "mediaType": "image",
                    "mediaPath": "c2fb1c53e70e3831ab0662aba3748a0c/cdc235e450173a9381644d2835dea96d",
                    "mediaUrl": "https://pbs.twimg.com/media/GfeCvLcaQAAw8Pj.jpg"
                }
            ]
        },
        {
            "style": "温馨生活",
            "content": "家居装修小贴士",
            "mediaList": []
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n=== 测试用例 {i} ===")
        print(f"样式: {test_case['style']}")
        print(f"内容: {test_case['content']}")
        print(f"媒体文件数量: {len(test_case['mediaList'])}")
        
        try:
            # 生成封面HTML
            html_result = await call_anthropic_api(
                test_case["style"],
                test_case["content"],
                test_case["mediaList"]
            )
            
            # 保存结果到文件
            filename = f"test_cover_{i}_{test_case['style']}.html"
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(html_result)
            
            print(f"✅ 封面生成成功，已保存到: {filename}")
            print(f"HTML长度: {len(html_result)} 字符")
            
        except Exception as e:
            print(f"❌ 封面生成失败: {str(e)}")

def test_extract_html():
    """测试HTML提取功能"""
    print("\n=== 测试HTML提取功能 ===")
    
    # 测试用例
    test_responses = [
        # 包含HTML代码块的响应
        """这是一个封面设计：

```html
<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body><h1>测试</h1></body>
</html>
```

希望你喜欢！""",
        
        # 包含普通代码块的响应
        """这是封面代码：

```
<!DOCTYPE html>
<html><body>测试内容</body></html>
```""",
        
        # 直接包含HTML的响应
        """<!DOCTYPE html>
<html lang="zh-CN">
<head><title>直接HTML</title></head>
<body><h1>直接HTML测试</h1></body>
</html>""",
        
        # 纯文本响应
        "这是一个纯文本响应，没有HTML代码"
    ]
    
    for i, response in enumerate(test_responses, 1):
        print(f"\n--- 测试用例 {i} ---")
        extracted = extract_html(response)
        print(f"原始响应长度: {len(response)}")
        print(f"提取结果长度: {len(extracted)}")
        print(f"是否包含DOCTYPE: {'<!DOCTYPE html>' in extracted}")
        
        # 保存提取结果
        filename = f"extracted_html_{i}.html"
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(extracted)
        print(f"已保存到: {filename}")

async def main():
    """主测试函数"""
    print("开始测试封面生成功能...")
    
    # 测试HTML提取功能
    test_extract_html()
    
    # 测试封面生成功能（需要API配置）
    print("\n" + "="*50)
    print("注意: 封面生成测试需要配置Claude API")
    print("如果没有配置API，以下测试将会失败")
    print("="*50)
    
    try:
        await test_cover_generation()
    except Exception as e:
        print(f"封面生成测试跳过: {str(e)}")
    
    print("\n测试完成！")

if __name__ == "__main__":
    asyncio.run(main()) 