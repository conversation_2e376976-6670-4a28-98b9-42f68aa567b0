#!/usr/bin/env python3
"""
HTML颜色对比度测试脚本
验证生成的HTML报告是否符合可读性要求
"""

import re
import colorsys
from typing import List, Tuple, Dict

class ColorContrastTester:
    """颜色对比度测试器"""
    
    def __init__(self):
        self.color_mappings = {
            # TailwindCSS颜色映射
            'text-white': '#FFFFFF',
            'text-gray-900': '#111827',
            'text-gray-800': '#1F2937',
            'text-gray-700': '#374151',
            'bg-white': '#FFFFFF',
            'bg-blue-600': '#2E86AB',
            'bg-green-600': '#10B981',
            'bg-amber-500': '#F59E0B',
            'bg-purple-700': '#A23B72',
            'bg-red-500': '#EF4444',
        }
    
    def hex_to_rgb(self, hex_color: str) -> <PERSON><PERSON>[int, int, int]:
        """将十六进制颜色转换为RGB"""
        hex_color = hex_color.lstrip('#')
        rgb_values = [int(hex_color[i:i+2], 16) for i in (0, 2, 4)]
        return (rgb_values[0], rgb_values[1], rgb_values[2])
    
    def get_relative_luminance(self, rgb: Tuple[int, int, int]) -> float:
        """计算相对亮度"""
        def get_component_luminance(component):
            c = component / 255.0
            if c <= 0.03928:
                return c / 12.92
            else:
                return pow((c + 0.055) / 1.055, 2.4)
        
        r, g, b = rgb
        return (0.2126 * get_component_luminance(r) + 
                0.7152 * get_component_luminance(g) + 
                0.0722 * get_component_luminance(b))
    
    def calculate_contrast_ratio(self, color1: str, color2: str) -> float:
        """计算两种颜色的对比度"""
        rgb1 = self.hex_to_rgb(color1)
        rgb2 = self.hex_to_rgb(color2)
        
        lum1 = self.get_relative_luminance(rgb1)
        lum2 = self.get_relative_luminance(rgb2)
        
        # 确保较亮的颜色在分子位置
        if lum1 > lum2:
            return (lum1 + 0.05) / (lum2 + 0.05)
        else:
            return (lum2 + 0.05) / (lum1 + 0.05)
    
    def check_wcag_compliance(self, contrast_ratio: float, level: str = 'AA') -> Dict[str, bool]:
        """检查WCAG合规性"""
        if level == 'AA':
            return {
                'normal_text': contrast_ratio >= 4.5,
                'large_text': contrast_ratio >= 3.0,
                'ui_components': contrast_ratio >= 3.0
            }
        elif level == 'AAA':
            return {
                'normal_text': contrast_ratio >= 7.0,
                'large_text': contrast_ratio >= 4.5,
                'ui_components': contrast_ratio >= 4.5
            }
        return {}
    
    def extract_color_pairs_from_html(self, html_content: str) -> List[Tuple[str, str, str]]:
        """从HTML中提取颜色配对信息"""
        pairs = []
        
        # 查找具有背景色和文字色的元素
        pattern = r'class="[^"]*(?:bg-\w+-\d+|bg-white|bg-black)[^"]*(?:text-\w+-\d+|text-white|text-black)[^"]*"'
        matches = re.findall(pattern, html_content)
        
        for match in matches:
            bg_color = None
            text_color = None
            
            # 提取背景色
            bg_match = re.search(r'bg-(\w+-\d+|\w+)', match)
            if bg_match:
                bg_class = f"bg-{bg_match.group(1)}"
                bg_color = self.color_mappings.get(bg_class)
            
            # 提取文字色
            text_match = re.search(r'text-(\w+-\d+|\w+)', match)
            if text_match:
                text_class = f"text-{text_match.group(1)}"
                text_color = self.color_mappings.get(text_class)
            
            if bg_color and text_color:
                pairs.append((bg_color, text_color, match))
        
        return pairs
    
    def extract_svg_text_colors(self, html_content: str) -> List[Tuple[str, str]]:
        """从SVG中提取文字颜色"""
        svg_texts = []
        
        # 查找SVG text元素
        pattern = r'<text[^>]*fill="([^"]*)"[^>]*>'
        matches = re.findall(pattern, html_content)
        
        for fill_color in matches:
            if fill_color and fill_color != 'currentColor' and fill_color != 'inherit':
                svg_texts.append(('SVG背景', fill_color))
        
        return svg_texts
    
    def test_html_contrast(self, html_content: str) -> Dict:
        """测试HTML的颜色对比度"""
        results = {
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': [],
            'recommendations': []
        }
        
        # 测试CSS类颜色配对
        color_pairs = self.extract_color_pairs_from_html(html_content)
        
        for bg_color, text_color, context in color_pairs:
            results['total_tests'] += 1
            contrast_ratio = self.calculate_contrast_ratio(bg_color, text_color)
            compliance = self.check_wcag_compliance(contrast_ratio)
            
            if compliance['normal_text']:
                results['passed_tests'] += 1
            else:
                results['failed_tests'].append({
                    'background': bg_color,
                    'text': text_color,
                    'contrast_ratio': round(contrast_ratio, 2),
                    'context': context[:100] + '...' if len(context) > 100 else context
                })
        
        # 测试SVG文字颜色
        svg_texts = self.extract_svg_text_colors(html_content)
        for bg_desc, text_color in svg_texts:
            results['total_tests'] += 1
            # 假设SVG在白色背景上
            if text_color == '#FFFFFF':
                # 白色文字需要深色背景
                results['failed_tests'].append({
                    'type': 'SVG',
                    'issue': '白色SVG文字可能在浅色背景上不可见',
                    'text_color': text_color
                })
            else:
                results['passed_tests'] += 1
        
        # 生成建议
        if results['failed_tests']:
            results['recommendations'].extend([
                "检查失败的颜色配对，确保对比度≥4.5:1",
                "为SVG文字添加明确的fill属性",
                "在渐变背景上添加文字阴影或半透明背景",
                "考虑使用更深的文字颜色或更浅的背景颜色"
            ])
        
        return results

def run_contrast_test():
    """运行颜色对比度测试"""
    tester = ColorContrastTester()
    
    # 测试修复后的颜色配对
    test_pairs = [
        ('#FFFFFF', '#111827', '白色背景+深灰文字'),
        ('#1E5A8A', '#FFFFFF', '深蓝背景+白色文字'),
        ('#047857', '#FFFFFF', '深绿背景+白色文字'),
        ('#B45309', '#FFFFFF', '深橙背景+白色文字'),
        ('#A23B72', '#FFFFFF', '紫色背景+白色文字'),
        ('#DC2626', '#FFFFFF', '深红背景+白色文字'),
        ('#EFF6FF', '#1E3A8A', '浅蓝背景+深蓝文字'),
    ]
    
    print("=== 颜色对比度测试结果 ===\n")
    
    for bg, text, desc in test_pairs:
        contrast = tester.calculate_contrast_ratio(bg, text)
        compliance = tester.check_wcag_compliance(contrast)
        
        status = "✅ 通过" if compliance['normal_text'] else "❌ 失败"
        print(f"{desc}: 对比度 {contrast:.2f} - {status}")
        
        if not compliance['normal_text']:
            print(f"   建议: 对比度需要≥4.5，当前仅{contrast:.2f}")
    
    print("\n=== 修复建议 ===")
    print("1. 确保模板中所有颜色配对符合上述测试结果")
    print("2. SVG文字必须明确设置fill属性")
    print("3. 渐变背景添加文字保护")
    print("4. 测试在不同设备和浏览器上的效果")

if __name__ == "__main__":
    run_contrast_test() 