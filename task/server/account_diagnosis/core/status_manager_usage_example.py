"""
Status Manager Usage Example
示例：如何使用更新后的状态管理器处理最终结果
"""

import asyncio
from status_manager import UnifiedProgressTracker, UnifiedTaskStatus

async def example_diagnosis_workflow():
    """示例：完整的诊断工作流程"""
    
    # 初始化进度跟踪器
    tracker = UnifiedProgressTracker(
        task_id="example_task_123",
        user_id="user_456",
        diagnosis_id=789,
        env="test",
        redis_queue="test:q:diagnosis:response",
        verbose=True,
        language="cn"
    )
    
    try:
        # 1. 开始诊断流程
        tracker.update_status(UnifiedTaskStatus.PLANNING, "开始分析您的账号...")
        await asyncio.sleep(1)
        
        # 2. 账号信息分析
        tracker.update_status(UnifiedTaskStatus.CRAWLING_ACCOUNT, "正在获取账号数据...")
        await asyncio.sleep(2)
        
        # 3. 搜索阶段
        tracker.update_status(UnifiedTaskStatus.SEARCHING, "正在搜索相关信息...")
        
        # 搜索进度更新示例
        for i in range(5):
            tracker.update_search_progress(
                search_type="tavily",
                query_text=f"查询 {i+1}",
                results_count=10 + i,
                is_completed=True,
                current_query_index=i,
                total_queries=5
            )
            await asyncio.sleep(0.5)
        
        # 4. 行业分析
        tracker.update_status(UnifiedTaskStatus.ANALYZING_INDUSTRY, "正在分析行业趋势...")
        await asyncio.sleep(2)
        
        # 5. 执行诊断
        tracker.update_status(UnifiedTaskStatus.PERFORMING_DIAGNOSIS, "正在进行深度诊断...")
        await asyncio.sleep(3)
        
        # 6. 生成报告
        tracker.update_status(UnifiedTaskStatus.GENERATING_REPORT, "正在生成诊断报告...")
        await asyncio.sleep(2)
        
        # 7. 准备最终结果
        final_results = {
            "diagnosisHtml": "<!DOCTYPE html><html><head><title>诊断报告</title></head><body><h1>账号诊断报告</h1><p>这是一个示例报告...</p></body></html>",
            "diagnosisReport": {
                "summary": "账号表现良好，但需要在内容质量方面进一步提升。",
                "tags": [
                    {"dimension": "CURRENT STATUS", "status": "良好"},
                    {"dimension": "GROWTH POTENTIAL", "status": "高"}
                ],
                "bottleneck": {
                    "title": "主要瓶颈",
                    "area": [
                        {"title": "内容质量", "title_en": "CONTENT QUALITY", "des": "需要提升内容的原创性"}
                    ]
                },
                "suggestion": [
                    {
                        "title": "内容优化建议",
                        "content": ["提高原创性", "增加互动性", "优化发布时间"]
                    }
                ]
            },
            "marketingProposal": "# 营销建议\n\n## 1. 内容策略\n- 聚焦优质内容创作\n\n## 2. 引流方案\n- 设置个人微信号",
            "marketingProposalHtml": "<html><body><h1>营销建议</h1><h2>内容策略</h2><p>聚焦优质内容创作</p></body></html>"
        }
        
        # 8. 标记任务完成并包含最终结果
        tracker.mark_completed("诊断完成！您的专业报告已准备就绪", final_results)
        
        # 或者，可以分步骤设置最终结果
        # tracker.set_final_results(final_results)
        # tracker.mark_completed("诊断完成！")
        
        # 或者，直接推送最终状态到Redis
        # success = await tracker.push_final_status_to_redis(final_results)
        # print(f"Final status pushed to Redis: {success}")
        
        print("诊断工作流程完成！")
        
    except Exception as e:
        # 处理错误
        tracker.mark_failed(f"诊断过程中发生错误: {str(e)}")
        print(f"诊断失败: {e}")

async def example_simple_usage():
    """示例：简单使用方式"""
    
    tracker = UnifiedProgressTracker(
        task_id="simple_task_456",
        env="test"
    )
    
    # 简单的状态更新
    tracker.update_status(UnifiedTaskStatus.PLANNING)
    tracker.update_status(UnifiedTaskStatus.SEARCHING, "正在搜索...")
    
    # 设置最终结果并完成
    results = {"diagnosisHtml": "<html>简单报告</html>"}
    tracker.mark_completed(final_results=results)

if __name__ == "__main__":
    # 运行示例
    print("运行完整诊断工作流程示例...")
    asyncio.run(example_diagnosis_workflow())
    
    print("\n运行简单使用示例...")
    asyncio.run(example_simple_usage())