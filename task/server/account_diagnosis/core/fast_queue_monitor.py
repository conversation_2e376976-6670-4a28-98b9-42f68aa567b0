"""
Fast Queue Monitor - 专门用于低延迟Redis队列监控
针对blpop操作进行极致优化，最小化消息处理延迟
"""

import asyncio
import json
import logging
import time
from typing import Dict, Optional, Callable, Any
from contextlib import asynccontextmanager

from .optimized_redis_manager import get_redis_connection


class FastQueueMonitor:
    """快速队列监控器 - 专门优化blpop响应速度"""
    
    def __init__(self, queue_name: str, logger: Optional[logging.Logger] = None):
        self.queue_name = queue_name
        self.logger = logger or logging.getLogger(__name__)
        self._shutdown_event = asyncio.Event()
        self._stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'processing_errors': 0,
            'connection_errors': 0,
            'start_time': time.time(),
            'last_message_time': None
        }
    
    async def start_monitoring(self, 
                             message_handler: Callable[[Dict], Any],
                             blpop_timeout: int = 60,
                             max_consecutive_errors: int = 5) -> None:
        """
        开始监控队列
        
        Args:
            message_handler: 消息处理函数，接收解析后的消息字典
            blpop_timeout: blpop超时时间（秒），建议60-120秒
            max_consecutive_errors: 最大连续错误次数
        """
        self.logger.info(f"🚀 开始快速监控队列: {self.queue_name}")
        self.logger.info(f"⏱️  BLPOP超时: {blpop_timeout}秒")
        
        consecutive_errors = 0
        
        while not self._shutdown_event.is_set():
            try:
                # 使用专用连接进行长时间blpop监听
                message_data = await self._fast_blpop(blpop_timeout)
                
                if message_data:
                    # 重置错误计数
                    consecutive_errors = 0
                    self._stats['messages_received'] += 1
                    self._stats['last_message_time'] = time.time()
                    
                    # 立即处理消息，无延迟
                    asyncio.create_task(self._handle_message(message_data, message_handler))
                else:
                    # blpop超时是正常情况，立即继续下一次监听
                    self.logger.debug(f"队列 {self.queue_name} 监听超时，继续监听...")
                    
            except Exception as e:
                consecutive_errors += 1
                error_msg = str(e)
                
                # 智能错误处理
                if any(keyword in error_msg.lower() for keyword in ['connection', 'timeout', 'network']):
                    self.logger.warning(f"网络连接问题 (连续错误: {consecutive_errors}): {error_msg}")
                    self._stats['connection_errors'] += 1
                else:
                    self.logger.error(f"队列监控异常 (连续错误: {consecutive_errors}): {error_msg}")
                
                # 连续错误处理
                if consecutive_errors >= max_consecutive_errors:
                    self.logger.error(f"连续错误达到上限 ({max_consecutive_errors})，暂停30秒...")
                    await asyncio.sleep(30)
                    consecutive_errors = 0
                else:
                    # 短暂等待后重试
                    wait_time = min(consecutive_errors * 0.5, 3)
                    await asyncio.sleep(wait_time)
        
        self.logger.info("✅ 快速队列监控已停止")
    
    async def _fast_blpop(self, timeout: int) -> Optional[Dict]:
        """
        快速blpop操作 - 专门优化的版本
        使用专用连接，最小化延迟
        """
        try:
            async with get_redis_connection() as redis_conn:
                # 直接使用blpop，这是最高效的队列监听方式
                result = await redis_conn.blpop(self.queue_name, timeout=timeout)
                
                if result:
                    _, message_json = result
                    
                    # 快速JSON解析
                    try:
                        if isinstance(message_json, str):
                            message_data = json.loads(message_json)
                            # 处理双重编码的情况
                            if isinstance(message_data, str):
                                message_data = json.loads(message_data)
                        else:
                            message_data = message_json
                        
                        if isinstance(message_data, dict):
                            return message_data
                        else:
                            self.logger.error(f"消息格式错误，不是字典类型: {type(message_data)}")
                            return None
                            
                    except json.JSONDecodeError as e:
                        self.logger.error(f"JSON解析失败: {e}")
                        return None
                else:
                    return None
                    
        except asyncio.TimeoutError:
            # 超时是正常情况
            return None
        except Exception as e:
            # 其他异常向上抛出
            raise e
    
    async def _handle_message(self, message_data: Dict, handler: Callable[[Dict], Any]):
        """
        异步处理消息 - 不阻塞主监听循环
        """
        try:
            # 异步执行消息处理器
            if asyncio.iscoroutinefunction(handler):
                await handler(message_data)
            else:
                # 如果是同步函数，在线程池中执行
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, handler, message_data)
            
            self._stats['messages_processed'] += 1
            
        except Exception as e:
            self._stats['processing_errors'] += 1
            self.logger.error(f"消息处理异常: {e}", exc_info=True)
    
    def stop_monitoring(self):
        """停止监控"""
        self._shutdown_event.set()
        self.logger.info("🛑 请求停止队列监控...")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        current_time = time.time()
        uptime = current_time - self._stats['start_time']
        
        stats = self._stats.copy()
        stats.update({
            'uptime_seconds': uptime,
            'messages_per_second': self._stats['messages_received'] / uptime if uptime > 0 else 0,
            'processing_success_rate': (
                (self._stats['messages_processed'] / self._stats['messages_received']) * 100
                if self._stats['messages_received'] > 0 else 0
            ),
            'last_message_ago_seconds': (
                current_time - self._stats['last_message_time']
                if self._stats['last_message_time'] else None
            )
        })
        
        return stats


class MultiQueueMonitor:
    """多队列监控器 - 同时监控多个队列"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._monitors: Dict[str, FastQueueMonitor] = {}
        self._tasks: Dict[str, asyncio.Task] = {}
        self._shutdown_event = asyncio.Event()
    
    def add_queue(self, queue_name: str, message_handler: Callable[[Dict], Any], 
                  blpop_timeout: int = 60) -> FastQueueMonitor:
        """添加队列监控"""
        if queue_name in self._monitors:
            raise ValueError(f"队列 {queue_name} 已在监控中")
        
        monitor = FastQueueMonitor(queue_name, self.logger)
        self._monitors[queue_name] = monitor
        
        # 创建监控任务
        task = asyncio.create_task(
            monitor.start_monitoring(message_handler, blpop_timeout)
        )
        self._tasks[queue_name] = task
        
        self.logger.info(f"✅ 添加队列监控: {queue_name}")
        return monitor
    
    def remove_queue(self, queue_name: str):
        """移除队列监控"""
        if queue_name in self._monitors:
            # 停止监控
            self._monitors[queue_name].stop_monitoring()
            
            # 取消任务
            if queue_name in self._tasks:
                self._tasks[queue_name].cancel()
                del self._tasks[queue_name]
            
            del self._monitors[queue_name]
            self.logger.info(f"🗑️  移除队列监控: {queue_name}")
    
    async def wait_for_completion(self):
        """等待所有监控任务完成"""
        if self._tasks:
            await asyncio.gather(*self._tasks.values(), return_exceptions=True)
    
    def stop_all(self):
        """停止所有监控"""
        self.logger.info("🛑 停止所有队列监控...")
        for monitor in self._monitors.values():
            monitor.stop_monitoring()
        
        for task in self._tasks.values():
            task.cancel()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有队列的统计信息"""
        return {
            queue_name: monitor.get_stats()
            for queue_name, monitor in self._monitors.items()
        }


# 便捷函数
async def monitor_single_queue(queue_name: str, 
                             message_handler: Callable[[Dict], Any],
                             blpop_timeout: int = 60,
                             logger: Optional[logging.Logger] = None):
    """
    监控单个队列的便捷函数
    
    使用示例:
    ```python
    async def handle_message(message_data: dict):
        print(f"收到消息: {message_data}")
    
    await monitor_single_queue("my_queue", handle_message)
    ```
    """
    monitor = FastQueueMonitor(queue_name, logger)
    await monitor.start_monitoring(message_handler, blpop_timeout)


# 使用示例
if __name__ == "__main__":
    async def example_handler(message_data: dict):
        print(f"处理消息: {message_data}")
        # 模拟处理时间
        await asyncio.sleep(0.1)
    
    async def main():
        # 单队列监控示例
        await monitor_single_queue("test_queue", example_handler)
    
    asyncio.run(main())