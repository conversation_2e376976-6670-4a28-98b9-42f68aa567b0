# Status Manager Updates

## 主要更新内容

### 1. Redis序列化方法更新

- **问题**: 原来使用简单的 `json.dumps()` 进行Redis序列化
- **解决方案**: 现在使用 `safe_redis_serialize_with_validation()` 进行双重JSON序列化
- **影响**: 确保HTML内容中的特殊字符被正确转义，避免JSON解析失败

```python
# 之前
message = json.dumps(status_data, ensure_ascii=False)

# 现在  
message = safe_redis_serialize_with_validation(status_data)
```

### 2. 最终结果合并机制

- **新增功能**: 当任务状态为 `FINISH` 时，自动将最终结果合并到状态响应中
- **符合规范**: 按照 `response.md` 文档要求，FINISH状态包含完整的诊断结果

```python
# 状态数据结构
{
  "taskInfo": {
    "aiTaskStatus": "FINISH",
    "aiTaskProgress": 100,
    ...
  },
  # 最终结果会自动合并到这里
  "diagnosisHtml": "...",
  "diagnosisReport": {...},
  "marketingProposal": "...",
  "marketingProposalHtml": "..."
}
```

### 3. 新增方法

#### `set_final_results(results: Dict[str, Any])`
设置最终结果数据，用于在任务完成时合并到响应中。

#### `mark_completed(custom_message, final_results)`
标记任务完成，支持直接传入最终结果。

#### `prepare_final_status_with_results(final_results)`
准备包含最终结果的完成状态数据。

#### `push_final_status_to_redis(final_results)`
直接推送包含最终结果的完成状态到Redis。

### 4. 消息去重优化

- **问题**: 双重序列化后的消息去重需要特殊处理
- **解决方案**: 更新了 `_batch_push_to_redis()` 方法，正确处理双重序列化的消息

```python
# 处理双重序列化的消息
json_str = json.loads(msg_str)  # 第一次反序列化
msg_json = json.loads(json_str)  # 第二次反序列化
```

## 使用方式

### 基本使用
```python
tracker = UnifiedProgressTracker(
    task_id="task_123",
    user_id="user_456", 
    diagnosis_id=789,
    env="production",
    redis_queue="prod:q:diagnosis:response"
)

# 正常的状态更新
tracker.update_status(UnifiedTaskStatus.SEARCHING, "正在搜索...")

# 完成时包含最终结果
final_results = {
    "diagnosisHtml": "<html>...</html>",
    "diagnosisReport": {...},
    "marketingProposal": "...",
}
tracker.mark_completed("诊断完成！", final_results)
```

### 高级使用
```python
# 分步设置最终结果
tracker.set_final_results({"diagnosisHtml": "..."})
tracker.set_final_results({"diagnosisReport": {...}})
tracker.mark_completed("诊断完成！")

# 直接推送最终状态到Redis
await tracker.push_final_status_to_redis(final_results)
```

## 兼容性

- 保持了与现有代码的完全兼容性
- 所有原有的方法和接口都继续工作
- 新增的功能是可选的，不影响现有功能

## 注意事项

1. **Redis序列化**: 现在使用双重JSON序列化，确保在反序列化时也要进行两次操作
2. **最终结果**: 只有在 `aiTaskStatus="FINISH"` 时才会合并最终结果
3. **错误处理**: 如果序列化失败，会有详细的错误日志
4. **性能**: 双重序列化会略微增加CPU使用，但确保了数据的可靠性

## 测试建议

1. 测试正常的状态更新流程
2. 测试包含HTML内容的最终结果序列化
3. 测试Redis队列的消息格式
4. 验证前端能正确解析双重序列化的消息