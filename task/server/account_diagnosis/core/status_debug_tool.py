#!/usr/bin/env python3
"""
状态管理调试工具
用于分析和诊断状态更新问题
"""

import asyncio
import json
import time
from typing import Dict, List, Optional
from datetime import datetime
from status_manager import (
    UnifiedProgressTracker, UnifiedTaskStatus, GlobalStatusCoordinator,
    StatusManager
)

class StatusDebugTool:
    """状态调试工具"""
    
    def __init__(self):
        self.debug_logs = []
        self.status_timeline = []
        
    def log_debug(self, message: str, level: str = "INFO"):
        """记录调试信息"""
        timestamp = time.time()
        log_entry = {
            "timestamp": timestamp,
            "datetime": datetime.fromtimestamp(timestamp).isoformat(),
            "level": level,
            "message": message
        }
        self.debug_logs.append(log_entry)
        print(f"[{level}] {log_entry['datetime']}: {message}")
    
    def track_status_change(self, task_id: str, old_status: UnifiedTaskStatus, 
                          new_status: UnifiedTaskStatus, old_progress: float, 
                          new_progress: float, module: str, allowed: bool):
        """跟踪状态变化"""
        timeline_entry = {
            "timestamp": time.time(),
            "task_id": task_id,
            "old_status": old_status.value if old_status else None,
            "new_status": new_status.value,
            "old_progress": old_progress,
            "new_progress": new_progress,
            "progress_diff": new_progress - old_progress,
            "module": module,
            "allowed": allowed
        }
        self.status_timeline.append(timeline_entry)
        
        # 分析异常情况
        if not allowed:
            self.log_debug(f"❌ 状态更新被拒绝: {old_status.value if old_status else 'None'} -> {new_status.value} "
                          f"({old_progress:.1f}% -> {new_progress:.1f}%) by {module}", "WARNING")
        elif timeline_entry["progress_diff"] < -5:
            self.log_debug(f"⚠️ 检测到进度倒退: {old_progress:.1f}% -> {new_progress:.1f}% "
                          f"(差值: {timeline_entry['progress_diff']:.1f}%) by {module}", "WARNING")
    
    def analyze_timeline(self) -> Dict:
        """分析状态时间线"""
        if not self.status_timeline:
            return {"error": "没有状态变化记录"}
        
        analysis = {
            "total_updates": len(self.status_timeline),
            "allowed_updates": sum(1 for entry in self.status_timeline if entry["allowed"]),
            "rejected_updates": sum(1 for entry in self.status_timeline if not entry["allowed"]),
            "progress_regressions": [],
            "module_stats": {},
            "status_transitions": [],
            "timeline_summary": []
        }
        
        # 分析进度倒退
        for entry in self.status_timeline:
            if entry["progress_diff"] < -1:  # 超过1%的倒退
                analysis["progress_regressions"].append({
                    "timestamp": entry["timestamp"],
                    "old_progress": entry["old_progress"],
                    "new_progress": entry["new_progress"],
                    "diff": entry["progress_diff"],
                    "module": entry["module"],
                    "allowed": entry["allowed"]
                })
        
        # 分析模块统计
        for entry in self.status_timeline:
            module = entry["module"]
            if module not in analysis["module_stats"]:
                analysis["module_stats"][module] = {
                    "total_attempts": 0,
                    "allowed": 0,
                    "rejected": 0,
                    "avg_progress_change": 0
                }
            
            stats = analysis["module_stats"][module]
            stats["total_attempts"] += 1
            if entry["allowed"]:
                stats["allowed"] += 1
            else:
                stats["rejected"] += 1
        
        # 计算平均进度变化
        for module, stats in analysis["module_stats"].items():
            module_entries = [e for e in self.status_timeline if e["module"] == module and e["allowed"]]
            if module_entries:
                stats["avg_progress_change"] = sum(e["progress_diff"] for e in module_entries) / len(module_entries)
        
        # 生成时间线摘要
        for i, entry in enumerate(self.status_timeline[-10:]):  # 最近10个更新
            analysis["timeline_summary"].append({
                "index": len(self.status_timeline) - 10 + i,
                "time": datetime.fromtimestamp(entry["timestamp"]).strftime("%H:%M:%S"),
                "status": entry["new_status"],
                "progress": f"{entry['new_progress']:.1f}%",
                "module": entry["module"],
                "allowed": "✅" if entry["allowed"] else "❌"
            })
        
        return analysis
    
    def generate_report(self) -> str:
        """生成调试报告"""
        analysis = self.analyze_timeline()
        
        report = []
        report.append("=" * 60)
        report.append("状态管理调试报告")
        report.append("=" * 60)
        report.append(f"生成时间: {datetime.now().isoformat()}")
        report.append("")
        
        # 基本统计
        report.append("📊 基本统计:")
        report.append(f"  总更新次数: {analysis['total_updates']}")
        report.append(f"  允许更新: {analysis['allowed_updates']}")
        report.append(f"  拒绝更新: {analysis['rejected_updates']}")
        report.append(f"  拒绝率: {analysis['rejected_updates']/analysis['total_updates']*100:.1f}%")
        report.append("")
        
        # 进度倒退分析
        if analysis["progress_regressions"]:
            report.append("⚠️ 进度倒退分析:")
            for regression in analysis["progress_regressions"][-5:]:  # 最近5个倒退
                time_str = datetime.fromtimestamp(regression["timestamp"]).strftime("%H:%M:%S")
                report.append(f"  {time_str}: {regression['old_progress']:.1f}% -> {regression['new_progress']:.1f}% "
                            f"(差值: {regression['diff']:.1f}%) by {regression['module']} "
                            f"{'✅' if regression['allowed'] else '❌'}")
        else:
            report.append("✅ 没有检测到进度倒退")
        report.append("")
        
        # 模块统计
        report.append("📈 模块统计:")
        for module, stats in analysis["module_stats"].items():
            success_rate = stats["allowed"] / stats["total_attempts"] * 100
            report.append(f"  {module}:")
            report.append(f"    尝试: {stats['total_attempts']}, 成功: {stats['allowed']}, 失败: {stats['rejected']}")
            report.append(f"    成功率: {success_rate:.1f}%")
            report.append(f"    平均进度变化: {stats['avg_progress_change']:.2f}%")
        report.append("")
        
        # 最近时间线
        if analysis["timeline_summary"]:
            report.append("🕒 最近更新时间线:")
            for entry in analysis["timeline_summary"]:
                report.append(f"  #{entry['index']:2d} {entry['time']} {entry['status']:20s} "
                            f"{entry['progress']:6s} {entry['module']:15s} {entry['allowed']}")
        
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def save_report(self, filename: str = None):
        """保存调试报告"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"status_debug_report_{timestamp}.txt"
        
        report = self.generate_report()
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"📄 调试报告已保存到: {filename}")
        return filename

class DebugProgressTracker(UnifiedProgressTracker):
    """带调试功能的进度跟踪器"""
    
    def __init__(self, *args, debug_tool: StatusDebugTool = None, **kwargs):
        super().__init__(*args, **kwargs)
        self.debug_tool = debug_tool or StatusDebugTool()
        self._last_status = None
        self._last_progress = 0.0
    
    def update_status(self, status: UnifiedTaskStatus, custom_message: Optional[str] = None,
                     step_progress: Optional[float] = None, error_message: Optional[str] = None,
                     source: str = "default") -> None:
        """重写状态更新方法，添加调试跟踪"""
        
        # 记录更新尝试
        new_progress = StatusManager.get_progress_percentage(status)
        self.debug_tool.log_debug(f"尝试更新状态: {self._last_status} -> {status.value} "
                                 f"({self._last_progress:.1f}% -> {new_progress:.1f}%) by {source}")
        
        # 调用原始方法
        super().update_status(status, custom_message, step_progress, error_message, source)
        
        # 检查是否实际更新了
        actual_status = self.progress.current_status
        actual_progress = self.progress.progress_percentage
        
        allowed = (actual_status == status and abs(actual_progress - new_progress) < 1.0)
        
        # 记录到调试工具
        self.debug_tool.track_status_change(
            self.task_id, self._last_status, status,
            self._last_progress, new_progress, source, allowed
        )
        
        # 更新跟踪状态
        if allowed:
            self._last_status = actual_status
            self._last_progress = actual_progress

async def test_debug_tool():
    """测试调试工具"""
    print("🔧 测试状态管理调试工具")
    
    debug_tool = StatusDebugTool()
    
    # 创建调试版本的进度跟踪器
    tracker = DebugProgressTracker(
        task_id="debug_test_123",
        user_id="debug_user",
        diagnosis_id=1,
        env="dev",
        debug_tool=debug_tool,
        verbose=True
    )
    
    # 模拟正常的状态更新序列
    print("\n1. 模拟正常状态更新...")
    tracker.update_status(UnifiedTaskStatus.PLANNING, "开始规划")
    await asyncio.sleep(0.1)
    
    tracker.update_status(UnifiedTaskStatus.CRAWLING_ACCOUNT, "分析账号")
    await asyncio.sleep(0.1)
    
    tracker.update_status(UnifiedTaskStatus.SEARCHING, "开始搜索")
    await asyncio.sleep(0.1)
    
    # 模拟问题场景
    print("\n2. 模拟进度倒退...")
    tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, "执行搜索", source="search_engine")
    await asyncio.sleep(0.1)
    
    # 尝试倒退到更早的状态
    tracker.update_status(UnifiedTaskStatus.PLANNING, "重新规划", source="diagnosis_core")
    await asyncio.sleep(0.1)
    
    # 模拟多模块冲突
    print("\n3. 模拟多模块冲突...")
    tracker.update_status(UnifiedTaskStatus.ANALYZING_INDUSTRY, "行业分析", source="analysis_engine")
    await asyncio.sleep(0.1)
    
    tracker.update_status(UnifiedTaskStatus.SEARCHING, "继续搜索", source="search_pipeline")
    await asyncio.sleep(0.1)
    
    tracker.update_status(UnifiedTaskStatus.PERFORMING_DIAGNOSIS, "执行诊断", source="diagnosis_core")
    await asyncio.sleep(0.1)
    
    # 完成任务
    tracker.mark_completed("测试完成")
    
    # 生成并显示报告
    print("\n" + debug_tool.generate_report())
    
    # 保存报告
    debug_tool.save_report()

if __name__ == "__main__":
    asyncio.run(test_debug_tool())