"""
状态适配器 - 为现有模块提供统一队列接口
使现有代码无需大幅修改即可使用新的统一状态队列
"""
import asyncio
from typing import Optional
from .unified_status_queue import get_unified_queue
from .status_manager import UnifiedTaskStatus
import logging

logger = logging.getLogger(__name__)

class StatusUpdateAdapter:
    """状态更新适配器 - 将现有的状态更新调用转换为统一队列调用"""
    
    def __init__(self, task_id: str, module_name: str = "default"):
        self.task_id = task_id
        self.module_name = module_name
    
    async def update_status_immediate(self, status: UnifiedTaskStatus, 
                                    custom_message: Optional[str] = None,
                                    progress: Optional[int] = None) -> bool:
        """
        立即更新状态 - 适配器接口
        兼容原有的update_status_immediate调用
        """
        try:
            queue = await get_unified_queue()
            
            # 转换状态为API格式
            api_status = UnifiedTaskStatus.to_api_status(status)
            
            # 计算进度 - 从状态管理器获取标准进度值
            if progress is not None:
                final_progress = float(progress)
            else:
                # 使用状态管理器的进度计算方法
                from .status_manager import StatusManager
                final_progress = StatusManager.get_progress_percentage(status)
            
            # 生成消息
            if custom_message:
                message = custom_message
            else:
                message = self._get_default_message(status)
            
            # 提交到队列
            success = await queue.submit_status_update(
                task_id=self.task_id,
                status=api_status,
                progress=float(final_progress),
                module_name=self.module_name,
                message=message
            )
            
            if success:
                logger.debug(f"状态适配器更新成功: {self.module_name} -> {final_progress}%")
            else:
                logger.warning(f"状态适配器更新被拒绝: {self.module_name} -> {final_progress}%")
                
            return success
            
        except Exception as e:
            logger.error(f"状态适配器更新失败: {e}")
            return False
    
    def _get_default_message(self, status: UnifiedTaskStatus) -> str:
        """获取状态的默认消息"""
        status_messages = {
            UnifiedTaskStatus.INIT: "任务初始化中...",
            UnifiedTaskStatus.PROCESSING: "正在处理任务...",
            UnifiedTaskStatus.ANALYZING: "正在分析数据...",
            UnifiedTaskStatus.SEARCHING: "正在搜索相关信息...",
            UnifiedTaskStatus.FINALIZING: "正在生成最终结果...",
            UnifiedTaskStatus.FINISHED: "任务已完成",
            UnifiedTaskStatus.ERROR: "任务执行出错"
        }
        return status_messages.get(status, "正在处理...")

def create_progress_tracker_adapter(task_id: str, module_name: str = "diagnosis_core"):
    """创建进度跟踪器适配器 - 用于替换原有的TaskProgressTracker"""
    return StatusUpdateAdapter(task_id, module_name)

# 异步包装器，用于在同步上下文中调用异步方法
def sync_update_status(adapter: StatusUpdateAdapter, status: UnifiedTaskStatus,
                      custom_message: Optional[str] = None,
                      progress: Optional[int] = None) -> bool:
    """同步状态更新包装器"""
    try:
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(
            adapter.update_status_immediate(status, custom_message, progress)
        )
    except RuntimeError:
        # 如果没有事件循环，创建一个新的
        return asyncio.run(
            adapter.update_status_immediate(status, custom_message, progress)
        )
    except Exception as e:
        logger.error(f"同步状态更新失败: {e}")
        return False