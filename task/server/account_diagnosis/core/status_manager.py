"""
Unified Status Management System
统一状态管理系统 - 合并原有的TaskStatus和TaskProgressTracker功能
"""

import time
import logging
import asyncio
import json
from enum import Enum
from typing import Dict, Optional, Callable, Any, Union, Set, List
from datetime import datetime
from dataclasses import dataclass, field

# Import the proper Redis serialization function
try:
    from task.lib.json_utils import safe_redis_serialize_with_validation
    _SERIALIZATION_AVAILABLE = True
except ImportError:
    _SERIALIZATION_AVAILABLE = False
    # Fallback if import fails
    def safe_redis_serialize_with_validation(data: dict) -> str:
        """Fallback serialization function - double JSON dumps"""
        try:
            # First serialization: Python object -> JSON string
            first_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            # Second serialization: JSON string -> escaped JSON string
            result = json.dumps(first_json, ensure_ascii=False)
            return result
        except Exception as e:
            raise ValueError(f"Failed to serialize data to double JSON format: {e}")


class UnifiedTaskStatus(Enum):
    """统一任务状态枚举 - 支持基础和深度研究模式"""
    
    # 基础状态
    TASK_QUEUED = "TASK_QUEUED"
    PLANNING = "PLANNING"
    CRAWLING_ACCOUNT = "CRAWLING_ACCOUNT"
    SEARCHING = "SEARCHING"
    ANALYZING_INDUSTRY = "ANALYZING_INDUSTRY"
    PERFORMING_DIAGNOSIS = "PERFORMING_DIAGNOSIS"
    GENERATING_REPORT = "GENERATING_REPORT"
    PAGE_RENDERING = "PAGE_RENDERING"
    TASK_COMPLETED = "TASK_COMPLETED"
    TASK_FAILED = "TASK_FAILED"
    
    # 深度研究专用状态
    INITIALIZING_RESEARCH = "INITIALIZING_RESEARCH"
    GENERATING_QUERIES = "GENERATING_QUERIES"
    OPTIMIZING_QUERIES = "OPTIMIZING_QUERIES"
    EXECUTING_SEARCHES = "EXECUTING_SEARCHES"
    PROCESSING_RESULTS = "PROCESSING_RESULTS"
    INDUSTRY_ANALYSIS = "INDUSTRY_ANALYSIS"
    COMPREHENSIVE_ANALYSIS = "COMPREHENSIVE_ANALYSIS"
    FINALIZING_REPORT = "FINALIZING_REPORT"
    
    @classmethod
    def to_api_status(cls, status: 'UnifiedTaskStatus') -> str:
        """将内部状态转换为API状态（RUNNING/FINISH/FAILED）"""
        if status == cls.TASK_COMPLETED:
            return "FINISH"
        elif status == cls.TASK_FAILED:
            return "FAILED"
        else:
            return "RUNNING"


@dataclass
class StatusTransition:
    """状态转换规则"""
    from_status: UnifiedTaskStatus
    to_status: UnifiedTaskStatus
    progress_percentage: float
    estimated_duration: float = 0.0  # 预估耗时(秒)
    priority_level: int = 0  # 优先级：数字越大优先级越高
    execution_phase: str = "default"  # 执行阶段标识
    allow_concurrent: bool = False  # 是否允许与其他状态并发


@dataclass
class StatusUpdate:
    """状态更新请求"""
    status: UnifiedTaskStatus
    custom_message: Optional[str] = None
    step_progress: Optional[float] = None
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    source: str = "default"  # 更新来源标识
    phase: str = "default"  # 执行阶段标识
    priority: int = 0  # 更新优先级


class StatusManager:
    """状态管理器 - 处理状态转换和进度映射"""
    
    # 状态转换映射表 - 修复进度倒退问题
    STATUS_TRANSITIONS = {
        # 基础诊断流程 - 主线流程，高优先级
        UnifiedTaskStatus.TASK_QUEUED: StatusTransition(
            UnifiedTaskStatus.TASK_QUEUED, UnifiedTaskStatus.PLANNING, 1.0, 0.1, 
            priority_level=10, execution_phase="initialization"
        ),
        UnifiedTaskStatus.PLANNING: StatusTransition(
            UnifiedTaskStatus.PLANNING, UnifiedTaskStatus.CRAWLING_ACCOUNT, 3.0, 0.1,
            priority_level=9, execution_phase="initialization"
        ),
        UnifiedTaskStatus.CRAWLING_ACCOUNT: StatusTransition(
            UnifiedTaskStatus.CRAWLING_ACCOUNT, UnifiedTaskStatus.SEARCHING, 8.0, 0.1,
            priority_level=8, execution_phase="data_collection"
        ),
        UnifiedTaskStatus.SEARCHING: StatusTransition(
            UnifiedTaskStatus.SEARCHING, UnifiedTaskStatus.ANALYZING_INDUSTRY, 12.0, 0.1,
            priority_level=7, execution_phase="data_collection", allow_concurrent=True
        ),
        UnifiedTaskStatus.ANALYZING_INDUSTRY: StatusTransition(
            UnifiedTaskStatus.ANALYZING_INDUSTRY, UnifiedTaskStatus.PERFORMING_DIAGNOSIS, 25.0, 25.0,
            priority_level=6, execution_phase="analysis"
        ),
        UnifiedTaskStatus.PERFORMING_DIAGNOSIS: StatusTransition(
            UnifiedTaskStatus.PERFORMING_DIAGNOSIS, UnifiedTaskStatus.GENERATING_REPORT, 70.0, 30.0,
            priority_level=5, execution_phase="analysis"
        ),
        UnifiedTaskStatus.GENERATING_REPORT: StatusTransition(
            UnifiedTaskStatus.GENERATING_REPORT, UnifiedTaskStatus.PAGE_RENDERING, 89.0, 180.0,
            priority_level=4, execution_phase="generation"
        ),
        UnifiedTaskStatus.PAGE_RENDERING: StatusTransition(
            UnifiedTaskStatus.PAGE_RENDERING, UnifiedTaskStatus.TASK_COMPLETED, 97.0, 0.5,
            priority_level=3, execution_phase="finalization"
        ),
        
        # 深度研究流程 - 并行流程，中等优先级，但在特定阶段可以覆盖基础流程
        UnifiedTaskStatus.INITIALIZING_RESEARCH: StatusTransition(
            UnifiedTaskStatus.INITIALIZING_RESEARCH, UnifiedTaskStatus.GENERATING_QUERIES, 12.0, 5.0,
            priority_level=7, execution_phase="deep_research", allow_concurrent=True
        ),
        UnifiedTaskStatus.GENERATING_QUERIES: StatusTransition(
            UnifiedTaskStatus.GENERATING_QUERIES, UnifiedTaskStatus.OPTIMIZING_QUERIES, 16.0, 8.0,
            priority_level=7, execution_phase="deep_research", allow_concurrent=True
        ),
        UnifiedTaskStatus.OPTIMIZING_QUERIES: StatusTransition(
            UnifiedTaskStatus.OPTIMIZING_QUERIES, UnifiedTaskStatus.EXECUTING_SEARCHES, 20.0, 3.0,
            priority_level=7, execution_phase="deep_research", allow_concurrent=True
        ),
        UnifiedTaskStatus.EXECUTING_SEARCHES: StatusTransition(
            UnifiedTaskStatus.EXECUTING_SEARCHES, UnifiedTaskStatus.PROCESSING_RESULTS, 45.0, 15.0,
            priority_level=8, execution_phase="deep_research", allow_concurrent=True  # 搜索执行时优先级提高
        ),
        UnifiedTaskStatus.PROCESSING_RESULTS: StatusTransition(
            UnifiedTaskStatus.PROCESSING_RESULTS, UnifiedTaskStatus.INDUSTRY_ANALYSIS, 55.0, 8.0,
            priority_level=7, execution_phase="deep_research", allow_concurrent=True
        ),
        UnifiedTaskStatus.INDUSTRY_ANALYSIS: StatusTransition(
            UnifiedTaskStatus.INDUSTRY_ANALYSIS, UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS, 65.0, 10.0,
            priority_level=6, execution_phase="deep_research"
        ),
        UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS: StatusTransition(
            UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS, UnifiedTaskStatus.FINALIZING_REPORT, 80.0, 8.0,
            priority_level=6, execution_phase="deep_research"
        ),
        UnifiedTaskStatus.FINALIZING_REPORT: StatusTransition(
            UnifiedTaskStatus.FINALIZING_REPORT, UnifiedTaskStatus.GENERATING_REPORT, 87.0, 5.0,
            priority_level=5, execution_phase="deep_research"
        ),
    }
    
    @classmethod
    def get_progress_percentage(cls, status: UnifiedTaskStatus) -> float:
        """获取状态对应的进度百分比"""
        # 特殊处理完成和失败状态
        if status == UnifiedTaskStatus.TASK_COMPLETED:
            return 100.0
        elif status == UnifiedTaskStatus.TASK_FAILED:
            return 0.0
        
        transition = cls.STATUS_TRANSITIONS.get(status)
        return transition.progress_percentage if transition else 0.0
    
    @classmethod
    def get_estimated_duration(cls, status: UnifiedTaskStatus) -> float:
        """获取状态预估耗时"""
        transition = cls.STATUS_TRANSITIONS.get(status)
        return transition.estimated_duration if transition else 0.0
    
    @classmethod
    def get_next_status(cls, current_status: UnifiedTaskStatus) -> Optional[UnifiedTaskStatus]:
        """获取下一个状态"""
        transition = cls.STATUS_TRANSITIONS.get(current_status)
        return transition.to_status if transition else None
    
    @classmethod
    def get_status_priority(cls, status: UnifiedTaskStatus) -> int:
        """获取状态优先级"""
        transition = cls.STATUS_TRANSITIONS.get(status)
        return transition.priority_level if transition else 0
    
    @classmethod
    def get_status_phase(cls, status: UnifiedTaskStatus) -> str:
        """获取状态所属阶段"""
        transition = cls.STATUS_TRANSITIONS.get(status)
        return transition.execution_phase if transition else "default"
    
    @classmethod
    def can_status_run_concurrent(cls, status: UnifiedTaskStatus) -> bool:
        """检查状态是否允许并发执行"""
        transition = cls.STATUS_TRANSITIONS.get(status)
        return transition.allow_concurrent if transition else False


class GlobalStatusCoordinator:
    """增强版全局状态协调器 - 中央化状态管理，唯一的Redis写入点"""
    _instance = None
    _instance_lock = asyncio.Lock()
    
    def __init__(self):
        self._active_tasks: Dict[str, Dict[str, Any]] = {}  # task_id -> task_info
        self._completed_tasks: Dict[str, float] = {}  # task_id -> completion_timestamp
        self._running_processes: Dict[str, Dict[str, Any]] = {}  # task_id -> process_info (防止多进程并发)
        self._global_lock = asyncio.Lock()
        self._module_priorities = {
            "diagnosis_engine": 10,
            "diagnosis_core": 9,
            "search_pipeline": 7,
            "analysis_engine": 6,
            "service_implementation": 5,
            "progress_tracker": 4,
            "default": 3
        }
        self.logger = logging.getLogger(f"{__name__}.GlobalStatusCoordinator")
        
        # 新增：中央化消息管理
        self._message_sequence = 0
        self._start_time = time.time()
        self._redis_write_queue = asyncio.Queue()
        self._redis_writer_task = None
        
        # 添加Redis可用性检测和备用队列
        self._redis_available = True
        self._last_redis_check = 0
        self._fallback_queue: Dict[str, List[Dict]] = {}
        self._redis_check_interval = 30  # 30秒检查一次Redis可用性
        
        self._start_redis_writer()
    
    @classmethod
    async def get_instance(cls):
        """获取全局状态协调器单例"""
        if not cls._instance:
            async with cls._instance_lock:
                if not cls._instance:
                    cls._instance = cls()
        return cls._instance
    
    def _start_redis_writer(self):
        """启动后台Redis写入任务"""
        try:
            import asyncio
            loop = asyncio.get_running_loop()
            self._redis_writer_task = loop.create_task(self._background_redis_writer())
        except RuntimeError:
            # 如果没有运行的事件循环，延迟启动
            self._redis_writer_task = None
    
    async def _background_redis_writer(self):
        """后台Redis写入任务 - 批量处理消息确保顺序"""
        while True:
            try:
                messages = []
                
                # 收集一批消息（最多等待5ms，大幅减少延迟）
                try:
                    message = await asyncio.wait_for(self._redis_write_queue.get(), timeout=0.005)
                    messages.append(message)
                    
                    # 尝试收集更多消息（非阻塞）
                    while not self._redis_write_queue.empty() and len(messages) < 10:
                        try:
                            message = self._redis_write_queue.get_nowait()
                            messages.append(message)
                        except asyncio.QueueEmpty:
                            break
                            
                except asyncio.TimeoutError:
                    # No messages available, continue immediately without delay
                    continue
                
                # 批量写入Redis
                if messages:
                    await self._batch_write_to_redis(messages)
                    
            except Exception as e:
                self.logger.error(f"Background Redis writer error: {e}")
                await asyncio.sleep(0.01)  # Reduced error recovery delay
    
    async def _batch_write_to_redis(self, messages: list):
        """批量写入Redis，确保消息顺序，包含Redis可用性检测和备用机制"""
        try:
            # 检查Redis可用性
            if not await self._check_redis_availability():
                # Redis不可用，存储到备用队列
                for message in messages:
                    task_id = message.get("taskInfo", {}).get("taskId", "unknown")
                    await self._store_to_fallback_queue(task_id, message)
                self.logger.warning(f"Redis不可用，{len(messages)}条消息已存储到备用队列")
                return
            
            # 按序列号排序确保顺序
            sorted_messages = sorted(messages, 
                key=lambda m: m.get("taskInfo", {}).get("sequence", 0))
            
            # 导入Redis连接（延迟导入避免循环依赖）
            try:
                # 修复导入路径 - 从当前目录的上级目录导入
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                from base_async_service import get_redis_connection
                async with get_redis_connection() as redis_conn:
                    for message in sorted_messages:
                        # 使用正确的序列化方法
                        serialized = safe_redis_serialize_with_validation(message)
                        
                        # 从消息中提取队列名
                        env = message.get("taskInfo", {}).get("env", "dev")
                        queue_name = f"{env}:q:diagnosis:response"
                        
                        await redis_conn.lpush(queue_name, serialized)
                    
                    self.logger.debug(f"✅ 批量写入 {len(messages)} 条消息到Redis")
                    
                    # Redis恢复后，尝试刷新备用队列
                    if not self._redis_available:
                        self._redis_available = True
                        self.logger.info("Redis连接已恢复，开始刷新备用队列")
                        await self._flush_all_fallback_queues()
                    
            except ImportError:
                self.logger.error("无法导入Redis连接，跳过Redis写入")
                
        except Exception as e:
            if "Redis Cluster cannot be connected" in str(e) or "Timeout connecting" in str(e):
                self.logger.warning(f"Redis连接超时，消息存储到备用队列: {e}")
                self._redis_available = False
                # 存储到备用队列
                for message in messages:
                    task_id = message.get("taskInfo", {}).get("taskId", "unknown")
                    await self._store_to_fallback_queue(task_id, message)
            else:
                self.logger.error(f"批量Redis写入失败: {e}")
    
    async def _check_redis_availability(self) -> bool:
        """检查Redis可用性"""
        try:
            current_time = time.time()
            # 每30秒检查一次Redis可用性
            if current_time - self._last_redis_check < self._redis_check_interval:
                return self._redis_available
            
            self._last_redis_check = current_time
            
            # 尝试简单的Redis操作
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                from base_async_service import get_redis_connection
                
                async with get_redis_connection() as redis_conn:
                    await asyncio.wait_for(redis_conn.ping(), timeout=5.0)
                
                if not self._redis_available:
                    self.logger.info("Redis连接已恢复")
                    self._redis_available = True
                    # 尝试刷新所有备用队列
                    await self._flush_all_fallback_queues()
                
                return True
                
            except Exception as e:
                if self._redis_available:
                    self.logger.warning(f"Redis连接检查失败，切换到备用模式: {e}")
                    self._redis_available = False
                return False
                
        except Exception as e:
            self.logger.error(f"Redis可用性检查异常: {e}")
            return False
    
    async def _store_to_fallback_queue(self, task_id: str, message: Dict):
        """将消息存储到备用队列"""
        try:
            if task_id not in self._fallback_queue:
                self._fallback_queue[task_id] = []
            
            self._fallback_queue[task_id].append(message)
            
            # 限制备用队列大小，避免内存溢出
            if len(self._fallback_queue[task_id]) > 50:
                self._fallback_queue[task_id] = self._fallback_queue[task_id][-50:]
            
            self.logger.debug(f"消息已存储到备用队列: {task_id}")
        except Exception as e:
            self.logger.error(f"存储到备用队列失败: {e}")
    
    async def _flush_fallback_queue(self, task_id: str):
        """当Redis恢复时，刷新指定任务的备用队列到Redis"""
        try:
            if task_id in self._fallback_queue and self._fallback_queue[task_id]:
                messages = self._fallback_queue[task_id].copy()
                self.logger.info(f"Redis恢复，刷新备用队列到Redis: {task_id} ({len(messages)} 条消息)")
                
                # 直接调用Redis写入，但不再检查可用性（避免递归）
                await self._direct_write_to_redis(messages)
                
                # 清空已刷新的消息
                del self._fallback_queue[task_id]
                
        except Exception as e:
            self.logger.error(f"刷新备用队列失败: {e}")
    
    async def _flush_all_fallback_queues(self):
        """刷新所有备用队列"""
        try:
            task_ids = list(self._fallback_queue.keys())
            for task_id in task_ids:
                await self._flush_fallback_queue(task_id)
        except Exception as e:
            self.logger.error(f"刷新所有备用队列失败: {e}")
    
    async def _direct_write_to_redis(self, messages: list):
        """直接写入Redis，不进行可用性检查（用于备用队列刷新）"""
        try:
            # 按序列号排序确保顺序
            sorted_messages = sorted(messages, 
                key=lambda m: m.get("taskInfo", {}).get("sequence", 0))
            
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            from base_async_service import get_redis_connection
            
            async with get_redis_connection() as redis_conn:
                for message in sorted_messages:
                    # 使用正确的序列化方法
                    serialized = safe_redis_serialize_with_validation(message)
                    
                    # 从消息中提取队列名
                    env = message.get("taskInfo", {}).get("env", "dev")
                    queue_name = f"{env}:q:diagnosis:response"
                    
                    await redis_conn.lpush(queue_name, serialized)
                
                self.logger.debug(f"✅ 直接写入 {len(messages)} 条消息到Redis")
                
        except Exception as e:
            self.logger.error(f"直接Redis写入失败: {e}")
            raise  # 重新抛出异常，让调用者知道失败了
    
    async def update_task_status_centralized(self, task_id: str, status: UnifiedTaskStatus, 
                                           module_name: str = "default", custom_message: str = None,
                                           user_id: str = None, diagnosis_id: int = None, 
                                           env: str = "dev", progress_override: int = None) -> bool:
        """中央化状态更新方法 - 唯一的状态更新入口点"""
        try:
            # 1. 通过现有协调逻辑检查是否允许更新
            allowed = await self.coordinate_status_update(task_id, status, module_name, custom_message)
            if not allowed:
                return False
            
            # 2. 生成有序消息
            async with self._global_lock:
                self._message_sequence += 1
                current_time = time.time()
                
                # 计算进度
                progress = progress_override if progress_override is not None else StatusManager.get_progress_percentage(status)
                
                # 构建标准消息格式
                message = {
                    "taskInfo": {
                        "env": env,
                        "taskId": task_id,
                        "userId": user_id,
                        "diagnosisId": diagnosis_id,
                        "aiTaskStatus": UnifiedTaskStatus.to_api_status(status),
                        "aiTaskMsg": custom_message or StatusMessageManager.get_message(status, "en"),
                        "aiTaskMsgCN": custom_message or StatusMessageManager.get_message(status, "cn"),
                        "aiTaskProgress": int(progress),
                        "timestamp": datetime.fromtimestamp(current_time).isoformat(),
                        "sequence": self._message_sequence,  # 全局序列号
                        "elapsed_time": current_time - self._start_time,
                        "module_source": module_name
                    }
                }
            
            # 3. 加入Redis写入队列
            await self._redis_write_queue.put(message)
            
            # 4. 确保Redis写入任务正在运行
            if not self._redis_writer_task or self._redis_writer_task.done():
                self._start_redis_writer()
            
            self.logger.debug(f"✅ 状态更新已加入队列: {task_id} -> {status.value} (seq: {self._message_sequence})")
            return True
            
        except Exception as e:
            self.logger.error(f"中央化状态更新失败: {e}")
            return False
    
    async def register_task(self, task_id: str, module_name: str = "default"):
        """注册任务到全局协调器"""
        # Note: This method should be called when the global lock is already held
        
        # 检查任务是否已经完成过
        if task_id in self._completed_tasks:
            completion_time = self._completed_tasks[task_id]
            # self.logger.warning(f"[DEBUG] 任务已完成，拒绝重新注册: {task_id} (完成时间: {completion_time})")
            # Don't add to _active_tasks if already completed
            return
        
        current_time = time.time()
        self._active_tasks[task_id] = {
            "current_status": UnifiedTaskStatus.TASK_QUEUED,
            "start_time": current_time,
            "last_update_time": current_time,
            "last_module": module_name,
            "progress": 0.0,
            "is_completed": False,
            "update_count": 0,
            "rejected_updates": 0
        }
        self.logger.debug(f"注册任务到全局协调器: {task_id} by {module_name}")
    
    async def coordinate_status_update(self, task_id: str, status: UnifiedTaskStatus, 
                                     module_name: str = "default", custom_message: Optional[str] = None) -> bool:
        """协调状态更新 - 决定是否允许更新，增强版防止冲突"""
        # self.logger.warning(f"[DEBUG] coordinate_status_update started: task_id={task_id}, status={status.value}, module={module_name}")
        
        try:
            async with self._global_lock:
                # 特殊处理：如果是default模块且存在更高优先级的模块正在更新，则延迟处理
                if module_name == "default" and task_id in self._active_tasks:
                    task_info = self._active_tasks[task_id]
                    last_module = task_info.get("last_module", "")
                    
                    # 如果最后更新的是高优先级模块（如diagnosis_core），给它一些时间完成
                    if last_module in ["diagnosis_core", "search_pipeline", "analysis_engine"]:
                        time_since_last_update = time.time() - task_info.get("last_update_time", 0)
                        if time_since_last_update < 2.0:  # 2秒内不允许default模块覆盖
                            self.logger.debug(f"延迟default模块更新，等待{last_module}模块完成 (已等待{time_since_last_update:.1f}s)")
                            return False
                # self.logger.warning(f"[DEBUG] Acquired global lock for {task_id}")
                
                if task_id not in self._active_tasks:
                    # self.logger.warning(f"[DEBUG] Task not registered, registering: {task_id}")
                    await self.register_task(task_id, module_name)
                    # self.logger.warning(f"[DEBUG] Task registered successfully: {task_id}")
                    
                    # Check if registration was rejected due to completed task
                    if task_id not in self._active_tasks:
                        # self.logger.warning(f"[DEBUG] Task registration rejected - task already completed: {task_id}")
                        return False
                
                task_info = self._active_tasks[task_id]
                # self.logger.warning(f"[DEBUG] Got task info: {task_info}")
                
                # 如果任务已完成，禁止后续更新
                if task_info.get("is_completed", False):
                    # self.logger.warning(f"[DEBUG] Task completed, rejecting update: {task_id}")
                    return False
                
                # 检查任务是否运行时间过长（紧急停止机制）
                task_runtime = time.time() - task_info.get("start_time", time.time())
                if task_runtime > 600:  # 10分钟超时
                    self.logger.error(f"[EMERGENCY] 任务运行时间过长 ({task_runtime:.1f}秒)，强制完成: {task_id}")
                    task_info["is_completed"] = True
                    task_info["current_status"] = UnifiedTaskStatus.TASK_FAILED
                    task_info["error_message"] = "任务超时，自动终止"
                    return False
                
                # 检查模块优先级
                current_module_priority = self._module_priorities.get(task_info["last_module"], 5)
                new_module_priority = self._module_priorities.get(module_name, 5)
                # self.logger.warning(f"[DEBUG] Module priorities: current={current_module_priority}, new={new_module_priority}")
                
                # 获取进度信息
                current_status = task_info["current_status"]
                new_progress = StatusManager.get_progress_percentage(status)
                current_progress = task_info["progress"]
                # self.logger.warning(f"[DEBUG] Progress info: current={current_progress}%, new={new_progress}%, current_status={current_status.value}")
                
                # 决策逻辑
                # self.logger.warning(f"[DEBUG] Calling _should_allow_update")
                should_allow = self._should_allow_update(
                    current_status, status, current_progress, new_progress,
                    current_module_priority, new_module_priority, module_name
                )
                # self.logger.warning(f"[DEBUG] Decision made: should_allow={should_allow}")
                
                if should_allow:
                    # 更新任务信息
                    task_info["current_status"] = status
                    task_info["last_update_time"] = time.time()
                    task_info["last_module"] = module_name
                    task_info["progress"] = new_progress
                    task_info["update_count"] = task_info.get("update_count", 0) + 1
                    
                    # 标记完成状态 - 只有来自正确模块的真正完成状态才算完成
                    if status in [UnifiedTaskStatus.TASK_COMPLETED, UnifiedTaskStatus.TASK_FAILED]:
                        # 只有来自diagnosis_core、immediate或者达到失败状态时才真正标记为完成
                        if (module_name in ["diagnosis_core", "immediate"] or 
                            status == UnifiedTaskStatus.TASK_FAILED):
                            task_info["is_completed"] = True
                            task_runtime = time.time() - task_info.get("start_time", time.time())
                            self.logger.info(f"任务正式标记为完成: {task_id} -> {status.value} by {module_name} (运行时间: {task_runtime:.1f}秒)")
                        else:
                            # 其他模块的完成信号先不标记为真正完成，等待diagnosis_core确认
                            self.logger.info(f"收到非主模块完成信号: {task_id} -> {status.value} by {module_name}，等待主模块确认")
                        # self.logger.warning(f"[DEBUG] Task marked as completed: {task_id} -> {status.value} (运行时间: {task_runtime:.1f}秒, 更新次数: {task_info['update_count']})")
                    
                    # self.logger.warning(f"[DEBUG] 允许状态更新: {task_id} -> {status.value} ({new_progress}%) by {module_name}")
                    return True
                else:
                    task_info["rejected_updates"] = task_info.get("rejected_updates", 0) + 1
                    # self.logger.warning(f"[DEBUG] 拒绝状态更新: {task_id} -> {status.value} ({new_progress}%) by {module_name} - current_progress={current_progress}%, current_status={current_status.value} (拒绝次数: {task_info['rejected_updates']})")
                    
                    # 如果拒绝次数过多，可能存在问题
                    if task_info["rejected_updates"] > 50:
                        self.logger.error(f"[WARNING] 任务 {task_id} 被拒绝更新次数过多 ({task_info['rejected_updates']}次)，可能存在循环问题")
                    
                    return False
                    
        except Exception as e:
            # self.logger.error(f"[DEBUG] Exception in coordinate_status_update: {e}", exc_info=True)
            self.logger.error(f"Exception in coordinate_status_update: {e}", exc_info=True)
            return True  # Fallback to allow update
    
    def _should_allow_update(self, current_status: UnifiedTaskStatus, new_status: UnifiedTaskStatus,
                           current_progress: float, new_progress: float,
                           current_priority: int, new_priority: int, module_name: str) -> bool:
        """判断是否允许状态更新 - 增强版，解决进度冲突问题"""
        
        # 规则1: 总是允许完成和失败状态
        if new_status in [UnifiedTaskStatus.TASK_COMPLETED, UnifiedTaskStatus.TASK_FAILED]:
            return True
        
        # 规则2: 相同状态的消息更新总是允许
        if new_status == current_status:
            return True
        
        # 计算进度差值
        progress_diff = new_progress - current_progress
        
        # 规则3: 前进进度总是允许
        if progress_diff >= 0:
            return True
        
        # 规则4: diagnosis_core 模块拥有最高优先级，允许更大的倒退幅度
        if module_name == "diagnosis_core":
            if progress_diff >= -15.0:  # diagnosis_core 允许最多15%的倒退
                self.logger.info(f"diagnosis_core 模块进度更新: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            else:
                self.logger.warning(f"diagnosis_core 模块被进度保护拒绝: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return False
        
        # 规则5: 小幅倒退（< 3%）通常是可接受的
        if progress_diff >= -3.0:
            return True
        
        # 规则6: 中等倒退（3-8%）需要额外条件
        if progress_diff >= -8.0:
            # 6a: 高优先级模块允许中等倒退
            if new_priority > current_priority:
                self.logger.info(f"高优先级模块进度倒退: {current_progress:.1f}% -> {new_progress:.1f}% (优先级: {new_priority} > {current_priority})")
                return True
            
            # 6b: 早期阶段（< 25%）允许较大的调整
            if current_progress < 25.0:
                self.logger.info(f"早期阶段进度调整: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            
            # 6c: 如果是状态回退到更早的阶段，可能是合理的流程调整
            current_stage = self._get_status_stage(current_status)
            new_stage = self._get_status_stage(new_status)
            if new_stage < current_stage and progress_diff >= -6.0:
                self.logger.info(f"状态回退调整: {current_status.value} -> {new_status.value} (差值: {progress_diff:.1f}%)")
                return True
            
            self.logger.warning(f"拒绝中等进度倒退: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%) from {module_name}")
            return False
        
        # 规则7: 大幅倒退（8-25%）需要特殊条件
        if progress_diff >= -25.0:
            # 7a: 早期阶段（< 30%）允许较大调整
            if current_progress < 30.0:
                self.logger.info(f"早期阶段大幅调整: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            
            # 7b: 高优先级模块允许大幅倒退
            if new_priority > current_priority:
                self.logger.info(f"高优先级模块大幅倒退: {current_progress:.1f}% -> {new_progress:.1f}% (优先级: {new_priority} > {current_priority})")
                return True
            
            # 7c: 如果是从很高进度回到合理的中间阶段（可能是流程重置）
            if current_progress > 60.0 and new_progress >= 15.0:
                self.logger.info(f"高进度流程重置: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            
            self.logger.warning(f"拒绝大幅进度倒退: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%) from {module_name}")
            return False
        
        # 规则8: 巨大倒退（> 25%）需要非常特殊的条件
        if progress_diff >= -40.0:
            # 8a: 只在极早期阶段（< 20%）允许
            if current_progress < 20.0:
                self.logger.info(f"极早期阶段巨大调整: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            
            # 8b: diagnosis_core模块的特殊权限
            if module_name == "diagnosis_core" and new_progress >= 10.0:
                self.logger.info(f"diagnosis_core模块巨大倒退: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%)")
                return True
            
            self.logger.warning(f"拒绝巨大进度倒退: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%) from {module_name}")
            return False
        
        # 规则9: 极端倒退（> 40%）几乎总是拒绝
        self.logger.error(f"拒绝极端进度倒退: {current_progress:.1f}% -> {new_progress:.1f}% (差值: {progress_diff:.1f}%) from {module_name}")
        return False
    
    def _get_status_stage(self, status: UnifiedTaskStatus) -> int:
        """获取状态对应的阶段编号，用于判断状态回退是否合理"""
        stage_mapping = {
            UnifiedTaskStatus.TASK_QUEUED: 0,
            UnifiedTaskStatus.PLANNING: 1,
            UnifiedTaskStatus.CRAWLING_ACCOUNT: 2,
            UnifiedTaskStatus.SEARCHING: 3,
            UnifiedTaskStatus.INITIALIZING_RESEARCH: 4,
            UnifiedTaskStatus.EXECUTING_SEARCHES: 5,
            UnifiedTaskStatus.PROCESSING_RESULTS: 6,
            UnifiedTaskStatus.ANALYZING_INDUSTRY: 7,
            UnifiedTaskStatus.PERFORMING_DIAGNOSIS: 8,
            UnifiedTaskStatus.GENERATING_REPORT: 9,
            UnifiedTaskStatus.PAGE_RENDERING: 10,
            UnifiedTaskStatus.TASK_COMPLETED: 11,
            UnifiedTaskStatus.TASK_FAILED: 11
        }
        return stage_mapping.get(status, 5)  # 默认返回中间阶段
    
    async def cleanup_task(self, task_id: str):
        """清理已完成的任务"""
        async with self._global_lock:
            if task_id in self._active_tasks:
                # 记录任务完成时间到completed_tasks
                self._completed_tasks[task_id] = time.time()
                
                # 从active_tasks中移除
                del self._active_tasks[task_id]
                self.logger.debug(f"任务已清理并标记为已完成: {task_id}")
            
            # 清理过期的已完成任务记录（超过1小时）
            current_time = time.time()
            expired_tasks = [
                tid for tid, completion_time in self._completed_tasks.items()
                if current_time - completion_time > 3600  # 1小时 = 3600秒
            ]
            for expired_task_id in expired_tasks:
                del self._completed_tasks[expired_task_id]
                self.logger.debug(f"清理过期的已完成任务记录: {expired_task_id}")

    async def force_update_status(self, task_id: str, status: str, progress: float, 
                                message: str, module_name: str) -> bool:
        """
        强制更新状态，跳过冲突检测 - 优化为非阻塞版本
        专用于统一队列系统调用，因为队列已经处理了冲突
        """
        try:
            # 转换为UnifiedTaskStatus
            if isinstance(status, str):
                # 从API状态转换
                unified_status = None
                for us in UnifiedTaskStatus:
                    if UnifiedTaskStatus.to_api_status(us) == status:
                        unified_status = us
                        break
                if unified_status is None:
                    self.logger.error(f"无法转换状态: {status}")
                    return False
            else:
                unified_status = status
            
            # 快速更新本地状态（最小锁范围）
            async with self._global_lock:
                task_info = self._active_tasks.get(task_id, {})
                task_info.update({
                    "current_status": unified_status,
                    "progress": progress,
                    "custom_message": message,
                    "last_module": module_name,
                    "last_update_time": time.time()
                })
                self._active_tasks[task_id] = task_info
            
            # 异步发送到Redis等外部系统（无锁，避免阻塞）
            asyncio.create_task(self._send_status_update_async(task_id, unified_status, message, progress))
            
            self.logger.debug(f"强制状态更新成功: {task_id} -> {progress}% ({module_name})")
            return True
            
        except Exception as e:
            self.logger.error(f"强制状态更新失败: {e}")
            return False
    
    async def _send_status_update(self, task_id: str, status: UnifiedTaskStatus, message: str, progress: float):
        """发送状态更新到外部系统"""
        try:
            # 转换为API状态格式
            api_status = UnifiedTaskStatus.to_api_status(status)
            
            # 构建状态更新数据
            status_data = {
                "task_id": task_id,
                "status": api_status,
                "progress": progress,
                "message": message,
                "timestamp": time.time()
            }
            
            # 发送到Redis等外部系统
            await self._push_to_redis(task_id, api_status, message, progress)
            
            self.logger.debug(f"状态更新已发送到外部系统: {task_id} -> {api_status} ({progress}%)")
            
        except Exception as e:
            self.logger.error(f"发送状态更新到外部系统失败: {e}")
    
    async def _send_status_update_async(self, task_id: str, status: UnifiedTaskStatus, message: str, progress: float):
        """异步发送状态更新到外部系统（非阻塞版本）"""
        try:
            # 转换为API状态格式
            api_status = UnifiedTaskStatus.to_api_status(status)
            
            # 构建状态更新数据
            status_data = {
                "task_id": task_id,
                "status": api_status,
                "progress": progress,
                "message": message,
                "timestamp": time.time()
            }
            
            # 异步发送到Redis等外部系统
            await self._push_to_redis(task_id, api_status, message, progress)
            
            self.logger.debug(f"异步状态更新已发送: {task_id} -> {api_status} ({progress}%)")
            
        except Exception as e:
            self.logger.error(f"异步发送状态更新失败: {e}")

    async def _push_to_redis(self, task_id: str, api_status: str, message: str, progress: float):
        """推送状态更新到Redis队列"""
        try:
            # 尝试不同的导入路径
            try:
                from ..simple_redis_manager import get_simple_redis_connection
            except ImportError:
                try:
                    from task.server.account_diagnosis.simple_redis_manager import get_simple_redis_connection
                except ImportError:
                    import sys
                    import os
                    sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                    from simple_redis_manager import get_simple_redis_connection
            
            # 构建Redis消息，使用与TaskProgressTracker相同的格式
            redis_data = {
                "task_id": task_id,
                "taskInfo": {
                    "aiTaskStatus": api_status,
                    "aiTaskProgress": progress,
                    "aiTaskMessage": message,
                    "updateTime": datetime.now().isoformat()
                }
            }
            
            # 序列化消息
            if _SERIALIZATION_AVAILABLE:
                serialized = safe_redis_serialize_with_validation(redis_data)
            else:
                serialized = json.dumps(redis_data, ensure_ascii=False)
            
            # 推送到Redis队列
            queue_name = f"q:diagnosis:response"
            async with get_simple_redis_connection() as redis_conn:
                await redis_conn.lpush(queue_name, serialized)
            
            self.logger.debug(f"状态更新已推送到Redis队列 {queue_name}: {task_id} -> {progress}%")
            
        except Exception as e:
            if "Redis Cluster cannot be connected" in str(e) or "Timeout connecting" in str(e):
                self.logger.warning(f"Redis连接超时，跳过此次推送: {e}")
            else:
                self.logger.error(f"推送状态到Redis失败: {e}")


class StatusSequencer:
    """状态序列器 - 智能处理并发状态更新"""
    
    def __init__(self, logger: logging.Logger):
        self.logger = logger
        self._pending_updates: List[StatusUpdate] = []
        self._current_phase = "initialization"
        self._phase_lock = asyncio.Lock()
        self._update_lock = asyncio.Lock()
        self._last_processed_update: Optional[StatusUpdate] = None
        self._global_coordinator: Optional[GlobalStatusCoordinator] = None
        
    async def should_process_update(self, update: StatusUpdate, current_status: UnifiedTaskStatus, 
                                  current_progress: float) -> tuple[bool, str]:
        """
        智能判断是否应该处理状态更新
        
        Returns:
            (should_process, reason)
        """
        async with self._update_lock:
            current_priority = StatusManager.get_status_priority(current_status)
            update_priority = StatusManager.get_status_priority(update.status)
            update_progress = StatusManager.get_progress_percentage(update.status)
            
            # 规则1: 总是允许失败和完成状态
            if update.status in [UnifiedTaskStatus.TASK_FAILED, UnifiedTaskStatus.TASK_COMPLETED]:
                return True, "终止状态总是被允许"
            
            # 规则2: 如果是相同状态的消息更新，总是允许
            if update.status == current_status and update.custom_message:
                return True, "相同状态的消息更新"
            
            # 规则3: 检查进度是否严重倒退（超过5%）
            if update_progress < current_progress - 5.0:
                # 允许高优先级状态覆盖低优先级状态，但不允许从高进度跳到低进度的深度研究状态
                if update_priority > current_priority and not (current_progress > 50 and update_progress < 30):
                    return True, f"高优先级状态覆盖 (优先级 {update_priority} > {current_priority})"
                else:
                    return False, f"进度严重倒退被拒绝 ({update_progress:.1f}% < {current_progress:.1f}%)"
            
            # 规则4: 检查时间序列 - 如果更新时间过旧，可能拒绝
            if self._last_processed_update and update.timestamp < self._last_processed_update.timestamp - 2.0:
                # 但如果是高优先级更新，仍然允许
                if update_priority > current_priority:
                    return True, f"高优先级的延迟更新被允许"
                else:
                    return False, "更新时间过旧被拒绝"
            
            # 规则5: 阶段兼容性检查
            current_phase = StatusManager.get_status_phase(current_status)
            update_phase = StatusManager.get_status_phase(update.status)
            
            # 同一阶段内的更新通常允许
            if current_phase == update_phase:
                return True, f"同一阶段内的更新 ({update_phase})"
            
            # 跨阶段更新需要检查优先级
            if update_phase != current_phase:
                # 进入新阶段的更新，如果优先级合适就允许
                if update_priority >= current_priority or update_progress > current_progress:
                    self._current_phase = update_phase
                    return True, f"进入新阶段 ({current_phase} -> {update_phase})"
                else:
                    return False, f"跨阶段更新优先级不足"
            
            # 规则6: 并发状态检查
            if StatusManager.can_status_run_concurrent(update.status):
                return True, "并发状态更新被允许"
            
            # 规则7: 默认进度前进检查
            if update_progress >= current_progress:
                return True, f"进度前进 ({current_progress:.1f}% -> {update_progress:.1f}%)"
            
            # 规则8: 轻微进度倒退但有合理原因
            if update_progress >= current_progress - 1.5:  # 允许1.5%的轻微倒退
                return True, f"轻微进度调整 ({current_progress:.1f}% -> {update_progress:.1f}%)"
            
            # 默认拒绝
            return False, f"状态更新被拒绝 (进度: {update_progress:.1f}%, 优先级: {update_priority})"
    
    def mark_update_processed(self, update: StatusUpdate):
        """标记更新已处理"""
        self._last_processed_update = update
        self.logger.debug(f"标记更新已处理: {update.status.value} at {update.timestamp}")


class StatusMessageManager:
    """状态消息管理器 - 支持国际化和详细模式"""
    
    STATUS_MESSAGES = {
        UnifiedTaskStatus.TASK_QUEUED: {
            "cn": "任务已创建，正在等待处理...",
            "en": "Task created and waiting to be processed...",
            "cn_verbose": "系统已接收您的诊断请求，正在队列中排队等待处理",
            "en_verbose": "Your diagnosis request has been received and is currently queued for processing"
        },
        UnifiedTaskStatus.PLANNING: {
            "cn": "收到指令，正在规划任务路径...",
            "en": "Instructions received. Planning the research path...",
            "cn_verbose": "正在分析您的需求并制定个性化的诊断策略",
            "en_verbose": "Analyzing your requirements and developing a personalized diagnosis strategy"
        },
        UnifiedTaskStatus.CRAWLING_ACCOUNT: {
            "cn": "正在分析账号基础信息...",
            "en": "Analyzing your account information...",
            "cn_verbose": "正在深度分析您的账号数据，提取关键特征和表现指标",
            "en_verbose": "Conducting in-depth analysis of your account data, extracting key features and performance metrics"
        },
        UnifiedTaskStatus.SEARCHING: {
            "cn": "正在搜索行业热点与最新动态...",
            "en": "Performing a deep search and mining for information...",
            "cn_verbose": "正在搜索相关行业的最新趋势、热点话题和竞争对手动态",
            "en_verbose": "Searching for the latest industry trends, hot topics, and competitor dynamics"
        },
        UnifiedTaskStatus.ANALYZING_INDUSTRY: {
            "cn": "正在进行行业分析，洞察市场趋势...",
            "en": "Analyzing the industry to identify market trends...",
            "cn_verbose": "基于搜索结果进行深度行业分析，识别市场机会和挑战",
            "en_verbose": "Conducting comprehensive industry analysis based on search results to identify market opportunities and challenges"
        },
        UnifiedTaskStatus.PERFORMING_DIAGNOSIS: {
            "cn": "正在进行多维度账号比对与诊断...",
            "en": "Conducting a multi-dimensional diagnosis...",
            "cn_verbose": "运用AI算法对您的账号进行全方位诊断，识别优势和改进空间",
            "en_verbose": "Using AI algorithms to conduct comprehensive account diagnosis, identifying strengths and areas for improvement"
        },
        UnifiedTaskStatus.GENERATING_REPORT: {
            "cn": "正在生成专业诊断报告...",
            "en": "Generating a professional diagnosis report...",
            "cn_verbose": "正在生成包含详细分析和建议的专业诊断报告",
            "en_verbose": "Generating a professional diagnosis report with detailed analysis and recommendations"
        },
        UnifiedTaskStatus.PAGE_RENDERING: {
            "cn": "正在渲染页面，即将完成...",
            "en": "Rendering pages, almost complete...",
            "cn_verbose": "正在优化报告格式和视觉呈现，确保最佳阅读体验",
            "en_verbose": "Optimizing report format and visual presentation to ensure the best reading experience"
        },
        UnifiedTaskStatus.TASK_COMPLETED: {
            "cn": "任务已完成！",
            "en": "Task completed successfully!",
            "cn_verbose": "诊断任务已成功完成，您的专业报告已准备就绪",
            "en_verbose": "Diagnosis task has been completed successfully, your professional report is ready"
        },
        UnifiedTaskStatus.TASK_FAILED: {
            "cn": "任务执行失败",
            "en": "Task execution failed",
            "cn_verbose": "很抱歉，诊断过程中遇到了问题，我们正在努力解决",
            "en_verbose": "We apologize, there was an issue during the diagnosis process, we are working to resolve it"
        },
        
        # 深度研究专用状态消息
        UnifiedTaskStatus.INITIALIZING_RESEARCH: {
            "cn": "正在初始化深度研究模块...",
            "en": "Initializing deep research module...",
            "cn_verbose": "正在启动高级AI引擎，准备进行深度行业研究",
            "en_verbose": "Launching advanced AI engine to prepare for deep industry research"
        },
        UnifiedTaskStatus.GENERATING_QUERIES: {
            "cn": "正在生成智能搜索查询...",
            "en": "Generating intelligent search queries...",
            "cn_verbose": "基于您的行业特点生成多层次、多角度的智能搜索策略",
            "en_verbose": "Generating multi-layered, multi-angle intelligent search strategies based on your industry characteristics"
        },
        UnifiedTaskStatus.OPTIMIZING_QUERIES: {
            "cn": "正在优化搜索策略...",
            "en": "Optimizing search strategy...",
            "cn_verbose": "运用机器学习算法优化搜索查询，提高信息获取精度",
            "en_verbose": "Using machine learning algorithms to optimize search queries and improve information acquisition accuracy"
        },
        UnifiedTaskStatus.EXECUTING_SEARCHES: {
            "cn": "正在执行大规模信息搜索...",
            "en": "Executing large-scale information search...",
            "cn_verbose": "并行执行多个专业搜索引擎，收集最新最全面的行业信息",
            "en_verbose": "Parallel execution of multiple professional search engines to collect the latest and most comprehensive industry information"
        },
        UnifiedTaskStatus.PROCESSING_RESULTS: {
            "cn": "正在处理和分析搜索结果...",
            "en": "Processing and analyzing search results...",
            "cn_verbose": "对收集到的大量信息进行智能筛选、去重和质量评估",
            "en_verbose": "Intelligent filtering, deduplication, and quality assessment of collected extensive information"
        },
        UnifiedTaskStatus.INDUSTRY_ANALYSIS: {
            "cn": "正在进行深度行业分析...",
            "en": "Conducting deep industry analysis...",
            "cn_verbose": "基于最新数据进行深度行业洞察，识别趋势和机会",
            "en_verbose": "Conducting deep industry insights based on latest data to identify trends and opportunities"
        },
        UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS: {
            "cn": "正在进行综合智能分析...",
            "en": "Conducting comprehensive intelligent analysis...",
            "cn_verbose": "整合所有信息进行全方位智能分析，生成专业洞察",
            "en_verbose": "Integrating all information for comprehensive intelligent analysis and generating professional insights"
        },
        UnifiedTaskStatus.FINALIZING_REPORT: {
            "cn": "正在完善深度研究报告...",
            "en": "Finalizing deep research report...",
            "cn_verbose": "对深度研究结果进行最终整理和专业化呈现",
            "en_verbose": "Final organization and professional presentation of deep research results"
        }
    }
    
    @classmethod
    def get_message(cls, status: UnifiedTaskStatus, language: str = "cn", 
                   verbose: bool = False, custom_message: Optional[str] = None) -> str:
        """
        获取状态消息
        
        Args:
            status: 任务状态
            language: 语言 ("cn" 或 "en")
            verbose: 是否使用详细模式
            custom_message: 自定义消息（优先级最高）
        """
        if custom_message:
            return custom_message
            
        messages = cls.STATUS_MESSAGES.get(status, {})
        
        if verbose:
            key = f"{language}_verbose"
            if key in messages:
                return messages[key]
        
        return messages.get(language, f"Unknown status: {status.value}")


@dataclass
class TaskProgress:
    """任务进度信息"""
    task_id: str
    current_status: UnifiedTaskStatus
    progress_percentage: float
    start_time: float
    last_update_time: float
    estimated_completion_time: Optional[float] = None
    custom_message: Optional[str] = None
    error_message: Optional[str] = None
    performance_stats: Dict[str, Any] = field(default_factory=dict)
    step_progress: Optional[float] = None  # 步骤内的细粒度进度


class UnifiedProgressTracker:
    """
    统一进度跟踪器
    合并原有的TaskProgressTracker功能，支持Redis推送和本地回调
    """
    
    def __init__(self, task_id: str, user_id: Optional[str] = None, 
                 diagnosis_id: Optional[int] = None, env: str = "dev",
                 callback: Optional[Callable] = None, enable_deep_research: bool = False,
                 redis_manager: Optional[Any] = None, redis_queue: Optional[str] = None,
                 verbose: bool = False, language: str = "cn"):
        """
        初始化统一进度跟踪器
        
        Args:
            task_id: 任务ID
            user_id: 用户ID  
            diagnosis_id: 诊断ID
            env: 环境标识
            callback: 本地回调函数
            enable_deep_research: 是否启用深度研究模式
            redis_manager: Redis管理器
            redis_queue: Redis队列名
            verbose: 是否使用详细模式消息
            language: 消息语言
        """
        self.task_id = task_id
        self.user_id = user_id
        self.diagnosis_id = diagnosis_id
        self.env = env
        self.callback = callback
        self.enable_deep_research = enable_deep_research
        self.redis_manager = redis_manager
        self.redis_queue = redis_queue or f"{env.lower()}:q:diagnosis:response"
        self.verbose = verbose
        self.language = language
        
        # 初始化进度信息
        self.progress = TaskProgress(
            task_id=task_id,
            current_status=UnifiedTaskStatus.TASK_QUEUED,
            progress_percentage=0.0,
            start_time=time.time(),
            last_update_time=time.time()
        )
        
        # 性能统计
        self.step_timings = {}
        self.status_history = []
        
        # 最终结果存储（用于FINISH状态）
        self.final_results = {}
        
        # 日志记录器 - 必须在_push_status_update之前初始化
        self.logger = logging.getLogger(f"{__name__}.{task_id}")
        
        # 状态序列器 - 新增，用于智能处理并发状态更新
        self.status_sequencer = StatusSequencer(self.logger)
        
        # 全局状态协调器
        self._global_coordinator: Optional[GlobalStatusCoordinator] = None
        
        # 立即发送初始状态，确保第一个状态更新不会延迟
        self._push_status_update()
        
        # 立即发送"收到指令"状态，解决前端启动延迟感知问题
        self.update_status(UnifiedTaskStatus.PLANNING)
    
    def update_status(self, status: UnifiedTaskStatus, custom_message: Optional[str] = None,
                     step_progress: Optional[float] = None, error_message: Optional[str] = None,
                     source: str = "default") -> None:
        """
        更新任务状态 - 增强版，支持智能冲突解决
        
        Args:
            status: 新的任务状态
            custom_message: 自定义消息
            step_progress: 步骤内进度(0-100)
            error_message: 错误消息
            source: 更新来源标识
        """
        # Debug logging for all status updates - using WARNING level to ensure visibility
        expected_progress = StatusManager.get_progress_percentage(status)
        # self.logger.warning(f"[DEBUG] update_status called: status={status.value}, expected_progress={expected_progress}%, current_progress={self.progress.progress_percentage}%, source={source}")
        
        # 创建状态更新请求
        update_request = StatusUpdate(
            status=status,
            custom_message=custom_message,
            step_progress=step_progress,
            error_message=error_message,
            source=source,
            phase=StatusManager.get_status_phase(status),
            priority=StatusManager.get_status_priority(status)
        )
        
        # 异步处理状态更新
        try:
            import asyncio
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果事件循环正在运行，创建任务
                # self.logger.warning(f"[DEBUG] Using async processing for {update_request.status.value}")
                asyncio.create_task(self._process_status_update_async(update_request))
            else:
                # 如果没有事件循环，同步处理
                # self.logger.warning(f"[DEBUG] No running event loop, using sync processing for {update_request.status.value}")
                self._process_status_update_sync(update_request)
        except RuntimeError as e:
            # 如果获取事件循环失败，使用同步处理
            # self.logger.warning(f"[DEBUG] RuntimeError getting event loop: {e}, using sync processing for {update_request.status.value}")
            self._process_status_update_sync(update_request)
    
    async def _process_status_update_async(self, update_request: StatusUpdate) -> None:
        """异步处理状态更新"""
        try:
            # self.logger.warning(f"[DEBUG] _process_status_update_async started for {update_request.status.value}")
            
            # 首先通过全局状态协调器进行协调
            if not self._global_coordinator:
                # self.logger.warning(f"[DEBUG] Creating global coordinator instance")
                self._global_coordinator = await GlobalStatusCoordinator.get_instance()
                # self.logger.warning(f"[DEBUG] Global coordinator created successfully")
            
            # 检查任务是否已经完成，避免过时更新
            if await self._is_task_already_completed(update_request):
                self.logger.warning(f"任务已完成，跳过过时更新: {update_request.status.value} from {update_request.source}")
                return
            
            # self.logger.warning(f"[DEBUG] Calling coordinate_status_update for {update_request.status.value}")
            # self.logger.warning(f"[DEBUG] Task ID: {self.task_id}, Source: {update_request.source}")
            try:
                global_allowed = await self._global_coordinator.coordinate_status_update(
                    self.task_id, update_request.status, update_request.source, update_request.custom_message
                )
                # self.logger.warning(f"[DEBUG] Global coordinator decision: {global_allowed} for {update_request.status.value}")
            except Exception as e:
                # self.logger.error(f"[DEBUG] Error in coordinate_status_update: {e}", exc_info=True)
                self.logger.error(f"Error in coordinate_status_update: {e}", exc_info=True)
                global_allowed = True  # Fallback to allow update
            
            if not global_allowed:
                # self.logger.warning(f"[DEBUG] 全局状态协调器拒绝状态更新: {update_request.status.value}")
                return
            
            # self.logger.warning(f"[DEBUG] Calling status_sequencer.should_process_update for {update_request.status.value}")
            should_process, reason = await self.status_sequencer.should_process_update(
                update_request, self.progress.current_status, self.progress.progress_percentage
            )
            
            # self.logger.warning(f"[DEBUG] Status sequencer decision: {should_process} - {reason}")
            
            if should_process:
                # 处理状态更新
                # self.logger.warning(f"[DEBUG] Applying status update for {update_request.status.value}")
                self._apply_status_update(update_request)
                self.status_sequencer.mark_update_processed(update_request)
                
                # 如果是完成状态，确保协程同步完成
                if update_request.status in [UnifiedTaskStatus.TASK_COMPLETED, UnifiedTaskStatus.TASK_FAILED]:
                    await self._handle_task_completion(update_request)
                
                # self.logger.warning(f"[DEBUG] 状态更新已处理: {update_request.status.value} - {reason}")
            else:
                # self.logger.warning(f"[DEBUG] 状态更新被智能过滤: {update_request.status.value} - {reason}")
                # 如果只是消息更新被拒绝，但不是状态变更，仍然允许
                if (update_request.status == self.progress.current_status and 
                    update_request.custom_message and 
                    update_request.custom_message != self.progress.custom_message):
                    self.progress.custom_message = update_request.custom_message
                    self._push_status_update()
                    self.logger.debug(f"允许相同状态下的消息更新: {update_request.custom_message}")
                    
        except Exception as e:
            self.logger.error(f"异步状态更新处理失败: {e}", exc_info=True)
            # 确保异常不会阻塞其他协程
            # Fall back to sync processing if async fails
            # self.logger.warning(f"[DEBUG] Falling back to sync processing due to async error")
            self._process_status_update_sync(update_request)
    
    async def _is_task_already_completed(self, update_request: StatusUpdate) -> bool:
        """检查任务是否已经完成，避免过时更新"""
        if not self._global_coordinator:
            return False
            
        # 通过全局协调器检查任务状态
        task_info = self._global_coordinator._active_tasks.get(self.task_id)
        if task_info and task_info.get("is_completed", False):
            # 允许完成状态和最终阶段状态的更新
            allowed_final_statuses = [
                UnifiedTaskStatus.TASK_COMPLETED, 
                UnifiedTaskStatus.TASK_FAILED,
                UnifiedTaskStatus.PAGE_RENDERING,  # 允许页面渲染状态
                UnifiedTaskStatus.GENERATING_REPORT  # 允许报告生成完成更新
            ]
            
            # 允许来自diagnosis_core模块的最终状态更新
            if (update_request.status in allowed_final_statuses or 
                update_request.source == "diagnosis_core"):
                return False
                
            # 允许immediate类型的关键更新通过
            if update_request.source == "immediate":
                return False
                
            return True
        return False
    
    async def _handle_task_completion(self, update_request: StatusUpdate) -> None:
        """处理任务完成时的协程同步"""
        try:
            # 设置短暂延迟，确保所有模块都收到完成信号
            await asyncio.sleep(0.1)
            
            # 通知全局协调器任务已完成
            if self._global_coordinator:
                await self._global_coordinator.cleanup_task(self.task_id)
            
            # 确保最终状态被正确推送
            self._push_status_update()
            
            # 记录任务完成
            completion_time = time.time() - self.progress.start_time
            self.logger.info(f"任务完成处理: {self.task_id}, 状态: {update_request.status.value}, 总耗时: {completion_time:.2f}秒")
            
        except Exception as e:
            self.logger.error(f"任务完成处理失败: {e}", exc_info=True)
    
    def _process_status_update_sync(self, update_request: StatusUpdate) -> None:
        """同步处理状态更新（简化版决策逻辑）"""
        current_priority = StatusManager.get_status_priority(self.progress.current_status)
        update_priority = StatusManager.get_status_priority(update_request.status)
        update_progress = StatusManager.get_progress_percentage(update_request.status)
        
        # self.logger.warning(f"[DEBUG] Sync processing: current_status={self.progress.current_status.value}, current_priority={current_priority}, update_priority={update_priority}, update_progress={update_progress}%")
        
        # 简化的同步决策逻辑
        should_process = (
            # 总是允许失败和完成状态
            update_request.status in [UnifiedTaskStatus.TASK_FAILED, UnifiedTaskStatus.TASK_COMPLETED] or
            # 相同状态的消息更新
            (update_request.status == self.progress.current_status and update_request.custom_message) or
            # 高优先级更新
            update_priority > current_priority or
            # 进度前进
            update_progress >= self.progress.progress_percentage or
            # 允许轻微倒退（小于2%）
            (update_progress >= self.progress.progress_percentage - 2.0)
        )
        
        # self.logger.warning(f"[DEBUG] Sync decision: should_process={should_process} for {update_request.status.value}")
        
        if should_process:
            self._apply_status_update(update_request)
            # self.logger.warning(f"[DEBUG] 状态更新已处理 (同步): {update_request.status.value}")
        else:
            # self.logger.warning(f"[DEBUG] 状态更新被过滤 (同步): {update_request.status.value}")
            pass
    
    def _apply_status_update(self, update_request: StatusUpdate) -> None:
        """应用状态更新"""
        current_time = time.time()
        
        # 计算新的进度
        base_progress = StatusManager.get_progress_percentage(update_request.status)
        new_progress = base_progress

        # 如果提供了步骤内进度，进行微调
        if update_request.step_progress is not None:
            next_status = StatusManager.get_next_status(update_request.status)
            if next_status:
                next_progress = StatusManager.get_progress_percentage(next_status)
                # 确保增量进度在合理范围内
                if next_progress > base_progress:
                    adjusted_progress = base_progress + (next_progress - base_progress) * (update_request.step_progress / 100.0)
                    new_progress = min(adjusted_progress, next_progress)
        
        # 记录状态转换时间
        if self.progress.current_status != update_request.status:
            duration = current_time - self.progress.last_update_time
            self.step_timings[self.progress.current_status.value] = duration
            self.status_history.append({
                'status': self.progress.current_status.value,
                'timestamp': self.progress.last_update_time,
                'duration': duration
            })
        
        # 更新进度信息
        self.progress.current_status = update_request.status
        self.progress.progress_percentage = new_progress
        self.progress.last_update_time = current_time
        self.progress.custom_message = update_request.custom_message
        self.progress.error_message = update_request.error_message
        
        # 更新预估完成时间
        if update_request.status not in [UnifiedTaskStatus.TASK_COMPLETED, UnifiedTaskStatus.TASK_FAILED]:
            remaining_duration = self._estimate_remaining_time(update_request.status)
            if remaining_duration > 0:
                self.progress.estimated_completion_time = current_time + remaining_duration
        
        # 记录性能统计
        self.progress.performance_stats.update({
            'total_duration': current_time - self.progress.start_time,
            'step_timings': self.step_timings.copy(),
            'status_transitions': len(self.status_history)
        })
        
        # 推送状态更新
        self._push_status_update()
        
        # 日志记录
        message = StatusMessageManager.get_message(
            update_request.status, self.language, self.verbose, update_request.custom_message
        )
        self.logger.info(f"Status updated to {update_request.status.value}: {message}")
    
    def update_status_immediate(self, status: UnifiedTaskStatus, custom_message: Optional[str] = None,
                               step_progress: Optional[float] = None, error_message: Optional[str] = None) -> None:
        """
        立即更新状态并强制推送到Redis，用于关键阶段确保及时响应
        
        Args:
            status: 新的任务状态
            custom_message: 自定义消息
            step_progress: 步骤内进度(0-100)
            error_message: 错误消息
        """
        # Debug logging for immediate status updates - using WARNING level to ensure visibility
        expected_progress = StatusManager.get_progress_percentage(status)
        # self.logger.warning(f"[DEBUG] update_status_immediate called: status={status.value}, expected_progress={expected_progress}%, current_progress={self.progress.progress_percentage}%")
        
        # 先正常更新状态，但标记为高优先级来源
        self.update_status(status, custom_message, step_progress, error_message, source="immediate")
        
        # Add a short delay and check if the update actually took effect
        import time
        time.sleep(0.1)  # Give async processing time to complete
        # self.logger.warning(f"[DEBUG] update_status_immediate completed: status={self.progress.current_status.value}, actual_progress={self.progress.progress_percentage}%")
    
    def update_step_progress(self, step_name: str, current: int, total: int,
                           custom_message: Optional[str] = None) -> None:
        """
        更新步骤级进度
        
        Args:
            step_name: 步骤名称
            current: 当前进度
            total: 总数
            custom_message: 自定义消息
        """
        if total > 0:
            step_percentage = (current / total) * 100
            message = custom_message or f"{step_name}: {current}/{total}"
            self.update_status(
                self.progress.current_status, 
                message, 
                step_percentage
            )
    
    def update_search_batch_progress(self, batch_name: str, completed_queries: int, total_queries: int,
                                   current_query: Optional[str] = None) -> None:
        """
        更新搜索批次进度 - 聚合多个搜索查询的进度更新
        
        Args:
            batch_name: 批次名称（如"深度搜索"）
            completed_queries: 已完成的查询数量
            total_queries: 总查询数量
            current_query: 当前正在执行的查询（可选）
        """
        if total_queries <= 0:
            return
            
        # 计算批次进度
        batch_progress = (completed_queries / total_queries) * 100
        
        # 构建消息
        if current_query:
            if len(current_query) > 25:
                query_desc = current_query[:25] + "..."
            else:
                query_desc = current_query
            message = f"{batch_name}: 正在执行 {query_desc} [{completed_queries + 1}/{total_queries}]"
        else:
            message = f"{batch_name}: 已完成 {completed_queries}/{total_queries} 个查询"
        
        # 只有在进度有显著变化时才更新（避免频繁更新）
        last_batch_progress = getattr(self, '_last_batch_progress', 0)
        if abs(batch_progress - last_batch_progress) >= 12.5:  # 至少12.5%的变化（对于8个查询，每个约12.5%）
            self._last_batch_progress = batch_progress
            # 仅更新消息，不改变主状态进度
            if self.progress.custom_message != message:
                self.progress.custom_message = message
                self._push_status_update()

    def update_search_progress(self, search_type: str, query_text: str, 
                             results_count: int = 0, is_completed: bool = False,
                             current_query_index: int = 0, total_queries: int = 0) -> None:
        """
        更新搜索进度 - 为搜索过程提供更平滑的状态更新
        
        Args:
            search_type: 搜索类型 (tavily, pplx, hybrid)
            query_text: 查询文本
            results_count: 结果数量
            is_completed: 是否已完成
            current_query_index: 当前查询索引（从0开始）
            total_queries: 总查询数量
        """
        # Debug logging to trace progress issue
        self.logger.info(f"update_search_progress called: current_progress={self.progress.progress_percentage}%, status={self.progress.current_status.value}, query={query_text[:30]}...")
        
        # Skip updates if progress is 0% - indicates status hasn't been properly initialized yet
        if self.progress.progress_percentage <= 0.1:
            self.logger.warning(f"Skipping search progress update - status not properly initialized (progress={self.progress.progress_percentage}%)")
            return
        
        # 计算搜索内部的细粒度进度
        search_step_progress = None
        if total_queries > 0 and current_query_index >= 0:
            # 计算当前搜索在整个搜索阶段内的进度
            if is_completed:
                # 完成时，进度为 (当前索引+1) / 总数 * 100
                search_step_progress = ((current_query_index + 1) / total_queries) * 100
            else:
                # 进行中时，进度为 当前索引 / 总数 * 100 + 一点增量
                search_step_progress = (current_query_index / total_queries) * 100 + 10  # 加10%表示正在进行
                search_step_progress = min(search_step_progress, 95)  # 不超过95%
            
        if len(query_text) > 30:
            query_desc = query_text[:30] + "..."
        else:
            query_desc = query_text
        
        if is_completed:
            message = f"✅ {query_desc} ({results_count} 个结果)"
            # 如果有总查询数，显示进度
            if total_queries > 0:
                message = f"✅ {query_desc} ({results_count} 个结果) [{current_query_index + 1}/{total_queries}]"
        else:
            message = f"开始搜索: {query_desc}"
            # 如果有总查询数，显示进度
            if total_queries > 0:
                message = f"开始搜索: {query_desc} [{current_query_index + 1}/{total_queries}]"
        
        # 仅更新消息，使用细粒度进度但不改变主要状态
        if self.progress.custom_message != message:
            self.progress.custom_message = message
            # 如果有搜索步骤进度，更新它但不改变主状态
            if search_step_progress is not None:
                # 临时存储原始进度，用细粒度进度更新，然后推送
                original_progress = self.progress.progress_percentage
                # 只有当细粒度进度不会导致倒退时才使用
                if search_step_progress >= original_progress - 1.0:  # 允许1%的容差
                    self.progress.step_progress = search_step_progress
                else:
                    self.progress.step_progress = None
            self._push_status_update()
    
    def _estimate_remaining_time(self, current_status: UnifiedTaskStatus) -> float:
        """估算剩余时间"""
        remaining_time = 0.0
        
        # 获取当前状态及后续状态的预估时间
        status = current_status
        while status and status not in [UnifiedTaskStatus.TASK_COMPLETED, UnifiedTaskStatus.TASK_FAILED]:
            remaining_time += StatusManager.get_estimated_duration(status)
            status = StatusManager.get_next_status(status)
        
        return remaining_time
    
    def _push_status_update(self) -> None:
        """推送状态更新到Redis和本地回调 - 优化版本，减少连接频率"""
        
        # 构建状态更新消息
        api_status = UnifiedTaskStatus.to_api_status(self.progress.current_status)
        
        # 使用步骤进度（如果有）或主进度
        display_progress = self.progress.step_progress if self.progress.step_progress is not None else self.progress.progress_percentage
        
        status_data = {
            "taskInfo": {
                "env": self.env,
                "taskId": self.task_id,
                "userId": self.user_id,
                "diagnosisId": self.diagnosis_id,
                "aiTaskStatus": api_status,
                "aiTaskMsg": StatusMessageManager.get_message(
                    self.progress.current_status, "en", self.verbose, 
                    self.progress.custom_message
                ),
                "aiTaskMsgCN": StatusMessageManager.get_message(
                    self.progress.current_status, "cn", self.verbose, 
                    self.progress.custom_message
                ),
                "aiTaskProgress": int(display_progress),
                "timestamp": datetime.fromtimestamp(self.progress.last_update_time).isoformat(),
                "elapsed_time": self.progress.performance_stats.get("total_duration", 0)
            }
        }
        
        # 对于FINISH状态，添加最终结果
        if api_status == "FINISH" and self.final_results:
            status_data.update(self.final_results)
        
        # 本地回调优先执行（不依赖Redis）
        if self.callback:
            try:
                import asyncio
                import inspect
                
                # 检查回调函数是否是协程函数
                if inspect.iscoroutinefunction(self.callback):
                    # 如果是协程函数，需要在事件循环中运行
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            # 如果事件循环正在运行，创建一个任务
                            asyncio.create_task(self.callback(status_data))
                        else:
                            # 如果事件循环没有运行，直接运行
                            loop.run_until_complete(self.callback(status_data))
                    except RuntimeError:
                        # 如果获取事件循环失败，创建新的事件循环
                        asyncio.run(self.callback(status_data))
                else:
                    # 如果是普通函数，直接调用
                    self.callback(status_data)
                
                self.logger.debug("Status sent to local callback")
            except Exception as e:
                self.logger.error(f"Failed to execute local callback: {e}")
        
        # Redis推送 - 使用proper serialization method
        # Note: We don't require redis_manager, just redis_queue since we use get_redis_connection()
        if self.redis_queue and self._should_push_to_redis(api_status):
            try:
                self.logger.debug(f"Preparing Redis push for status: {api_status}, progress: {display_progress}%")
                
                # Use the proper Redis serialization method (double JSON dumps)
                message = safe_redis_serialize_with_validation(status_data)
                
                # Debug logging to verify serialization
                if not _SERIALIZATION_AVAILABLE:
                    self.logger.warning("Using fallback serialization method")
                
                # Verify that the message is actually a string containing JSON
                if not isinstance(message, str):
                    self.logger.error(f"Serialization failed: expected string, got {type(message)}")
                    return
                
                # Verify double serialization by checking if we can deserialize twice
                try:
                    step1 = json.loads(message)  # Should get a JSON string
                    step2 = json.loads(step1)    # Should get the original data
                    if not isinstance(step1, str):
                        self.logger.error(f"Double serialization verification failed: step1 is {type(step1)}, expected str")
                        return
                    self.logger.debug("✅ Double serialization verified successfully")
                except (json.JSONDecodeError, TypeError) as e:
                    self.logger.error(f"Double serialization verification failed: {e}")
                    return
                
                # 使用更简单的推送机制 - 延迟推送，减少连接频率
                self._schedule_redis_push(message)
                self.logger.debug(f"Redis push scheduled for queue: {self.redis_queue}")
                    
            except Exception as e:
                self.logger.error(f"Failed to setup Redis push: {e}")
        else:
            # Debug why Redis push was skipped
            reasons = []
            if not self.redis_queue:
                reasons.append("no redis_queue")
            if not self._should_push_to_redis(api_status):
                reasons.append("should_push_to_redis returned False")
            self.logger.debug(f"Redis push skipped: {', '.join(reasons)}")
    
    def _should_push_to_redis(self, api_status: str) -> bool:
        """判断是否应该推送到Redis - 优化后的智能推送策略"""
        # 获取当前状态信息
        progress = self.progress.progress_percentage
        current_status = self.progress.current_status
        last_redis_progress = getattr(self, '_last_redis_progress', 0)
        last_redis_status = getattr(self, '_last_redis_status', None)
        last_push_time = getattr(self, '_last_redis_push_time', 0)
        last_redis_message = getattr(self, '_last_redis_message', '')
        current_time = time.time()
        current_message = self.progress.custom_message or ''
        
        # 规则1：总是推送关键状态变化
        important_statuses = {"FINISH", "FAILED"}
        if api_status in important_statuses:
            self._update_redis_tracking(progress, current_status, current_time, current_message)
            return True
        
        # 规则2：状态变化时总是推送
        if current_status != last_redis_status:
            self._update_redis_tracking(progress, current_status, current_time, current_message)
            return True
        
        # 规则3：显著进度前进才推送（避免微小变化）
        progress_threshold = 2.0  # 至少2%的进度变化
        if progress > last_redis_progress + progress_threshold:
            # 时间限制 - 避免过于频繁的推送（最小间隔0.5秒）
            time_elapsed = current_time - last_push_time
            if time_elapsed >= 0.5:
                self._update_redis_tracking(progress, current_status, current_time, current_message)
                return True
        
        # 规则4：重要进度节点总是推送
        important_progress_points = {5, 10, 15, 20, 25, 30, 40, 50, 60, 70, 80, 90, 95}
        if int(progress) in important_progress_points and int(last_redis_progress) not in important_progress_points:
            self._update_redis_tracking(progress, current_status, current_time, current_message)
            return True
        
        # 规则5：消息更新（相同状态但消息显著不同）- 更严格的控制
        if (current_status == last_redis_status and 
            current_message and 
            current_message != last_redis_message):
            
            time_elapsed = current_time - last_push_time
            
            # 对于搜索消息，使用更严格的时间间隔
            if any(keyword in current_message for keyword in ['搜索', '正在搜索', '搜索完成', '✅']):
                # 搜索消息：最小间隔2秒，且只推送重要的搜索节点
                if (time_elapsed >= 2.0 and 
                    (current_message.startswith('✅') or  # 完成消息
                     '启动并行搜索' in current_message or  # 启动消息
                     '[1/' in current_message or  # 第一个查询
                     '/8]' in current_message)):  # 最后一个查询（假设总共8个）
                    self._update_redis_tracking(progress, current_status, current_time, current_message)
                    return True
            else:
                # 非搜索消息：正常间隔1.5秒
                if time_elapsed >= 1.5:
                    self._update_redis_tracking(progress, current_status, current_time, current_message)
                    return True
        
        return False
    
    def _calculate_progress_threshold(self, progress: float, status: UnifiedTaskStatus) -> float:
        """根据当前阶段动态计算进度推送阈值"""
        # 初始阶段（0-20%）：更频繁的更新，每2%推送
        if progress <= 20:
            return 2.0
        
        # 核心工作阶段（20-80%）：适中频率，每5%推送
        elif progress <= 80:
            return 5.0
        
        # 完成阶段（80-100%）：高频更新，每2%推送
        else:
            return 2.0
    
    def _update_redis_tracking(self, progress: float, status: UnifiedTaskStatus, current_time: float, message: str = ''):
        """更新Redis推送跟踪信息"""
        self._last_redis_progress = progress
        self._last_redis_status = status
        self._last_redis_push_time = current_time
        self._last_redis_message = message
    
    def _schedule_redis_push(self, message: str) -> None:
        """调度Redis推送 - 优化延迟机制，减少等待时间"""
        # 存储待推送的消息
        if not hasattr(self, '_pending_redis_messages'):
            self._pending_redis_messages = []
        
        self._pending_redis_messages.append(message)
        
        # 如果没有正在进行的推送任务，启动一个（立即执行）
        if not hasattr(self, '_redis_push_task') or self._redis_push_task.done():
            # 立即推送，不再等待
            self._immediate_push_to_redis()
    
    async def _batch_push_to_redis(self) -> None:
        """实时推送到Redis，避免消息积压"""
        try:
            # 移除等待时间，实时推送避免消息积压
            # await asyncio.sleep(0.001) # 移除等待，改为实时推送
            
            # 获取所有待推送的消息
            messages = getattr(self, '_pending_redis_messages', [])
            if not messages:
                return
            
            # 清空待推送列表
            self._pending_redis_messages = []
            
            # 减少消息去重，保持更多实时更新
            # 只去重完全相同的消息，保留进度变化
            if len(messages) > 3:  # 只有消息过多时才去重
                final_messages = {}
                for msg_str in messages:
                    try:
                        json_str = json.loads(msg_str)
                        msg_json = json.loads(json_str)
                        # 使用更细粒度的key，包含进度和状态
                        progress = msg_json.get("taskInfo", {}).get("aiTaskProgress")
                        status = msg_json.get("taskInfo", {}).get("aiTaskStatus", "")
                        key = f"{progress}:{status}"
                        final_messages[key] = msg_str
                    except (json.JSONDecodeError, TypeError) as e:
                        self.logger.warning(f"Failed to parse message for deduplication: {e}")
                        final_messages[len(final_messages)] = msg_str
                
                messages = list(final_messages.values())
            # 否则发送所有消息，保持实时性
            
            # 推送到Redis
            try:
                # 修复导入路径
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                from base_async_service import get_redis_connection
                async with get_redis_connection() as redis_conn:
                    # 按顺序推送所有消息，保持时间戳顺序
                    for i, message in enumerate(messages):
                        # Debug logging to verify message format
                        if i == 0:  # Log first message for debugging
                            self.logger.debug(f"Pushing message type: {type(message)}")
                            self.logger.debug(f"Message preview: {message[:200]}...")
                            
                            # Verify it's a double-serialized string
                            try:
                                step1 = json.loads(message)
                                if isinstance(step1, str):
                                    step2 = json.loads(step1)
                                    self.logger.debug("✅ Verified double serialization before Redis push")
                                else:
                                    self.logger.error(f"❌ Message is not double-serialized! Step1 type: {type(step1)}")
                            except Exception as verify_e:
                                self.logger.error(f"❌ Failed to verify message serialization: {verify_e}")
                        
                        await redis_conn.lpush(self.redis_queue, message)
                    
                    queue_length = await redis_conn.llen(self.redis_queue)
                    self.logger.debug(f"Batch pushed {len(messages)} messages to Redis queue: {self.redis_queue}, length: {queue_length}")
                    
            except Exception as e:
                self.logger.error(f"Failed to batch push to Redis: {e}")
                
        except Exception as e:
            self.logger.error(f"Batch Redis push error: {e}")

    def _immediate_push_to_redis(self) -> None:
        """立即推送到Redis（优化版本）"""
        try:
            import asyncio
            loop = asyncio.get_running_loop()
            # 简化逻辑：直接创建任务，让asyncio处理并发
            # 移除任务检查，确保每次更新都能及时推送
            loop.create_task(self._single_push_to_redis())
        except RuntimeError:
            # 如果没有事件循环，则无法异步推送
            self.logger.warning("No running event loop, cannot push to Redis immediately.")
        except Exception as e:
            self.logger.error(f"Failed to schedule immediate Redis push: {e}")
    
    async def _single_push_to_redis(self) -> None:
        """单次推送最新消息，避免消息积压"""
        try:
            # 获取最新的一条或几条消息
            messages = getattr(self, '_pending_redis_messages', [])
            if not messages:
                return
            
            # 只取最新的几条消息，避免积压
            recent_messages = messages[-3:] if len(messages) > 3 else messages
            # 清空对应的消息
            if len(messages) > 3:
                self._pending_redis_messages = self._pending_redis_messages[:-3]
            else:
                self._pending_redis_messages = []
            
            # 推送到Redis
            try:
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                from base_async_service import get_redis_connection
                async with get_redis_connection() as redis_conn:
                    for message in recent_messages:
                        await redis_conn.lpush(self.redis_queue, message)
                        
            except Exception as e:
                if "Redis Cluster cannot be connected" in str(e) or "Timeout connecting" in str(e):
                    self.logger.warning(f"Redis连接超时，跳过此次推送: {e}")
                else:
                    self.logger.error(f"推送状态到Redis失败: {e}")
                    
        except Exception as e:
            self.logger.error(f"Single Redis push error: {e}")

    def _push_status_update_immediate(self):
        pass # This method is no longer needed and is left for compatibility.
    
    def _immediate_push_to_redis_sync(self, message: str) -> None:
        """立即推送到Redis（同步版本，用于没有事件循环的情况）"""
        try:
            import asyncio
            # 创建新的事件循环来处理推送
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                loop.run_until_complete(self._push_message_to_redis(message))
            finally:
                loop.close()
        except Exception as e:
            self.logger.error(f"Immediate Redis push failed: {e}")
    
    async def _push_message_to_redis(self, message: str) -> None:
        """推送单个消息到Redis"""
        try:
            # 修复导入路径
            import sys
            import os
            sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
            from base_async_service import get_redis_connection
            async with get_redis_connection() as redis_conn:
                await redis_conn.lpush(self.redis_queue, message)
                self.logger.debug(f"Pushed message to Redis queue: {self.redis_queue}")
        except Exception as e:
            self.logger.error(f"Failed to push message to Redis: {e}")
    
    def get_current_progress(self) -> Dict[str, Any]:
        """获取当前进度信息"""
        return {
            "task_id": self.progress.task_id,
            "status": self.progress.current_status.value,
            "progress": self.progress.progress_percentage,
            "message": StatusMessageManager.get_message(
                self.progress.current_status, self.language, self.verbose,
                self.progress.custom_message
            ),
            "start_time": self.progress.start_time,
            "last_update": self.progress.last_update_time,
            "estimated_completion": self.progress.estimated_completion_time,
            "performance_stats": self.progress.performance_stats,
            "error": self.progress.error_message
        }
    
    def mark_completed(self, custom_message: Optional[str] = None, final_results: Optional[Dict[str, Any]] = None) -> None:
        """
        标记任务完成
        
        Args:
            custom_message: 自定义完成消息
            final_results: 最终结果数据，将与FINISH状态一起发送
                          应包含 diagnosisHtml, diagnosisReport, marketingProposal 等字段
        """
        if final_results:
            # 确保final_results包含所有必要的字段
            self.final_results.update(final_results)
            
            # 验证关键字段是否存在
            expected_fields = ['diagnosisHtml', 'diagnosisReport', 'marketingProposal']
            missing_fields = [field for field in expected_fields if field not in self.final_results]
            if missing_fields:
                self.logger.warning(f"Final results missing expected fields: {missing_fields}")
        
        # 使用immediate update确保完成状态立即发送
        self.update_status_immediate(UnifiedTaskStatus.TASK_COMPLETED, custom_message)
    
    def mark_failed(self, error_message: str) -> None:
        """标记任务失败"""
        self.update_status(UnifiedTaskStatus.TASK_FAILED, error_message=error_message)
    
    def set_final_results(self, results: Dict[str, Any]) -> None:
        """
        设置最终结果数据
        
        Args:
            results: 最终结果数据，包含诊断报告、HTML等
        """
        self.final_results.update(results)
        self.logger.debug(f"Final results updated with keys: {list(results.keys())}")
    
    def get_final_results(self) -> Dict[str, Any]:
        """获取最终结果数据"""
        return self.final_results.copy()
    
    # ============================================
    # 兼容性接口 - 为了支持从 diagnosis_core.py 的平滑迁移
    # ============================================
    
    async def update_status_async(self, status: 'UnifiedTaskStatus', custom_message: Optional[str] = None,
                                progress: Optional[int] = None, extra_data: Optional[dict] = None,
                                step_details: Optional[dict] = None) -> None:
        """
        异步兼容接口 - 兼容 diagnosis_core.py 中的 TaskProgressTracker.update_status
        
        Args:
            status: 任务状态
            custom_message: 自定义消息
            progress: 自定义进度 (0-100)
            extra_data: 额外数据 (被忽略，仅为兼容性)
            step_details: 步骤详情 (被忽略，仅为兼容性)
        """
        # 如果提供了自定义进度，计算step_progress
        step_progress = None
        if progress is not None:
            current_progress = StatusManager.get_progress_percentage(status)
            next_status = StatusManager.get_next_status(status)
            if next_status:
                next_progress = StatusManager.get_progress_percentage(next_status)
                if next_progress > current_progress:
                    # 将progress转换为step_progress
                    step_progress = ((progress - current_progress) / (next_progress - current_progress)) * 100
        
        self.update_status(status, custom_message, step_progress)
    
    async def update_step_progress_async(self, step_name: str, step_progress: int,
                                       step_message: Optional[str] = None, step_data: Optional[dict] = None) -> None:
        """
        异步兼容接口 - 兼容 diagnosis_core.py 中的步骤进度更新
        
        Args:
            step_name: 步骤名称
            step_progress: 步骤进度 (0-100)
            step_message: 步骤消息
            step_data: 步骤数据 (被忽略，仅为兼容性)
        """
        # 将step_progress转换为当前/总数的格式
        current = int(step_progress)
        total = 100
        message = step_message or f"正在执行: {step_name}"
        self.update_step_progress(step_name, current, total, message)
    
    def get_current_status_compat(self) -> dict:
        """
        兼容性接口 - 返回与 diagnosis_core.py 兼容的状态格式
        """
        progress_info = self.get_current_progress()
        
        # 转换为 diagnosis_core.py 期望的格式
        api_status = UnifiedTaskStatus.to_api_status(self.progress.current_status)
        result = {
            "taskInfo": {
                "env": self.env,
                "taskId": progress_info["task_id"],
                "userId": self.user_id,
                "diagnosisId": self.diagnosis_id,
                "aiTaskStatus": api_status,
                "aiTaskMsg": StatusMessageManager.get_message(
                    self.progress.current_status, "en", self.verbose, 
                    self.progress.custom_message
                ),
                "aiTaskMsgCN": StatusMessageManager.get_message(
                    self.progress.current_status, "cn", self.verbose, 
                    self.progress.custom_message
                ),
                "aiTaskProgress": int(progress_info["progress"]),
                "timestamp": datetime.fromtimestamp(self.progress.last_update_time).isoformat(),
                "elapsed_time": progress_info.get("total_duration", 0)
            }
        }
        
        # 对于FINISH状态，添加最终结果
        if api_status == "FINISH" and self.final_results:
            result.update(self.final_results)
        
        return result
    
    def prepare_final_status_with_results(self, final_results: Dict[str, Any]) -> dict:
        """
        准备包含最终结果的完成状态数据
        
        Args:
            final_results: 最终结果数据
            
        Returns:
            dict: 完整的状态响应数据，包含taskInfo和最终结果
        """
        # 设置最终结果
        self.set_final_results(final_results)
        
        # 构建完整的响应数据
        status_data = {
            "taskInfo": {
                "env": self.env,
                "taskId": self.task_id,
                "userId": self.user_id,
                "diagnosisId": self.diagnosis_id,
                "aiTaskStatus": "FINISH",
                "aiTaskMsg": StatusMessageManager.get_message(
                    UnifiedTaskStatus.TASK_COMPLETED, "en", self.verbose
                ),
                "aiTaskMsgCN": StatusMessageManager.get_message(
                    UnifiedTaskStatus.TASK_COMPLETED, "cn", self.verbose
                ),
                "aiTaskProgress": 100,
                "timestamp": datetime.now().isoformat(),
                "elapsed_time": self.progress.performance_stats.get("total_duration", 0)
            }
        }
        
        # 合并最终结果
        status_data.update(final_results)
        
        return status_data
    
    async def push_final_status_to_redis(self, final_results: Dict[str, Any]) -> bool:
        """
        直接推送包含最终结果的完成状态到Redis
        
        Args:
            final_results: 最终结果数据
            
        Returns:
            bool: 是否推送成功
        """
        try:
            # 准备完整的状态数据
            status_data = self.prepare_final_status_with_results(final_results)
            
            # 使用proper Redis serialization
            message = safe_redis_serialize_with_validation(status_data)
            
            # Verify serialization
            if not isinstance(message, str):
                self.logger.error(f"Final status serialization failed: expected string, got {type(message)}")
                return False
            
            # Verify double serialization
            try:
                step1 = json.loads(message)
                step2 = json.loads(step1)
                self.logger.debug("Final status double serialization verified successfully")
            except (json.JSONDecodeError, TypeError) as e:
                self.logger.error(f"Final status double serialization verification failed: {e}")
                return False
            
            # 直接推送到Redis
            if self.redis_queue:
                # 修复导入路径
                import sys
                import os
                sys.path.append(os.path.join(os.path.dirname(__file__), '..'))
                from base_async_service import get_redis_connection
                async with get_redis_connection() as redis_conn:
                    await redis_conn.lpush(self.redis_queue, message)
                    queue_length = await redis_conn.llen(self.redis_queue)
                    self.logger.info(f"Final status pushed to Redis queue: {self.redis_queue}, length: {queue_length}")
                    return True
            else:
                self.logger.warning("No Redis queue configured for final status push")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to push final status to Redis: {e}")
            return False
    
    def get_status_history_compat(self) -> list:
        """
        兼容性接口 - 返回与 diagnosis_core.py 兼容的状态历史格式
        """
        # 基于当前实现构建历史记录
        return [{
            "status": self.progress.current_status.value,
            "progress": self.progress.progress_percentage,
            "timestamp": self.progress.last_update_time,
            "message": StatusMessageManager.get_message(
                self.progress.current_status, self.language, self.verbose
            ),
            "elapsed_time": self.progress.performance_stats.get("total_duration", 0)
        }]
    
    def get_performance_stats_compat(self) -> dict:
        """
        兼容性接口 - 返回与 diagnosis_core.py 兼容的性能统计格式
        """
        return {
            "total_elapsed_time": self.progress.performance_stats.get("total_duration", 0),
            "current_progress": self.progress.progress_percentage,
            "current_status": self.progress.current_status.value,
            "deep_research_enabled": self.enable_deep_research,
            "verbose_mode": self.verbose,
            "redis_available": self.redis_manager is not None,
            "status_updates_sent": self.progress.performance_stats.get("status_transitions", 0)
        }


# ============================================
# 兼容性类型别名和枚举映射
# ============================================

# 为了向后兼容，创建TaskStatus别名
TaskStatus = UnifiedTaskStatus

# 为了向后兼容，创建TaskProgressTracker别名
TaskProgressTracker = UnifiedProgressTracker