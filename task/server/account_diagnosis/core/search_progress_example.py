#!/usr/bin/env python3
"""
搜索进度更新使用示例
展示如何正确使用新的搜索进度更新方法来避免重复和倒退问题
"""

from status_manager import UnifiedProgressTracker, UnifiedTaskStatus
import asyncio
import time

async def example_search_with_proper_progress():
    """示例：正确的搜索进度更新方式"""
    
    # 创建进度跟踪器
    tracker = UnifiedProgressTracker(
        task_id="example_search_123",
        user_id="test_user",
        diagnosis_id=1,
        env="dev",
        enable_deep_research=True,
        verbose=True,
        language="cn"
    )
    
    # 1. 开始搜索阶段
    tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, "🚀 启动并行搜索引擎...")
    
    # 2. 定义搜索查询列表
    search_queries = [
        "Food & Beverage 发展机会 2025年",
        "Food & Beverage 用户行为分析 2025年", 
        "Food & Beverage 营销策略案例 2025年",
        "Food & Beverage 头部企业分析 2025年",
        "Food & Beverage 新兴技术应用 2025年",
        "Food & Beverage 行业最新趋势 2025年",
        "Food & Beverage 内容创作者发展策略 2025年",
        "Food & Beverage 博主运营技巧 2025年"
    ]
    
    total_queries = len(search_queries)
    
    # 3. 使用批次进度更新（推荐方式）
    print("=== 方式1：使用批次进度更新（推荐） ===")
    
    for i, query in enumerate(search_queries):
        # 开始查询
        tracker.update_search_batch_progress(
            batch_name="深度搜索",
            completed_queries=i,
            total_queries=total_queries,
            current_query=query
        )
        
        # 模拟搜索时间
        await asyncio.sleep(0.5)
        
        # 完成查询（每2个查询更新一次）
        if (i + 1) % 2 == 0 or i == total_queries - 1:
            tracker.update_search_batch_progress(
                batch_name="深度搜索",
                completed_queries=i + 1,
                total_queries=total_queries
            )
    
    await asyncio.sleep(1)
    
    # 4. 使用细粒度搜索进度更新（如果需要更详细的反馈）
    print("\n=== 方式2：使用细粒度搜索进度更新 ===")
    
    # 重置到搜索状态
    tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, "🔍 执行详细搜索...")
    
    for i, query in enumerate(search_queries):
        # 开始搜索
        tracker.update_search_progress(
            search_type="hybrid",
            query_text=query,
            current_query_index=i,
            total_queries=total_queries,
            is_completed=False
        )
        
        # 模拟搜索时间
        await asyncio.sleep(0.3)
        
        # 完成搜索
        results_count = 8 + (i % 3)  # 模拟不同的结果数量
        tracker.update_search_progress(
            search_type="hybrid",
            query_text=query,
            results_count=results_count,
            current_query_index=i,
            total_queries=total_queries,
            is_completed=True
        )
        
        # 短暂延迟
        await asyncio.sleep(0.2)
    
    # 5. 完成搜索阶段
    tracker.update_status(UnifiedTaskStatus.PROCESSING_RESULTS, "正在处理搜索结果...")
    
    await asyncio.sleep(1)
    
    # 6. 完成任务
    tracker.mark_completed("任务完成！")
    
    print("\n✅ 搜索进度更新示例完成")

async def example_wrong_way():
    """示例：错误的搜索进度更新方式（会导致重复和倒退）"""
    
    print("\n=== ❌ 错误方式示例（仅用于对比，不推荐） ===")
    
    tracker = UnifiedProgressTracker(
        task_id="example_wrong_123",
        user_id="test_user", 
        diagnosis_id=2,
        env="dev"
    )
    
    # 错误1：每个搜索都更新相同的状态和进度
    tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES)
    
    queries = ["查询1", "查询2", "查询3", "查询4"]
    
    for query in queries:
        # ❌ 错误：每次都用相同的状态和进度
        tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, f"搜索: {query}")
        await asyncio.sleep(0.2)
        
        # ❌ 错误：频繁的消息更新，没有进度变化
        tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, f"完成: {query}")
        await asyncio.sleep(0.2)
    
    # 错误2：突然跳到一个更低的进度状态
    # ❌ 这会导致进度倒退
    tracker.update_status(UnifiedTaskStatus.SEARCHING, "回到基础搜索...")
    
    print("❌ 错误方式演示完成（注意观察重复的进度和倒退问题）")

if __name__ == "__main__":
    print("🚀 搜索进度更新示例")
    print("这个示例展示了如何正确更新搜索进度，避免重复和倒退问题\n")
    
    # 运行正确的示例
    asyncio.run(example_search_with_proper_progress())
    
    # 运行错误的示例（用于对比）
    asyncio.run(example_wrong_way())