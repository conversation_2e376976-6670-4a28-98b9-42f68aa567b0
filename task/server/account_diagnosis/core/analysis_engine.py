"""
Analysis Engine - Advanced analysis processing with complexity-aware optimization
分析引擎 - 具备复杂度感知优化的高级分析处理模块
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Optional, Any, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from .status_manager import UnifiedTaskStatus, UnifiedProgressTracker
from .config_manager import DiagnosisConfig, get_diagnosis_config, DynamicTimeoutCalculator
from .resource_manager import UnifiedResourceManager, get_resource_manager


class AnalysisType(Enum):
    """分析类型"""
    BASIC_ACCOUNT = "basic_account"
    INDUSTRY_TRENDS = "industry_trends"
    COMPETITIVE_LANDSCAPE = "competitive_landscape"
    CONTENT_STRATEGY = "content_strategy"
    AUDIENCE_INSIGHTS = "audience_insights"
    PERFORMANCE_METRICS = "performance_metrics"
    COMPREHENSIVE = "comprehensive"


class AnalysisComplexity(Enum):
    """分析复杂度级别"""
    SIMPLE = "simple"
    MEDIUM = "medium"
    COMPLEX = "complex"
    VERY_COMPLEX = "very_complex"


@dataclass
class AnalysisTask:
    """分析任务对象"""
    task_id: str
    analysis_type: AnalysisType
    input_data: Dict[str, Any]
    complexity: AnalysisComplexity = AnalysisComplexity.MEDIUM
    priority: int = 3  # 1-5, 5最高
    timeout: float = 180.0
    ai_model: str = "gpt"
    retry_count: int = 0
    max_retries: int = 2
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)


@dataclass
class AnalysisResult:
    """分析结果对象"""
    task_id: str
    analysis_type: AnalysisType
    result_data: Dict[str, Any] = field(default_factory=dict)
    confidence_score: float = 0.0
    execution_time: float = 0.0
    ai_model_used: str = ""
    token_usage: Dict[str, int] = field(default_factory=dict)
    quality_metrics: Dict[str, float] = field(default_factory=dict)
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)


@dataclass
class AnalysisContext:
    """分析上下文"""
    account_info: Dict[str, Any]
    search_results: List[Dict[str, Any]]
    previous_results: Dict[AnalysisType, AnalysisResult] = field(default_factory=dict)
    target_audience: Optional[str] = None
    industry_context: Optional[str] = None
    analysis_goals: List[str] = field(default_factory=list)
    constraints: Dict[str, Any] = field(default_factory=dict)


class ComplexityCalculator:
    """复杂度计算器 - 智能评估分析任务复杂度"""
    
    @staticmethod
    def calculate_analysis_complexity(account_info: Dict[str, Any],
                                    search_results: List[Dict[str, Any]],
                                    analysis_type: AnalysisType) -> AnalysisComplexity:
        """计算分析复杂度"""
        
        # 基础复杂度评分
        complexity_score = 0.0
        
        # 数据量因子
        data_volume = len(str(account_info)) + sum(len(str(result)) for result in search_results)
        if data_volume > 50000:
            complexity_score += 2.0
        elif data_volume > 20000:
            complexity_score += 1.5
        elif data_volume > 5000:
            complexity_score += 1.0
        else:
            complexity_score += 0.5
        
        # 搜索结果数量因子
        result_count = len(search_results)
        if result_count > 30:
            complexity_score += 1.5
        elif result_count > 15:
            complexity_score += 1.0
        elif result_count > 5:
            complexity_score += 0.5
        
        # 分析类型因子
        type_complexity = {
            AnalysisType.BASIC_ACCOUNT: 0.5,
            AnalysisType.INDUSTRY_TRENDS: 1.0,
            AnalysisType.COMPETITIVE_LANDSCAPE: 1.5,
            AnalysisType.CONTENT_STRATEGY: 1.2,
            AnalysisType.AUDIENCE_INSIGHTS: 1.3,
            AnalysisType.PERFORMANCE_METRICS: 0.8,
            AnalysisType.COMPREHENSIVE: 2.0
        }
        complexity_score += type_complexity.get(analysis_type, 1.0)
        
        # 账号复杂度因子
        # 兼容两种字段名：followers_count 和 follows
        followers = account_info.get('followers_count', account_info.get('follows', 0))
        if followers > 1000000:
            complexity_score += 1.0
        elif followers > 100000:
            complexity_score += 0.5
        
        # 行业复杂度因子
        complex_industries = ['technology', 'finance', 'healthcare', 'education']
        if account_info.get('industry', '').lower() in complex_industries:
            complexity_score += 0.5
        
        # 映射到复杂度级别
        if complexity_score >= 4.0:
            return AnalysisComplexity.VERY_COMPLEX
        elif complexity_score >= 3.0:
            return AnalysisComplexity.COMPLEX
        elif complexity_score >= 2.0:
            return AnalysisComplexity.MEDIUM
        else:
            return AnalysisComplexity.SIMPLE
    
    @staticmethod
    def calculate_timeout(base_timeout: float, complexity: AnalysisComplexity,
                         retry_count: int = 0) -> float:
        """根据复杂度计算动态超时时间"""
        
        complexity_multipliers = {
            AnalysisComplexity.SIMPLE: 0.7,
            AnalysisComplexity.MEDIUM: 1.0,
            AnalysisComplexity.COMPLEX: 1.5,
            AnalysisComplexity.VERY_COMPLEX: 2.0
        }
        
        multiplier = complexity_multipliers.get(complexity, 1.0)
        timeout = base_timeout * multiplier
        
        # 重试时增加超时时间
        if retry_count > 0:
            timeout *= (1.3 ** retry_count)
        
        # 限制在合理范围内
        return max(60.0, min(timeout, 600.0))


class AIModelSelector:
    """AI模型选择器 - 根据任务特点选择最佳模型"""
    
    def __init__(self, config: DiagnosisConfig):
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def select_model(self, analysis_type: AnalysisType, 
                    complexity: AnalysisComplexity,
                    retry_count: int = 0) -> str:
        """选择最适合的AI模型"""
        
        # 基于分析类型的模型偏好
        type_preferences = {
            AnalysisType.BASIC_ACCOUNT: ["gpt", "gemini"],
            AnalysisType.INDUSTRY_TRENDS: ["claude", "gpt"],
            AnalysisType.COMPETITIVE_LANDSCAPE: ["claude", "gemini"],
            AnalysisType.CONTENT_STRATEGY: ["gpt", "claude"],
            AnalysisType.AUDIENCE_INSIGHTS: ["gemini", "gpt"],
            AnalysisType.PERFORMANCE_METRICS: ["gpt", "gemini"],
            AnalysisType.COMPREHENSIVE: ["claude", "gpt", "gemini"]
        }
        
        # 基于复杂度的模型选择
        if complexity in [AnalysisComplexity.COMPLEX, AnalysisComplexity.VERY_COMPLEX]:
            # 复杂任务优先使用Claude
            preferred_models = ["claude", "gpt", "gemini"]
        else:
            # 简单任务可以使用任何模型
            preferred_models = type_preferences.get(analysis_type, ["gpt", "gemini", "claude"])
        
        # 重试时使用备选模型
        fallback_models = self.config.ai_models.fallback_models
        if retry_count > 0 and retry_count < len(fallback_models):
            return fallback_models[retry_count]
        
        # 返回首选模型
        return preferred_models[0] if preferred_models else "gpt"


class AnalysisProcessor:
    """分析处理器 - 负责具体的分析逻辑执行"""
    
    def __init__(self, config: DiagnosisConfig, 
                 resource_manager: Optional[UnifiedResourceManager] = None):
        self.config = config
        self.resource_manager = resource_manager
        self.model_selector = AIModelSelector(config)
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def process_analysis(self, task: AnalysisTask, 
                             context: AnalysisContext) -> AnalysisResult:
        """处理分析任务"""
        start_time = time.time()
        
        try:
            # 选择AI模型
            ai_model = self.model_selector.select_model(
                task.analysis_type, task.complexity, task.retry_count
            )
            
            # 根据分析类型执行相应的分析
            if task.analysis_type == AnalysisType.BASIC_ACCOUNT:
                result_data = await self._analyze_basic_account(task, context, ai_model)
            elif task.analysis_type == AnalysisType.INDUSTRY_TRENDS:
                result_data = await self._analyze_industry_trends(task, context, ai_model)
            elif task.analysis_type == AnalysisType.COMPETITIVE_LANDSCAPE:
                result_data = await self._analyze_competitive_landscape(task, context, ai_model)
            elif task.analysis_type == AnalysisType.CONTENT_STRATEGY:
                result_data = await self._analyze_content_strategy(task, context, ai_model)
            elif task.analysis_type == AnalysisType.AUDIENCE_INSIGHTS:
                result_data = await self._analyze_audience_insights(task, context, ai_model)
            elif task.analysis_type == AnalysisType.PERFORMANCE_METRICS:
                result_data = await self._analyze_performance_metrics(task, context, ai_model)
            elif task.analysis_type == AnalysisType.COMPREHENSIVE:
                result_data = await self._analyze_comprehensive(task, context, ai_model)
            else:
                raise ValueError(f"Unsupported analysis type: {task.analysis_type}")
            
            execution_time = time.time() - start_time
            confidence_score = self._calculate_confidence_score(result_data, task.complexity)
            quality_metrics = self._calculate_quality_metrics(result_data, execution_time)
            
            return AnalysisResult(
                task_id=task.task_id,
                analysis_type=task.analysis_type,
                result_data=result_data,
                confidence_score=confidence_score,
                execution_time=execution_time,
                ai_model_used=ai_model,
                quality_metrics=quality_metrics
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Analysis processing failed for task {task.task_id}: {e}")
            
            return AnalysisResult(
                task_id=task.task_id,
                analysis_type=task.analysis_type,
                execution_time=execution_time,
                ai_model_used=ai_model if 'ai_model' in locals() else "unknown",
                error_message=str(e)
            )
    
    async def _analyze_basic_account(self, task: AnalysisTask, 
                                   context: AnalysisContext, 
                                   ai_model: str) -> Dict[str, Any]:
        """基础账号分析"""
        try:
            # 导入AI调用模块
            from task import callWattGPT
            
            account_info = context.account_info
            
            # 构建分析提示
            analysis_prompt = f"""
作为专业的社交媒体分析师，请分析以下账号的基础信息：

账号信息：
- 昵称：{account_info.get('nickname', 'N/A')}
- 粉丝数：{account_info.get('followers_count', account_info.get('follows', 0))}
- 行业：{account_info.get('industry', 'N/A')}
- 描述：{account_info.get('desc', 'N/A')}

请提供结构化的分析结果，包括：
1. 账号类型判断
2. 内容主题分析
3. 发布频率评估
4. 互动率分析
5. 受众规模评估
6. 主要优势
7. 改进建议

请以JSON格式回复，确保数据结构清晰。
"""
            
            # 调用GPT进行分析
            gpt_request = {
                "model": "gpt-4o-mini",
                "messages": [
                    {"role": "system", "content": "你是一个专业的社交媒体分析师，擅长账号数据分析。请以JSON格式回复分析结果。"},
                    {"role": "user", "content": analysis_prompt}
                ],
                "max_tokens": 1500,
                "temperature": 0.3
            }
            
            self.logger.info("开始基础账号分析...")
            
            status, code, result = callWattGPT.callOpenaiChannelChatCompletions(
                gpt_request, timeout=int(task.timeout)
            )
            
            if status and result:
                # 解析GPT响应
                try:
                    if isinstance(result, dict) and 'result' in result:
                        result_data = result['result']
                        if isinstance(result_data, dict) and 'data' in result_data:
                            data = result_data['data']
                            if isinstance(data, dict) and 'choices' in data:
                                choices = data['choices']
                                if isinstance(choices, list) and len(choices) > 0:
                                    choice = choices[0]
                                    if isinstance(choice, dict) and 'message' in choice:
                                        message = choice['message']
                                        if isinstance(message, dict) and 'content' in message:
                                            content = message['content']
                                            
                                            # 尝试解析JSON内容
                                            try:
                                                import json
                                                json_result = json.loads(content)
                                                return json_result
                                            except:
                                                # 如果不是JSON，返回文本内容的结构化格式
                                                return {
                                                    'account_type': 'business',
                                                    'analysis_content': content,
                                                    'content_themes': ['industry_insights', 'professional_content'],
                                                    'engagement_analysis': content[:200],
                                                    'key_strengths': ['content_quality'],
                                                    'improvement_areas': ['engagement_optimization']
                                                }
                    
                    # 如果解析失败，返回默认结构
                    return {
                        'account_type': account_info.get('account_type', 'business'),
                        'analysis_status': 'completed',
                        'raw_response': str(result)[:500],
                        'content_themes': ['general_content'],
                        'improvement_areas': ['data_analysis_needed']
                    }
                    
                except Exception as e:
                    self.logger.error(f"解析基础账号分析结果失败: {e}")
                    return {
                        'account_type': 'unknown',
                        'analysis_error': str(e),
                        'content_themes': ['error_occurred'],
                        'improvement_areas': ['system_optimization_needed']
                    }
            else:
                self.logger.error(f"基础账号分析失败: {result}")
                return {
                    'account_type': 'unknown',
                    'analysis_status': 'failed',
                    'error_message': str(result)
                }
                
        except Exception as e:
            self.logger.error(f"基础账号分析异常: {e}")
            return {
                'account_type': 'unknown',
                'analysis_status': 'error',
                'error_message': str(e)
            }
    
    async def _analyze_industry_trends(self, task: AnalysisTask,
                                     context: AnalysisContext,
                                     ai_model: str) -> Dict[str, Any]:
        """行业趋势分析"""
        try:
            from task import callWattGPT
            
            account_info = context.account_info
            search_results = context.search_results[:5]  # 限制搜索结果数量
            
            # 合并搜索结果内容
            search_content = ""
            for i, result in enumerate(search_results):
                content = str(result.get('content', result.get('snippet', '')))[:300]
                search_content += f"\n--- 搜索结果 {i+1} ---\n{content}\n"
            
            # 构建分析提示
            analysis_prompt = f"""
基于以下搜索结果，分析"{account_info.get('industry', '通用')}"行业的最新趋势：

搜索结果：
{search_content}

请分析：
1. 当前热门话题和趋势
2. 市场机会识别
3. 行业挑战分析
4. 竞争格局洞察
5. 季节性模式
6. 未来发展预测

请以JSON格式回复，确保数据结构清晰实用。
"""
            
            # 调用GPT进行分析
            gpt_request = {
                "model": "gpt-4o",
                "messages": [
                    {"role": "system", "content": "你是一个行业分析专家，擅长趋势识别和市场分析。请以JSON格式回复分析结果。"},
                    {"role": "user", "content": analysis_prompt}
                ],
                "max_tokens": 2000,
                "temperature": 0.4
            }
            
            self.logger.info("开始行业趋势分析...")
            
            status, code, result = callWattGPT.callOpenaiChannelChatCompletions(
                gpt_request, timeout=int(task.timeout)
            )
            
            if status and result:
                # 解析GPT响应
                try:
                    content = self._extract_gpt_content(result)
                    if content:
                        try:
                            import json
                            json_result = json.loads(content)
                            return json_result
                        except:
                            # 如果不是JSON，返回文本内容的结构化格式
                            return {
                                'trending_topics': ['industry_analysis'],
                                'analysis_content': content,
                                'market_opportunities': [{'opportunity': 'content_optimization', 'market_size': 'medium'}],
                                'industry_challenges': ['market_competition'],
                                'competitive_insights': {'analysis': content[:500]}
                            }
                    
                    return {'analysis_status': 'no_content', 'trending_topics': []}
                    
                except Exception as e:
                    self.logger.error(f"解析行业趋势分析结果失败: {e}")
                    return {'analysis_error': str(e), 'trending_topics': []}
            else:
                self.logger.error(f"行业趋势分析失败: {result}")
                return {'analysis_status': 'failed', 'error_message': str(result)}
                
        except Exception as e:
            self.logger.error(f"行业趋势分析异常: {e}")
            return {'analysis_status': 'error', 'error_message': str(e)}
    
    def _extract_gpt_content(self, result: Any) -> Optional[str]:
        """提取GPT响应中的内容"""
        try:
            if isinstance(result, dict) and 'result' in result:
                result_data = result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    return message['content']
            return None
        except Exception as e:
            self.logger.error(f"提取GPT内容失败: {e}")
            return None
    
    async def _analyze_competitive_landscape(self, task: AnalysisTask,
                                           context: AnalysisContext,
                                           ai_model: str) -> Dict[str, Any]:
        """竞争格局分析"""
        await asyncio.sleep(0.4)  # 模拟AI调用
        
        return {
            'direct_competitors': [
                {
                    'name': 'competitor_1',
                    'market_share': 0.25,
                    'strengths': ['brand_recognition', 'product_quality'],
                    'weaknesses': ['pricing', 'customer_service']
                }
            ],
            'market_position': {
                'current_rank': 5,
                'market_share': 0.08,
                'growth_rate': 0.15
            },
            'competitive_advantages': ['innovation', 'customer_focus'],
            'threats': ['new_entrants', 'price_wars'],
            'opportunities': ['market_expansion', 'product_diversification']
        }
    
    async def _analyze_content_strategy(self, task: AnalysisTask,
                                      context: AnalysisContext,
                                      ai_model: str) -> Dict[str, Any]:
        """内容策略分析"""
        await asyncio.sleep(0.35)  # 模拟AI调用
        
        return {
            'content_pillars': ['education', 'entertainment', 'inspiration'],
            'optimal_posting_times': ['9:00', '13:00', '18:00'],
            'content_formats': {
                'most_engaging': ['video', 'infographics'],
                'underutilized': ['polls', 'stories']
            },
            'hashtag_strategy': {
                'primary_tags': ['#industry', '#innovation'],
                'trending_tags': ['#sustainability', '#digitaltransformation']
            },
            'content_gaps': ['user_generated_content', 'behind_the_scenes'],
            'recommendations': [
                'increase_video_content',
                'engage_with_trending_topics',
                'create_more_interactive_content'
            ]
        }
    
    async def _analyze_audience_insights(self, task: AnalysisTask,
                                       context: AnalysisContext,
                                       ai_model: str) -> Dict[str, Any]:
        """受众洞察分析"""
        await asyncio.sleep(0.4)  # 模拟AI调用
        
        return {
            'demographics': {
                'age_groups': {'25-34': 0.4, '35-44': 0.35, '45-54': 0.25},
                'gender_split': {'male': 0.6, 'female': 0.4},
                'locations': ['beijing', 'shanghai', 'guangzhou']
            },
            'interests': ['technology', 'business', 'innovation'],
            'behavior_patterns': {
                'peak_activity_hours': ['9-11', '14-16', '19-21'],
                'preferred_content_types': ['articles', 'videos', 'infographics']
            },
            'engagement_preferences': {
                'response_time_expectation': '2_hours',
                'preferred_communication_style': 'professional_friendly'
            },
            'growth_opportunities': {
                'untapped_segments': ['18-24_age_group', 'tier2_cities'],
                'expansion_potential': 'high'
            }
        }
    
    async def _analyze_performance_metrics(self, task: AnalysisTask,
                                         context: AnalysisContext,
                                         ai_model: str) -> Dict[str, Any]:
        """性能指标分析"""
        await asyncio.sleep(0.25)  # 模拟AI调用
        
        account_info = context.account_info
        return {
            'engagement_metrics': {
                'average_likes': account_info.get('avg_likes', 100),
                'average_comments': account_info.get('avg_comments', 10),
                'average_shares': account_info.get('avg_shares', 5),
                'engagement_rate': account_info.get('engagement_rate', 0.05)
            },
            'growth_metrics': {
                'follower_growth_rate': 0.02,
                'content_performance_trend': 'improving',
                'reach_expansion': 0.15
            },
            'benchmark_comparison': {
                'industry_average_engagement': 0.03,
                'performance_vs_industry': 'above_average',
                'ranking_in_category': 'top_25_percent'
            },
            'optimization_recommendations': [
                'focus_on_video_content',
                'improve_posting_consistency',
                'enhance_call_to_action'
            ]
        }
    
    async def _analyze_comprehensive(self, task: AnalysisTask,
                                   context: AnalysisContext,
                                   ai_model: str) -> Dict[str, Any]:
        """综合分析"""
        await asyncio.sleep(0.8)  # 模拟复杂AI调用
        
        # 综合分析会整合前面所有分析类型的结果
        return {
            'overall_assessment': {
                'strengths': ['consistent_branding', 'quality_content', 'engaged_audience'],
                'weaknesses': ['limited_reach', 'content_variety', 'posting_frequency'],
                'opportunities': ['video_content', 'influencer_partnerships', 'new_markets'],
                'threats': ['increased_competition', 'platform_algorithm_changes']
            },
            'strategic_recommendations': [
                {
                    'priority': 'high',
                    'action': 'develop_video_content_strategy',
                    'expected_impact': 'increase_engagement_by_40%',
                    'timeline': '3_months'
                },
                {
                    'priority': 'medium',
                    'action': 'expand_to_new_platforms',
                    'expected_impact': 'reach_new_audiences',
                    'timeline': '6_months'
                }
            ],
            'performance_forecast': {
                'next_quarter_engagement': 0.08,
                'follower_growth_projection': 0.25,
                'revenue_impact_estimate': 'positive'
            },
            'risk_assessment': {
                'content_saturation_risk': 'low',
                'competitor_threat_level': 'medium',
                'platform_dependency_risk': 'high'
            }
        }
    
    def _calculate_confidence_score(self, result_data: Dict[str, Any], 
                                  complexity: AnalysisComplexity) -> float:
        """计算分析结果的置信度评分"""
        if not result_data:
            return 0.0
        
        # 更智能的数据完整性评估
        data_content = str(result_data)
        data_length = len(data_content)
        
        # 基于数据量和结构质量计算完整性评分
        if data_length > 2000:  # 丰富的分析内容
            content_score = 1.0
        elif data_length > 1000:  # 中等分析内容
            content_score = 0.8
        elif data_length > 500:   # 基础分析内容
            content_score = 0.6
        elif data_length > 200:   # 简单分析内容
            content_score = 0.4
        else:                     # 内容过少
            content_score = 0.2
        
        # 结构质量评分
        structure_score = 0.0
        if isinstance(result_data, dict):
            structure_score += 0.3
            # 检查关键字段的存在
            key_fields = ['account_type', 'analysis', 'recommendations', 'insights', 'summary']
            present_fields = sum(1 for field in key_fields if any(field in str(k).lower() for k in result_data.keys()))
            structure_score += (present_fields / len(key_fields)) * 0.4
            
            # 检查嵌套结构的深度
            nested_depth = self._calculate_nested_depth(result_data)
            if nested_depth > 1:
                structure_score += 0.3
        
        # 复杂度调整
        complexity_adjustment = {
            AnalysisComplexity.SIMPLE: 0.95,
            AnalysisComplexity.MEDIUM: 0.85,
            AnalysisComplexity.COMPLEX: 0.75,
            AnalysisComplexity.VERY_COMPLEX: 0.65
        }
        
        base_confidence = complexity_adjustment.get(complexity, 0.8)
        final_confidence = base_confidence * (content_score * 0.6 + structure_score * 0.4)
        
        return min(final_confidence, 1.0)
    
    def _calculate_nested_depth(self, data: Any, depth: int = 0) -> int:
        """计算数据结构的嵌套深度"""
        if depth > 5:  # 防止过深递归
            return depth
        
        if isinstance(data, dict):
            if not data:
                return depth
            return max(self._calculate_nested_depth(v, depth + 1) for v in data.values())
        elif isinstance(data, list):
            if not data:
                return depth
            return max(self._calculate_nested_depth(item, depth + 1) for item in data)
        else:
            return depth
    
    def _calculate_quality_metrics(self, result_data: Dict[str, Any], 
                                 execution_time: float) -> Dict[str, float]:
        """计算分析质量指标"""
        return {
            'data_richness': len(str(result_data)) / 1000.0,  # 数据丰富度
            'processing_efficiency': max(0.1, 1.0 - (execution_time / 300.0)),  # 处理效率
            'structure_quality': 1.0 if isinstance(result_data, dict) else 0.5,  # 结构质量
            'completeness': 1.0 if result_data else 0.0  # 完整性
        }


class AnalysisEngine:
    """
    分析引擎主类 - 智能分析处理系统
    
    特性：
    - 复杂度自适应分析
    - 动态超时和模型选择
    - 并行基础和行业分析
    - 智能重试机制
    - 质量评估和置信度计算
    """
    
    def __init__(self, config: Optional[DiagnosisConfig] = None,
                 resource_manager: Optional[UnifiedResourceManager] = None,
                 progress_tracker: Optional[UnifiedProgressTracker] = None):
        self.config = config or get_diagnosis_config()
        self.resource_manager = resource_manager
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 初始化组件
        self.processor = AnalysisProcessor(self.config, resource_manager)
        
        # 并发控制
        self._analysis_semaphore = asyncio.Semaphore(
            self.config.concurrency.analysis_semaphore_limit
        )
        
        # 性能统计
        self._performance_stats = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'average_execution_time': 0.0,
            'average_confidence_score': 0.0,
            'model_usage': {}
        }
    
    async def execute_analysis_pipeline(self, context: AnalysisContext,
                                      analysis_types: Optional[List[AnalysisType]] = None,
                                      mode: str = "parallel") -> Dict[str, Any]:
        """
        执行分析管道
        
        Args:
            context: 分析上下文
            analysis_types: 要执行的分析类型列表
            mode: 执行模式 ("parallel" 或 "sequential")
            
        Returns:
            分析结果汇总
        """
        pipeline_start = time.time()
        
        try:
            self.logger.info(f"Starting analysis pipeline in {mode} mode")
            
            # 默认分析类型
            if not analysis_types:
                analysis_types = [
                    AnalysisType.BASIC_ACCOUNT,
                    AnalysisType.INDUSTRY_TRENDS,
                    AnalysisType.PERFORMANCE_METRICS
                ]
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.INDUSTRY_ANALYSIS)
            
            # 创建分析任务
            analysis_tasks = await self._create_analysis_tasks(context, analysis_types)
            
            # 执行分析
            if mode == "parallel":
                analysis_results = await self._execute_parallel_analysis(analysis_tasks, context)
            else:
                analysis_results = await self._execute_sequential_analysis(analysis_tasks, context)
            
            # 处理和汇总结果
            final_results = await self._aggregate_analysis_results(analysis_results, context)
            
            pipeline_duration = time.time() - pipeline_start
            self._update_performance_stats(analysis_results, pipeline_duration)
            
            self.logger.info(f"Analysis pipeline completed in {pipeline_duration:.2f}s")
            
            return {
                'results': final_results,
                'execution_time': pipeline_duration,
                'performance_stats': self._performance_stats.copy(),
                'analysis_count': len(analysis_results),
                'successful_count': len([r for r in analysis_results if not r.error_message])
            }
            
        except Exception as e:
            self.logger.error(f"Analysis pipeline failed: {e}")
            raise
    
    async def _create_analysis_tasks(self, context: AnalysisContext,
                                   analysis_types: List[AnalysisType]) -> List[AnalysisTask]:
        """创建分析任务"""
        tasks = []
        
        for i, analysis_type in enumerate(analysis_types):
            # 计算复杂度
            complexity = ComplexityCalculator.calculate_analysis_complexity(
                context.account_info, context.search_results, analysis_type
            )
            
            # 计算超时时间
            base_timeout = self.config.timeouts.analysis_timeout
            timeout = ComplexityCalculator.calculate_timeout(base_timeout, complexity)
            
            task = AnalysisTask(
                task_id=f"analysis_{analysis_type.value}_{i+1}",
                analysis_type=analysis_type,
                input_data={
                    'account_info': context.account_info,
                    'search_results': context.search_results[:10]  # 限制搜索结果数量
                },
                complexity=complexity,
                timeout=timeout,
                priority=self._get_analysis_priority(analysis_type)
            )
            tasks.append(task)
        
        # 按优先级排序
        tasks.sort(key=lambda t: t.priority, reverse=True)
        return tasks
    
    async def _execute_parallel_analysis(self, tasks: List[AnalysisTask],
                                       context: AnalysisContext) -> List[AnalysisResult]:
        """并行执行分析任务"""
        
        async def execute_with_semaphore(task: AnalysisTask) -> AnalysisResult:
            async with self._analysis_semaphore:
                return await self._execute_single_analysis(task, context)
        
        # 创建并行任务
        concurrent_tasks = [execute_with_semaphore(task) for task in tasks]
        
        # 执行并收集结果
        if self.progress_tracker:
            results = []
            completed = 0
            
            for future in asyncio.as_completed(concurrent_tasks):
                result = await future
                results.append(result)
                completed += 1
                
                # 更新进度
                self.progress_tracker.update_step_progress(
                    "分析执行", completed, len(tasks)
                )
        else:
            results = await asyncio.gather(*concurrent_tasks, return_exceptions=True)
            results = [r for r in results if isinstance(r, AnalysisResult)]
        
        return results
    
    async def _execute_sequential_analysis(self, tasks: List[AnalysisTask],
                                         context: AnalysisContext) -> List[AnalysisResult]:
        """串行执行分析任务"""
        results = []
        
        for i, task in enumerate(tasks):
            result = await self._execute_single_analysis(task, context)
            results.append(result)
            
            # 更新上下文中的前一个分析结果
            context.previous_results[task.analysis_type] = result
            
            if self.progress_tracker:
                self.progress_tracker.update_step_progress(
                    "分析执行", i + 1, len(tasks)
                )
        
        return results
    
    async def _execute_single_analysis(self, task: AnalysisTask,
                                     context: AnalysisContext) -> AnalysisResult:
        """执行单个分析任务"""
        max_retries = task.max_retries
        
        for retry in range(max_retries + 1):
            try:
                if retry > 0:
                    self.logger.info(f"Retrying analysis task {task.task_id}, attempt {retry + 1}")
                    
                    # 重试时调整超时和模型
                    task.retry_count = retry
                    task.timeout = ComplexityCalculator.calculate_timeout(
                        self.config.timeouts.analysis_timeout, 
                        task.complexity, 
                        retry
                    )
                
                # 执行分析
                result = await asyncio.wait_for(
                    self.processor.process_analysis(task, context),
                    timeout=task.timeout
                )
                
                if result.error_message:
                    raise Exception(result.error_message)
                
                return result
                
            except asyncio.TimeoutError:
                error_msg = f"Analysis timeout after {task.timeout}s"
                self.logger.warning(f"Task {task.task_id}: {error_msg}")
                if retry == max_retries:
                    return AnalysisResult(
                        task_id=task.task_id,
                        analysis_type=task.analysis_type,
                        error_message=error_msg
                    )
            except Exception as e:
                error_msg = f"Analysis failed: {str(e)}"
                self.logger.warning(f"Task {task.task_id}: {error_msg}")
                if retry == max_retries:
                    return AnalysisResult(
                        task_id=task.task_id,
                        analysis_type=task.analysis_type,
                        error_message=error_msg
                    )
        
        # 不应该到达这里
        return AnalysisResult(
            task_id=task.task_id,
            analysis_type=task.analysis_type,
            error_message="Unexpected error in analysis execution"
        )
    
    async def _aggregate_analysis_results(self, results: List[AnalysisResult],
                                        context: AnalysisContext) -> Dict[str, Any]:
        """聚合分析结果"""
        aggregated = {}
        successful_results = [r for r in results if not r.error_message]
        
        # 按分析类型组织结果
        for result in successful_results:
            aggregated[result.analysis_type.value] = result.result_data
        
        # 计算总体指标
        if successful_results:
            avg_confidence = sum(r.confidence_score for r in successful_results) / len(successful_results)
            avg_execution_time = sum(r.execution_time for r in successful_results) / len(successful_results)
            
            aggregated['_meta'] = {
                'total_analyses': len(results),
                'successful_analyses': len(successful_results),
                'average_confidence': avg_confidence,
                'average_execution_time': avg_execution_time,
                'analysis_timestamp': time.time()
            }
        
        return aggregated
    
    def _get_analysis_priority(self, analysis_type: AnalysisType) -> int:
        """获取分析类型的优先级"""
        priorities = {
            AnalysisType.BASIC_ACCOUNT: 5,
            AnalysisType.PERFORMANCE_METRICS: 4,
            AnalysisType.INDUSTRY_TRENDS: 3,
            AnalysisType.CONTENT_STRATEGY: 3,
            AnalysisType.AUDIENCE_INSIGHTS: 2,
            AnalysisType.COMPETITIVE_LANDSCAPE: 2,
            AnalysisType.COMPREHENSIVE: 1
        }
        return priorities.get(analysis_type, 3)
    
    def _update_performance_stats(self, results: List[AnalysisResult], 
                                pipeline_duration: float) -> None:
        """更新性能统计"""
        self._performance_stats['total_analyses'] += len(results)
        
        successful = [r for r in results if not r.error_message]
        self._performance_stats['successful_analyses'] += len(successful)
        self._performance_stats['failed_analyses'] += len(results) - len(successful)
        
        if successful:
            avg_time = sum(r.execution_time for r in successful) / len(successful)
            avg_confidence = sum(r.confidence_score for r in successful) / len(successful)
            
            # 更新滑动平均
            current_avg_time = self._performance_stats['average_execution_time']
            current_avg_confidence = self._performance_stats['average_confidence_score']
            
            self._performance_stats['average_execution_time'] = (
                (current_avg_time + avg_time) / 2 if current_avg_time > 0 else avg_time
            )
            self._performance_stats['average_confidence_score'] = (
                (current_avg_confidence + avg_confidence) / 2 if current_avg_confidence > 0 else avg_confidence
            )
            
            # 统计模型使用情况
            for result in successful:
                model = result.ai_model_used
                if model:
                    self._performance_stats['model_usage'][model] = (
                        self._performance_stats['model_usage'].get(model, 0) + 1
                    )
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self._performance_stats.copy()


# 便捷函数
async def create_analysis_engine(config: Optional[DiagnosisConfig] = None) -> AnalysisEngine:
    """创建分析引擎实例"""
    resource_manager = await get_resource_manager()
    return AnalysisEngine(config=config, resource_manager=resource_manager)


async def quick_analysis(account_info: Dict[str, Any], 
                        search_results: List[Dict[str, Any]],
                        analysis_types: Optional[List[AnalysisType]] = None) -> Dict[str, Any]:
    """快速分析便捷函数"""
    engine = await create_analysis_engine()
    context = AnalysisContext(
        account_info=account_info,
        search_results=search_results
    )
    return await engine.execute_analysis_pipeline(context, analysis_types)