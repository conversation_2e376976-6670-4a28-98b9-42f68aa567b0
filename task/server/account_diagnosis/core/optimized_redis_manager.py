"""
Optimized Redis Manager - 优化的Redis资源管理器
平衡简单性和功能性，提供可靠的Redis连接池而不过度复杂化
"""

import asyncio
import logging
import ssl
import threading
import time
import sys
import os
from typing import Dict, Optional, Any, AsyncContextManager, List
from contextlib import asynccontextmanager
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass

# 导入Redis配置
try:
    from task import REDIS_CLUSTER_CONFIG
except ImportError:
    # 如果无法直接导入，尝试添加路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    try:
        from task import REDIS_CLUSTER_CONFIG
    except ImportError:
        # 提供默认配置
        REDIS_CLUSTER_CONFIG = {
            'host': 'localhost',
            'port': 6379,
            'password': None,
            'ssl': False,
            'decode_responses': True
        }
        logging.warning("使用默认Redis配置，请检查REDIS_CLUSTER_CONFIG导入")


@dataclass
class PoolConfig:
    """连接池配置 - 针对低延迟队列监控优化"""
    min_connections: int = 5  # 增加最小连接数，确保立即可用
    max_connections: int = 20  # 增加最大连接数，支持更高并发
    connection_timeout: float = 10.0  # 适中的连接超时
    socket_timeout: float = 60.0  # 增加socket超时，适配长时间blpop
    idle_timeout: float = 600.0  # 10分钟空闲超时，适配长时间blpop
    health_check_interval: float = 180.0  # 3分钟健康检查间隔
    disconnect_timeout: float = 3.0  # 减少断开连接超时，快速释放资源
    retry_on_timeout: bool = True  # 启用超时重试
    max_retries: int = 2  # 减少重试次数，快速失败


class RedisConnection:
    """Redis连接包装器"""
    
    def __init__(self, connection, connection_id: str):
        self.connection = connection
        self.connection_id = connection_id
        self.created_at = time.time()
        self.last_used_at = time.time()
        self.is_healthy = True
        self.usage_count = 0
        self._lock = asyncio.Lock()
    
    async def ping(self) -> bool:
        """检查连接健康状态 - 快速ping检查"""
        try:
            async with self._lock:
                await asyncio.wait_for(self.connection.ping(), timeout=5.0)  # 5秒ping超时，快速检测
                self.last_used_at = time.time()
                self.is_healthy = True
                return True
        except Exception:
            self.is_healthy = False
            return False
    
    async def execute(self, command: str, *args, **kwargs):
        """执行Redis命令"""
        async with self._lock:
            try:
                self.last_used_at = time.time()
                self.usage_count += 1
                
                # 根据命令类型执行
                if hasattr(self.connection, command):
                    method = getattr(self.connection, command)
                    # 对于Redis集群，所有方法都应该被await
                    result = method(*args, **kwargs)
                    if asyncio.iscoroutine(result):
                        return await result
                    else:
                        return result
                else:
                    raise AttributeError(f"Redis connection has no method: {command}")
                    
            except Exception as e:
                self.is_healthy = False
                raise e
    
    # 常用Redis方法的代理 - 直接调用底层连接，避免双重锁定
    async def blpop(self, *args, **kwargs):
        """BLPOP命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.blpop(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def brpop(self, *args, **kwargs):
        """BRPOP命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.brpop(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def lpush(self, *args, **kwargs):
        """LPUSH命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.lpush(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def rpush(self, *args, **kwargs):
        """RPUSH命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.rpush(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def llen(self, *args, **kwargs):
        """LLEN命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.llen(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def get(self, *args, **kwargs):
        """GET命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.get(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def set(self, *args, **kwargs):
        """SET命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.set(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def delete(self, *args, **kwargs):
        """DELETE命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.delete(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def ttl(self, *args, **kwargs):
        """TTL命令代理"""
        try:
            self.last_used_at = time.time()
            self.usage_count += 1
            return await self.connection.ttl(*args, **kwargs)
        except Exception as e:
            self.is_healthy = False
            raise e
    
    async def close(self):
        """关闭连接"""
        try:
            if self.connection and hasattr(self.connection, 'close'):
                # 使用超时关闭连接，避免长时间等待
                await asyncio.wait_for(self.connection.close(), timeout=2.0)
        except (asyncio.TimeoutError, Exception):
            # 超时或其他错误时直接忽略，避免阻塞
            pass
    
    @property
    def is_idle_timeout(self) -> bool:
        """检查是否空闲超时 - 适配长时间blpop操作"""
        return (time.time() - self.last_used_at) > 600.0  # 10分钟，适配长时间blpop


class OptimizedRedisPool:
    """优化的Redis连接池"""
    
    def __init__(self, config: Optional[PoolConfig] = None):
        self.config = config or PoolConfig()
        self.logger = logging.getLogger(f"{__name__}.RedisPool")
        
        self._connections: List[RedisConnection] = []
        self._available_connections: List[RedisConnection] = []
        self._lock = asyncio.Lock()
        self._next_connection_id = 0
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
        # 启动健康检查任务
        self._start_health_check()
    
    def _start_health_check(self):
        """启动健康检查任务"""
        if not self._health_check_task or self._health_check_task.done():
            self._health_check_task = asyncio.create_task(self._health_check_loop())
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown:
            try:
                await asyncio.sleep(self.config.health_check_interval)
                await self._cleanup_unhealthy_connections()
                await self._ensure_min_connections()
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Health check error: {e}")
    
    async def _create_connection(self) -> RedisConnection:
        """创建新的Redis连接"""
        try:
            import redis.asyncio as redis
            
            redis_config = REDIS_CLUSTER_CONFIG.copy()
            
            # SSL配置
            if redis_config.get('ssl', False):
                redis_config.update({
                    'ssl_cert_reqs': ssl.CERT_NONE,
                    'ssl_ca_certs': None,
                })
            
            # 创建连接
            connection = redis.RedisCluster(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config['password'],
                ssl=redis_config.get('ssl', False),
                ssl_cert_reqs=redis_config.get('ssl_cert_reqs'),
                ssl_ca_certs=redis_config.get('ssl_ca_certs'),
                decode_responses=redis_config.get('decode_responses', True),
                socket_connect_timeout=self.config.connection_timeout,
                socket_timeout=self.config.socket_timeout,
                max_connections=5,  # 减少每个连接的内部连接池
                require_full_coverage=False,
                health_check_interval=30  # 添加健康检查间隔
            )
            
            # 测试连接
            await asyncio.wait_for(connection.ping(), timeout=self.config.connection_timeout)
            
            # 创建连接包装器
            connection_id = f"redis_conn_{self._next_connection_id}"
            self._next_connection_id += 1
            
            redis_conn = RedisConnection(connection, connection_id)
            self.logger.info(f"Created new Redis connection: {connection_id}")
            
            return redis_conn
            
        except asyncio.TimeoutError as e:
            error_msg = f"Redis connection timeout to {redis_config.get('host', 'unknown')}:{redis_config.get('port', 'unknown')} (timeout: {self.config.connection_timeout}s)"
            self.logger.warning(f"⚠️ {error_msg}")
            raise Exception(error_msg) from e
        except Exception as e:
            error_msg = f"Failed to create Redis connection to {redis_config.get('host', 'unknown')}:{redis_config.get('port', 'unknown')}: {e}"
            self.logger.warning(f"⚠️ {error_msg}")
            raise Exception(error_msg) from e
    
    async def _ensure_min_connections(self):
        """确保最小连接数"""
        async with self._lock:
            current_count = len(self._connections)
            if current_count < self.config.min_connections:
                need_create = self.config.min_connections - current_count
                self.logger.info(f"Creating {need_create} connections to reach minimum")
                
                for _ in range(need_create):
                    try:
                        conn = await self._create_connection()
                        self._connections.append(conn)
                        self._available_connections.append(conn)
                    except Exception as e:
                        self.logger.error(f"Failed to create minimum connection: {e}")
                        break
    
    async def _cleanup_unhealthy_connections(self):
        """清理不健康的连接"""
        async with self._lock:
            unhealthy_connections = []
            
            for conn in self._connections[:]:  # 创建副本以避免修改问题
                # 检查健康状态
                if not conn.is_healthy or conn.is_idle_timeout:
                    unhealthy_connections.append(conn)
                elif not await conn.ping():
                    unhealthy_connections.append(conn)
            
            # 移除不健康的连接
            for conn in unhealthy_connections:
                self.logger.info(f"Removing unhealthy connection: {conn.connection_id}")
                
                if conn in self._connections:
                    self._connections.remove(conn)
                if conn in self._available_connections:
                    self._available_connections.remove(conn)
                
                # 异步关闭连接
                asyncio.create_task(conn.close())
    
    @asynccontextmanager
    async def get_connection(self):
        """获取Redis连接的上下文管理器"""
        connection = None
        try:
            connection = await self._acquire_connection()
            yield connection
        finally:
            if connection:
                await self._release_connection(connection)
    
    async def _acquire_connection(self) -> RedisConnection:
        """获取连接 - 优化版本，减少锁竞争"""
        # 快速路径：无锁检查可用连接
        if self._available_connections:
            async with self._lock:
                if self._available_connections:
                    conn = self._available_connections.pop(0)
                    # 快速健康检查
                    if conn.is_healthy and not conn.is_idle_timeout:
                        self.logger.debug(f"Reusing healthy connection: {conn.connection_id}")
                        return conn
                    else:
                        # 连接不健康或超时，移除并重新创建
                        self.logger.warning(f"Removing unhealthy/timeout connection: {conn.connection_id}")
                        if conn in self._connections:
                            self._connections.remove(conn)
                        asyncio.create_task(conn.close())
        
        # 创建新连接（如果需要）
        async with self._lock:
            # 再次检查可用连接（防止竞争条件）
            if self._available_connections:
                conn = self._available_connections.pop(0)
                if conn.is_healthy and not conn.is_idle_timeout:
                    return conn
                else:
                    if conn in self._connections:
                        self._connections.remove(conn)
                    asyncio.create_task(conn.close())
            
            # 如果没有可用连接且未达到最大限制，创建新连接
            if len(self._connections) < self.config.max_connections:
                try:
                    self.logger.info(f"Creating new connection (current: {len(self._connections)}/{self.config.max_connections})")
                    conn = await self._create_connection()
                    self._connections.append(conn)
                    return conn
                except Exception as e:
                    self.logger.error(f"Failed to create new connection: {e}")
        
        # 最后手段：等待连接可用（在锁外等待以减少竞争）
        self.logger.warning(f"No available connections, waiting... (current: {len(self._connections)}, available: {len(self._available_connections)})")
        
        for attempt in range(2):  # 进一步减少重试次数
            await asyncio.sleep(0.05 * (attempt + 1))  # 进一步减少等待时间
            
            # 在锁内快速检查
            async with self._lock:
                if self._available_connections:
                    conn = self._available_connections.pop(0)
                    if conn.is_healthy and not conn.is_idle_timeout:
                        self.logger.info(f"Got available connection after wait: {conn.connection_id}")
                        return conn
                    else:
                        # 连接不健康，移除
                        if conn in self._connections:
                            self._connections.remove(conn)
                        asyncio.create_task(conn.close())
        
        # 最后尝试：强制创建连接
        async with self._lock:
            try:
                self.logger.warning("Force creating connection as last resort")
                conn = await self._create_connection()
                self._connections.append(conn)
                return conn
            except Exception as e:
                self.logger.error(f"Force connection creation failed: {e}")
                raise RuntimeError(f"Unable to acquire Redis connection: {e}")
            
        raise RuntimeError(f"Unable to acquire Redis connection after exhaustive retry (connections: {len(self._connections)}, available: {len(self._available_connections)})")
    
    async def _release_connection(self, connection: RedisConnection):
        """释放连接"""
        async with self._lock:
            if connection in self._connections and connection not in self._available_connections:
                self._available_connections.append(connection)
    
    async def execute_command(self, command: str, *args, **kwargs):
        """执行Redis命令的便捷方法"""
        async with self.get_connection() as conn:
            return await conn.execute(command, *args, **kwargs)
    
    # 常用Redis操作的便捷方法
    async def lpush(self, key: str, *values) -> int:
        """List push操作"""
        return await self.execute_command('lpush', key, *values)
    
    async def brpop(self, keys, timeout: int = 0):
        """Blocking right pop操作"""
        return await self.execute_command('brpop', keys, timeout)
    
    async def llen(self, key: str) -> int:
        """List length操作"""
        return await self.execute_command('llen', key)
    
    async def ping(self) -> bool:
        """Ping操作"""
        try:
            await self.execute_command('ping')
            return True
        except Exception:
            return False
    
    async def close(self):
        """关闭连接池"""
        self.logger.info("Closing Redis connection pool")
        self._shutdown = True
        
        # 取消健康检查任务
        if self._health_check_task and not self._health_check_task.done():
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 关闭所有连接
        async with self._lock:
            close_tasks = []
            for conn in self._connections:
                close_tasks.append(conn.close())
            
            if close_tasks:
                # 使用超时批量关闭连接，避免长时间等待
                try:
                    await asyncio.wait_for(
                        asyncio.gather(*close_tasks, return_exceptions=True),
                        timeout=5.0
                    )
                except asyncio.TimeoutError:
                    self.logger.warning("Connection close timeout, proceeding anyway")
            
            self._connections.clear()
            self._available_connections.clear()
        
        self.logger.info("Redis connection pool closed")
    
    @property
    def stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        return {
            "total_connections": len(self._connections),
            "available_connections": len(self._available_connections),
            "active_connections": len(self._connections) - len(self._available_connections),
            "config": {
                "min_connections": self.config.min_connections,
                "max_connections": self.config.max_connections,
                "connection_timeout": self.config.connection_timeout,
                "socket_timeout": self.config.socket_timeout,
            }
        }


class OptimizedRedisManager:
    """优化的Redis资源管理器"""
    
    def __init__(self, pool_config: Optional[PoolConfig] = None):
        self.logger = logging.getLogger(__name__)
        self._pool: Optional[OptimizedRedisPool] = None
        self._thread_pool: Optional[ThreadPoolExecutor] = None
        self._lock = threading.Lock()
        self.pool_config = pool_config or PoolConfig()
    
    async def get_pool(self) -> OptimizedRedisPool:
        """获取Redis连接池"""
        if self._pool is None:
            self._pool = OptimizedRedisPool(self.pool_config)
            # 确保最小连接数
            await self._pool._ensure_min_connections()
        return self._pool
    
    def get_thread_pool(self) -> ThreadPoolExecutor:
        """获取线程池"""
        if self._thread_pool is None or self._thread_pool._shutdown:
            with self._lock:
                if self._thread_pool is None or self._thread_pool._shutdown:
                    if self._thread_pool and self._thread_pool._shutdown:
                        self.logger.info("Previous thread pool was shutdown, creating new one")
                    
                    self._thread_pool = ThreadPoolExecutor(
                        max_workers=4,
                        thread_name_prefix="optimized_redis_"
                    )
                    self.logger.info("Created new thread pool for Redis operations")
        
        return self._thread_pool
    
    @asynccontextmanager
    async def get_redis_connection(self):
        """获取Redis连接的上下文管理器"""
        pool = await self.get_pool()
        async with pool.get_connection() as connection:
            yield connection
    
    async def redis_lpush(self, queue_name: str, item: str) -> bool:
        """向Redis队列推送消息"""
        try:
            pool = await self.get_pool()
            await pool.lpush(queue_name, item)
            return True
        except Exception as e:
            self.logger.error(f"Failed to push to queue {queue_name}: {e}")
            return False
    
    async def redis_brpop(self, queue_name: str, timeout: int = 1) -> Optional[str]:
        """从Redis队列弹出消息"""
        try:
            pool = await self.get_pool()
            result = await pool.brpop(queue_name, timeout=timeout)
            if result:
                return result[1]  # result是(queue_name, value)的元组
            return None
        except Exception as e:
            self.logger.error(f"Failed to pop from queue {queue_name}: {e}")
            return None
    
    async def redis_llen(self, queue_name: str) -> int:
        """获取Redis队列长度"""
        try:
            pool = await self.get_pool()
            return await pool.llen(queue_name)
        except Exception as e:
            self.logger.error(f"Failed to get queue length {queue_name}: {e}")
            return 0
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            pool = await self.get_pool()
            
            # 尝试ping测试
            ping_success = False
            error_message = None
            
            try:
                ping_success = await pool.ping()
            except Exception as e:
                error_message = str(e)
                # 检查是否是网络连接问题（AWS ElastiCache不可达）
                if any(keyword in error_message.lower() for keyword in 
                       ['connection', 'timeout', 'network', 'unreachable', 'refused']):
                    # 这是预期的网络问题，不是系统错误
                    ping_success = False
                else:
                    # 其他类型的错误需要关注
                    raise e
            
            stats = pool.stats
            
            # 重新评估健康状态
            if ping_success:
                status = "healthy"
            elif error_message and any(keyword in error_message.lower() for keyword in 
                                     ['connection', 'timeout', 'network', 'unreachable', 'refused']):
                # 网络问题但系统功能正常
                status = "network_unavailable"
            else:
                status = "unhealthy"
            
            return {
                "status": status,
                "ping_success": ping_success,
                "pool_stats": stats,
                "timestamp": time.time(),
                "error_message": error_message,
                "notes": "network_unavailable状态表示Redis服务不可达但系统功能正常"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e),
                "timestamp": time.time()
            }
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("Cleaning up Optimized Redis Manager")
        
        # 关闭Redis连接池
        if self._pool:
            await self._pool.close()
            self._pool = None
        
        # 关闭线程池
        if self._thread_pool and not self._thread_pool._shutdown:
            self._thread_pool.shutdown(wait=False)
            self.logger.info("Thread pool shutdown initiated")


# 全局管理器实例
_optimized_manager: Optional[OptimizedRedisManager] = None
_manager_lock = threading.Lock()


def get_optimized_redis_manager() -> OptimizedRedisManager:
    """获取优化的Redis管理器单例"""
    global _optimized_manager
    if _optimized_manager is None:
        with _manager_lock:
            if _optimized_manager is None:
                _optimized_manager = OptimizedRedisManager()
    return _optimized_manager


# 便捷函数
async def get_redis_pool() -> OptimizedRedisPool:
    """获取Redis连接池"""
    manager = get_optimized_redis_manager()
    return await manager.get_pool()


@asynccontextmanager
async def get_redis_connection():
    """获取Redis连接的便捷函数"""
    manager = get_optimized_redis_manager()
    async with manager.get_redis_connection() as connection:
        yield connection


async def redis_queue_push(queue_name: str, item: str) -> bool:
    """推送消息到Redis队列"""
    return await get_optimized_redis_manager().redis_lpush(queue_name, item)


async def redis_queue_pop(queue_name: str, timeout: int = 1) -> Optional[str]:
    """从Redis队列弹出消息"""
    return await get_optimized_redis_manager().redis_brpop(queue_name, timeout)


async def redis_queue_length(queue_name: str) -> int:
    """获取Redis队列长度"""
    return await get_optimized_redis_manager().redis_llen(queue_name) 