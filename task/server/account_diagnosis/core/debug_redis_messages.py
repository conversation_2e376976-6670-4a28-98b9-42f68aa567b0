#!/usr/bin/env python3
"""
Debug script to check Redis messages format
"""

import asyncio
import json
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

async def check_redis_queue_messages():
    """Check messages in Redis queue to see their format"""
    
    try:
        from task.server.account_diagnosis.base_async_service import get_redis_connection
        
        queue_name = "dev:q:diagnosis:response"
        
        print(f"Checking Redis queue: {queue_name}")
        
        async with get_redis_connection() as redis_conn:
            # Get queue length
            queue_length = await redis_conn.llen(queue_name)
            print(f"Queue length: {queue_length}")
            
            if queue_length == 0:
                print("Queue is empty")
                return
            
            # Get the latest few messages (without removing them)
            messages = await redis_conn.lrange(queue_name, 0, min(5, queue_length - 1))
            
            print(f"\nAnalyzing {len(messages)} messages:")
            
            for i, message in enumerate(messages):
                print(f"\n--- Message {i + 1} ---")
                print(f"Type: {type(message)}")
                print(f"Length: {len(message) if hasattr(message, '__len__') else 'N/A'}")
                
                # Convert bytes to string if needed
                if isinstance(message, bytes):
                    message = message.decode('utf-8')
                
                print(f"Preview: {message[:200]}...")
                
                # Try to parse as JSON
                try:
                    # First attempt: direct JSON parse
                    data = json.loads(message)
                    print(f"✅ Direct JSON parse successful, type: {type(data)}")
                    
                    if isinstance(data, str):
                        # This might be double-serialized
                        try:
                            inner_data = json.loads(data)
                            print(f"✅ Double-serialized JSON detected, inner type: {type(inner_data)}")
                            
                            # Check if it has the expected structure
                            if isinstance(inner_data, dict) and "taskInfo" in inner_data:
                                task_info = inner_data["taskInfo"]
                                print(f"✅ Valid taskInfo structure found:")
                                print(f"   - taskId: {task_info.get('taskId')}")
                                print(f"   - aiTaskStatus: {task_info.get('aiTaskStatus')}")
                                print(f"   - aiTaskProgress: {task_info.get('aiTaskProgress')}")
                                
                                # Check for final results
                                if "diagnosisHtml" in inner_data:
                                    print(f"   - Contains diagnosisHtml: {len(inner_data['diagnosisHtml'])} chars")
                                if "diagnosisReport" in inner_data:
                                    print(f"   - Contains diagnosisReport")
                            else:
                                print(f"❌ Invalid structure: {list(inner_data.keys()) if isinstance(inner_data, dict) else type(inner_data)}")
                                
                        except json.JSONDecodeError as e:
                            print(f"❌ Failed to parse inner JSON: {e}")
                    
                    elif isinstance(data, dict):
                        print(f"❌ Single-serialized JSON (should be double-serialized)")
                        print(f"   Keys: {list(data.keys())}")
                    
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON: {e}")
                
                print("-" * 40)
        
    except Exception as e:
        print(f"❌ Failed to check Redis queue: {e}")
        import traceback
        traceback.print_exc()

async def test_message_format():
    """Test the expected message format"""
    
    print("\nTesting expected message format...")
    
    # Create a test message in the expected format
    test_data = {
        "taskInfo": {
            "env": "dev",
            "taskId": "test_123",
            "userId": "user_456",
            "diagnosisId": 789,
            "aiTaskStatus": "FINISH",
            "aiTaskMsg": "Task completed!",
            "aiTaskMsgCN": "任务完成！",
            "aiTaskProgress": 100,
            "timestamp": "2025-07-20T00:00:00.000000",
            "elapsed_time": 10.5
        },
        "diagnosisHtml": "<html><body><h1>Test Report</h1></body></html>",
        "diagnosisReport": {
            "summary": "Test summary",
            "tags": [{"dimension": "TEST", "status": "OK"}]
        }
    }
    
    # Test double serialization
    try:
        from task.server.account_diagnosis.core.status_manager import safe_redis_serialize_with_validation
        
        # Double serialize
        serialized = safe_redis_serialize_with_validation(test_data)
        print(f"✅ Serialization successful, type: {type(serialized)}, length: {len(serialized)}")
        
        # Test deserialization
        step1 = json.loads(serialized)
        print(f"✅ Step 1 deserialization: {type(step1)}")
        
        step2 = json.loads(step1)
        print(f"✅ Step 2 deserialization: {type(step2)}")
        
        # Verify data integrity
        if step2 == test_data:
            print("✅ Data integrity verified")
        else:
            print("❌ Data integrity check failed")
        
        # Show what the Java backend should expect
        print(f"\nJava backend should receive:")
        print(f"- A string containing JSON")
        print(f"- First JSON.parse() gives: JSON string")
        print(f"- Second JSON.parse() gives: actual data object")
        print(f"- The data object should have 'taskInfo' and other fields")
        
        return True
        
    except Exception as e:
        print(f"❌ Message format test failed: {e}")
        return False

if __name__ == "__main__":
    print("Redis Message Format Debug Tool")
    print("=" * 50)
    
    async def run_debug():
        await check_redis_queue_messages()
        await test_message_format()
    
    asyncio.run(run_debug())