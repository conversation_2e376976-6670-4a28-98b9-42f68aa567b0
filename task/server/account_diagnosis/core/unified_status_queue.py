"""
统一状态更新队列系统
解决多模块状态更新冲突和进度倒退问题
"""
import asyncio
import time
from typing import Dict, Optional, List, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

@dataclass
class StatusUpdate:
    """状态更新请求"""
    task_id: str
    status: str
    progress: float
    module_name: str
    message: str
    timestamp: float
    priority: int = 1  # 1=低, 2=中, 3=高
    sequence_id: int = 0  # 序列号，确保顺序

class ModulePriority(Enum):
    """模块优先级定义"""
    DEFAULT = 1
    SERVICE_IMPL = 2  
    SEARCH_PIPELINE = 3
    ANALYSIS_ENGINE = 4
    DIAGNOSIS_CORE = 5  # 最高优先级

MODULE_PRIORITIES = {
    "default": ModulePriority.DEFAULT.value,
    "service_implementation": ModulePriority.SERVICE_IMPL.value,
    "search_pipeline": ModulePriority.SEARCH_PIPELINE.value,
    "analysis_engine": ModulePriority.ANALYSIS_ENGINE.value,
    "diagnosis_core": ModulePriority.DIAGNOSIS_CORE.value,
}

class UnifiedStatusQueue:
    """统一状态更新队列，确保状态更新的有序性和一致性"""
    
    def __init__(self):
        self._update_queue: asyncio.Queue = asyncio.Queue()
        self._task_states: Dict[str, Dict] = {}  # task_id -> 当前状态信息
        self._sequence_counter = 0
        self._processing = False
        self._lock = asyncio.Lock()
        
    async def start_processing(self):
        """启动状态更新处理器"""
        if self._processing:
            return
            
        self._processing = True
        asyncio.create_task(self._process_status_updates())
        logger.info("统一状态队列处理器已启动")
    
    async def submit_status_update(self, 
                                 task_id: str,
                                 status: str, 
                                 progress: float,
                                 module_name: str,
                                 message: str) -> bool:
        """提交状态更新请求到队列"""
        
        # 获取模块优先级
        priority = MODULE_PRIORITIES.get(module_name, 1)
        
        # 创建状态更新对象
        update = StatusUpdate(
            task_id=task_id,
            status=status,
            progress=progress,
            module_name=module_name,
            message=message,
            timestamp=time.time(),
            priority=priority,
            sequence_id=self._get_next_sequence()
        )
        
        # 快速验证，避免明显的问题更新进入队列
        if not await self._quick_validate(update):
            logger.warning(f"快速验证失败，拒绝更新: {module_name} -> {progress}%")
            return False
            
        # 添加到队列
        await self._update_queue.put(update)
        logger.debug(f"状态更新已加入队列: {module_name} -> {progress}% (优先级: {priority})")
        return True
    
    async def _quick_validate(self, update: StatusUpdate) -> bool:
        """快速验证状态更新是否合理"""
        task_id = update.task_id
        
        if task_id not in self._task_states:
            return True  # 新任务，直接允许
            
        current_state = self._task_states[task_id]
        current_progress = current_state.get("progress", 0)
        current_priority = current_state.get("priority", 1)
        
        progress_diff = update.progress - current_progress
        
        # 规则1: 进度前进总是允许
        if progress_diff >= 0:
            return True
            
        # 规则2: 高优先级模块可以覆盖低优先级
        if update.priority > current_priority:
            return True
            
        # 规则3: 小幅倒退(-5%以内)允许
        if progress_diff >= -5.0:
            return True
            
        # 规则4: 拒绝大幅倒退
        logger.debug(f"快速验证拒绝大幅倒退: {current_progress}% -> {update.progress}% (diff: {progress_diff:.1f}%)")
        return False
    
    async def _process_status_updates(self):
        """处理状态更新队列"""
        while self._processing:
            try:
                # 获取下一个更新请求 - 大幅减少超时时间，提高响应速度
                update = await asyncio.wait_for(self._update_queue.get(), timeout=0.01)
                
                # 处理更新
                await self._handle_status_update(update)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理状态更新时出错: {e}")
    
    async def _handle_status_update(self, update: StatusUpdate):
        """处理单个状态更新 - 移除阻塞锁，改为非阻塞处理"""
        task_id = update.task_id
        
        # 快速获取当前状态（无锁）
        current_state = self._task_states.get(task_id, {})
        
        # 详细验证
        if not self._detailed_validate(update, current_state):
            logger.warning(f"详细验证失败，丢弃更新: {update.module_name} -> {update.progress}%")
            return
        
        # 快速更新状态（最小锁范围）
        async with self._lock:
            self._task_states[task_id] = {
                "progress": update.progress,
                "status": update.status,
                "message": update.message,
                "module_name": update.module_name,
                "priority": update.priority,
                "timestamp": update.timestamp,
                "sequence_id": update.sequence_id
            }
        
        # 异步发送到状态管理器（无锁，避免阻塞）
        asyncio.create_task(self._send_to_status_manager_async(update))
        
        logger.info(f"✅ 状态更新成功: {update.module_name} -> {update.progress}% - {update.message}")
    
    def _detailed_validate(self, update: StatusUpdate, current_state: Dict) -> bool:
        """详细验证状态更新"""
        if not current_state:
            return True  # 新任务
            
        current_progress = current_state.get("progress", 0)
        current_priority = current_state.get("priority", 1)
        current_timestamp = current_state.get("timestamp", 0)
        
        progress_diff = update.progress - current_progress
        time_diff = update.timestamp - current_timestamp
        
        # 规则1: 时间戳不能倒退太多（防止延迟消息）- 减少延迟容忍度
        if time_diff < -0.5:  # 超过0.5秒的延迟消息
            logger.debug(f"拒绝延迟消息: 时间差 {time_diff:.1f}s")
            return False
            
        # 规则2: 相同优先级的模块，不允许大幅倒退
        if update.priority == current_priority and progress_diff < -10.0:
            logger.debug(f"拒绝同优先级大幅倒退: {progress_diff:.1f}%")
            return False
            
        # 规则3: 低优先级不能覆盖高优先级的最新更新 - 减少时间窗口
        if update.priority < current_priority and time_diff < 1.0:
            logger.debug(f"拒绝低优先级覆盖: {update.priority} < {current_priority}")
            return False
            
        return True
    
    async def _send_to_status_manager(self, update: StatusUpdate):
        """发送状态更新到实际的状态管理器"""
        try:
            # 这里调用原有的状态管理器，但跳过所有冲突检测
            # 因为我们已经在队列层面解决了冲突
            from .status_manager import GlobalStatusCoordinator
            
            coordinator = await GlobalStatusCoordinator.get_instance()
            
            # 直接更新，不进行冲突检测
            await coordinator.force_update_status(
                task_id=update.task_id,
                status=update.status,
                progress=update.progress,
                message=update.message,
                module_name=update.module_name
            )
            
        except Exception as e:
            logger.error(f"发送状态到管理器失败: {e}")
    
    async def _send_to_status_manager_async(self, update: StatusUpdate):
        """异步发送状态更新到实际的状态管理器（非阻塞版本）"""
        try:
            # 这里调用原有的状态管理器，但跳过所有冲突检测
            # 因为我们已经在队列层面解决了冲突
            from .status_manager import GlobalStatusCoordinator
            
            coordinator = await GlobalStatusCoordinator.get_instance()
            
            # 直接更新，不进行冲突检测
            await coordinator.force_update_status(
                task_id=update.task_id,
                status=update.status,
                progress=update.progress,
                message=update.message,
                module_name=update.module_name
            )
            
            logger.debug(f"异步发送状态成功: {update.module_name} -> {update.progress}%")
            
        except Exception as e:
            logger.error(f"异步发送状态到管理器失败: {e}")
    
    def _get_next_sequence(self) -> int:
        """获取下一个序列号"""
        self._sequence_counter += 1
        return self._sequence_counter
    
    async def get_task_status(self, task_id: str) -> Optional[Dict]:
        """获取任务当前状态"""
        return self._task_states.get(task_id)
    
    async def stop_processing(self):
        """停止处理"""
        self._processing = False

# 全局实例
_global_queue_instance = None

async def get_unified_queue() -> UnifiedStatusQueue:
    """获取全局队列实例"""
    global _global_queue_instance
    if _global_queue_instance is None:
        _global_queue_instance = UnifiedStatusQueue()
        await _global_queue_instance.start_processing()
    return _global_queue_instance