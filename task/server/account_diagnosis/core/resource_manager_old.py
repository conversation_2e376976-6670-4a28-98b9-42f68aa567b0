"""
Unified Resource Manager
统一资源管理器 - 管理Redis连接池、AI客户端、缓存等资源
"""

import asyncio
import logging
import time
import weakref
from typing import Dict, Optional, Any, List, Callable, Union, AsyncContextManager, Type
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from enum import Enum
import threading
from concurrent.futures import ThreadPoolExecutor
import ssl
from task import REDIS_CLUSTER_CONFIG


class ResourceType(Enum):
    """资源类型枚举"""
    REDIS = "redis"
    AI_CLIENT = "ai_client"
    HTTP_CLIENT = "http_client"
    CACHE = "cache"
    THREAD_POOL = "thread_pool"
    CONNECTION_POOL = "connection_pool"
    CUSTOM = "custom"


class ResourceStatus(Enum):
    """资源状态枚举"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"


@dataclass
class ResourceConfig:
    """资源配置类"""
    max_instances: int = 10
    min_instances: int = 1
    idle_timeout: float = 300.0  # 5分钟
    connection_timeout: float = 30.0
    operation_timeout: float = 60.0
    health_check_interval: float = 60.0  # 1分钟
    max_retries: int = 3
    cleanup_on_exit: bool = True


@dataclass
class ResourceMetrics:
    """资源指标"""
    created_at: float = field(default_factory=time.time)
    last_used_at: float = field(default_factory=time.time)
    usage_count: int = 0
    error_count: int = 0
    total_operation_time: float = 0.0
    active_connections: int = 0
    peak_connections: int = 0


class ManagedResource:
    """托管资源"""
    
    def __init__(self, resource_id: str, resource_type: ResourceType,
                 resource_obj: Any, cleanup_func: Optional[Callable] = None,
                 config: Optional[ResourceConfig] = None):
        self.resource_id = resource_id
        self.resource_type = resource_type
        self.resource_obj = resource_obj
        self.cleanup_func = cleanup_func
        self.config = config or ResourceConfig()
        self.status = ResourceStatus.INITIALIZING
        self.metrics = ResourceMetrics()
        self._lock = asyncio.Lock()
        self._health_check_task: Optional[asyncio.Task] = None
        
    async def mark_active(self):
        """标记为活跃状态"""
        async with self._lock:
            self.status = ResourceStatus.ACTIVE
            self.metrics.last_used_at = time.time()
            self.metrics.usage_count += 1
    
    async def mark_idle(self):
        """标记为空闲状态"""
        async with self._lock:
            if self.status == ResourceStatus.ACTIVE:
                self.status = ResourceStatus.IDLE
    
    async def mark_error(self, error_msg: str = ""):
        """标记为错误状态"""
        async with self._lock:
            self.status = ResourceStatus.ERROR
            self.metrics.error_count += 1
    
    async def is_healthy(self) -> bool:
        """检查资源健康状态"""
        try:
            # 基础健康检查
            if self.status == ResourceStatus.ERROR:
                return False
            
            if self.status == ResourceStatus.CLOSED:
                return False
            
            # 检查空闲超时
            idle_time = time.time() - self.metrics.last_used_at
            if idle_time > self.config.idle_timeout:
                return False
            
            # 特定资源类型的健康检查
            if self.resource_type == ResourceType.REDIS:
                return await self._check_redis_health()
            elif self.resource_type == ResourceType.HTTP_CLIENT:
                return await self._check_http_client_health()
            elif self.resource_type == ResourceType.THREAD_POOL:
                return await self._check_thread_pool_health()
            
            return True
            
        except Exception:
            return False
    
    async def _check_redis_health(self) -> bool:
        """检查Redis连接健康状态"""
        try:
            if hasattr(self.resource_obj, 'ping'):
                await self.resource_obj.ping()
                return True
        except Exception:
            pass
        return False
    
    async def _check_http_client_health(self) -> bool:
        """检查HTTP客户端健康状态"""
        try:
            if hasattr(self.resource_obj, 'closed'):
                return not self.resource_obj.closed
        except Exception:
            pass
        return True
    
    async def _check_thread_pool_health(self) -> bool:
        """检查线程池健康状态"""
        try:
            if hasattr(self.resource_obj, '_shutdown'):
                # ThreadPoolExecutor已经关闭
                if self.resource_obj._shutdown:
                    return False
            
            # 检查线程池是否还能接受新任务
            if hasattr(self.resource_obj, '_threads'):
                # 如果线程数为0且不是刚创建的，可能有问题
                if len(self.resource_obj._threads) == 0 and time.time() - self.metrics.created_at > 60:
                    return False
            
            return True
        except Exception:
            return False
    
    async def cleanup(self):
        """清理资源"""
        async with self._lock:
            if self.status == ResourceStatus.CLOSED:
                return
            
            self.status = ResourceStatus.CLOSING
            
            try:
                if self._health_check_task:
                    self._health_check_task.cancel()
                
                if self.cleanup_func:
                    if asyncio.iscoroutinefunction(self.cleanup_func):
                        await self.cleanup_func(self.resource_obj)
                    else:
                        self.cleanup_func(self.resource_obj)
                
                # 特定资源类型的清理
                await self._cleanup_by_type()
                
                self.status = ResourceStatus.CLOSED
                
            except Exception as e:
                logging.error(f"Error cleaning up resource {self.resource_id}: {e}")
                self.status = ResourceStatus.ERROR
    
    async def _cleanup_by_type(self):
        """根据类型清理资源"""
        try:
            if self.resource_type == ResourceType.REDIS:
                if hasattr(self.resource_obj, 'close'):
                    await self.resource_obj.close()
            elif self.resource_type == ResourceType.HTTP_CLIENT:
                if hasattr(self.resource_obj, 'close'):
                    await self.resource_obj.close()
            elif self.resource_type == ResourceType.THREAD_POOL:
                if hasattr(self.resource_obj, 'shutdown'):
                    self.resource_obj.shutdown(wait=True)
        except Exception as e:
            logging.error(f"Error in type-specific cleanup: {e}")


class ResourcePool:
    """资源池"""
    
    def __init__(self, resource_type: ResourceType, factory_func: Callable,
                 config: ResourceConfig):
        self.resource_type = resource_type
        self.factory_func = factory_func
        self.config = config
        self._pool: List[ManagedResource] = []
        self._lock = asyncio.Lock()
        self._next_id = 0
        
    async def acquire(self) -> Optional[ManagedResource]:
        """获取资源"""
        async with self._lock:
            # 寻找可用的资源
            for resource in self._pool:
                if resource.status == ResourceStatus.IDLE and await resource.is_healthy():
                    await resource.mark_active()
                    return resource
            
            # 如果没有可用资源且未达到最大限制，创建新资源
            if len(self._pool) < self.config.max_instances:
                resource = await self._create_resource()
                if resource:
                    await resource.mark_active()
                    return resource
            
            return None
    
    async def release(self, resource: ManagedResource):
        """释放资源"""
        async with self._lock:
            await resource.mark_idle()
    
    async def _create_resource(self) -> Optional[ManagedResource]:
        """创建新资源"""
        try:
            resource_id = f"{self.resource_type.value}_{self._next_id}"
            self._next_id += 1
            
            # 调用工厂函数创建资源对象
            if asyncio.iscoroutinefunction(self.factory_func):
                resource_obj = await self.factory_func()
            else:
                resource_obj = self.factory_func()
            
            resource = ManagedResource(
                resource_id=resource_id,
                resource_type=self.resource_type,
                resource_obj=resource_obj,
                config=self.config
            )
            
            self._pool.append(resource)
            return resource
            
        except Exception as e:
            logging.error(f"Failed to create resource: {e}")
            return None
    
    async def health_check(self):
        """健康检查，清理不健康的资源"""
        async with self._lock:
            unhealthy_resources = []
            
            for resource in self._pool:
                if not await resource.is_healthy():
                    unhealthy_resources.append(resource)
            
            # 清理不健康的资源
            for resource in unhealthy_resources:
                self._pool.remove(resource)
                asyncio.create_task(resource.cleanup())
    
    async def cleanup_all(self):
        """清理所有资源"""
        async with self._lock:
            cleanup_tasks = []
            for resource in self._pool:
                cleanup_tasks.append(resource.cleanup())
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            self._pool.clear()


class UnifiedResourceManager:
    """统一资源管理器"""
    
    _instance: Optional['UnifiedResourceManager'] = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # Prevent re-initialization
        if hasattr(self, '_initialized') and self._initialized:
            return

        self.logger = logging.getLogger(__name__)
        self._pools: Dict[str, ResourcePool] = {}
        self._singletons: Dict[str, ManagedResource] = {}
        self._health_check_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        self._initialized = False

    async def initialize(self):
        """初始化资源管理器"""
        # DISABLED: 禁用UnifiedResourceManager以防止健康检查冲突
        # 请使用OptimizedRedisManager替代
        self.logger.warning("UnifiedResourceManager已被禁用，请使用OptimizedRedisManager")
        self._initialized = True
        return
        
        if self._initialized:
            return

        with self._lock:
            if self._initialized:
                return
            self._initialized = True

        self.logger.info("Initializing Unified Resource Manager")
        
        # 启动健康检查任务
        self._health_check_task = asyncio.create_task(self._health_check_loop())

        # 注册默认资源池
        await self.register_pool(
            "redis_pool",
            ResourceType.REDIS,
            create_redis_connection,
            ResourceConfig(max_instances=10, min_instances=2)
        )
        
        await self.register_pool(
            "http_pool",
            ResourceType.HTTP_CLIENT,
            create_http_client,
            ResourceConfig(max_instances=5, min_instances=1)
        )
        
        # 注册线程池单例
        thread_pool_config = ResourceConfig(
            idle_timeout=1800.0,  # 30分钟，比默认5分钟更宽松
            health_check_interval=120.0  # 2分钟检查一次，减少频繁检查
        )
        await self.register_singleton(
            "thread_pool",
            ResourceType.THREAD_POOL,
            create_thread_pool(),
            cleanup_func=lambda pool: pool.shutdown(wait=True),
            config=thread_pool_config
        )

    async def register_pool(self, pool_name: str, resource_type: ResourceType,
                           factory_func: Callable, config: ResourceConfig):
        """注册资源池"""
        if pool_name in self._pools:
            self.logger.warning(f"Resource pool {pool_name} already exists")
            return
            
        pool = ResourcePool(resource_type, factory_func, config)
        self._pools[pool_name] = pool
        
        self.logger.info(f"Registered resource pool: {pool_name}")
    
    async def register_singleton(self, resource_name: str, resource_type: ResourceType,
                                resource_obj: Any, cleanup_func: Optional[Callable] = None,
                                config: Optional[ResourceConfig] = None):
        """注册单例资源"""
        if resource_name in self._singletons:
            self.logger.warning(f"Singleton resource {resource_name} already exists")
            return
            
        resource = ManagedResource(
            resource_id=resource_name,
            resource_type=resource_type,
            resource_obj=resource_obj,
            cleanup_func=cleanup_func,
            config=config or ResourceConfig()
        )
        
        self._singletons[resource_name] = resource
        self.logger.info(f"Registered singleton resource: {resource_name}")
    
    @asynccontextmanager
    async def acquire_from_pool(self, pool_name: str):
        """从资源池获取资源的上下文管理器"""
        if pool_name not in self._pools:
            raise ValueError(f"Resource pool {pool_name} not found")
        
        pool = self._pools[pool_name]
        resource = await pool.acquire()
        
        if resource is None:
            raise RuntimeError(f"Failed to acquire resource from pool {pool_name}")
        
        try:
            yield resource.resource_obj
        finally:
            await pool.release(resource)
    
    async def get_singleton(self, resource_name: str) -> Any:
        """获取单例资源"""
        if resource_name not in self._singletons:
            raise ValueError(f"Singleton resource {resource_name} not found")
        
        resource = self._singletons[resource_name]
        await resource.mark_active()
        return resource.resource_obj
    
    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._shutdown_event.is_set():
            try:
                await asyncio.sleep(60)  # 每分钟检查一次
                
                # 检查资源池
                for pool_name, pool in self._pools.items():
                    await pool.health_check()
                
                # 检查单例资源并自动恢复
                unhealthy_singletons = []
                for name, resource in self._singletons.items():
                    if not await resource.is_healthy():
                        unhealthy_singletons.append((name, resource))
                        self.logger.warning(f"Singleton resource {name} is unhealthy, attempting recovery")
                
                # 自动恢复不健康的单例资源
                for name, old_resource in unhealthy_singletons:
                    try:
                        await self._recover_singleton_resource(name, old_resource)
                    except Exception as e:
                        self.logger.error(f"Failed to recover singleton resource {name}: {e}")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
    
    async def _recover_singleton_resource(self, resource_name: str, old_resource: ManagedResource):
        """恢复不健康的单例资源"""
        try:
            self.logger.info(f"Starting recovery for singleton resource: {resource_name}")
            
            # 先清理旧资源
            await old_resource.cleanup()
            
            # 根据资源类型创建新资源
            if old_resource.resource_type == ResourceType.THREAD_POOL:
                new_resource_obj = create_thread_pool()
                cleanup_func = lambda pool: pool.shutdown(wait=True)
            elif old_resource.resource_type == ResourceType.REDIS:
                new_resource_obj = await create_redis_connection()
                cleanup_func = None
            elif old_resource.resource_type == ResourceType.HTTP_CLIENT:
                new_resource_obj = await create_http_client()
                cleanup_func = None
            else:
                self.logger.error(f"Unknown resource type for recovery: {old_resource.resource_type}")
                return
            
            # 创建新的托管资源
            new_resource = ManagedResource(
                resource_id=resource_name,
                resource_type=old_resource.resource_type,
                resource_obj=new_resource_obj,
                cleanup_func=cleanup_func,
                config=old_resource.config
            )
            
            # 替换旧资源
            self._singletons[resource_name] = new_resource
            self.logger.info(f"Successfully recovered singleton resource: {resource_name}")
            
        except Exception as e:
            self.logger.error(f"Failed to recover singleton resource {resource_name}: {e}")
            # 如果恢复失败，标记旧资源为错误状态
            await old_resource.mark_error(f"Recovery failed: {str(e)}")
            raise
    
    async def get_metrics(self) -> Dict[str, Any]:
        """获取资源指标"""
        metrics = {
            "pools": {},
            "singletons": {},
            "total_resources": 0
        }
        
        # 资源池指标
        for pool_name, pool in self._pools.items():
            pool_metrics = {
                "total_resources": len(pool._pool),
                "active_resources": sum(1 for r in pool._pool if r.status == ResourceStatus.ACTIVE),
                "idle_resources": sum(1 for r in pool._pool if r.status == ResourceStatus.IDLE),
                "error_resources": sum(1 for r in pool._pool if r.status == ResourceStatus.ERROR)
            }
            metrics["pools"][pool_name] = pool_metrics
            metrics["total_resources"] += pool_metrics["total_resources"]
        
        # 单例资源指标
        for name, resource in self._singletons.items():
            metrics["singletons"][name] = {
                "status": resource.status.value,
                "usage_count": resource.metrics.usage_count,
                "error_count": resource.metrics.error_count,
                "last_used": resource.metrics.last_used_at
            }
            metrics["total_resources"] += 1
        
        return metrics
    
    async def force_recover_singleton(self, resource_name: str) -> bool:
        """手动强制恢复指定的单例资源"""
        try:
            if resource_name not in self._singletons:
                self.logger.error(f"Singleton resource {resource_name} not found")
                return False
            
            old_resource = self._singletons[resource_name]
            self.logger.info(f"Force recovering singleton resource: {resource_name}")
            
            await self._recover_singleton_resource(resource_name, old_resource)
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to force recover singleton resource {resource_name}: {e}")
            return False
    
    async def cleanup(self):
        """清理所有资源"""
        self.logger.info("Cleaning up Unified Resource Manager")
        
        # 停止健康检查
        self._shutdown_event.set()
        if self._health_check_task:
            self._health_check_task.cancel()
            try:
                await self._health_check_task
            except asyncio.CancelledError:
                pass
        
        # 清理资源池
        cleanup_tasks = []
        for pool in self._pools.values():
            cleanup_tasks.append(pool.cleanup_all())
        
        # 清理单例资源
        for resource in self._singletons.values():
            cleanup_tasks.append(resource.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        self._pools.clear()
        self._singletons.clear()
        
        self.logger.info("Resource cleanup completed")


# Redis连接池工厂函数
async def create_redis_connection():
    """创建Redis连接"""
    try:
        import redis.asyncio as redis
        
        redis_config = REDIS_CLUSTER_CONFIG.copy()
        
        # Add SSL configuration if SSL is enabled
        if redis_config.get('ssl', False):
            redis_config.update({
                'ssl_cert_reqs': ssl.CERT_NONE,
                'ssl_ca_certs': None,
            })

        connection = redis.RedisCluster(
            host=redis_config['host'],
            port=redis_config['port'],
            password=redis_config['password'],
            ssl=redis_config.get('ssl', False),
            ssl_cert_reqs=redis_config.get('ssl_cert_reqs'),
            ssl_ca_certs=redis_config.get('ssl_ca_certs'),
            decode_responses=redis_config.get('decode_responses', True),
            socket_connect_timeout=10,
            socket_timeout=10,
            max_connections=20,
            require_full_coverage=False
        )
        await connection.ping()
        return connection
    except Exception as e:
        logging.error(f"Failed to create Redis connection: {e}")
        raise


# HTTP客户端工厂函数
async def create_http_client():
    """创建HTTP客户端"""
    try:
        import aiohttp
        client = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=30),
            connector=aiohttp.TCPConnector(limit=100, limit_per_host=30)
        )
        return client
    except Exception as e:
        logging.error(f"Failed to create HTTP client: {e}")
        raise


# 线程池工厂函数
def create_thread_pool():
    """创建线程池"""
    return ThreadPoolExecutor(max_workers=4, thread_name_prefix="diagnosis_")


# 全局资源管理器实例
_resource_manager_lock = threading.Lock()
_resource_manager: Optional[UnifiedResourceManager] = None


async def get_resource_manager() -> UnifiedResourceManager:
    """获取资源管理器单例"""
    global _resource_manager
    if _resource_manager is None:
        with _resource_manager_lock:
            if _resource_manager is None:
                _resource_manager = UnifiedResourceManager()
                await _resource_manager.initialize()
    
    # Ensure initialization is complete, especially for concurrent access on startup
    if not _resource_manager._initialized:
        await _resource_manager.initialize()

    return _resource_manager


# 便捷函数
@asynccontextmanager
async def get_redis_connection():
    """获取Redis连接的便捷函数"""
    manager = await get_resource_manager()
    async with manager.acquire_from_pool("redis_pool") as connection:
        yield connection


@asynccontextmanager
async def get_http_client():
    """获取HTTP客户端的便捷函数"""
    manager = await get_resource_manager()
    async with manager.acquire_from_pool("http_pool") as client:
        yield client


async def get_thread_pool():
    """获取线程池的便捷函数 - 切换到简单管理器"""
    # 导入简单资源管理器
    from .simple_resource_manager import get_thread_pool as get_simple_thread_pool
    return get_simple_thread_pool()


async def recover_thread_pool() -> bool:
    """手动恢复线程池的便捷函数"""
    manager = await get_resource_manager()
    return await manager.force_recover_singleton("thread_pool")


async def get_resource_health_status() -> Dict[str, Any]:
    """获取资源健康状态的便捷函数"""
    manager = await get_resource_manager()
    return await manager.get_metrics()