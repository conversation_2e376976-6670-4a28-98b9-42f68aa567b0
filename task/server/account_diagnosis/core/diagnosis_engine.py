"""
Diagnosis Engine - Core diagnosis processing with optimized parallel execution
诊断引擎 - 核心诊断处理模块，优化并行执行
"""

import asyncio
import time
import logging
from typing import Dict, Optional, Any, Tuple, List, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
from colorama import Fore, Style, init
init(autoreset=True)

from .status_manager import UnifiedTaskStatus, UnifiedProgressTracker
from .config_manager import DiagnosisConfig, get_diagnosis_config
from .resource_manager import UnifiedResourceManager, get_resource_manager


class DiagnosisMode(Enum):
    """诊断模式"""
    BASIC = "basic"
    DEEP_RESEARCH = "deep_research"


class ExecutionStage(Enum):
    """执行阶段"""
    INITIALIZATION = "initialization"
    DATA_EXTRACTION = "data_extraction"
    ANALYSIS = "analysis"
    REPORT_GENERATION = "report_generation"
    FINALIZATION = "finalization"


@dataclass
class DiagnosisContext:
    """诊断上下文"""
    task_id: str
    user_id: Optional[str] = None
    diagnosis_id: Optional[int] = None
    account_info: Dict[str, Any] = field(default_factory=dict)
    search_results: List[Dict[str, Any]] = field(default_factory=list)
    analysis_results: Dict[str, Any] = field(default_factory=dict)
    report_data: Dict[str, Any] = field(default_factory=dict)
    mode: DiagnosisMode = DiagnosisMode.BASIC
    start_time: float = field(default_factory=time.time)
    complexity_factor: float = 1.0
    error_count: int = 0
    retry_count: int = 0


@dataclass
class StageResult:
    """阶段执行结果"""
    stage: ExecutionStage
    success: bool
    data: Dict[str, Any] = field(default_factory=dict)
    duration: float = 0.0
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class DiagnosisEngine:
    """
    诊断引擎主类 - 实现三阶段执行流程和并行优化
    
    执行阶段：
    1. 初始化和数据提取 (并行)
    2. 搜索和分析 (条件并行)
    3. 报告生成 (并行)
    """
    
    def __init__(self, config: Optional[DiagnosisConfig] = None,
                 resource_manager: Optional[UnifiedResourceManager] = None,
                 progress_tracker: Optional[UnifiedProgressTracker] = None):
        """
        初始化诊断引擎
        
        Args:
            config: 诊断配置
            resource_manager: 资源管理器
            progress_tracker: 进度跟踪器
        """
        self.config = config or get_diagnosis_config()
        self.resource_manager = resource_manager
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 执行状态
        self._execution_context: Optional[DiagnosisContext] = None
        self._stage_results: Dict[ExecutionStage, StageResult] = {}
        self._cancellation_token = asyncio.Event()
        
        # 性能统计
        self._performance_metrics = {
            'total_duration': 0.0,
            'stage_durations': {},
            'parallel_efficiency': 0.0,
            'cache_hits': 0,
            'error_count': 0
        }
    
    async def diagnose(self, context: DiagnosisContext) -> Dict[str, Any]:
        """
        执行完整诊断流程
        
        Args:
            context: 诊断上下文
            
        Returns:
            诊断结果字典
        """
        self._execution_context = context
        self._performance_metrics['total_start_time'] = time.time()
        
        try:
            self.logger.info(f"Starting diagnosis for task {context.task_id}, mode: {context.mode.value}")
            
            # 阶段1: 初始化和数据提取
            await self._execute_stage_1(context)
            
            # 阶段2: 搜索和分析
            await self._execute_stage_2(context)
            
            # 阶段3: 报告生成
            await self._execute_stage_3(context)
            
            # 计算性能指标
            self._calculate_performance_metrics()
            
            # 构建最终结果
            result = self._build_final_result(context)
            
            if self.progress_tracker:
                # 不要在这里标记完成，因为还有报告生成阶段
                # 只更新状态表示诊断分析完成，准备生成报告
                self.progress_tracker.update_status(UnifiedTaskStatus.PERFORMING_DIAGNOSIS, "诊断分析完成，准备生成报告...")
            
            self.logger.info(f"Diagnosis completed for task {context.task_id}")
            return result
            
        except Exception as e:
            self.logger.error(f"Diagnosis failed for task {context.task_id}: {e}")
            context.error_count += 1
            
            if self.progress_tracker:
                self.progress_tracker.mark_failed(f"诊断失败: {str(e)}")
            
            # 返回错误结果
            return self._build_error_result(context, str(e))
    
    async def _execute_stage_1(self, context: DiagnosisContext) -> None:
        """
        阶段1: 初始化和数据提取 (并行执行)
        """
        stage_start = time.time()
        
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.PLANNING)
        
        try:
            # 并行执行多个初始化任务
            tasks = [
                self._initialize_resources(),
                self._extract_account_data(context),
                self._validate_input_data(context),
                self._calculate_complexity(context)
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 检查并处理结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.warning(f"Stage 1 task {i} failed: {result}")
                    context.error_count += 1
                    # 如果是关键错误，直接抛出
                    if "Account info is required" in str(result):
                        raise result
            
            # 记录阶段结果
            stage_duration = time.time() - stage_start
            self._stage_results[ExecutionStage.INITIALIZATION] = StageResult(
                stage=ExecutionStage.INITIALIZATION,
                success=context.error_count == 0,
                duration=stage_duration,
                metadata={'parallel_tasks': len(tasks)}
            )
            
            self.logger.info(f"Stage 1 completed in {stage_duration:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Stage 1 execution failed: {e}")
            raise
    
    async def _execute_stage_2(self, context: DiagnosisContext) -> None:
        """
        阶段2: 搜索和分析 (条件并行)
        """
        stage_start = time.time()
        
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.CRAWLING_ACCOUNT)
        
        try:
            # 根据模式决定执行策略
            if context.mode == DiagnosisMode.DEEP_RESEARCH:
                await self._execute_deep_research_flow(context)
            else:
                await self._execute_basic_diagnosis_flow(context)
            
            stage_duration = time.time() - stage_start
            self._stage_results[ExecutionStage.ANALYSIS] = StageResult(
                stage=ExecutionStage.ANALYSIS,
                success=True,
                duration=stage_duration,
                data={'mode': context.mode.value}
            )
            
            self.logger.info(f"Stage 2 completed in {stage_duration:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Stage 2 execution failed: {e}")
            raise
    
    async def _execute_stage_3(self, context: DiagnosisContext) -> None:
        """
        阶段3: 报告生成 (并行)
        """
        stage_start = time.time()
        
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.GENERATING_REPORT)
        
        try:
            # 并行生成不同格式的报告
            report_tasks = [
                self._generate_diagnosis_text_report(context),
                self._generate_html_report(context),
                self._generate_json_report(context),
                self._generate_sales_proposal_md(context),
                self._generate_sales_proposal_html(context)
            ]
            
            results = await asyncio.gather(*report_tasks, return_exceptions=True)
            
            # 合并报告结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.warning(f"Stage 3 report task {i} failed: {result}")
                elif isinstance(result, dict):
                    context.report_data.update(result)
            
            stage_duration = time.time() - stage_start
            self._stage_results[ExecutionStage.REPORT_GENERATION] = StageResult(
                stage=ExecutionStage.REPORT_GENERATION,
                success=len(context.report_data) > 0,
                duration=stage_duration,
                data={'reports_generated': len(context.report_data)}
            )
            
            self.logger.info(f"Stage 3 completed in {stage_duration:.2f}s")
            
        except Exception as e:
            self.logger.error(f"Stage 3 execution failed: {e}")
            raise
    
    async def _initialize_resources(self) -> None:
        """初始化资源"""
        if not self.resource_manager:
            self.resource_manager = await get_resource_manager()
    
    async def _extract_account_data(self, context: DiagnosisContext) -> None:
        """提取账号数据"""
        # 这里应该实现账号数据提取逻辑
        # 暂时使用模拟数据
        await asyncio.sleep(0.1)  # 模拟I/O操作
        context.account_info['extracted'] = True
    
    async def _validate_input_data(self, context: DiagnosisContext) -> None:
        """验证输入数据"""
        await asyncio.sleep(0.05)  # 模拟验证操作
        # 实现数据验证逻辑
        if not context.account_info:
            raise ValueError("Account info is required")
    
    async def _calculate_complexity(self, context: DiagnosisContext) -> None:
        """计算复杂度因子"""
        # 基于账号数据和配置计算复杂度
        data_size = len(str(context.account_info))
        search_queries = 3 if context.mode == DiagnosisMode.BASIC else 8
        
        # 使用配置管理器的动态超时计算器
        from .config_manager import DynamicTimeoutCalculator
        context.complexity_factor = DynamicTimeoutCalculator.calculate_complexity_factor(
            data_size=data_size,
            search_queries=search_queries,
            industry_complexity="medium"
        )
    
    async def _execute_basic_diagnosis_flow(self, context: DiagnosisContext) -> None:
        """执行基础诊断流程"""
        # 并行执行搜索和基础分析
        search_task = self._perform_basic_search(context)
        analysis_task = self._perform_basic_analysis(context)
        
        search_result, analysis_result = await asyncio.gather(
            search_task, analysis_task, return_exceptions=True
        )
        
        if isinstance(search_result, Exception):
            self.logger.warning(f"Basic search failed: {search_result}")
        if isinstance(analysis_result, Exception):
            self.logger.warning(f"Basic analysis failed: {analysis_result}")
    
    async def _execute_deep_research_flow(self, context: DiagnosisContext) -> None:
        """执行深度研究流程"""
        # 检查模式是否真的是深度研究，防止意外调用
        if context.mode != DiagnosisMode.DEEP_RESEARCH:
            self.logger.warning(f"Deep research flow called but mode is {context.mode}, skipping")
            return
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.INITIALIZING_RESEARCH)
        
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}🔬 开始深度研究模式")
        print(f"{Fore.CYAN}账号信息: {context.account_info.get('nickname', 'N/A')}")
        print(f"{Fore.CYAN}{'='*80}")
        
        # 深度研究的多阶段执行
        await self._perform_deep_search_with_progress(context)
        await self._perform_comprehensive_analysis_with_progress(context)
    
    async def _perform_basic_search(self, context: DiagnosisContext) -> None:
        """执行基础搜索"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.SEARCHING)
        
        print(f"\n{Fore.BLUE}🔍 执行基础搜索...")
        
        try:
            # 使用真实的搜索管道
            from .search_pipeline import SearchPipeline
            from .config_manager import get_diagnosis_config
            
            config = get_diagnosis_config()
            search_pipeline = SearchPipeline(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            search_results = await search_pipeline.execute_search_pipeline(
                account_info=context.account_info,
                mode="basic"
            )
            
            # 正确获取搜索结果 - 从 'results' 字段中获取
            pipeline_results = search_results.get('results', {})
            context.search_results = pipeline_results.get('all_results', [])
            
            print(f"{Fore.GREEN}✅ 基础搜索完成")
            print(f"{Fore.YELLOW}📊 搜索统计:")
            print(f"{Fore.YELLOW}   - 搜索结果数量: {len(context.search_results)}")
            
            # 显示搜索结果摘要
            for i, result in enumerate(context.search_results[:3]):  # 显示前3个结果
                print(f"{Fore.YELLOW}   - 结果 {i+1}: {result.get('summary', 'N/A')[:100]}...")
                
        except Exception as e:
            self.logger.warning(f"真实搜索失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 搜索失败，使用模拟数据: {e}")
            await asyncio.sleep(0.2)  # 模拟搜索操作
            context.search_results = [{'type': 'basic', 'data': 'mock_search_result', 'summary': '模拟搜索结果'}]
    
    async def _perform_basic_analysis(self, context: DiagnosisContext) -> None:
        """执行基础分析"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.ANALYZING_INDUSTRY)
        
        print(f"\n{Fore.MAGENTA}🔬 执行基础分析...")
        
        try:
            # 使用真实的分析引擎
            from .analysis_engine import AnalysisEngine, AnalysisContext, AnalysisType
            from .config_manager import get_diagnosis_config
            
            config = get_diagnosis_config()
            analysis_engine = AnalysisEngine(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            # 创建分析上下文
            analysis_context = AnalysisContext(
                account_info=context.account_info,
                search_results=context.search_results,
                industry_context=context.account_info.get('industry', 'general'),
                analysis_goals=['基础账号分析', '性能指标分析']
            )
            
            # 执行基础分析
            analysis_results = await analysis_engine.execute_analysis_pipeline(
                context=analysis_context,
                analysis_types=[AnalysisType.BASIC_ACCOUNT, AnalysisType.PERFORMANCE_METRICS],
                mode="parallel"
            )
            
            context.analysis_results.update(analysis_results)
            
            print(f"{Fore.GREEN}✅ 基础分析完成")
            print(f"{Fore.YELLOW}📊 分析统计:")
            
            # 显示分析结果摘要
            results_data = analysis_results.get('results', {})
            performance_stats = analysis_results.get('performance_stats', {})
            
            print(f"{Fore.YELLOW}   - results: 置信度 {performance_stats.get('average_confidence_score', 0):.2f}, 耗时 {analysis_results.get('execution_time', 0):.2f}s")
            print(f"{Fore.YELLOW}   - performance_stats: 置信度 {performance_stats.get('average_confidence_score', 0):.2f}, 耗时 {analysis_results.get('execution_time', 0):.2f}s")
            
            # 显示各个分析类型的详细结果
            if results_data and isinstance(results_data, dict):
                for analysis_type, result_data in results_data.items():
                    if analysis_type != '_meta' and result_data:
                        print(f"{Fore.CYAN}   - {analysis_type}: 数据完整性 {len(str(result_data))//100:.1f}分")
                    
        except Exception as e:
            self.logger.warning(f"真实分析失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 分析失败，使用模拟数据: {e}")
            await asyncio.sleep(0.3)  # 模拟分析操作
            context.analysis_results['basic'] = {
                'completed': True, 
                'result_data': {'mock': True, 'analysis_type': 'basic'},
                'confidence_score': 0.8
            }
    
    async def _perform_deep_search(self, context: DiagnosisContext) -> None:
        """执行深度搜索"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.GENERATING_QUERIES)
        
        print(f"\n{Fore.BLUE}🚀 执行深度搜索...")
        
        try:
            # 使用真实的搜索管道
            from .search_pipeline import SearchPipeline
            from .config_manager import get_diagnosis_config
            
            print(f"{Fore.CYAN}📋 账号信息：")
            print(f"{Fore.CYAN}   - 昵称: {context.account_info.get('nickname', 'N/A')}")
            print(f"{Fore.CYAN}   - 行业: {context.account_info.get('industry', 'N/A')}")
            print(f"{Fore.CYAN}   - 粉丝数: {context.account_info.get('follows', 'N/A')}")
            
            config = get_diagnosis_config()
            search_pipeline = SearchPipeline(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            print(f"{Fore.BLUE}🚀 开始执行深度搜索管道...")
            search_results = await search_pipeline.execute_search_pipeline(
                account_info=context.account_info,
                mode="deep"
            )
            
            print(f"{Fore.BLUE}📊 搜索管道返回数据结构：")
            print(f"{Fore.BLUE}   - 搜索管道结果键: {list(search_results.keys())}")
            print(f"{Fore.BLUE}   - 执行的查询数量: {search_results.get('queries_executed', 'N/A')}")
            print(f"{Fore.BLUE}   - 总搜索结果数: {search_results.get('total_results', 'N/A')}")
            
            # 正确获取搜索结果 - 从 'results' 字段中获取
            pipeline_results = search_results.get('results', {})
            print(f"{Fore.BLUE}   - pipeline_results键: {list(pipeline_results.keys()) if pipeline_results else 'Empty'}")
            print(f"{Fore.BLUE}   - 原始结果数量: {pipeline_results.get('total_count', 'N/A')}")
            
            context.search_results = pipeline_results.get('all_results', [])
            
            # 显示原始搜索结果的详细信息
            print(f"{Fore.CYAN}🔍 搜索结果详细信息:")
            if context.search_results:
                for i, result in enumerate(context.search_results[:2]):
                    print(f"{Fore.CYAN}   结果 {i+1}:")
                    print(f"{Fore.CYAN}     - 标题: {result.get('title', 'N/A')[:80]}...")
                    print(f"{Fore.CYAN}     - 来源: {result.get('source', 'N/A')}")
                    print(f"{Fore.CYAN}     - 相关性: {result.get('relevance_score', 'N/A')}")
                    print(f"{Fore.CYAN}     - 内容长度: {len(result.get('content', '')) if result.get('content') else 0} 字符")
                    if result.get('snippet'):
                        print(f"{Fore.CYAN}     - 片段: {result.get('snippet', 'N/A')[:100]}...")
            else:
                print(f"{Fore.RED}   ⚠️ 无搜索结果")
            
            print(f"{Fore.GREEN}✅ 深度搜索完成")
            print(f"{Fore.YELLOW}📊 深度搜索统计:")
            print(f"{Fore.YELLOW}   - 搜索结果数量: {len(context.search_results)}")
            print(f"{Fore.YELLOW}   - 搜索质量评分: {pipeline_results.get('average_quality', 'N/A')}")
            print(f"{Fore.YELLOW}   - 执行的查询数量: {search_results.get('queries_executed', 'N/A')}")
            print(f"{Fore.YELLOW}   - 总搜索耗时: {search_results.get('execution_time', 0):.2f}秒")
            
            # 显示详细搜索结果
            for i, result in enumerate(context.search_results[:5]):  # 显示前5个结果
                relevance = result.get('relevance_score', 0)
                color = Fore.GREEN if relevance > 0.8 else Fore.YELLOW if relevance > 0.6 else Fore.RED
                snippet = result.get('snippet', result.get('content', 'N/A'))[:80]
                print(f"{color}   - 搜索 {i+1}: 相关性 {relevance:.2f}, {snippet}...")
                
        except Exception as e:
            self.logger.warning(f"真实深度搜索失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 深度搜索失败，使用模拟数据: {e}")
            await asyncio.sleep(0.4)  # 模拟深度搜索
            context.search_results = [
                {'type': 'deep', 'data': 'mock_deep_search_result', 'summary': '模拟深度搜索结果', 'quality_score': 0.85}
            ]
    
    async def _perform_comprehensive_analysis(self, context: DiagnosisContext) -> None:
        """执行综合分析"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS)
        
        print(f"\n{Fore.MAGENTA}🧠 执行综合分析...")
        
        try:
            # 使用真实的分析引擎
            from .analysis_engine import AnalysisEngine, AnalysisContext, AnalysisType
            from .config_manager import get_diagnosis_config
            
            config = get_diagnosis_config()
            analysis_engine = AnalysisEngine(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            # 创建分析上下文
            analysis_context = AnalysisContext(
                account_info=context.account_info,
                search_results=context.search_results,
                industry_context=context.account_info.get('industry', 'general'),
                analysis_goals=['综合账号分析', '行业趋势分析', '竞争对手分析']
            )
            
            # 执行综合分析
            analysis_results = await analysis_engine.execute_analysis_pipeline(
                context=analysis_context,
                analysis_types=[
                    AnalysisType.BASIC_ACCOUNT, 
                    AnalysisType.INDUSTRY_TRENDS, 
                    AnalysisType.COMPETITIVE_LANDSCAPE,
                    AnalysisType.PERFORMANCE_METRICS
                ],
                mode="parallel"
            )
            
            context.analysis_results.update(analysis_results)
            
            print(f"{Fore.GREEN}✅ 综合分析完成")
            print(f"{Fore.YELLOW}📊 综合分析统计:")
            
            # 正确显示详细分析结果
            results_data = analysis_results.get('results', {})
            performance_stats = analysis_results.get('performance_stats', {})
            total_confidence = performance_stats.get('average_confidence_score', 0)
            total_time = analysis_results.get('execution_time', 0)
            
            color = Fore.GREEN if total_confidence > 0.8 else Fore.YELLOW if total_confidence > 0.6 else Fore.RED
            print(f"{color}   - 总体置信度: {total_confidence:.2f}, 总耗时: {total_time:.2f}s")
            print(f"{Fore.YELLOW}   - 成功分析数: {analysis_results.get('successful_count', 0)}/{analysis_results.get('analysis_count', 0)}")
            
            # 显示各个分析类型的详细结果
            if results_data and isinstance(results_data, dict):
                for analysis_type, result_data in results_data.items():
                    if analysis_type != '_meta' and result_data:
                        data_size = len(str(result_data))
                        result_color = Fore.GREEN if data_size > 500 else Fore.YELLOW if data_size > 200 else Fore.RED
                        print(f"{result_color}   - {analysis_type}: 数据量 {data_size} 字符, 完整性 {min(data_size//100, 10)}/10")
                    
                    # 显示分析内容摘要（如果有的话）
                    if isinstance(result_data, dict) and 'analysis_content' in result_data:
                        content = result_data['analysis_content']
                        if content and len(str(content)) > 50:
                            print(f"{Fore.CYAN}     摘要: {str(content)[:100]}...")
                        
        except Exception as e:
            self.logger.warning(f"真实综合分析失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 综合分析失败，使用模拟数据: {e}")
            await asyncio.sleep(0.5)  # 模拟综合分析
            context.analysis_results['comprehensive'] = {
                'completed': True,
                'result_data': {'mock': True, 'analysis_type': 'comprehensive'},
                'confidence_score': 0.75,
                'execution_time': 0.5
            }
    
    async def _perform_deep_search_with_progress(self, context: DiagnosisContext) -> None:
        """执行深度搜索，带有详细的进度更新"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.GENERATING_QUERIES, "正在初始化深度搜索...")
        
        print(f"\n{Fore.BLUE}🚀 执行深度搜索...")
        
        try:
            # 使用真实的搜索管道
            from .search_pipeline import SearchPipeline
            from .config_manager import get_diagnosis_config
            
            print(f"{Fore.CYAN}📋 账号信息：")
            print(f"{Fore.CYAN}   - 昵称: {context.account_info.get('nickname', 'N/A')}")
            print(f"{Fore.CYAN}   - 行业: {context.account_info.get('industry', 'N/A')}")
            print(f"{Fore.CYAN}   - 粉丝数: {context.account_info.get('follows', 'N/A')}")
            
            # 更新状态：正在优化查询
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.OPTIMIZING_QUERIES, "正在优化搜索策略...")
            
            config = get_diagnosis_config()
            search_pipeline = SearchPipeline(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            # 更新状态：开始执行搜索
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, "正在执行大规模搜索...")
            
            print(f"{Fore.BLUE}🚀 开始执行深度搜索管道...")
            search_results = await search_pipeline.execute_search_pipeline(
                account_info=context.account_info,
                mode="deep"
            )
            
            # 更新状态：处理搜索结果
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.PROCESSING_RESULTS, "正在处理和分析搜索结果...")
            
            # 处理搜索结果
            pipeline_results = search_results.get('results', {})
            context.search_results = pipeline_results.get('all_results', [])
            
            # 更新状态：搜索完成，开始整理结果
            if self.progress_tracker:
                result_count = len(context.search_results)
                self.progress_tracker.update_status(
                    UnifiedTaskStatus.PROCESSING_RESULTS, 
                    f"已获取 {result_count} 个搜索结果，正在整理分析..."
                )
            
            print(f"{Fore.GREEN}✅ 深度搜索完成")
                
        except Exception as e:
            self.logger.warning(f"真实深度搜索失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 深度搜索失败，使用模拟数据: {e}")
            
            # 在模拟期间也要更新状态
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, "搜索遇到问题，正在重试...")
            
            await asyncio.sleep(0.2)  # 模拟重试等待
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.PROCESSING_RESULTS, "使用备用搜索策略...")
            
            await asyncio.sleep(0.2)  # 模拟备用搜索
            context.search_results = [
                {'type': 'deep', 'data': 'mock_deep_search_result', 'summary': '模拟深度搜索结果', 'quality_score': 0.85}
            ]

    async def _perform_comprehensive_analysis_with_progress(self, context: DiagnosisContext) -> None:
        """执行综合分析，带有详细的进度更新"""
        if self.progress_tracker:
            self.progress_tracker.update_status(UnifiedTaskStatus.INDUSTRY_ANALYSIS, "正在初始化行业分析...")
        
        print(f"\n{Fore.MAGENTA}🧠 执行综合分析...")
        
        try:
            # 使用真实的分析引擎
            from .analysis_engine import AnalysisEngine, AnalysisContext, AnalysisType
            from .config_manager import get_diagnosis_config
            
            # 更新状态：准备分析引擎
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.INDUSTRY_ANALYSIS, "正在准备AI分析引擎...")
            
            config = get_diagnosis_config()
            analysis_engine = AnalysisEngine(
                config=config,
                progress_tracker=self.progress_tracker
            )
            
            # 创建分析上下文
            analysis_context = AnalysisContext(
                account_info=context.account_info,
                search_results=context.search_results,
                industry_context=context.account_info.get('industry', 'general'),
                analysis_goals=['综合账号分析', '行业趋势分析', '竞争对手分析']
            )
            
            # 更新状态：开始综合分析
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS, "正在进行多维度智能分析...")
            
            # 执行综合分析
            analysis_results = await analysis_engine.execute_analysis_pipeline(
                context=analysis_context,
                analysis_types=[
                    AnalysisType.BASIC_ACCOUNT, 
                    AnalysisType.INDUSTRY_TRENDS, 
                    AnalysisType.COMPETITIVE_LANDSCAPE,
                    AnalysisType.PERFORMANCE_METRICS
                ],
                mode="parallel"
            )
            
            # 更新状态：整合分析结果
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.FINALIZING_REPORT, "正在整合分析结果...")
            
            context.analysis_results.update(analysis_results)
            
            # 最终状态更新：分析完成
            if self.progress_tracker:
                successful_count = analysis_results.get('successful_count', 0)
                total_count = analysis_results.get('analysis_count', 0)
                self.progress_tracker.update_status(
                    UnifiedTaskStatus.FINALIZING_REPORT, 
                    f"分析完成 ({successful_count}/{total_count})，正在生成报告..."
                )
            
            print(f"{Fore.GREEN}✅ 综合分析完成")
                        
        except Exception as e:
            self.logger.warning(f"真实综合分析失败，使用模拟数据: {e}")
            print(f"{Fore.RED}⚠️ 综合分析失败，使用模拟数据: {e}")
            
            # 在模拟期间也要更新状态
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.COMPREHENSIVE_ANALYSIS, "分析遇到问题，正在使用备用策略...")
            
            await asyncio.sleep(0.25)  # 模拟重试等待
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.FINALIZING_REPORT, "正在生成模拟分析结果...")
            
            await asyncio.sleep(0.25)  # 模拟生成结果
            context.analysis_results['comprehensive'] = {
                'completed': True,
                'result_data': {'mock': True, 'analysis_type': 'comprehensive'},
                'confidence_score': 0.75,
                'execution_time': 0.5
            }
    async def _generate_html_report(self, context: DiagnosisContext) -> Dict[str, str]:
        """生成HTML报告"""
        await asyncio.sleep(0.2)  # 模拟报告生成
        return {'html_report': '<html>Mock HTML Report</html>'}
    
    async def _generate_json_report(self, context: DiagnosisContext) -> Dict[str, str]:
        """生成JSON报告"""
        await asyncio.sleep(0.1)  # 模拟报告生成
        return {'json_report': '{"status": "completed"}'}
    
    async def _generate_sales_proposal_md(self, context: DiagnosisContext) -> Dict[str, str]:
        """生成销售提案Markdown"""
        await asyncio.sleep(0.15)  # 模拟报告生成
        return {'sales_proposal_md': '# Sales Proposal\nMock content'}
    
    async def _generate_sales_proposal_html(self, context: DiagnosisContext) -> Dict[str, str]:
        """生成销售提案HTML"""
        await asyncio.sleep(0.15)  # 模拟报告生成
        return {'sales_proposal_html': '<html>Sales Proposal</html>'}
    
    async def _generate_diagnosis_text_report(self, context: DiagnosisContext) -> Dict[str, str]:
        """生成诊断文本报告"""
        try:
            # 导入AI调用模块
            from task import callWattGPT
            
            # 从分析结果构建诊断提示
            analysis_results = context.analysis_results
            account_info = context.account_info
            
            # 构建综合诊断提示
            diagnosis_prompt = f"""
基于以下分析结果，为账号“{account_info.get('nickname', 'N/A')}”生成综合诊断报告：

账号基础信息：
- 昵称：{account_info.get('nickname', 'N/A')}
- 粉丝数：{account_info.get('follows', 0)}
- 行业：{account_info.get('industry', 'N/A')}
- 描述：{account_info.get('desc', 'N/A')}

分析结果摘要：
{self._format_analysis_summary(analysis_results)}

请生成一份专业的诊断报告，包含：

1. **账号现状分析**
   - 当前发展阶段
   - 主要优势和特点
   - 存在的问题

2. **市场环境洞察**  
   - 行业趋势分析
   - 竞争环境评估
   - 机会与威胁

3. **优化建议**
   - 内容策略优化
   - 运营策略改进
   - 增长路径规划

4. **行动方案**
   - 短期优化重点
   - 中长期发展策略
   - 具体执行建议

要求：
- 使用专业但易懂的语言
- 提供具体可操作的建议
- 总长度控制在800-1200字
- 结构清晰，逻辑严谨
"""
            
            # 调用GPT生成诊断报告
            gpt_request = {
                "model": "gpt-4o",
                "messages": [
                    {"role": "system", "content": "你是一位资深的社交媒体运营专家，擅长账号诊断和优化策略制定。"},
                    {"role": "user", "content": diagnosis_prompt}
                ],
                "max_tokens": 2500,
                "temperature": 0.7
            }
            
            self.logger.info("开始生成诊断文本报告...")
            
            status, code, result = callWattGPT.callOpenaiChannelChatCompletions(
                gpt_request, timeout=180
            )
            
            if status and result:
                content = self._extract_gpt_content(result)
                if content and len(content.strip()) > 100:
                    return {'diagnosis_report': content}
                else:
                    return {'diagnosis_report': f"诊断报告生成失败：内容不足 ({len(content) if content else 0} 字符)"}
            else:
                return {'diagnosis_report': f"诊断报告生成失败：{result}"}
                
        except Exception as e:
            self.logger.error(f"生成诊断文本报告失败: {e}")
            return {'diagnosis_report': f"诊断报告生成异常：{str(e)}"}
    
    def _format_analysis_summary(self, analysis_results: Dict[str, Any]) -> str:
        """格式化分析结果摘要"""
        summary_parts = []
        
        # 基础账号分析摘要
        basic_account = analysis_results.get('basic_account', {})
        if basic_account:
            summary_parts.append("基础账号分析：")
            if 'account_type' in basic_account:
                summary_parts.append(f"- 账号类型：{basic_account['account_type']}")
            if 'improvement_areas' in basic_account:
                summary_parts.append(f"- 主要改进点：{', '.join(basic_account['improvement_areas'][:3])}")
        
        # 行业趋势分析摘要
        industry_trends = analysis_results.get('industry_trends', {})
        if industry_trends:
            summary_parts.append("\n行业趋势分析：")
            if 'trending_topics' in industry_trends:
                summary_parts.append(f"- 热门趋势：{', '.join(industry_trends['trending_topics'][:3])}")
            if 'market_opportunities' in industry_trends:
                opportunities = industry_trends['market_opportunities']
                if opportunities:
                    summary_parts.append(f"- 市场机会：{opportunities[0].get('opportunity', 'N/A')}")
        
        # 性能指标分析摘要
        performance = analysis_results.get('performance_metrics', {})
        if performance:
            summary_parts.append("\n性能指标：")
            if 'engagement_metrics' in performance:
                engagement = performance['engagement_metrics']
                if 'engagement_rate' in engagement:
                    summary_parts.append(f"- 互动率：{engagement['engagement_rate']}")
        
        return '\n'.join(summary_parts) if summary_parts else "分析结果处理中..."
    
    def _extract_gpt_content(self, result: Any) -> Optional[str]:
        """提取GPT响应中的内容"""
        try:
            if isinstance(result, dict) and 'result' in result:
                result_data = result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    return message['content']
            return None
        except Exception as e:
            self.logger.error(f"提取GPT内容失败: {e}")
            return None
    
    def _calculate_performance_metrics(self) -> None:
        """计算性能指标"""
        total_duration = time.time() - self._performance_metrics['total_start_time']
        self._performance_metrics['total_duration'] = total_duration
        
        # 计算各阶段耗时
        for stage, result in self._stage_results.items():
            self._performance_metrics['stage_durations'][stage.value] = result.duration
        
        # 计算并行效率（理论串行时间 vs 实际执行时间）
        total_stage_time = sum(result.duration for result in self._stage_results.values())
        if total_stage_time > 0 and total_duration > 0:
            # 并行效率 = 理论串行时间 / 实际执行时间，但最少为1.0
            self._performance_metrics['parallel_efficiency'] = max(1.0, total_stage_time / total_duration)
        else:
            self._performance_metrics['parallel_efficiency'] = 1.0
    
    def _build_final_result(self, context: DiagnosisContext) -> Dict[str, Any]:
        """构建最终结果"""
        return {
            'task_id': context.task_id,
            'status': 'completed',
            'mode': context.mode.value,
            'account_info': context.account_info,
            'search_results': context.search_results,
            'analysis_results': context.analysis_results,
            'reports': context.report_data,
            'performance_metrics': self._performance_metrics,
            'complexity_factor': context.complexity_factor,
            'error_count': context.error_count,
            'execution_time': self._performance_metrics['total_duration']
        }
    
    def _build_error_result(self, context: DiagnosisContext, error_message: str) -> Dict[str, Any]:
        """构建错误结果"""
        return {
            'task_id': context.task_id,
            'status': 'failed',
            'error_message': error_message,
            'error_count': context.error_count,
            'partial_results': {
                'account_info': context.account_info,
                'search_results': context.search_results,
                'analysis_results': context.analysis_results
            },
            'execution_time': time.time() - context.start_time
        }
    
    async def cancel(self) -> None:
        """取消执行"""
        self._cancellation_token.set()
        self.logger.info("Diagnosis execution cancelled")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return self._performance_metrics.copy()
    
    def get_stage_results(self) -> Dict[ExecutionStage, StageResult]:
        """获取阶段执行结果"""
        return self._stage_results.copy()


# 便捷函数
async def create_diagnosis_engine(config: Optional[DiagnosisConfig] = None) -> DiagnosisEngine:
    """创建诊断引擎实例"""
    resource_manager = await get_resource_manager()
    return DiagnosisEngine(config=config, resource_manager=resource_manager)


async def quick_diagnose(task_id: str, account_info: Dict[str, Any], 
                        mode: DiagnosisMode = DiagnosisMode.BASIC,
                        user_id: Optional[str] = None) -> Dict[str, Any]:
    """快速诊断便捷函数"""
    engine = await create_diagnosis_engine()
    context = DiagnosisContext(
        task_id=task_id,
        user_id=user_id,
        account_info=account_info,
        mode=mode
    )
    return await engine.diagnose(context)