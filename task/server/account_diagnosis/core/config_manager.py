"""
Unified Configuration Management System
统一配置管理系统 - 整合所有配置组件
"""

import os
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field, asdict
from pathlib import Path
from enum import Enum


class ConfigSource(Enum):
    """配置源类型"""
    DEFAULT = "default"
    ENV_VAR = "env_var"
    CONFIG_FILE = "config_file"
    RUNTIME = "runtime"


@dataclass
class AIModelConfig:
    """AI模型配置类"""
    gpt_model: str = "gpt-4.1"
    pplx_model: str = "sonar"
    gemini_model: str = "gemini-2.5-flash"
    claude_model: str = "claude-sonnet-4-20250514"
    
    # 模型性能配置
    gpt_max_tokens: int = 8000
    gemini_max_tokens: int = 8000
    claude_max_tokens: int = 4000
    
    # 模型降级策略
    fallback_models: List[str] = field(default_factory=lambda: ["gemini", "gpt", "claude"])
    
    # 模型特定超时配置
    model_timeouts: Dict[str, int] = field(default_factory=lambda: {
        "gpt": 120,
        "gemini": 120,
        "claude": 120,
        "pplx": 150
    })


@dataclass
class ConcurrencyConfig:
    """并发控制配置"""
    max_concurrent_tasks: int = 8
    max_concurrent_searches: int = 3
    max_concurrent_reports: int = 4
    
    # 任务队列配置
    queue_timeout: int = 300
    task_retry_limit: int = 3
    
    # 信号量配置
    search_semaphore_limit: int = 5
    analysis_semaphore_limit: int = 3
    report_semaphore_limit: int = 4


@dataclass
class TimeoutConfig:
    """超时配置类"""
    # 基础超时
    connection_timeout: float = 30.0
    operation_timeout: float = 60.0
    idle_timeout: float = 300.0
    
    # AI调用超时
    ai_base_timeout: int = 120
    ai_max_timeout: int = 300
    ai_retry_multiplier: float = 1.5
    
    # 搜索超时
    search_timeout: int = 60
    search_max_timeout: int = 180
    
    # 分析超时
    analysis_timeout: int = 180
    analysis_max_timeout: int = 360
    
    # 报告生成超时
    report_timeout: int = 120
    report_max_timeout: int = 240


@dataclass
class CacheConfig:
    """缓存配置类"""
    enable_caching: bool = True
    
    # 缓存TTL配置 (秒)
    search_cache_ttl: int = 3600  # 1小时
    analysis_cache_ttl: int = 1800  # 30分钟
    report_cache_ttl: int = 900  # 15分钟
    
    # 缓存大小限制
    max_cache_size: int = 1000
    max_memory_mb: int = 500
    
    # 缓存清理策略
    cleanup_interval: int = 3600  # 1小时
    cache_hit_threshold: float = 0.3  # 命中率阈值


@dataclass
class RedisConfig:
    """Redis配置类"""
    # 连接池配置
    max_connections: int = 20
    connection_timeout: int = 10
    socket_timeout: int = 10
    
    # 重试配置
    retry_on_timeout: bool = True
    max_retries: int = 3
    retry_delay: float = 0.5
    
    # 队列配置
    queue_prefix: str = ""
    message_ttl: int = 86400  # 24小时


@dataclass
class DeepResearchConfig:
    """深度研究配置类"""
    # 查询生成配置
    max_queries_per_layer: int = 3
    min_search_results: int = 2
    query_optimization_enabled: bool = True
    
    # 搜索配置
    search_engines: List[str] = field(default_factory=lambda: ["pplx", "serper"])
    search_result_limit: int = 10
    
    # 分析配置
    analysis_depth_level: int = 2
    enable_industry_analysis: bool = True
    enable_competitor_analysis: bool = True


@dataclass
class DiagnosisConfig:
    """主诊断配置类 - 整合所有配置组件"""
    
    # 基础配置
    service_name: str = "diagnosis"
    environment: str = "dev"
    debug_mode: bool = False
    
    # 队列配置
    input_queue: str = ""
    output_queue: str = ""
    
    # 子配置组件
    ai_models: AIModelConfig = field(default_factory=AIModelConfig)
    concurrency: ConcurrencyConfig = field(default_factory=ConcurrencyConfig)
    timeouts: TimeoutConfig = field(default_factory=TimeoutConfig)
    cache: CacheConfig = field(default_factory=CacheConfig)
    redis: RedisConfig = field(default_factory=RedisConfig)
    deep_research: DeepResearchConfig = field(default_factory=DeepResearchConfig)
    
    # 功能开关
    enable_deep_research: bool = False
    enable_parallel_reports: bool = True
    enable_performance_monitoring: bool = True
    
    # 自定义配置
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """初始化后处理"""
        # 自动设置队列名称
        if not self.input_queue:
            self.input_queue = f"{self.environment.lower()}:q:diagnosis:request"
        if not self.output_queue:
            self.output_queue = f"{self.environment.lower()}:q:diagnosis:response"
        
        # 设置Redis队列前缀
        if not self.redis.queue_prefix:
            self.redis.queue_prefix = f"{self.environment.lower()}:q:"


class DynamicTimeoutCalculator:
    """动态超时计算器"""
    
    @staticmethod
    def calculate_ai_timeout(base_timeout: int, complexity_factor: float = 1.0,
                           retry_count: int = 0, model_type: str = "gpt") -> int:
        """
        计算AI调用的动态超时时间
        
        Args:
            base_timeout: 基础超时时间
            complexity_factor: 复杂度系数 (0.5-3.0)
            retry_count: 重试次数
            model_type: 模型类型
        """
        # 模型特定的超时调整
        model_multipliers = {
            "gpt": 1.0,
            "gemini": 0.8,
            "claude": 1.2,
            "pplx": 0.6
        }
        
        multiplier = model_multipliers.get(model_type, 1.0)
        
        # 基础计算：基础超时 × 复杂度系数 × 模型系数
        timeout = int(base_timeout * complexity_factor * multiplier)
        
        # 重试次数增加超时时间
        if retry_count > 0:
            timeout = int(timeout * (1.5 ** retry_count))
        
        # 限制在合理范围内
        return max(30, min(timeout, 600))
    
    @staticmethod
    def calculate_complexity_factor(data_size: int, search_queries: int = 0,
                                  industry_complexity: str = "medium") -> float:
        """
        计算复杂度系数
        
        Args:
            data_size: 数据量大小 (字节)
            search_queries: 搜索查询数量
            industry_complexity: 行业复杂度 ("simple", "medium", "complex")
        """
        # 基础复杂度
        base_factor = 1.0
        
        # 数据量影响 (每10KB增加0.1)
        data_factor = 1.0 + (data_size / 10240) * 0.1
        
        # 搜索查询影响
        search_factor = 1.0 + (search_queries * 0.15)
        
        # 行业复杂度影响
        industry_factors = {
            "simple": 0.8,
            "medium": 1.0,
            "complex": 1.3
        }
        industry_factor = industry_factors.get(industry_complexity, 1.0)
        
        # 综合计算
        total_factor = base_factor * data_factor * search_factor * industry_factor
        
        # 限制在合理范围内
        return max(0.5, min(total_factor, 3.0))


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None, environment: str = "dev"):
        self.logger = logging.getLogger(__name__)
        self.environment = environment
        self.config_file = config_file
        self._config = DiagnosisConfig(environment=environment)
        self._config_sources: Dict[str, ConfigSource] = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        # 1. 加载默认配置
        self._apply_default_config()
        
        # 2. 加载配置文件
        if self.config_file:
            self._load_from_file(self.config_file)
        else:
            # 尝试加载默认配置文件
            default_config_file = f"diagnosis_config_{self.environment}.json"
            if Path(default_config_file).exists():
                self._load_from_file(default_config_file)
        
        # 3. 加载环境变量
        self._load_from_env()
        
        self.logger.info(f"Configuration loaded for environment: {self.environment}")
    
    def _apply_default_config(self):
        """应用默认配置"""
        # 根据环境调整默认配置
        if self.environment.lower() == "prod":
            self._config.concurrency.max_concurrent_tasks = 12
            self._config.timeouts.ai_base_timeout = 150
            self._config.cache.enable_caching = True
            self._config.debug_mode = False
        elif self.environment.lower() == "dev":
            self._config.concurrency.max_concurrent_tasks = 6
            self._config.timeouts.ai_base_timeout = 90
            self._config.debug_mode = True
        
        # 记录配置源
        for key in asdict(self._config).keys():
            self._config_sources[key] = ConfigSource.DEFAULT
    
    def _load_from_file(self, config_file: str):
        """从配置文件加载"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
            
            self._merge_config(file_config, ConfigSource.CONFIG_FILE)
            self.logger.info(f"Configuration loaded from file: {config_file}")
            
        except FileNotFoundError:
            self.logger.warning(f"Configuration file not found: {config_file}")
        except json.JSONDecodeError as e:
            self.logger.error(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            self.logger.error(f"Failed to load configuration file: {e}")
    
    def _load_from_env(self):
        """从环境变量加载配置"""
        env_mappings = {
            'DIAGNOSIS_MAX_CONCURRENT_TASKS': ('concurrency.max_concurrent_tasks', int),
            'DIAGNOSIS_AI_TIMEOUT': ('timeouts.ai_base_timeout', int),
            'DIAGNOSIS_ENABLE_CACHE': ('cache.enable_caching', bool),
            'DIAGNOSIS_ENABLE_DEEP_RESEARCH': ('enable_deep_research', bool),
            'DIAGNOSIS_DEBUG_MODE': ('debug_mode', bool),
            'DIAGNOSIS_REDIS_MAX_CONNECTIONS': ('redis.max_connections', int),
            'DIAGNOSIS_SEARCH_TIMEOUT': ('timeouts.search_timeout', int),
        }
        
        for env_var, (config_path, value_type) in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    # 类型转换
                    if value_type == bool:
                        parsed_value = env_value.lower() in ('true', '1', 'yes', 'on')
                    else:
                        parsed_value = value_type(env_value)
                    
                    # 设置配置值
                    self._set_nested_config(config_path, parsed_value)
                    self._config_sources[config_path] = ConfigSource.ENV_VAR
                    
                    self.logger.debug(f"Environment variable {env_var} -> {config_path} = {parsed_value}")
                    
                except (ValueError, TypeError) as e:
                    self.logger.error(f"Invalid value for environment variable {env_var}: {env_value} ({e})")
    
    def _merge_config(self, new_config: Dict[str, Any], source: ConfigSource):
        """合并配置"""
        def merge_recursive(current_dict, new_dict, path=""):
            for key, value in new_dict.items():
                current_path = f"{path}.{key}" if path else key
                
                if isinstance(value, dict) and hasattr(current_dict, key):
                    current_attr = getattr(current_dict, key)
                    if hasattr(current_attr, '__dict__'):
                        merge_recursive(current_attr, value, current_path)
                    else:
                        setattr(current_dict, key, value)
                        self._config_sources[current_path] = source
                else:
                    if hasattr(current_dict, key):
                        setattr(current_dict, key, value)
                        self._config_sources[current_path] = source
        
        merge_recursive(self._config, new_config)
    
    def _set_nested_config(self, path: str, value: Any):
        """设置嵌套配置值"""
        parts = path.split('.')
        current = self._config
        
        for part in parts[:-1]:
            if hasattr(current, part):
                current = getattr(current, part)
            else:
                raise ValueError(f"Invalid configuration path: {path}")
        
        if hasattr(current, parts[-1]):
            setattr(current, parts[-1], value)
        else:
            raise ValueError(f"Invalid configuration path: {path}")
    
    def get_config(self) -> DiagnosisConfig:
        """获取完整配置"""
        return self._config
    
    def get_ai_timeout(self, model_type: str = "gpt", complexity_factor: float = 1.0,
                      retry_count: int = 0) -> int:
        """获取动态AI超时时间"""
        base_timeout = self._config.timeouts.ai_base_timeout
        return DynamicTimeoutCalculator.calculate_ai_timeout(
            base_timeout, complexity_factor, retry_count, model_type
        )
    
    def update_config(self, config_updates: Dict[str, Any], source: ConfigSource = ConfigSource.RUNTIME):
        """运行时更新配置"""
        self._merge_config(config_updates, source)
        self.logger.info(f"Configuration updated: {config_updates}")
    
    def save_config(self, file_path: Optional[str] = None):
        """保存配置到文件"""
        if not file_path:
            file_path = f"diagnosis_config_{self.environment}.json"
        
        try:
            config_dict = asdict(self._config)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Configuration saved to: {file_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
    
    def validate_config(self) -> List[str]:
        """验证配置有效性"""
        errors = []
        
        # 验证并发配置
        if self._config.concurrency.max_concurrent_tasks <= 0:
            errors.append("max_concurrent_tasks must be positive")
        
        # 验证超时配置
        if self._config.timeouts.ai_base_timeout <= 0:
            errors.append("ai_base_timeout must be positive")
        
        # 验证Redis配置
        if self._config.redis.max_connections <= 0:
            errors.append("redis max_connections must be positive")
        
        # 验证队列名称
        if not self._config.input_queue or not self._config.output_queue:
            errors.append("Queue names cannot be empty")
        
        # 验证AI模型配置
        if not self._config.ai_models.fallback_models:
            errors.append("At least one fallback model must be configured")
        
        return errors
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "environment": self._config.environment,
            "service_name": self._config.service_name,
            "debug_mode": self._config.debug_mode,
            "max_concurrent_tasks": self._config.concurrency.max_concurrent_tasks,
            "ai_base_timeout": self._config.timeouts.ai_base_timeout,
            "enable_deep_research": self._config.enable_deep_research,
            "cache_enabled": self._config.cache.enable_caching,
            "config_sources": self._config_sources
        }


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(environment: str = "dev", config_file: Optional[str] = None) -> ConfigManager:
    """获取配置管理器单例"""
    global _config_manager
    
    if _config_manager is None or _config_manager.environment != environment:
        _config_manager = ConfigManager(config_file, environment)
    
    return _config_manager


def get_diagnosis_config(environment: str = "dev") -> DiagnosisConfig:
    """获取诊断配置"""
    return get_config_manager(environment).get_config()


def calculate_dynamic_timeout(base_timeout: int, data_size: int = 0,
                            search_queries: int = 0, industry_complexity: str = "medium",
                            model_type: str = "gpt", retry_count: int = 0) -> int:
    """计算动态超时时间的便捷函数"""
    complexity_factor = DynamicTimeoutCalculator.calculate_complexity_factor(
        data_size, search_queries, industry_complexity
    )
    
    return DynamicTimeoutCalculator.calculate_ai_timeout(
        base_timeout, complexity_factor, retry_count, model_type
    )