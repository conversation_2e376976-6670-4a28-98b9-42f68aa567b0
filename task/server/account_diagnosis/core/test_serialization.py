#!/usr/bin/env python3
"""
Test script to verify Redis serialization is working correctly
"""

import json
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

def test_fallback_serialization():
    """Test the fallback serialization function"""
    
    def safe_redis_serialize_with_validation(data: dict) -> str:
        """Fallback serialization function - double JSON dumps"""
        try:
            # First serialization: Python object -> JSON string
            first_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            # Second serialization: JSON string -> escaped JSON string
            result = json.dumps(first_json, ensure_ascii=False)
            return result
        except Exception as e:
            raise ValueError(f"Failed to serialize data to double JSON format: {e}")
    
    # Test data similar to what status_manager sends
    test_data = {
        "taskInfo": {
            "env": "dev",
            "taskId": "1946601751360704512",
            "userId": "55ccab22f5a2631435eda8d4",
            "diagnosisId": 287,
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "开始搜索: 开始搜索: 科技 营销策略案例 2025年",
            "aiTaskMsgCN": "开始搜索: 开始搜索: 科技 营销策略案例 2025年",
            "aiTaskProgress": 54,
            "timestamp": "2025-07-20T00:07:27.665770",
            "elapsed_time": 0.545712947845459
        }
    }
    
    print("Testing serialization...")
    print(f"Original data type: {type(test_data)}")
    
    # Serialize
    try:
        serialized = safe_redis_serialize_with_validation(test_data)
        print(f"Serialized data type: {type(serialized)}")
        print(f"Serialized data length: {len(serialized)}")
        print(f"Serialized data preview: {serialized[:100]}...")
        
        # Verify double deserialization
        step1 = json.loads(serialized)
        print(f"Step 1 deserialization type: {type(step1)}")
        
        step2 = json.loads(step1)
        print(f"Step 2 deserialization type: {type(step2)}")
        
        # Verify data integrity
        if step2 == test_data:
            print("✅ Serialization test PASSED - data integrity maintained")
            return True
        else:
            print("❌ Serialization test FAILED - data integrity lost")
            print(f"Original: {test_data}")
            print(f"Recovered: {step2}")
            return False
            
    except Exception as e:
        print(f"❌ Serialization test FAILED with exception: {e}")
        return False

def test_with_html_content():
    """Test serialization with HTML content (like final results)"""
    
    def safe_redis_serialize_with_validation(data: dict) -> str:
        """Fallback serialization function - double JSON dumps"""
        try:
            # First serialization: Python object -> JSON string
            first_json = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
            # Second serialization: JSON string -> escaped JSON string
            result = json.dumps(first_json, ensure_ascii=False)
            return result
        except Exception as e:
            raise ValueError(f"Failed to serialize data to double JSON format: {e}")
    
    # Test data with HTML content
    test_data = {
        "taskInfo": {
            "env": "dev",
            "taskId": "test_123",
            "aiTaskStatus": "FINISH",
            "aiTaskProgress": 100
        },
        "diagnosisHtml": """<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8" />
    <title>诊断报告</title>
</head>
<body>
    <h1>账号诊断报告</h1>
    <p>这是一个包含换行符和特殊字符的HTML内容</p>
    <div class="content">
        <p>测试内容：引号"双引号"，单引号'单引号'</p>
    </div>
</body>
</html>""",
        "diagnosisReport": {
            "summary": "账号表现良好，需要优化内容质量。",
            "tags": [{"dimension": "STATUS", "status": "良好"}]
        }
    }
    
    print("\nTesting serialization with HTML content...")
    
    try:
        serialized = safe_redis_serialize_with_validation(test_data)
        print(f"✅ HTML serialization successful, length: {len(serialized)}")
        
        # Verify deserialization
        step1 = json.loads(serialized)
        step2 = json.loads(step1)
        
        if step2 == test_data:
            print("✅ HTML serialization test PASSED")
            return True
        else:
            print("❌ HTML serialization test FAILED - data mismatch")
            return False
            
    except Exception as e:
        print(f"❌ HTML serialization test FAILED: {e}")
        return False

def test_actual_import():
    """Test importing the actual serialization function"""
    
    print("\nTesting actual import...")
    
    try:
        from task.lib.json_utils import safe_redis_serialize_with_validation
        print("✅ Successfully imported safe_redis_serialize_with_validation")
        
        # Test with simple data
        test_data = {"test": "data", "number": 123}
        result = safe_redis_serialize_with_validation(test_data)
        
        if isinstance(result, str):
            print("✅ Import test PASSED - function returns string")
            return True
        else:
            print(f"❌ Import test FAILED - function returns {type(result)}")
            return False
            
    except ImportError as e:
        print(f"⚠️  Import failed: {e}")
        print("Will use fallback serialization")
        return False
    except Exception as e:
        print(f"❌ Import test FAILED: {e}")
        return False

if __name__ == "__main__":
    print("Redis Serialization Test")
    print("=" * 50)
    
    # Run tests
    test1 = test_fallback_serialization()
    test2 = test_with_html_content()
    test3 = test_actual_import()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Fallback serialization: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"HTML content serialization: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"Actual import test: {'✅ PASS' if test3 else '⚠️  FALLBACK'}")
    
    if test1 and test2:
        print("\n🎉 All critical tests passed! Serialization should work correctly.")
    else:
        print("\n⚠️  Some tests failed. Check the implementation.")