"""
持续监控配置 - 专门用于Redis队列的持续监控
"""

from dataclasses import dataclass
from typing import Optional


@dataclass
class ContinuousMonitorConfig:
    """持续监控配置"""
    # Redis连接配置
    connection_timeout: float = 10.0  # 连接超时
    socket_timeout: float = 20.0      # Socket超时
    
    # 队列监听配置
    queue_poll_timeout: int = 3       # 队列轮询超时（秒）
    max_consecutive_failures: int = 15 # 最大连续失败次数
    failure_backoff_base: float = 0.5  # 失败退避基础时间
    failure_backoff_max: float = 10.0  # 失败退避最大时间
    
    # 健康检查配置
    health_check_interval: float = 300.0  # 5分钟健康检查
    connection_pool_min: int = 2          # 最小连接池大小
    connection_pool_max: int = 10         # 最大连接池大小
    
    # 日志配置
    log_timeout_as_debug: bool = True     # 将超时日志记录为debug级别
    log_heartbeat_interval: float = 300.0 # 5分钟记录一次心跳日志
    
    # 重试配置
    max_retries_per_operation: int = 5    # 每个操作的最大重试次数
    retry_exponential_base: float = 1.5   # 重试指数退避基数


def get_continuous_monitor_config() -> ContinuousMonitorConfig:
    """获取持续监控配置"""
    return ContinuousMonitorConfig()


def get_optimized_pool_config():
    """获取针对持续监控优化的连接池配置"""
    from .optimized_redis_manager import PoolConfig
    
    config = get_continuous_monitor_config()
    
    return PoolConfig(
        min_connections=config.connection_pool_min,
        max_connections=config.connection_pool_max,
        connection_timeout=config.connection_timeout,
        socket_timeout=config.socket_timeout,
        idle_timeout=600.0,  # 10分钟空闲超时
        health_check_interval=config.health_check_interval,
        disconnect_timeout=5.0,
        retry_on_timeout=True,
        max_retries=config.max_retries_per_operation
    )