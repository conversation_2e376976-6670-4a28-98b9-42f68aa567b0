"""
Search Pipeline - Optimized parallel search execution with intelligent caching
搜索管道 - 优化的并行搜索执行和智能缓存系统
"""

import asyncio
import time
import json
import hashlib
import logging
from typing import Dict, List, Optional, Any, Tuple, Union, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

from .status_manager import UnifiedTaskStatus, UnifiedProgressTracker
from .config_manager import DiagnosisConfig, get_diagnosis_config
from .resource_manager import UnifiedResourceManager, get_resource_manager

def get_current_year() -> str:
    """获取当前年份"""
    return str(datetime.now().year)


class QueryType(Enum):
    """查询类型"""
    BASIC = "basic"
    INDUSTRY = "industry"
    COMPETITOR = "competitor"
    TREND = "trend"
    CUSTOM = "custom"


class SearchProvider(Enum):
    """搜索提供商"""
    PPLX = "pplx"
    SERPER = "serper"
    TAVILY = "tavily"
    CUSTOM = "custom"


class TimeRange(Enum):
    """时间范围枚举"""
    WEEK = "week"
    MONTH = "month"
    QUARTER = "quarter"
    YEAR = "year"
    TWO_YEARS = "two_years"


class TimeUtils:
    """时间工具类 - 为不同类型的查询提供合适的时间范围"""
    
    @staticmethod
    def get_time_range_for_query_type(query_type: QueryType, attempt: int = 0) -> TimeRange:
        """根据查询类型和重试次数获取合适的时间范围"""
        base_ranges = {
            QueryType.BASIC: [TimeRange.MONTH, TimeRange.QUARTER, TimeRange.YEAR],
            QueryType.INDUSTRY: [TimeRange.QUARTER, TimeRange.YEAR, TimeRange.TWO_YEARS],  # 包含营销案例，放宽为季度开始
            QueryType.COMPETITOR: [TimeRange.QUARTER, TimeRange.YEAR, TimeRange.TWO_YEARS],  # 竞争对手分析，放宽为季度开始
            QueryType.TREND: [TimeRange.QUARTER, TimeRange.YEAR, TimeRange.TWO_YEARS],
            QueryType.CUSTOM: [TimeRange.QUARTER, TimeRange.YEAR, TimeRange.TWO_YEARS]  # 自定义查询，放宽为季度开始
        }
        
        ranges = base_ranges.get(query_type, [TimeRange.MONTH, TimeRange.YEAR])
        # 根据重试次数扩展时间窗口
        range_index = min(attempt, len(ranges) - 1)
        return ranges[range_index]
    
    @staticmethod
    def get_tavily_time_range(time_range: TimeRange) -> str:
        """将TimeRange转换为Tavily API的时间范围参数"""
        mapping = {
            TimeRange.WEEK: "week",
            TimeRange.MONTH: "month",
            TimeRange.QUARTER: "month",  # Tavily没有quarter，使用month
            TimeRange.YEAR: "year",
            TimeRange.TWO_YEARS: "year"  # Tavily没有two_years，使用year
        }
        return mapping.get(time_range, "month")
    
    @staticmethod
    def get_pplx_time_range(time_range: TimeRange) -> str:
        """将TimeRange转换为PPLX API的时间范围参数"""
        mapping = {
            TimeRange.WEEK: "week",
            TimeRange.MONTH: "month",
            TimeRange.QUARTER: "month",  # PPLX没有quarter，使用month
            TimeRange.YEAR: "year",
            TimeRange.TWO_YEARS: "year"  # PPLX没有two_years，使用year
        }
        return mapping.get(time_range, "month")
    
    @staticmethod
    def get_time_aware_query_suffix(query_type: QueryType, time_range: TimeRange) -> str:
        """根据查询类型和时间范围生成查询后缀"""
        current_year = get_current_year()
        last_year = str(int(current_year) - 1)
        
        if time_range == TimeRange.WEEK:
            return "最近一周"
        elif time_range == TimeRange.MONTH:
            return f"{current_year}年最新"
        elif time_range == TimeRange.QUARTER:
            return f"{current_year}年"
        elif time_range == TimeRange.YEAR:
            if query_type in [QueryType.INDUSTRY, QueryType.TREND]:
                # 行业趋势查询包含去年数据，防止今年数据不足
                return f"{last_year}-{current_year}年"
            else:
                return f"{current_year}年"
        elif time_range == TimeRange.TWO_YEARS:
            return f"{str(int(current_year) - 1)}-{current_year}年"
        else:
            return f"{current_year}年"
    
    @staticmethod
    def should_retry_with_expanded_window(result_count: int, quality_score: float, 
                                        current_attempt: int, max_attempts: int = 3) -> bool:
        """判断是否应该扩展时间窗口重试"""
        if current_attempt >= max_attempts:
            return False
        
        # 重试条件：结果太少或质量太低
        return (result_count < 2) or (quality_score < 0.4)


@dataclass
class SearchQuery:
    """搜索查询对象"""
    query_id: str
    query_text: str
    query_type: QueryType
    provider: SearchProvider
    priority: int = 1  # 1-5, 5最高
    timeout: float = 120.0  # 增加默认超时到2分钟
    max_results: int = 10
    language: str = "zh"
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: float = field(default_factory=time.time)


@dataclass
class SearchResult:
    """搜索结果对象"""
    query_id: str
    provider: SearchProvider
    results: List[Dict[str, Any]] = field(default_factory=list)
    total_count: int = 0
    execution_time: float = 0.0
    cache_hit: bool = False
    quality_score: float = 0.0
    error_message: Optional[str] = None
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchBatch:
    """搜索批次"""
    batch_id: str
    queries: List[SearchQuery] = field(default_factory=list)
    concurrency_limit: int = 3
    timeout: float = 180.0
    retry_failed: bool = True
    max_retries: int = 2


class QueryGenerator:
    """查询生成器 - 负责生成优化的搜索查询"""
    
    def __init__(self, config: Optional[DiagnosisConfig] = None):
        self.config = config or get_diagnosis_config()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def generate_basic_queries(self, account_info: Dict[str, Any], 
                                   count: int = 3) -> List[SearchQuery]:
        """生成基础搜索查询 - 时间感知版本"""
        queries = []
        
        # 基于账号信息生成基础查询
        industry = account_info.get('industry', '社交媒体')
        desc = account_info.get('desc', '')
        
        # 为基础查询选择合适的时间范围
        time_range = TimeUtils.get_time_range_for_query_type(QueryType.BASIC, 0)
        time_suffix = TimeUtils.get_time_aware_query_suffix(QueryType.BASIC, time_range)
        
        # 根据不同行业生成更精确的搜索查询（不再硬编码年份）
        if industry == '美食':
            base_queries = [
                f"美食博主运营策略 小红书",
                "美食账号如何提高粉丝互动率",
                "美食探店内容创作技巧",
                "美食摄影 小红书 涨粉方法",
                "美食KOL 商业变现模式"
            ]
        elif industry in ['时尚', '美妆']:
            base_queries = [
                f"{industry} 博主运营策略",
                f"{industry} 账号内容创作趋势",
                f"{industry} KOL 商业合作模式"
            ]
        elif industry in ['科技', '数码']:
            base_queries = [
                f"{industry} 内容创作者发展趋势",
                f"{industry} 博主 粉丝增长策略",
                f"{industry} 行业 KOL 营销案例"
            ]
        else:
            # 通用查询
            base_queries = [
                f"{industry} 行业最新趋势",
                f"{industry} 内容创作者发展策略",
                f"{industry} 博主运营技巧"
            ]
        
        # 如果有账号描述，基于描述内容生成更精准的查询
        if desc and '上海' in desc:
            base_queries.insert(0, "上海本地美食博主 运营策略")
        if desc and '探店' in desc:
            base_queries.append("探店达人 内容创作 涨粉技巧")
        
        self.logger.info(f"为 {industry} 行业生成 {len(base_queries)} 个基础查询，时间范围: {time_range.value}")
        
        for i, base_query in enumerate(base_queries[:count]):
            # 将时间信息添加到查询中
            query_text = f"{base_query} {time_suffix}"
            
            query = SearchQuery(
                query_id=f"basic_{i+1}",
                query_text=query_text,
                query_type=QueryType.BASIC,
                provider=SearchProvider.TAVILY,  # 基础查询默认使用Tavily
                priority=3,
                timeout=60.0,
                metadata={'time_range': time_range.value, 'base_query': base_query}
            )
            queries.append(query)
            self.logger.info(f"生成查询 {i+1}: {query_text}")
        
        return queries
    
    async def generate_industry_queries(self, account_info: Dict[str, Any],
                                      search_results: Optional[List[SearchResult]] = None,
                                      count: int = 5) -> List[SearchQuery]:
        """生成行业专用查询 - 时间感知版本"""
        queries = []
        
        if 'industry' in account_info:
            industry = account_info['industry']
            
            # 为行业查询选择合适的时间范围（通常更长期）
            time_range = TimeUtils.get_time_range_for_query_type(QueryType.INDUSTRY, 0)
            time_suffix = TimeUtils.get_time_aware_query_suffix(QueryType.INDUSTRY, time_range)
            
            # 更深入的行业查询（不再硬编码年份）
            industry_base_queries = [
                f"{industry} 发展机会",
                f"{industry} 用户行为分析",
                f"{industry} 营销策略案例",
                f"{industry} 头部企业分析",
                f"{industry} 新兴技术应用"
            ]
            
            self.logger.info(f"为 {industry} 行业生成深度查询，时间范围: {time_range.value}")
            
            for i, base_query in enumerate(industry_base_queries[:count]):
                # 将时间信息添加到行业查询中
                query_text = f"{base_query} {time_suffix}"
                
                query = SearchQuery(
                    query_id=f"industry_{i+1}",
                    query_text=query_text,
                    query_type=QueryType.INDUSTRY,
                    provider=SearchProvider.PPLX,  # 行业查询使用PPLX获得更深度的分析
                    priority=4,
                    timeout=150.0,  # 使用配置中的PPLX超时时间
                    max_results=15,
                    metadata={'time_range': time_range.value, 'base_query': base_query}
                )
                queries.append(query)
                self.logger.info(f"生成行业查询 {i+1}: {query_text}")
        
        return queries
    
    async def generate_competitor_queries(self, account_info: Dict[str, Any],
                                        competitors: Optional[List[str]] = None,
                                        count: int = 4) -> List[SearchQuery]:
        """生成竞争对手分析查询 - 时间感知版本"""
        queries = []
        
        if competitors:
            # 为竞争对手查询选择合适的时间范围（通常需要较新的信息）
            time_range = TimeUtils.get_time_range_for_query_type(QueryType.COMPETITOR, 0)
            time_suffix = TimeUtils.get_time_aware_query_suffix(QueryType.COMPETITOR, time_range)
            
            self.logger.info(f"生成竞争对手查询，时间范围: {time_range.value}")
            
            for i, competitor in enumerate(competitors[:count]):
                # 将时间信息添加到竞争对手查询中
                base_query = f"{competitor} 营销策略分析 动态"
                query_text = f"{base_query} {time_suffix}"
                
                query = SearchQuery(
                    query_id=f"competitor_{i+1}",
                    query_text=query_text,
                    query_type=QueryType.COMPETITOR,
                    provider=SearchProvider.TAVILY,  # 竞争对手信息使用Tavily快速搜索
                    priority=3,
                    timeout=75.0,
                    metadata={'time_range': time_range.value, 'competitor': competitor, 'base_query': base_query}
                )
                queries.append(query)
                self.logger.info(f"生成竞争对手查询 {i+1}: {query_text}")
        
        return queries
    
    async def optimize_queries(self, queries: List[SearchQuery], progress_tracker=None) -> List[SearchQuery]:
        """优化查询列表 - 去重、排序、合并"""
        
        if progress_tracker:
            progress_tracker.update_status(
                UnifiedTaskStatus.OPTIMIZING_QUERIES, 
                f"🔄 正在分析 {len(queries)} 个原始查询...",
                step_progress=30.0
            )
        
        # 去重处理
        unique_queries = {}
        for i, query in enumerate(queries):
            # 使用查询文本的哈希作为去重键
            query_hash = hashlib.md5(query.query_text.encode()).hexdigest()
            if query_hash not in unique_queries or query.priority > unique_queries[query_hash].priority:
                unique_queries[query_hash] = query
            
            # 中间进度更新
            if progress_tracker and (i + 1) % max(1, len(queries) // 3) == 0:
                progress = 30.0 + ((i + 1) / len(queries)) * 40.0
                progress_tracker.update_status(
                    UnifiedTaskStatus.OPTIMIZING_QUERIES, 
                    f"📋 去重处理: {len(unique_queries)} 个独特查询 ({i+1}/{len(queries)})",
                    step_progress=progress
                )
        
        if progress_tracker:
            progress_tracker.update_status(
                UnifiedTaskStatus.OPTIMIZING_QUERIES, 
                f"🎯 查询去重完成，从 {len(queries)} 个缩减至 {len(unique_queries)} 个独特查询",
                step_progress=70.0
            )
        
        # 按优先级排序
        optimized = sorted(unique_queries.values(), key=lambda q: q.priority, reverse=True)
        
        if progress_tracker:
            progress_tracker.update_status(
                UnifiedTaskStatus.OPTIMIZING_QUERIES, 
                f"📊 查询排序完成，按优先级重新安排执行顺序",
                step_progress=85.0
            )
        
        self.logger.info(f"Optimized {len(queries)} queries to {len(optimized)} unique queries")
        return optimized


class SearchCache:
    """搜索缓存管理器"""
    
    def __init__(self, ttl: int = 3600, max_size: int = 1000):
        self.ttl = ttl  # 缓存生存时间(秒)
        self.max_size = max_size
        self._cache: Dict[str, Tuple[SearchResult, float]] = {}
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def _generate_cache_key(self, query: SearchQuery) -> str:
        """生成缓存键"""
        key_data = {
            'query_text': query.query_text,
            'provider': query.provider.value,
            'language': query.language,
            'max_results': query.max_results
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_str.encode()).hexdigest()
    
    async def get(self, query: SearchQuery) -> Optional[SearchResult]:
        """从缓存获取搜索结果"""
        cache_key = self._generate_cache_key(query)
        
        if cache_key in self._cache:
            result, timestamp = self._cache[cache_key]
            
            # 检查是否过期
            if time.time() - timestamp < self.ttl:
                result.cache_hit = True
                query_text_safe = str(query.query_text) if query.query_text else ""
                self.logger.debug(f"Cache hit for query: {query_text_safe[:50]}")
                return result
            else:
                # 清理过期缓存
                del self._cache[cache_key]
        
        return None
    
    async def set(self, query: SearchQuery, result: SearchResult) -> None:
        """设置缓存"""
        cache_key = self._generate_cache_key(query)
        
        # 如果缓存满了，清理最旧的条目
        if len(self._cache) >= self.max_size:
            oldest_key = min(self._cache.keys(), 
                           key=lambda k: self._cache[k][1])
            del self._cache[oldest_key]
        
        self._cache[cache_key] = (result, time.time())
        query_text_safe = str(query.query_text) if query.query_text else ""
        self.logger.debug(f"Cached result for query: {query_text_safe[:50]}")
    
    async def clear_expired(self) -> None:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = [
            key for key, (_, timestamp) in self._cache.items()
            if current_time - timestamp >= self.ttl
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            self.logger.info(f"Cleared {len(expired_keys)} expired cache entries")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return {
            'total_entries': len(self._cache),
            'max_size': self.max_size,
            'ttl': self.ttl,
            'memory_usage_estimate': len(self._cache) * 1024  # 粗略估计
        }


class SearchExecutor:
    """搜索执行器 - 负责实际执行搜索任务"""
    
    def __init__(self, config: Optional[DiagnosisConfig] = None,
                 resource_manager: Optional[UnifiedResourceManager] = None):
        self.config = config or get_diagnosis_config()
        self.resource_manager = resource_manager
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    async def execute_search(self, query: SearchQuery) -> SearchResult:
        """执行单个搜索查询"""
        start_time = time.time()
        
        try:
            if query.provider == SearchProvider.TAVILY:
                results = await self._execute_tavily_search(query)
            elif query.provider == SearchProvider.PPLX:
                results = await self._execute_pplx_search(query)
            elif query.provider == SearchProvider.SERPER:
                results = await self._execute_serper_search(query)
            else:
                results = await self._execute_custom_search(query)
            
            execution_time = time.time() - start_time
            
            # 计算质量评分
            quality_score = self._calculate_quality_score(query, results)
            
            return SearchResult(
                query_id=query.query_id,
                provider=query.provider,
                results=results,
                total_count=len(results),
                execution_time=execution_time,
                quality_score=quality_score,
                cache_hit=False
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Search execution failed for query {query.query_id}: {e}")
            
            return SearchResult(
                query_id=query.query_id,
                provider=query.provider,
                execution_time=execution_time,
                error_message=str(e)
            )
    
    async def _execute_pplx_search(self, query: SearchQuery) -> List[Dict[str, Any]]:
        """执行PPLX搜索"""
        return await self._execute_pplx_search_with_time_range(query, "month")
    
    async def _execute_pplx_search_with_time_range(self, query: SearchQuery, time_range: str) -> List[Dict[str, Any]]:
        """执行带时间范围的PPLX搜索"""
        try:
            # 导入真实的AI调用模块
            from task import callWattGPT
            
            # 构建PPLX搜索请求 - 使用最新的sonar模型
            pplx_request = {
                "model": "sonar",
                "messages": [
                    {"role": "user", "content": query.query_text}
                ],
                "search_recency_filter": time_range,
                "max_tokens": 2048,
                "temperature": 0.1
            }
            
            query_text_safe = str(query.query_text) if query.query_text else ""
            self.logger.info(f"执行PPLX搜索 ({time_range}): {query_text_safe[:50]}...")
            
            # 调用PPLX搜索
            status, code, response = callWattGPT.callPplxChannelChatCompletions(
                pplx_request, timeout=int(query.timeout)
            )
            
            if not status:
                self.logger.error(f"PPLX搜索失败: {response}")
                return []
            
            # 解析PPLX响应
            try:
                if isinstance(response, dict) and 'result' in response:
                    result_data = response['result']
                    if isinstance(result_data, dict) and 'data' in result_data:
                        data = result_data['data']
                        if isinstance(data, dict) and 'choices' in data:
                            choices = data['choices']
                            if isinstance(choices, list) and len(choices) > 0:
                                choice = choices[0]
                                if isinstance(choice, dict) and 'message' in choice:
                                    message = choice['message']
                                    if isinstance(message, dict) and 'content' in message:
                                        content = message['content']
                                        
                                        # 将PPLX响应转换为标准搜索结果格式
                                        return [
                                            {
                                                'title': f'PPLX搜索结果: {query.query_text}',
                                                'url': 'https://pplx.ai/search',
                                                'snippet': content[:500],  # 限制长度
                                                'content': content,
                                                'relevance_score': 0.9,
                                                'source': 'pplx',
                                                'timestamp': time.time(),
                                                'search_time_range': time_range
                                            }
                                        ]
                
                self.logger.warning("PPLX响应格式异常，返回空结果")
                return []
                
            except Exception as e:
                self.logger.error(f"解析PPLX响应失败: {e}")
                return []
                
        except Exception as e:
            self.logger.error(f"PPLX搜索异常: {e}")
            return []
    
    async def _execute_tavily_search(self, query: SearchQuery) -> List[Dict[str, Any]]:
        """执行Tavily搜索 - 快速、低成本的初筛搜索"""
        return await self._execute_tavily_search_with_time_range(query, "month")
    
    async def _execute_tavily_search_with_time_range(self, query: SearchQuery, time_range: str) -> List[Dict[str, Any]]:
        """执行带时间范围的Tavily搜索"""
        try:
            import requests
            import random
            from task import TAVILY_API_KEY
            
            query_text_safe = str(query.query_text) if query.query_text else ""
            self.logger.info(f"执行Tavily搜索 ({time_range}): {query_text_safe[:50]}...")
            
            url = "https://api.tavily.com/search"
            api_key = random.choice(TAVILY_API_KEY)
            headers = {
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
            
            body = {
                "query": query.query_text,
                "search_depth": "advanced",
                "max_results": min(query.max_results, 8),  # 限制结果数量
                "time_range": time_range,
                "include_raw_content": True
            }
            
            response = requests.post(url, headers=headers, json=body, timeout=int(query.timeout))
            
            if response.status_code != 200:
                self.logger.error(f"Tavily搜索失败: {response.status_code}, 响应: {response.text}")
                return []
            
            # 添加响应验证
            try:
                response_data = response.json()
            except Exception as e:
                self.logger.error(f"Tavily响应JSON解析失败: {e}")
                return []
            
            # 验证响应数据结构
            if not isinstance(response_data, dict):
                self.logger.error(f"Tavily响应数据格式异常: 期望dict，得到{type(response_data)}")
                return []
            
            # 检查响应中是否包含results字段
            if 'results' not in response_data:
                self.logger.error(f"Tavily响应中缺少results字段。响应键: {list(response_data.keys())}")
                return []
            
            tavily_results = response_data.get('results', [])
            if not isinstance(tavily_results, list):
                self.logger.error(f"Tavily results字段格式异常: 期望list，得到{type(tavily_results)}")
                return []
            
            results = []
            
            for i, res in enumerate(tavily_results):
                # 确保res是字典类型
                if not isinstance(res, dict):
                    self.logger.warning(f"跳过非字典类型的结果项: {type(res)}")
                    continue
                
                # 过滤低质量结果
                if res.get('score', 0) < 0.1:
                    continue
                
                # 安全获取和处理内容字段，避免NoneType切片错误
                snippet_content = res.get('content', '') or ''
                raw_content = res.get('raw_content', '') or ''
                content_fallback = res.get('content', '') or ''
                
                current_res = {
                    'title': res.get('title', '') or '',
                    'url': res.get('url', '') or '',
                    'snippet': str(snippet_content)[:300],  # 安全切片
                    'content': str(raw_content or content_fallback)[:1000],  # 安全切片
                    'relevance_score': res.get('score', 0.5),
                    'source': 'tavily',
                    'timestamp': time.time(),
                    'search_time_range': time_range
                }
                
                # 确保必要字段存在且非空
                if current_res['title'] and current_res['url'] and current_res['content']:
                    results.append(current_res)
                    # 每找到一个结果就记录日志，让用户知道搜索在进行中
                    self.logger.info(f"找到Tavily结果 {i+1}: {current_res['title'][:30]}...")
                else:
                    self.logger.debug(f"跳过不完整的结果: title={bool(current_res['title'])}, url={bool(current_res['url'])}, content={bool(current_res['content'])}")
            
            self.logger.info(f"Tavily搜索完成 ({time_range}): {len(results)} 个有效结果")
            return results
            
        except Exception as e:
            self.logger.error(f"Tavily搜索异常: {e}")
            return []
    
    async def _execute_serper_search(self, query: SearchQuery) -> List[Dict[str, Any]]:
        """执行Serper搜索 - 使用Tavily作为备用"""
        try:
            # 使用Tavily作为Serper的备用实现
            query_text_safe = str(query.query_text) if query.query_text else ""
            self.logger.info(f"使用Tavily作为Serper备用: {query_text_safe[:50]}...")
            
            # 创建一个Tavily查询作为备用
            tavily_query = SearchQuery(
                query_id=query.query_id,
                query_text=query.query_text,
                query_type=query.query_type,
                provider=SearchProvider.TAVILY,
                timeout=query.timeout,
                max_results=query.max_results
            )
            
            results = await self._execute_tavily_search(tavily_query)
            
            # 修改source标识
            for result in results:
                result['source'] = 'serper_fallback'
            
            return results
            
        except Exception as e:
            self.logger.error(f"Serper搜索异常: {e}")
            return []
    
    async def execute_hybrid_search_with_retry(self, query: SearchQuery, max_attempts: int = 3) -> SearchResult:
        """执行带自适应重试的混合搜索策略"""
        start_time = time.time()
        all_attempts = []
        
        for attempt in range(max_attempts):
            try:
                # 根据重试次数调整时间范围
                time_range = TimeUtils.get_time_range_for_query_type(query.query_type, attempt)
                time_suffix = TimeUtils.get_time_aware_query_suffix(query.query_type, time_range)
                
                # 调整查询文本包含时间信息
                adjusted_query_text = f"{query.query_text} {time_suffix}"
                
                self.logger.info(f"混合搜索尝试 {attempt + 1}/{max_attempts}: {time_range.value} - {adjusted_query_text[:50]}...")
                
                # 执行一次混合搜索
                result = await self._execute_single_hybrid_search(query, adjusted_query_text, time_range, attempt)
                all_attempts.append(result)
                
                # 检查是否需要重试
                should_retry = TimeUtils.should_retry_with_expanded_window(
                    len(result.results), result.quality_score, attempt, max_attempts
                )
                
                if not should_retry:
                    self.logger.info(f"搜索成功，结果满足要求: {len(result.results)} 个结果, 质量: {result.quality_score:.2f}")
                    
                    # 合并所有尝试的结果
                    final_result = self._merge_search_attempts(all_attempts, query)
                    final_result.execution_time = time.time() - start_time
                    return final_result
                else:
                    self.logger.info(f"第 {attempt + 1} 次搜索结果不满足要求，扩展时间窗口重试")
            
            except Exception as e:
                self.logger.error(f"混合搜索第 {attempt + 1} 次尝试失败: {e}")
                if attempt == max_attempts - 1:
                    # 最后一次尝试失败，返回错误结果
                    return SearchResult(
                        query_id=query.query_id,
                        provider=SearchProvider.TAVILY,
                        execution_time=time.time() - start_time,
                        error_message=str(e)
                    )
        
        # 如果所有尝试都完成，合并结果
        final_result = self._merge_search_attempts(all_attempts, query)
        final_result.execution_time = time.time() - start_time
        return final_result
    
    async def _execute_single_hybrid_search(self, query: SearchQuery, adjusted_query_text: str, 
                                          time_range: TimeRange, attempt: int) -> SearchResult:
        """执行单次混合搜索"""
        start_time = time.time()
        
        try:
            # 第一阶段：Tavily快速搜索
            tavily_query = SearchQuery(
                query_id=f"{query.query_id}_tavily_attempt_{attempt}",
                query_text=adjusted_query_text,
                query_type=query.query_type,
                provider=SearchProvider.TAVILY,
                timeout=min(query.timeout * 0.3, 45),  # 给Tavily更多时间，并且为PPLX留出更多时间
                max_results=query.max_results
            )
            
            # 设置Tavily时间范围
            tavily_time_range = TimeUtils.get_tavily_time_range(time_range)
            tavily_results = await self._execute_tavily_search_with_time_range(tavily_query, tavily_time_range)
            tavily_quality = self._calculate_quality_score(tavily_query, tavily_results)
            
            self.logger.info(f"Tavily初筛 (尝试{attempt+1}): {len(tavily_results)} 个结果, 质量评分: {tavily_quality:.2f}")
            
            # 第二阶段：根据质量决定是否需要PPLX增强
            final_results = tavily_results
            
            # 质量阈值判断
            needs_enhancement = (
                tavily_quality < 0.6 or 
                len(tavily_results) < 3 or
                query.priority >= 4
            )
            
            if needs_enhancement:
                self.logger.info(f"启动PPLX增强搜索 (尝试{attempt+1})")
                
                pplx_query = SearchQuery(
                    query_id=f"{query.query_id}_pplx_attempt_{attempt}",
                    query_text=adjusted_query_text,
                    query_type=query.query_type,
                    provider=SearchProvider.PPLX,
                    timeout=max(query.timeout, 150.0),  # 使用更长的超时时间，至少150秒
                    max_results=min(query.max_results, 5)
                )
                
                # 设置PPLX时间范围
                pplx_time_range = TimeUtils.get_pplx_time_range(time_range)
                pplx_results = await self._execute_pplx_search_with_time_range(pplx_query, pplx_time_range)
                
                # 合并结果，PPLX结果优先
                final_results = pplx_results + tavily_results
                self.logger.info(f"PPLX增强完成 (尝试{attempt+1}): +{len(pplx_results)} 个结果")
            
            execution_time = time.time() - start_time
            quality_score = self._calculate_quality_score(query, final_results)
            
            return SearchResult(
                query_id=f"{query.query_id}_attempt_{attempt}",
                provider=SearchProvider.TAVILY,
                results=final_results,
                total_count=len(final_results),
                execution_time=execution_time,
                quality_score=quality_score,
                cache_hit=False,
                metadata={
                    'attempt': attempt,
                    'time_range': time_range.value,
                    'adjusted_query': adjusted_query_text,
                    'tavily_results': len(tavily_results),
                    'pplx_enhanced': needs_enhancement,
                    'tavily_quality': tavily_quality
                }
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"单次混合搜索失败 (尝试{attempt+1}): {e}")
            return SearchResult(
                query_id=f"{query.query_id}_attempt_{attempt}",
                provider=SearchProvider.TAVILY,
                execution_time=execution_time,
                error_message=str(e),
                metadata={'attempt': attempt, 'time_range': time_range.value}
            )
    
    def _merge_search_attempts(self, attempts: List[SearchResult], original_query: SearchQuery) -> SearchResult:
        """合并多次搜索尝试的结果"""
        if not attempts:
            return SearchResult(
                query_id=original_query.query_id,
                provider=SearchProvider.TAVILY,
                error_message="No search attempts completed"
            )
        
        # 合并所有成功的结果
        all_results = []
        total_execution_time = 0.0
        quality_scores = []
        metadata_summary = {'attempts': len(attempts), 'time_ranges_tried': []}
        
        for attempt in attempts:
            if attempt.results:
                all_results.extend(attempt.results)
                quality_scores.append(attempt.quality_score)
            total_execution_time += attempt.execution_time
            
            if 'time_range' in attempt.metadata:
                metadata_summary['time_ranges_tried'].append(attempt.metadata['time_range'])
        
        # 去重处理
        unique_results = self._deduplicate_results_simple(all_results)
        
        # 计算综合质量评分
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        
        return SearchResult(
            query_id=original_query.query_id,
            provider=SearchProvider.TAVILY,
            results=unique_results,
            total_count=len(unique_results),
            execution_time=total_execution_time,
            quality_score=avg_quality,
            cache_hit=False,
            metadata=metadata_summary
        )
    
    def _deduplicate_results_simple(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """简单的结果去重"""
        seen_urls = set()
        seen_content = set()
        unique_results = []
        
        for result in results:
            url = result.get('url', '')
            content = result.get('content', '')[:200]  # 使用前200字符作为去重依据
            
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
            elif not url and content and content not in seen_content:
                seen_content.add(content)
                unique_results.append(result)
        
        return unique_results

    async def execute_hybrid_search(self, query: SearchQuery) -> SearchResult:
        """执行混合搜索策略: Tavily初筛 + PPLX质量提升 + 自适应重试"""
        return await self.execute_hybrid_search_with_retry(query)
    
    async def _execute_custom_search(self, query: SearchQuery) -> List[Dict[str, Any]]:
        """执行自定义搜索 - 使用基础PPLX搜索"""
        try:
            query_text_safe = str(query.query_text) if query.query_text else ""
            self.logger.info(f"执行自定义搜索: {query_text_safe[:50]}...")
            
            # 创建PPLX查询
            pplx_query = SearchQuery(
                query_id=query.query_id,
                query_text=query.query_text,
                query_type=query.query_type,
                provider=SearchProvider.PPLX,
                timeout=query.timeout,
                max_results=min(query.max_results, 3)  # 限制结果数量
            )
            
            results = await self._execute_pplx_search(pplx_query)
            
            # 修改source标识
            for result in results:
                result['source'] = 'custom'
            
            return results
            
        except Exception as e:
            self.logger.error(f"自定义搜索异常: {e}")
            return []
    
    def _calculate_quality_score(self, query: SearchQuery, results: List[Dict[str, Any]]) -> float:
        """计算搜索结果质量评分"""
        if not results:
            return 0.0
        
        # 基于结果数量和相关性计算质量评分
        result_count_score = min(len(results) / query.max_results, 1.0)
        
        # 计算平均相关性评分
        relevance_scores = [r.get('relevance_score', 0.5) for r in results]
        avg_relevance = sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0.0
        
        # 综合评分
        quality_score = (result_count_score * 0.3 + avg_relevance * 0.7)
        return min(quality_score, 1.0)


class SearchPipeline:
    """
    搜索管道主类 - 统一管理查询生成、优化、执行和结果处理
    
    特性：
    - 并发控制的查询执行
    - 智能缓存机制
    - 查询优化和去重
    - 结果质量评估
    - 错误处理和重试
    """
    
    def __init__(self, config: Optional[DiagnosisConfig] = None,
                 resource_manager: Optional[UnifiedResourceManager] = None,
                 progress_tracker: Optional[UnifiedProgressTracker] = None):
        self.config = config or get_diagnosis_config()
        self.resource_manager = resource_manager
        self.progress_tracker = progress_tracker
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # 初始化组件
        self.query_generator = QueryGenerator(config)
        self.search_cache = SearchCache(
            ttl=self.config.cache.search_cache_ttl,
            max_size=self.config.cache.max_cache_size
        )
        self.search_executor = SearchExecutor(config, resource_manager)
        
        # 并发控制
        self._search_semaphore = asyncio.Semaphore(
            self.config.concurrency.max_concurrent_searches
        )
        
        # 性能统计
        self._performance_stats = {
            'total_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'failed_queries': 0,
            'total_execution_time': 0.0,
            'average_quality_score': 0.0
        }
    
    async def execute_search_pipeline(self, account_info: Dict[str, Any],
                                    mode: str = "basic",
                                    custom_queries: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        执行完整搜索管道
        
        Args:
            account_info: 账号信息
            mode: 搜索模式 ("basic" 或 "deep")
            custom_queries: 自定义查询列表
            
        Returns:
            搜索结果汇总
        """
        pipeline_start = time.time()
        
        try:
            self.logger.info(f"Starting search pipeline in {mode} mode")
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.GENERATING_QUERIES, "开始生成智能搜索查询...", step_progress=20.0)
            
            # 第一阶段：生成查询
            queries = await self._generate_all_queries(account_info, mode, custom_queries)
            # _generate_all_queries 已经在内部更新了进度，不需要重复更新
            
            # 第二阶段：优化查询 - 从查询生成的结束进度继续
            optimized_queries = await self.query_generator.optimize_queries(queries, self.progress_tracker)
            
            # 立即通知搜索开始
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.EXECUTING_SEARCHES, f"🚀 启动并行搜索引擎，执行 {len(optimized_queries)} 个查询...", step_progress=98.0)
            
            # 第三阶段：执行搜索
            search_results = await self._execute_batch_search(optimized_queries, mode)
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.PROCESSING_RESULTS, "📊 开始汇总和分析搜索结果...", step_progress=70.0)
            
            # 第四阶段：处理结果
            processed_results = await self._process_search_results(search_results)
            
            if self.progress_tracker:
                self.progress_tracker.update_status(UnifiedTaskStatus.PROCESSING_RESULTS, f"✅ 结果处理完成，获得 {processed_results.get('total_count', 0)} 个高质量结果", step_progress=85.0)
            
            pipeline_duration = time.time() - pipeline_start
            self._performance_stats['total_execution_time'] = pipeline_duration
            
            self.logger.info(f"Search pipeline completed in {pipeline_duration:.2f}s")
            
            return {
                'results': processed_results,
                'queries_executed': len(optimized_queries),
                'total_results': sum(len(r.results) for r in search_results),
                'execution_time': pipeline_duration,
                'performance_stats': self._performance_stats.copy(),
                'cache_stats': self.search_cache.get_stats()
            }
            
        except Exception as e:
            self.logger.error(f"Search pipeline failed: {e}")
            raise
    
    async def _generate_all_queries(self, account_info: Dict[str, Any],
                                  mode: str, custom_queries: Optional[List[str]] = None) -> List[SearchQuery]:
        """生成所有搜索查询"""
        all_queries = []
        
        # 第一步：基础查询生成
        if self.progress_tracker:
            self.progress_tracker.update_status(
                UnifiedTaskStatus.GENERATING_QUERIES, 
                "🔄 正在生成基础行业查询...",
                step_progress=15.0
            )
        
        basic_queries = await self.query_generator.generate_basic_queries(account_info, 3)
        all_queries.extend(basic_queries)
        
        if self.progress_tracker:
            self.progress_tracker.update_status(
                UnifiedTaskStatus.GENERATING_QUERIES, 
                f"✓ 基础查询完成 ({len(basic_queries)} 个) - 正在生成行业深度查询...",
                step_progress=45.0
            )
        
        # 第二步：深度模式查询生成
        if mode == "deep":
            if self.progress_tracker:
                self.progress_tracker.update_status(
                    UnifiedTaskStatus.GENERATING_QUERIES, 
                    f"🎯 深度模式：生成 {account_info.get('industry', '目标行业')} 专业查询...",
                    step_progress=65.0
                )
            
            industry_queries = await self.query_generator.generate_industry_queries(account_info, count=5)
            all_queries.extend(industry_queries)
            
            if self.progress_tracker:
                self.progress_tracker.update_status(
                    UnifiedTaskStatus.GENERATING_QUERIES, 
                    f"✓ 行业查询完成 ({len(industry_queries)} 个) - 正在分析竞争对手...",
                    step_progress=80.0
                )
            
            # 竞争对手查询
            competitors = account_info.get('competitors', [])
            if competitors:
                if self.progress_tracker:
                    self.progress_tracker.update_status(
                        UnifiedTaskStatus.GENERATING_QUERIES, 
                        f"🔍 竞争对手分析：正在为 {len(competitors)} 个竞品生成查询...",
                        step_progress=90.0
                    )
                
                competitor_queries = await self.query_generator.generate_competitor_queries(
                    account_info, competitors=competitors, count=3
                )
                all_queries.extend(competitor_queries)
        
        # 第三步：添加自定义查询
        if custom_queries:
            if self.progress_tracker:
                self.progress_tracker.update_status(
                    UnifiedTaskStatus.GENERATING_QUERIES, 
                    f"➕ 添加 {len(custom_queries)} 个自定义查询...",
                    step_progress=95.0
                )
            
            for i, custom_query in enumerate(custom_queries):
                query = SearchQuery(
                    query_id=f"custom_{i+1}",
                    query_text=custom_query,
                    query_type=QueryType.CUSTOM,
                    provider=SearchProvider.PPLX,
                    priority=2
                )
                all_queries.append(query)
        
        self.logger.info(f"Generated {len(all_queries)} total queries")
        return all_queries
    
    async def _execute_batch_search(self, queries: List[SearchQuery], mode: str = "basic") -> List[SearchResult]:
        """批量执行搜索查询"""
        search_results = []
        completed_count = 0
        
        # 使用信号量控制并发
        async def execute_with_semaphore(query: SearchQuery, query_index: int) -> SearchResult:
            nonlocal completed_count
            
            async with self._search_semaphore:
                # 立即通知开始执行特定查询
                if self.progress_tracker:
                    # 提供更有意义的查询描述
                    query_desc = query.query_text[:50] + "..." if len(query.query_text) > 50 else query.query_text
                    self.progress_tracker.update_search_progress(
                        query.provider.value, f"开始搜索: {query_desc}", 0, False
                    )
                
                # 先检查缓存
                cached_result = await self.search_cache.get(query)
                if cached_result:
                    self._performance_stats['cache_hits'] += 1
                    if self.progress_tracker:
                        query_desc = query.query_text[:30] + "..." if len(query.query_text) > 30 else query.query_text
                        self.progress_tracker.update_search_progress(
                            query.provider.value, f"💾 缓存命中: {query_desc}", 
                            len(cached_result.results), True
                        )
                    
                    # 更新整体搜索进度（即使是缓存命中）
                    completed_count += 1
                    if self.progress_tracker:
                        # 不再重复发送相同的45%进度，而是动态计算搜索子进度
                        if completed_count <= len(queries):
                            # 计算在EXECUTING_SEARCHES阶段内的子进度 (0% 到 100%)
                            search_sub_progress = (completed_count / len(queries)) * 100
                            self.progress_tracker.update_status(
                                UnifiedTaskStatus.EXECUTING_SEARCHES, 
                                f"搜索执行: {completed_count}/{len(queries)}",
                                step_progress=search_sub_progress
                            )
                    
                    return cached_result
                
                # 执行混合搜索策略
                self._performance_stats['cache_misses'] += 1
                
                # 根据查询优先级和模式选择搜索策略
                if query.priority >= 3 or mode == "deep":
                    # 高优先级或深度模式使用混合搜索
                    search_result = await self.search_executor.execute_hybrid_search(query)
                else:
                    # 低优先级使用单一搜索（默认Tavily快速搜索）
                    if query.provider == SearchProvider.PPLX:
                        # 如果明确指定PPLX，则使用PPLX
                        search_result = await self.search_executor.execute_search(query)
                    else:
                        # 否则优先使用Tavily快速搜索
                        tavily_query = SearchQuery(
                            query_id=query.query_id,
                            query_text=query.query_text,
                            query_type=query.query_type,
                            provider=SearchProvider.TAVILY,
                            priority=query.priority,
                            timeout=query.timeout,
                            max_results=query.max_results
                        )
                        search_result = await self.search_executor.execute_search(tavily_query)
                
                # 缓存结果（如果成功）
                if search_result.results and not search_result.error_message:
                    await self.search_cache.set(query, search_result)
                    if self.progress_tracker:
                        query_desc = query.query_text[:30] + "..." if len(query.query_text) > 30 else query.query_text
                        self.progress_tracker.update_search_progress(
                            query.provider.value, f"✅ {query_desc}", 
                            len(search_result.results), True
                        )
                else:
                    self._performance_stats['failed_queries'] += 1
                    if self.progress_tracker:
                        query_desc = query.query_text[:30] + "..." if len(query.query_text) > 30 else query.query_text
                        self.progress_tracker.update_search_progress(
                            query.provider.value, f"❌ {query_desc}", 0, True
                        )
                
                # 更新整体搜索进度
                completed_count += 1
                if self.progress_tracker:
                    # 不再重复发送相同的45%进度，而是动态计算搜索子进度
                    if completed_count <= len(queries):
                        # 计算在EXECUTING_SEARCHES阶段内的子进度 (0% 到 100%)
                        search_sub_progress = (completed_count / len(queries)) * 100
                        self.progress_tracker.update_status(
                            UnifiedTaskStatus.EXECUTING_SEARCHES, 
                            f"搜索执行: {completed_count}/{len(queries)}",
                            step_progress=search_sub_progress
                        )
                
                return search_result
        
        # 创建搜索任务
        search_tasks = [execute_with_semaphore(query, i) for i, query in enumerate(queries)]
        
        # 执行搜索并收集结果
        self._performance_stats['total_queries'] = len(queries)
        
        # 并发执行所有搜索任务
        search_results_raw = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        # 过滤掉异常，只保留SearchResult对象
        search_results = [r for r in search_results_raw if isinstance(r, SearchResult)]
        
        # 记录异常的数量
        exceptions_count = len(search_results_raw) - len(search_results)
        if exceptions_count > 0:
            self.logger.warning(f"Filtered out {exceptions_count} failed search tasks")
        
        return search_results
    
    async def _process_search_results(self, search_results: List[SearchResult]) -> Dict[str, Any]:
        """处理和汇总搜索结果"""
        # 按查询类型分组结果
        results_by_type = {}
        total_results = []
        quality_scores = []
        
        for result in search_results:
            if result.results:
                total_results.extend(result.results)
                quality_scores.append(result.quality_score)
                
                # 根据查询ID推断类型
                query_type = self._infer_query_type(result.query_id)
                if query_type not in results_by_type:
                    results_by_type[query_type] = []
                results_by_type[query_type].extend(result.results)
        
        # 计算平均质量评分
        if quality_scores:
            self._performance_stats['average_quality_score'] = sum(quality_scores) / len(quality_scores)
        
        # 去重和排序
        unique_results = self._deduplicate_results(total_results)
        sorted_results = sorted(
            unique_results, 
            key=lambda x: x.get('relevance_score', 0.0), 
            reverse=True
        )
        
        return {
            'all_results': sorted_results[:50],  # 限制结果数量
            'results_by_type': results_by_type,
            'total_count': len(sorted_results),
            'average_quality': self._performance_stats['average_quality_score']
        }
    
    def _infer_query_type(self, query_id: str) -> str:
        """从查询ID推断查询类型"""
        if query_id.startswith('basic_'):
            return 'basic'
        elif query_id.startswith('industry_'):
            return 'industry'
        elif query_id.startswith('competitor_'):
            return 'competitor'
        elif query_id.startswith('custom_'):
            return 'custom'
        else:
            return 'unknown'
    
    def _deduplicate_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重搜索结果 - 基于内容相似度而非URL"""
        import hashlib
        
        seen_content_hashes = set()
        seen_urls = set()
        unique_results = []
        
        for result in results:
            # 生成内容哈希用于去重
            content = result.get('snippet', result.get('content', ''))
            title = result.get('title', '')
            url = result.get('url', '')
            
            # 组合标题和内容的前200个字符作为去重依据
            dedup_content = f"{title[:100]}{content[:200]}".strip()
            content_hash = hashlib.md5(dedup_content.encode('utf-8')).hexdigest()
            
            # 多重去重策略：
            # 1. 如果有有效URL且不是通用PPLX URL，优先使用URL去重
            # 2. 否则使用内容哈希去重
            
            is_generic_url = url in ['https://pplx.ai/search', ''] or not url
            
            if not is_generic_url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
                self.logger.debug(f"保留结果(URL去重): {title[:50]}...")
            elif is_generic_url and content_hash not in seen_content_hashes and len(dedup_content) > 20:
                seen_content_hashes.add(content_hash)
                unique_results.append(result)
                self.logger.debug(f"保留结果(内容去重): {title[:50]}...")
            else:
                self.logger.debug(f"去重过滤: {title[:50]}...")
        
        self.logger.info(f"去重前: {len(results)} 个结果，去重后: {len(unique_results)} 个结果")
        return unique_results
    
    async def cleanup(self) -> None:
        """清理资源"""
        await self.search_cache.clear_expired()
        self.logger.info("Search pipeline cleanup completed")
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        return self._performance_stats.copy()


# 便捷函数
async def create_search_pipeline(config: Optional[DiagnosisConfig] = None) -> SearchPipeline:
    """创建搜索管道实例"""
    resource_manager = await get_resource_manager()
    return SearchPipeline(config=config, resource_manager=resource_manager)


async def quick_search(account_info: Dict[str, Any], mode: str = "basic",
                      custom_queries: Optional[List[str]] = None) -> Dict[str, Any]:
    """快速搜索便捷函数"""
    pipeline = await create_search_pipeline()
    try:
        return await pipeline.execute_search_pipeline(account_info, mode, custom_queries)
    finally:
        await pipeline.cleanup()