"""
简化的资源管理器 - 只保留必要的Redis连接功能
替代原有的复杂UnifiedResourceManager系统
"""

import redis.asyncio as redis
import logging
from contextlib import asynccontextmanager
from typing import Optional
import os


logger = logging.getLogger(__name__)


def get_redis_config():
    """获取Redis配置"""
    # 从环境变量获取Redis配置
    redis_host = os.getenv('REDIS_HOST', 'localhost')
    redis_port = int(os.getenv('REDIS_PORT', 6379))
    redis_password = os.getenv('REDIS_PASSWORD')
    redis_db = int(os.getenv('REDIS_DB', 0))
    
    # AWS ElastiCache集群配置
    redis_cluster_mode = os.getenv('REDIS_CLUSTER_MODE', 'false').lower() == 'true'
    
    config = {
        'host': redis_host,
        'port': redis_port,
        'db': redis_db,
        'decode_responses': True,
        'socket_timeout': 30,
        'socket_connect_timeout': 30,
        'health_check_interval': 30
    }
    
    if redis_password:
        config['password'] = redis_password
    
    return config, redis_cluster_mode


@asynccontextmanager
async def get_redis_connection():
    """获取Redis连接的简化版本"""
    config, is_cluster = get_redis_config()
    
    connection = None
    try:
        if is_cluster:
            # 集群模式 - 简化配置
            connection = redis.RedisCluster(
                host=config['host'],
                port=config['port'],
                password=config.get('password'),
                decode_responses=config['decode_responses'],
                socket_timeout=config['socket_timeout']
            )
        else:
            # 单实例模式
            connection = redis.Redis(**config)
        
        # 测试连接
        await connection.ping()
        yield connection
        
    except Exception as e:
        logger.warning(f"Redis连接失败 (这在开发环境下是正常的): {e}")
        # 返回一个模拟连接以便测试
        yield MockRedisConnection()
    finally:
        if connection and hasattr(connection, 'close'):
            try:
                await connection.close()
            except Exception:
                pass


class MockRedisConnection:
    """模拟Redis连接，用于测试和开发环境"""
    
    def __init__(self):
        self._data = {}
        self._lists = {}
    
    async def ping(self):
        return True
    
    async def get(self, key):
        return self._data.get(key)
    
    async def set(self, key, value, ex=None):
        self._data[key] = value
        return True
    
    async def delete(self, *keys):
        count = 0
        for key in keys:
            if key in self._data:
                del self._data[key]
                count += 1
        return count
    
    async def ttl(self, key):
        return -1  # 永不过期
    
    async def rpush(self, key, *values):
        if key not in self._lists:
            self._lists[key] = []
        self._lists[key].extend(values)
        return len(self._lists[key])
    
    async def blpop(self, keys, timeout=0):
        # 模拟阻塞弹出，实际返回None表示超时
        return None
    
    async def llen(self, key):
        return len(self._lists.get(key, []))
    
    async def close(self):
        pass


# 保持向后兼容的便捷函数
@asynccontextmanager
async def get_http_client():
    """HTTP客户端便捷函数（空实现，保持兼容性）"""
    import aiohttp
    async with aiohttp.ClientSession() as session:
        yield session


def get_thread_pool():
    """线程池便捷函数（空实现，保持兼容性）"""
    from concurrent.futures import ThreadPoolExecutor
    return ThreadPoolExecutor(max_workers=4)


# === 兼容层 - 支持旧代码中的UnifiedResourceManager调用 ===

class UnifiedResourceManager:
    """兼容层 - 简化的UnifiedResourceManager实现"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialized = True
    
    async def initialize(self):
        """初始化（空实现）"""
        self.logger.info("兼容层UnifiedResourceManager已初始化")
        return
    
    async def cleanup(self):
        """清理（空实现）"""
        self.logger.info("兼容层UnifiedResourceManager已清理")
        return
    
    @asynccontextmanager
    async def acquire_from_pool(self, pool_name: str):
        """获取资源的兼容方法"""
        if pool_name == "redis_pool":
            async with get_redis_connection() as conn:
                yield conn
        elif pool_name == "http_pool":
            async with get_http_client() as client:
                yield client
        elif pool_name == "thread_pool":
            yield get_thread_pool()
        else:
            self.logger.warning(f"未知的资源池: {pool_name}")
            yield None


# 全局实例
_resource_manager: Optional[UnifiedResourceManager] = None


async def get_resource_manager() -> UnifiedResourceManager:
    """获取资源管理器（兼容层）"""
    global _resource_manager
    if _resource_manager is None:
        _resource_manager = UnifiedResourceManager()
        await _resource_manager.initialize()
    return _resource_manager