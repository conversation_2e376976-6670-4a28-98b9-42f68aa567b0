#!/usr/bin/env python3
"""
Test script to verify status manager Redis integration
"""

import asyncio
import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../..'))

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

async def test_status_manager_redis():
    """Test status manager with Redis integration"""
    
    try:
        from task.server.account_diagnosis.core.status_manager import UnifiedProgressTracker, UnifiedTaskStatus
        
        print("Testing UnifiedProgressTracker with Redis...")
        
        # Create tracker with Redis queue
        tracker = UnifiedProgressTracker(
            task_id="test_redis_123",
            user_id="test_user",
            diagnosis_id=999,
            env="dev",
            redis_queue="dev:q:diagnosis:response",  # Make sure this is set
            verbose=True,
            language="cn"
        )
        
        print(f"Tracker created with redis_queue: {tracker.redis_queue}")
        print(f"Tracker redis_manager: {tracker.redis_manager}")
        
        # Test basic status update
        print("\n1. Testing basic status update...")
        tracker.update_status(UnifiedTaskStatus.PLANNING, "开始规划任务...")
        await asyncio.sleep(1)
        
        # Test search status
        print("\n2. Testing search status...")
        tracker.update_status(UnifiedTaskStatus.SEARCHING, "正在搜索相关信息...")
        await asyncio.sleep(1)
        
        # Test with custom progress
        print("\n3. Testing custom progress...")
        tracker.update_status(UnifiedTaskStatus.ANALYZING_INDUSTRY, "正在分析行业数据...", step_progress=50)
        await asyncio.sleep(1)
        
        # Test completion with final results
        print("\n4. Testing completion with final results...")
        final_results = {
            "diagnosisHtml": "<html><body><h1>测试报告</h1><p>这是一个测试报告</p></body></html>",
            "diagnosisReport": {
                "summary": "测试总结",
                "tags": [{"dimension": "TEST", "status": "成功"}]
            },
            "marketingProposal": "# 测试营销建议\n\n这是测试内容"
        }
        
        tracker.mark_completed("测试完成！", final_results)
        await asyncio.sleep(2)  # Give time for async operations
        
        print("\n✅ Status manager test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Status manager test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_redis_push():
    """Test direct Redis push functionality"""
    
    try:
        from task.server.account_diagnosis.core.status_manager import UnifiedProgressTracker
        
        print("\nTesting direct Redis push...")
        
        tracker = UnifiedProgressTracker(
            task_id="test_direct_redis_456",
            env="dev",
            redis_queue="dev:q:diagnosis:response"
        )
        
        # Test direct push
        final_results = {
            "diagnosisHtml": "<html><body><h1>直接推送测试</h1></body></html>",
            "diagnosisReport": {"summary": "直接推送测试"}
        }
        
        success = await tracker.push_final_status_to_redis(final_results)
        print(f"Direct Redis push result: {success}")
        
        return success
        
    except Exception as e:
        print(f"❌ Direct Redis push test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Status Manager Redis Integration Test")
    print("=" * 60)
    
    # Run tests
    async def run_all_tests():
        test1 = await test_status_manager_redis()
        test2 = await test_direct_redis_push()
        
        print("\n" + "=" * 60)
        print("Test Results:")
        print(f"Status manager integration: {'✅ PASS' if test1 else '❌ FAIL'}")
        print(f"Direct Redis push: {'✅ PASS' if test2 else '❌ FAIL'}")
        
        if test1 and test2:
            print("\n🎉 All tests passed! Status manager should work correctly with Redis.")
        else:
            print("\n⚠️  Some tests failed. Check the Redis connection and configuration.")
    
    # Run the tests
    asyncio.run(run_all_tests())