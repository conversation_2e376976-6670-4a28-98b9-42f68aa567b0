"""
增强过程监控脚本
用于监控和分析GPT增强验证的成功率
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional
from collections import defaultdict

logger = logging.getLogger(__name__)

class EnhancementMonitor:
    """增强过程监控器"""
    
    def __init__(self):
        self.validation_stats = defaultdict(int)
        self.validation_details = []
        self.start_time = datetime.now()
    
    def record_validation_attempt(self, original: str, enhanced: str, 
                                result: bool, details: Dict[str, Any]):
        """记录验证尝试"""
        
        self.validation_stats['total_attempts'] += 1
        if result:
            self.validation_stats['passed'] += 1
        else:
            self.validation_stats['failed'] += 1
        
        # 记录详细信息
        record = {
            'timestamp': datetime.now().isoformat(),
            'original': original,
            'enhanced': enhanced,
            'result': result,
            'details': details
        }
        
        self.validation_details.append(record)
        
        # 实时统计
        success_rate = self.validation_stats['passed'] / self.validation_stats['total_attempts']
        
        if self.validation_stats['total_attempts'] % 10 == 0:  # 每10次统计一次
            logger.info(f"验证统计: 总计{self.validation_stats['total_attempts']}, "
                       f"成功{self.validation_stats['passed']}, "
                       f"失败{self.validation_stats['failed']}, "
                       f"成功率{success_rate:.1%}")
    
    def analyze_failure_patterns(self) -> Dict[str, Any]:
        """分析失败模式"""
        
        failed_records = [r for r in self.validation_details if not r['result']]
        
        if not failed_records:
            return {'message': '没有失败记录'}
        
        # 分析失败原因
        failure_reasons = defaultdict(int)
        length_issues = 0
        overlap_issues = 0
        improvement_issues = 0
        relevance_issues = 0
        
        for record in failed_records:
            details = record.get('details', {})
            
            if details.get('length_invalid'):
                length_issues += 1
                failure_reasons['length'] += 1
            
            if details.get('overlap_low'):
                overlap_issues += 1
                failure_reasons['overlap'] += 1
            
            if details.get('no_improvement'):
                improvement_issues += 1
                failure_reasons['improvement'] += 1
            
            if details.get('relevance_low'):
                relevance_issues += 1
                failure_reasons['relevance'] += 1
        
        return {
            'total_failures': len(failed_records),
            'failure_reasons': dict(failure_reasons),
            'length_issues': length_issues,
            'overlap_issues': overlap_issues,
            'improvement_issues': improvement_issues,
            'relevance_issues': relevance_issues,
            'failure_rate': len(failed_records) / len(self.validation_details)
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        
        total = self.validation_stats['total_attempts']
        if total == 0:
            return {'message': '没有验证记录'}
        
        success_rate = self.validation_stats['passed'] / total
        failure_analysis = self.analyze_failure_patterns()
        
        return {
            'total_attempts': total,
            'passed': self.validation_stats['passed'],
            'failed': self.validation_stats['failed'],
            'success_rate': success_rate,
            'failure_analysis': failure_analysis,
            'monitoring_duration': str(datetime.now() - self.start_time)
        }
    
    def export_details(self, filename: Optional[str] = None) -> str:
        """导出详细记录"""
        
        if not filename:
            filename = f"enhancement_validation_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = {
            'statistics': self.get_statistics(),
            'details': self.validation_details
        }
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"验证详情已导出到: {filename}")
            return filename
            
        except Exception as e:
            logger.error(f"导出验证详情失败: {e}")
            return f"export_failed_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

# 全局监控实例
_enhancement_monitor = None

def get_enhancement_monitor() -> EnhancementMonitor:
    """获取增强监控器实例"""
    global _enhancement_monitor
    if _enhancement_monitor is None:
        _enhancement_monitor = EnhancementMonitor()
    return _enhancement_monitor

def record_validation(original: str, enhanced: str, result: bool, **kwargs):
    """记录验证结果的便捷函数"""
    monitor = get_enhancement_monitor()
    monitor.record_validation_attempt(original, enhanced, result, kwargs)

def get_validation_stats() -> Dict[str, Any]:
    """获取验证统计的便捷函数"""
    monitor = get_enhancement_monitor()
    return monitor.get_statistics()

def export_validation_log(filename: Optional[str] = None) -> str:
    """导出验证日志的便捷函数"""
    monitor = get_enhancement_monitor()
    return monitor.export_details(filename) 