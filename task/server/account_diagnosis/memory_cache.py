"""
简单的内存缓存管理器
替代禁用的Redis缓存系统
"""

import asyncio
import logging
import time
import json
import hashlib
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

@dataclass
class CacheItem:
    """缓存项"""
    data: Any
    created_at: float
    ttl: int = 3600  # 默认1小时过期
    
    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl <= 0:
            return False  # 永不过期
        return time.time() - self.created_at > self.ttl

class MemoryCacheManager:
    """简单的内存缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self._cache: Dict[str, CacheItem] = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.hits = 0
        self.misses = 0
        self.sets = 0
        self._cleanup_task = None
        self._executor = ThreadPoolExecutor(max_workers=2)
        logger.info(f"内存缓存管理器已初始化，最大容量: {max_size}, 默认TTL: {default_ttl}秒")
        
    def _generate_key(self, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'args': str(args),
            'kwargs': str(sorted(kwargs.items()))
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _cleanup_expired(self):
        """清理过期缓存项"""
        expired_keys = []
        for key, item in self._cache.items():
            if item.is_expired():
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            
        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期缓存项")
    
    def _evict_if_needed(self):
        """如果需要则淘汰缓存项"""
        if len(self._cache) >= self.max_size:
            # 简单的LRU策略：删除最旧的项
            oldest_key = min(self._cache.keys(), 
                           key=lambda k: self._cache[k].created_at)
            del self._cache[oldest_key]
            logger.debug(f"缓存已满，淘汰键: {oldest_key}")
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if key in self._cache:
            item = self._cache[key]
            if not item.is_expired():
                self.hits += 1
                return item.data
            else:
                # 删除过期项
                del self._cache[key]
        
        self.misses += 1
        return None
    
    async def set(self, key: str, data: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        try:
            self._cleanup_expired()
            self._evict_if_needed()
            
            ttl = ttl if ttl is not None else self.default_ttl
            self._cache[key] = CacheItem(
                data=data,
                created_at=time.time(),
                ttl=ttl
            )
            self.sets += 1
            return True
        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存数据"""
        if key in self._cache:
            del self._cache[key]
            return True
        return False
    
    async def exists(self, key: str) -> bool:
        """检查缓存是否存在且未过期"""
        if key in self._cache:
            item = self._cache[key]
            if not item.is_expired():
                return True
            else:
                del self._cache[key]
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_ops = self.hits + self.misses
        hit_rate = (self.hits / total_ops * 100) if total_ops > 0 else 0
        
        return {
            "hits": self.hits,
            "misses": self.misses,
            "sets": self.sets,
            "hit_rate": f"{hit_rate:.2f}%",
            "total_operations": total_ops,
            "current_size": len(self._cache),
            "max_size": self.max_size,
            "cache_type": "memory"
        }
    
    async def clear_cache(self) -> int:
        """清空缓存"""
        size = len(self._cache)
        self._cache.clear()
        logger.info(f"已清空缓存，清理了 {size} 个项目")
        return size
    
    async def close(self):
        """关闭缓存管理器"""
        if self._cleanup_task:
            self._cleanup_task.cancel()
        self._executor.shutdown(wait=True)
        logger.info("内存缓存管理器已关闭")

class MemoryIntelligentCacheManager:
    """内存智能缓存管理器"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.cache_manager = MemoryCacheManager(max_size, default_ttl)
        self.cache_enabled = True
        logger.info("内存智能缓存管理器已初始化")
    
    def _generate_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_data = {
            'prefix': prefix,
            'args': str(args),
            'kwargs': str(sorted(kwargs.items()))
        }
        key_str = json.dumps(key_data, sort_keys=True)
        return f"{prefix}:{hashlib.md5(key_str.encode()).hexdigest()}"
    
    async def get_or_set(self, cache_key: str, ttl: int, callback: Callable, *callback_args):
        """获取缓存或设置缓存"""
        if not self.cache_enabled:
            # 直接执行回调
            result = callback(*callback_args) if callback_args else callback()
            if asyncio.iscoroutine(result):
                return await result
            return result
        
        # 尝试从缓存获取
        cached_data = await self.cache_manager.get(cache_key)
        if cached_data is not None:
            logger.debug(f"缓存命中: {cache_key}")
            return cached_data
        
        # 缓存未命中，执行回调获取数据
        try:
            result = callback(*callback_args) if callback_args else callback()
            if asyncio.iscoroutine(result):
                result = await result
            
            # 缓存结果
            await self.cache_manager.set(cache_key, result, ttl)
            logger.debug(f"数据已缓存: {cache_key}")
            return result
        except Exception as e:
            logger.error(f"获取数据失败: {e}")
            return None
    
    async def get_cached_query_results(self, account_id: str, query_params: Dict) -> Optional[List[Dict]]:
        """获取缓存的查询结果"""
        cache_key = self._generate_cache_key("query_results", account_id, query_params)
        return await self.cache_manager.get(cache_key)
    
    async def cache_query_results(self, account_id: str, query_params: Dict, 
                                results: List[Dict], ttl: int = 1800) -> bool:
        """缓存查询结果"""
        cache_key = self._generate_cache_key("query_results", account_id, query_params)
        return await self.cache_manager.set(cache_key, results, ttl)
    
    async def get_cached_final_report(self, account_id: str, report_params: Dict) -> Optional[Dict]:
        """获取缓存的最终报告"""
        cache_key = self._generate_cache_key("final_report", account_id, report_params)
        return await self.cache_manager.get(cache_key)
    
    async def cache_final_report(self, account_id: str, report_params: Dict, 
                               report: Dict, ttl: int = 3600) -> bool:
        """缓存最终报告"""
        cache_key = self._generate_cache_key("final_report", account_id, report_params)
        return await self.cache_manager.set(cache_key, report, ttl)
    
    async def get_cached_data(self, cache_key: str) -> Optional[Any]:
        """获取缓存数据"""
        return await self.cache_manager.get(cache_key)
    
    async def cache_data(self, cache_key: str, data: Any, ttl: int = 3600) -> bool:
        """缓存数据"""
        return await self.cache_manager.set(cache_key, data, ttl)
    
    async def invalidate_cache(self, pattern: str) -> bool:
        """失效匹配模式的缓存"""
        # 简单实现：删除包含pattern的键
        keys_to_delete = []
        for key in self.cache_manager._cache.keys():
            if pattern in key:
                keys_to_delete.append(key)
        
        for key in keys_to_delete:
            await self.cache_manager.delete(key)
        
        logger.info(f"已失效 {len(keys_to_delete)} 个缓存项，模式: {pattern}")
        return len(keys_to_delete) > 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return self.cache_manager.get_stats()
    
    async def close(self):
        """关闭缓存管理器"""
        await self.cache_manager.close()

# 全局内存缓存管理器实例
_memory_cache_manager = None
_memory_intelligent_cache_manager = None

def get_memory_cache_manager():
    """获取内存缓存管理器"""
    global _memory_cache_manager
    if _memory_cache_manager is None:
        _memory_cache_manager = MemoryCacheManager()
    return _memory_cache_manager

def get_intelligent_cache_manager():
    """获取智能缓存管理器"""
    global _memory_intelligent_cache_manager
    if _memory_intelligent_cache_manager is None:
        _memory_intelligent_cache_manager = MemoryIntelligentCacheManager()
    return _memory_intelligent_cache_manager

# 便捷函数
async def cache_query_results(account_id: str, query_params: Dict, 
                            results: List[Dict], ttl: int = 1800) -> bool:
    """缓存查询结果"""
    manager = get_intelligent_cache_manager()
    return await manager.cache_query_results(account_id, query_params, results, ttl)

async def get_cached_query_results(account_id: str, query_params: Dict) -> Optional[List[Dict]]:
    """获取缓存的查询结果"""
    manager = get_intelligent_cache_manager()
    return await manager.get_cached_query_results(account_id, query_params)

logger.info("内存缓存系统已初始化")