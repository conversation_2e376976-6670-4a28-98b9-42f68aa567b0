"""
动态关键词生成器
解决hardcode行业关键词的泛化性问题
"""

import asyncio
import json
import logging
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime
import time

logger = logging.getLogger(__name__)

@dataclass
class KeywordGenerationResult:
    """关键词生成结果"""
    industry: str
    primary_keywords: List[str]
    secondary_keywords: List[str]
    trending_keywords: List[str]
    related_technologies: List[str]
    target_audience_keywords: List[str]
    generation_time: str
    confidence_score: float

class DynamicKeywordGenerator:
    """动态关键词生成器"""
    
    def __init__(self, config=None):
        from .service_config import get_deep_research_config
        
        if config is None:
            config = get_deep_research_config()
            
        self.config = config
        self.gpt_model = config.models.gpt_model
        self.pplx_model = config.models.pplx_model
        
        # 导入AI调用模块
        try:
            from task import callWattGPT
            self.callWattGPT = callWattGPT
            self.ai_available = True
            logger.info("AI模块导入成功，动态关键词生成可用")
        except ImportError as e:
            logger.error(f"无法导入AI模块: {e}")
            self.ai_available = False
        
        # 基础关键词模板（作为后备）
        self.base_keywords = {
            "通用": ["行业", "趋势", "发展", "创新", "技术", "市场", "用户", "品牌"],
            "商业": ["营销", "策略", "增长", "收入", "客户", "竞争", "机会", "风险"],
            "技术": ["数字化", "智能化", "自动化", "平台", "工具", "解决方案", "系统"],
            "用户": ["需求", "体验", "服务", "满意度", "反馈", "行为", "偏好", "决策"]
        }
        
        # 缓存生成的关键词
        self.keyword_cache = {}
        self.cache_ttl = 86400  # 24小时缓存
    
    def _get_cache_key(self, industry: str, account_info: Dict) -> str:
        """生成缓存键"""
        import hashlib
        key_str = f"{industry}_{account_info.get('follows', 0)}_{datetime.now().strftime('%Y-%m-%d')}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """检查缓存是否有效"""
        cache_time = cache_entry.get('timestamp', 0)
        current_time = time.time()
        return (current_time - cache_time) < self.cache_ttl
    
    async def generate_industry_keywords(self, industry: str, account_info: Dict) -> KeywordGenerationResult:
        """
        动态生成行业关键词
        
        Args:
            industry: 行业名称
            account_info: 账号信息
            
        Returns:
            KeywordGenerationResult: 关键词生成结果
        """
        try:
            # 1. 检查缓存
            cache_key = self._get_cache_key(industry, account_info)
            if cache_key in self.keyword_cache:
                cache_entry = self.keyword_cache[cache_key]
                if self._is_cache_valid(cache_entry):
                    logger.info(f"从缓存获取行业关键词: {industry}")
                    return cache_entry['result']
            
            # 2. 使用AI生成关键词
            if self.ai_available:
                result = await self._generate_keywords_with_ai(industry, account_info)
                if result:
                    # 缓存结果
                    self.keyword_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                    logger.info(f"AI生成行业关键词成功: {industry}")
                    return result
            
            # 3. 回退到基础关键词生成
            logger.warning(f"AI生成失败，使用基础关键词生成: {industry}")
            return self._generate_basic_keywords(industry, account_info)
            
        except Exception as e:
            logger.error(f"动态关键词生成失败: {e}")
            return self._generate_basic_keywords(industry, account_info)
    
    async def _generate_keywords_with_ai(self, industry: str, account_info: Dict) -> Optional[KeywordGenerationResult]:
        """使用AI生成关键词"""
        try:
            # 构建提示词
            prompt = self._build_keyword_generation_prompt(industry, account_info)
            
            # 调用GPT
            body = {
                "model": self.gpt_model,
                "messages": [
                    {"role": "system", "content": "你是一个专业的行业分析师和关键词专家，擅长分析各行业的核心术语和趋势关键词。"},
                    {"role": "user", "content": prompt}
                ],
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "industry_keywords",
                        "schema": {
                            "type": "object",
                            "properties": {
                                "industry": {"type": "string"},
                                "primary_keywords": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "核心关键词5-8个"
                                },
                                "secondary_keywords": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "次要关键词6-10个"
                                },
                                "trending_keywords": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "趋势关键词4-6个"
                                },
                                "related_technologies": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "相关技术3-5个"
                                },
                                "target_audience_keywords": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "目标用户关键词4-6个"
                                },
                                "confidence_score": {
                                    "type": "number",
                                    "description": "置信度分数0-1"
                                }
                            },
                            "required": ["industry", "primary_keywords", "secondary_keywords", "trending_keywords", "related_technologies", "target_audience_keywords", "confidence_score"]
                        }
                    }
                }
            }
            
            # 执行AI调用
            gpt_status, code, gpt_result = self.callWattGPT.callOpenaiChannelChatCompletions(body, timeout=60)
            
            if not gpt_status:
                logger.error(f"AI关键词生成失败: {gpt_result}")
                return None
            
            # 解析结果
            if isinstance(gpt_result, dict) and 'result' in gpt_result:
                result_data = gpt_result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    content = message['content']
                                    keywords_data = json.loads(content)
                                    
                                    # 创建结果对象
                                    result = KeywordGenerationResult(
                                        industry=keywords_data.get('industry', industry),
                                        primary_keywords=keywords_data.get('primary_keywords', []),
                                        secondary_keywords=keywords_data.get('secondary_keywords', []),
                                        trending_keywords=keywords_data.get('trending_keywords', []),
                                        related_technologies=keywords_data.get('related_technologies', []),
                                        target_audience_keywords=keywords_data.get('target_audience_keywords', []),
                                        generation_time=datetime.now().isoformat(),
                                        confidence_score=keywords_data.get('confidence_score', 0.8)
                                    )
                                    
                                    return result
            
            logger.error("AI关键词生成结果解析失败")
            return None
            
        except Exception as e:
            logger.error(f"AI关键词生成异常: {e}")
            return None
    
    @staticmethod
    def _build_keyword_generation_prompt(industry: str, account_info: Dict) -> str:
        """构建关键词生成提示词"""
        followers_count = account_info.get('follows', 0)
        content_themes = account_info.get('content_themes', [])
        target_audience = account_info.get('target_audience', [])
        
        prompt = f"""
请为"{industry}"行业生成专业的关键词集合，用于社交媒体账号的深度研究分析。

账号背景信息：
- 行业：{industry}
- 粉丝数：{followers_count:,}
- 内容主题：{', '.join(content_themes) if content_themes else '未指定'}
- 目标受众：{', '.join(target_audience) if target_audience else '未指定'}

请生成以下类型的关键词：

1. **核心关键词 (primary_keywords)**：该行业最重要的5-8个专业术语
2. **次要关键词 (secondary_keywords)**：相关的6-10个扩展术语
3. **趋势关键词 (trending_keywords)**：{datetime.now().year}年该行业的4-6个热门趋势词
4. **相关技术 (related_technologies)**：影响该行业的3-5个技术关键词
5. **目标用户关键词 (target_audience_keywords)**：描述该行业用户群体的4-6个关键词

要求：
- 关键词要准确反映行业特点
- 包含中文和英文术语
- 考虑{datetime.now().year}年的最新趋势
- 适合用于搜索引擎查询
- 提供置信度分数（0-1）

请以JSON格式返回结果。
"""
        
        return prompt
    
    def _generate_basic_keywords(self, industry: str, account_info: Dict) -> KeywordGenerationResult:
        """生成基础关键词（后备方案）"""
        try:
            # 基于行业名称生成基础关键词
            primary_keywords = [industry]
            secondary_keywords = []
            trending_keywords = []
            related_technologies = []
            target_audience_keywords = []
            
            # 添加通用关键词
            secondary_keywords.extend(self.base_keywords["通用"])
            secondary_keywords.extend(self.base_keywords["商业"])
            
            # 根据行业特点添加特定关键词
            if "科技" in industry or "技术" in industry:
                secondary_keywords.extend(self.base_keywords["技术"])
                related_technologies.extend(["AI", "数字化", "智能化", "自动化"])
                trending_keywords.extend(["人工智能", "大数据", "云计算"])
            
            if "美妆" in industry or "护肤" in industry:
                primary_keywords.extend(["美妆", "护肤", "化妆品"])
                secondary_keywords.extend(["彩妆", "护肤品", "美容", "保养"])
                trending_keywords.extend(["天然成分", "可持续美容", "个性化护肤"])
            
            if "时尚" in industry or "穿搭" in industry:
                primary_keywords.extend(["时尚", "穿搭", "服装"])
                secondary_keywords.extend(["搭配", "潮流", "风格", "品牌"])
                trending_keywords.extend(["可持续时尚", "个性化定制", "快时尚"])
            
            # 添加目标用户关键词
            target_audience_keywords.extend(self.base_keywords["用户"])
            
            # 去重并限制数量
            primary_keywords = list(set(primary_keywords))[:8]
            secondary_keywords = list(set(secondary_keywords))[:10]
            trending_keywords = list(set(trending_keywords))[:6]
            related_technologies = list(set(related_technologies))[:5]
            target_audience_keywords = list(set(target_audience_keywords))[:6]
            
            result = KeywordGenerationResult(
                industry=industry,
                primary_keywords=primary_keywords,
                secondary_keywords=secondary_keywords,
                trending_keywords=trending_keywords,
                related_technologies=related_technologies,
                target_audience_keywords=target_audience_keywords,
                generation_time=datetime.now().isoformat(),
                confidence_score=0.6  # 基础生成的置信度较低
            )
            
            logger.info(f"基础关键词生成完成: {industry}")
            return result
            
        except Exception as e:
            logger.error(f"基础关键词生成失败: {e}")
            # 最后的后备方案
            return KeywordGenerationResult(
                industry=industry,
                primary_keywords=[industry],
                secondary_keywords=["行业", "趋势", "发展", "创新"],
                trending_keywords=[str(datetime.now().year), "最新", "热门"],
                related_technologies=["技术", "数字化"],
                target_audience_keywords=["用户", "客户", "消费者"],
                generation_time=datetime.now().isoformat(),
                confidence_score=0.3
            )
    
    async def get_enhanced_keywords(self, industry: str, account_info: Dict) -> List[str]:
        """获取增强的关键词列表（向后兼容）"""
        try:
            result = await self.generate_industry_keywords(industry, account_info)
            
            # 合并所有关键词
            all_keywords = []
            all_keywords.extend(result.primary_keywords)
            all_keywords.extend(result.secondary_keywords)
            all_keywords.extend(result.trending_keywords)
            all_keywords.extend(result.related_technologies)
            all_keywords.extend(result.target_audience_keywords)
            
            # 去重并返回
            return list(set(all_keywords))
            
        except Exception as e:
            logger.error(f"获取增强关键词失败: {e}")
            return [industry]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cached_industries": len(self.keyword_cache),
            "cache_ttl_hours": self.cache_ttl / 3600,
            "ai_available": self.ai_available
        }

# 全局实例
_dynamic_keyword_generator = None

def get_dynamic_keyword_generator() -> DynamicKeywordGenerator:
    """获取动态关键词生成器实例（单例模式）"""
    global _dynamic_keyword_generator
    if _dynamic_keyword_generator is None:
        _dynamic_keyword_generator = DynamicKeywordGenerator()
    return _dynamic_keyword_generator

async def generate_industry_keywords(industry: str, account_info: Dict) -> KeywordGenerationResult:
    """动态生成行业关键词的便捷函数"""
    generator = get_dynamic_keyword_generator()
    return await generator.generate_industry_keywords(industry, account_info)

if __name__ == "__main__":
    # 测试代码
    async def test_keyword_generation():
        test_account = {
            'follows': 25000,
            'content_themes': ['产品评测', '使用教程'],
            'target_audience': ['年轻用户', '科技爱好者']
        }
        
        # 测试已知行业
        result1 = await generate_industry_keywords("智能硬件", test_account)
        print(f"智能硬件关键词: {result1.primary_keywords}")
        
        # 测试未知行业
        result2 = await generate_industry_keywords("量子计算", test_account)
        print(f"量子计算关键词: {result2.primary_keywords}")
        
        # 测试新兴行业
        result3 = await generate_industry_keywords("元宇宙", test_account)
        print(f"元宇宙关键词: {result3.primary_keywords}")
    
    # 运行测试
    # asyncio.run(test_keyword_generation())