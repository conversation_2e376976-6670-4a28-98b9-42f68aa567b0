"""
增强的队列监控服务 - 专门用于持续监控Redis队列
解决超时问题，实现真正的持续监控
"""

import asyncio
import json
import time
import logging
from typing import Dict, Optional, Any
from contextlib import asynccontextmanager

from .core.continuous_monitor_config import get_continuous_monitor_config
from .simple_redis_manager import get_simple_redis_connection


class EnhancedQueueMonitor:
    """增强的队列监控器"""
    
    def __init__(self, queue_name: str, service_name: str = "queue_monitor"):
        self.queue_name = queue_name
        self.service_name = service_name
        self.config = get_continuous_monitor_config()
        
        # 使用简单的Redis连接管理器
        
        # 初始化日志
        self.logger = self._setup_logging()
        
        # 监控状态
        self._is_running = False
        self._shutdown_event = asyncio.Event()
        self._stats = {
            'start_time': None,
            'messages_processed': 0,
            'connection_errors': 0,
            'timeout_errors': 0,
            'last_message_time': None,
            'consecutive_failures': 0
        }
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(f'enhanced_queue_monitor_{self.service_name}')
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    async def _get_message_from_queue(self) -> Optional[Dict]:
        """从队列获取消息 - 优化版本"""
        max_retries = self.config.max_retries_per_operation
        
        for attempt in range(max_retries):
            try:
                async with get_simple_redis_connection() as conn:
                    # 使用较短的超时时间进行轮询
                    result = await conn.blpop(
                        self.queue_name, 
                        timeout=self.config.queue_poll_timeout
                    )
                
                if result:
                    _, message_data = result
                    self._stats['messages_processed'] += 1
                    self._stats['last_message_time'] = time.time()
                    self._stats['consecutive_failures'] = 0
                    
                    # 解析消息
                    if isinstance(message_data, str):
                        try:
                            parsed_data = json.loads(message_data)
                            return parsed_data
                        except json.JSONDecodeError as e:
                            self.logger.error(f"消息JSON解析失败: {e}")
                            return None
                    else:
                        return message_data
                else:
                    # 没有消息是正常情况
                    return None
                    
            except asyncio.TimeoutError:
                # 超时是正常的，不记录为错误
                if self.config.log_timeout_as_debug:
                    self.logger.debug(f"队列轮询超时 (尝试 {attempt + 1}/{max_retries})")
                return None
                
            except Exception as e:
                error_type = type(e).__name__
                error_msg = str(e)
                
                # 统计错误类型
                if 'timeout' in error_msg.lower():
                    self._stats['timeout_errors'] += 1
                elif 'connection' in error_msg.lower():
                    self._stats['connection_errors'] += 1
                
                self._stats['consecutive_failures'] += 1
                
                # 根据错误类型和重试次数调整日志级别
                if any(keyword in error_msg.lower() for keyword in ['timeout', 'connection']):
                    if attempt < max_retries - 1:
                        self.logger.debug(f"网络问题 (尝试 {attempt + 1}/{max_retries}) [{error_type}]: {error_msg}")
                        # 指数退避
                        wait_time = min(
                            self.config.failure_backoff_base * (self.config.retry_exponential_base ** attempt),
                            self.config.failure_backoff_max
                        )
                        await asyncio.sleep(wait_time)
                    else:
                        self.logger.warning(f"网络连接最终失败 [{error_type}]: {error_msg}")
                else:
                    self.logger.error(f"队列获取异常 (尝试 {attempt + 1}/{max_retries}) [{error_type}]: {error_msg}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(1)
        
        return None
    
    async def _handle_message(self, message: Dict) -> bool:
        """处理消息 - 子类应该重写此方法"""
        self.logger.info(f"收到消息: {message}")
        # 默认实现：只是记录消息
        return True
    
    async def _log_heartbeat(self):
        """记录心跳日志"""
        uptime = time.time() - self._stats['start_time']
        self.logger.info(
            f"📊 队列监控心跳 - "
            f"运行时间: {uptime:.0f}s, "
            f"处理消息: {self._stats['messages_processed']}, "
            f"连接错误: {self._stats['connection_errors']}, "
            f"超时错误: {self._stats['timeout_errors']}, "
            f"连续失败: {self._stats['consecutive_failures']}"
        )
    
    async def start_monitoring(self):
        """开始监控队列"""
        if self._is_running:
            self.logger.warning("监控已在运行中")
            return
        
        self._is_running = True
        self._stats['start_time'] = time.time()
        self._shutdown_event.clear()
        
        self.logger.info(f"🚀 开始监控队列: {self.queue_name}")
        
        # 启动心跳日志任务
        heartbeat_task = asyncio.create_task(self._heartbeat_loop())
        
        try:
            while not self._shutdown_event.is_set():
                try:
                    # 获取消息
                    message = await self._get_message_from_queue()
                    
                    if message:
                        # 处理消息
                        try:
                            success = await self._handle_message(message)
                            if not success:
                                self.logger.warning("消息处理失败")
                        except Exception as e:
                            self.logger.error(f"消息处理异常: {e}", exc_info=True)
                    
                    # 检查是否需要暂停（连续失败过多）
                    if self._stats['consecutive_failures'] >= self.config.max_consecutive_failures:
                        self.logger.error(
                            f"连续失败次数过多 ({self._stats['consecutive_failures']})，"
                            f"暂停 {self.config.failure_backoff_max} 秒..."
                        )
                        await asyncio.sleep(self.config.failure_backoff_max)
                        self._stats['consecutive_failures'] = 0
                    
                except Exception as e:
                    self.logger.error(f"监控循环异常: {e}", exc_info=True)
                    await asyncio.sleep(1)
        
        finally:
            # 取消心跳任务
            heartbeat_task.cancel()
            try:
                await heartbeat_task
            except asyncio.CancelledError:
                pass
            
            self._is_running = False
            self.logger.info("✅ 队列监控已停止")
    
    async def _heartbeat_loop(self):
        """心跳循环"""
        try:
            while not self._shutdown_event.is_set():
                await asyncio.sleep(self.config.log_heartbeat_interval)
                if not self._shutdown_event.is_set():
                    await self._log_heartbeat()
        except asyncio.CancelledError:
            pass
    
    async def stop_monitoring(self):
        """停止监控"""
        if not self._is_running:
            return
        
        self.logger.info("🛑 正在停止队列监控...")
        self._shutdown_event.set()
        
        # 等待监控循环结束
        while self._is_running:
            await asyncio.sleep(0.1)
        
        # Redis连接会自动清理
    
    def get_stats(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        stats = self._stats.copy()
        if stats['start_time']:
            stats['uptime'] = time.time() - stats['start_time']
        return stats


class CustomQueueMonitor(EnhancedQueueMonitor):
    """自定义队列监控器 - 可以根据需要重写消息处理逻辑"""
    
    def __init__(self, queue_name: str, message_handler=None, service_name: str = "custom_monitor"):
        super().__init__(queue_name, service_name)
        self.message_handler = message_handler
    
    async def _handle_message(self, message: Dict) -> bool:
        """处理消息"""
        if self.message_handler:
            try:
                if asyncio.iscoroutinefunction(self.message_handler):
                    return await self.message_handler(message)
                else:
                    return self.message_handler(message)
            except Exception as e:
                self.logger.error(f"自定义消息处理器异常: {e}", exc_info=True)
                return False
        else:
            # 默认处理：只记录消息
            self.logger.info(f"📨 收到队列消息: {json.dumps(message, ensure_ascii=False, indent=2)}")
            return True


# 便捷函数
async def monitor_queue_continuously(queue_name: str, message_handler=None, service_name: str = "monitor"):
    """持续监控队列的便捷函数"""
    monitor = CustomQueueMonitor(queue_name, message_handler, service_name)
    
    try:
        await monitor.start_monitoring()
    except KeyboardInterrupt:
        print("\n收到中断信号，正在停止监控...")
    finally:
        await monitor.stop_monitoring()


# 示例使用
if __name__ == "__main__":
    async def example_handler(message):
        print(f"处理消息: {message}")
        return True
    
    # 监控指定队列
    asyncio.run(monitor_queue_continuously("your_queue_name", example_handler))