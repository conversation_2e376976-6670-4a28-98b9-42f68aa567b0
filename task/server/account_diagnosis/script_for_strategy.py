#!/usr/bin/env python3
"""
新版策略服务脚本
使用统一的异步服务框架
支持常驻和定时任务两种模式
"""

import os
import sys

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import StrategyService
from task.server.account_diagnosis.base_async_service import create_service_main

# 创建服务主函数
# 默认为常驻模式，如需定时任务模式，请修改 run_mode="cron" 并设置 timeout
# main = create_service_main(StrategyService, max_concurrent_tasks=8)

# 定时任务模式示例（取消注释下面的代码并注释上面的代码）：
main = create_service_main(
    StrategyService, 
    run_mode="cron",     # 定时任务模式
    timeout=7080,  # 118分钟，激进设置，锁过期时间112.1分钟，缓冲7.9分钟
    use_service_lock=False,  # 禁用服务锁，允许随时重启
    max_concurrent_tasks=8
)

if __name__ == "__main__":
    main() 