"""
Enhanced Error Handling System for Account Diagnosis
增强的错误处理系统 - 统一、智能、可观测的错误处理机制
"""

import asyncio
import time
import traceback
import json
from enum import Enum
from typing import Dict, Optional, Callable, Any, List, Union
from dataclasses import dataclass, asdict
from datetime import datetime
from colorama import Fore, Style, init
import logging

# 初始化colorama
init(autoreset=True)

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"           # 可忽略的错误，记录但不影响流程
    MEDIUM = "medium"     # 需要重试的错误，可能影响性能
    HIGH = "high"         # 需要回退的错误，影响功能
    CRITICAL = "critical" # 系统级错误，需要立即处理

class ErrorCategory(Enum):
    """错误类别"""
    NETWORK = "network"           # 网络相关错误
    AI_SERVICE = "ai_service"     # AI服务错误
    DATA_VALIDATION = "data_validation"  # 数据验证错误
    TIMEOUT = "timeout"           # 超时错误
    RESOURCE = "resource"         # 资源不足错误
    BUSINESS_LOGIC = "business_logic"  # 业务逻辑错误
    REDIS = "redis"               # Redis相关错误
    UNKNOWN = "unknown"           # 未知错误

class RetryStrategy(Enum):
    """重试策略"""
    EXPONENTIAL_BACKOFF = "exponential_backoff"  # 指数退避
    LINEAR_BACKOFF = "linear_backoff"             # 线性退避
    FIXED_INTERVAL = "fixed_interval"             # 固定间隔
    NO_RETRY = "no_retry"                         # 不重试

@dataclass
class ErrorInfo:
    """错误信息"""
    category: ErrorCategory
    severity: ErrorSeverity
    message: str
    original_exception: Optional[Exception] = None
    retry_count: int = 0
    max_retries: int = 3
    retry_strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    backoff_factor: float = 2.0
    base_delay: float = 1.0
    max_delay: float = 60.0
    context: Optional[Dict] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class CircuitBreakerState(Enum):
    """熔断器状态"""
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""
    failure_threshold: int = 5      # 失败阈值
    recovery_timeout: int = 60      # 恢复超时时间（秒）
    success_threshold: int = 3      # 半开状态下成功阈值
    timeout: float = 30.0           # 操作超时时间

class CircuitBreaker:
    """熔断器实现"""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = None
        self.logger = logging.getLogger(f"CircuitBreaker.{name}")
    
    async def call(self, func: Callable, *args, **kwargs):
        """通过熔断器调用函数"""
        if self.state == CircuitBreakerState.OPEN:
            if self._should_attempt_reset():
                self._transition_to_half_open()
            else:
                self.logger.error(f"{Fore.RED}🚫 Circuit breaker {self.name} is OPEN - rejecting call{Style.RESET_ALL}")
                raise CircuitBreakerOpenException(f"Circuit breaker {self.name} is open")
        
        try:
            # 执行函数调用
            result = await asyncio.wait_for(func(*args, **kwargs), timeout=self.config.timeout)
            self._on_success()
            return result
            
        except asyncio.TimeoutError as e:
            self.logger.error(f"{Fore.YELLOW}⏰ Circuit breaker {self.name} - operation timeout{Style.RESET_ALL}")
            self._on_failure()
            raise e
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Circuit breaker {self.name} - operation failed: {str(e)}{Style.RESET_ALL}")
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """判断是否应该尝试重置"""
        if self.last_failure_time is None:
            return True
        return time.time() - self.last_failure_time > self.config.recovery_timeout
    
    def _transition_to_half_open(self):
        """转换到半开状态"""
        self.state = CircuitBreakerState.HALF_OPEN
        self.success_count = 0
        self.logger.info(f"{Fore.YELLOW}🔄 Circuit breaker {self.name} transitioning to HALF_OPEN{Style.RESET_ALL}")
    
    def _on_success(self):
        """成功回调"""
        if self.state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self._reset()
        elif self.state == CircuitBreakerState.CLOSED:
            self.failure_count = 0
    
    def _on_failure(self):
        """失败回调"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.state == CircuitBreakerState.HALF_OPEN:
            self._trip()
        elif self.state == CircuitBreakerState.CLOSED:
            if self.failure_count >= self.config.failure_threshold:
                self._trip()
    
    def _reset(self):
        """重置熔断器"""
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.logger.info(f"{Fore.GREEN}✅ Circuit breaker {self.name} RESET to CLOSED{Style.RESET_ALL}")
    
    def _trip(self):
        """触发熔断"""
        self.state = CircuitBreakerState.OPEN
        self.logger.error(f"{Fore.RED}🚨 Circuit breaker {self.name} TRIPPED to OPEN{Style.RESET_ALL}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取熔断器状态"""
        return {
            "name": self.name,
            "state": self.state.value,
            "failure_count": self.failure_count,
            "success_count": self.success_count,
            "last_failure_time": self.last_failure_time,
            "config": asdict(self.config)
        }

class ErrorClassifier:
    """错误分类器"""
    
    def __init__(self):
        self.classification_rules = {
            # 网络错误
            ErrorCategory.NETWORK: [
                "connection", "network", "dns", "host", "unreachable",
                "connection refused", "connection reset", "connection timeout"
            ],
            # 超时错误
            ErrorCategory.TIMEOUT: [
                "timeout", "timed out", "time limit", "deadline exceeded"
            ],
            # AI服务错误
            ErrorCategory.AI_SERVICE: [
                "api", "model", "token", "rate limit", "quota", "openai",
                "gemini", "claude", "gpt", "authentication", "api key"
            ],
            # Redis错误
            ErrorCategory.REDIS: [
                "redis", "connection pool", "cluster", "sentinel"
            ],
            # 数据验证错误
            ErrorCategory.DATA_VALIDATION: [
                "validation", "schema", "json", "parse", "decode",
                "invalid format", "malformed", "syntax error"
            ],
            # 资源错误
            ErrorCategory.RESOURCE: [
                "memory", "disk", "cpu", "resource", "limit", "quota",
                "out of memory", "disk full", "no space"
            ]
        }
    
    def classify(self, exception: Exception, context: Optional[Dict] = None) -> ErrorInfo:
        """分类错误"""
        error_msg = str(exception).lower()
        exception_type = type(exception).__name__.lower()
        
        # 分类错误类别
        category = ErrorCategory.UNKNOWN
        for cat, keywords in self.classification_rules.items():
            if any(keyword in error_msg or keyword in exception_type for keyword in keywords):
                category = cat
                break
        
        # 确定严重程度
        severity = self._determine_severity(exception, category, error_msg)
        
        # 确定重试策略
        retry_strategy, max_retries = self._determine_retry_strategy(category, severity)
        
        return ErrorInfo(
            category=category,
            severity=severity,
            message=str(exception),
            original_exception=exception,
            max_retries=max_retries,
            retry_strategy=retry_strategy,
            context=context or {}
        )
    
    def _determine_severity(self, exception: Exception, category: ErrorCategory, error_msg: str) -> ErrorSeverity:
        """确定错误严重程度"""
        # 关键词映射到严重程度
        critical_keywords = ["critical", "fatal", "system", "out of memory", "disk full"]
        high_keywords = ["rate limit", "quota exceeded", "authentication", "permission"]
        medium_keywords = ["timeout", "connection", "network", "temporary"]
        
        if any(keyword in error_msg for keyword in critical_keywords):
            return ErrorSeverity.CRITICAL
        elif any(keyword in error_msg for keyword in high_keywords):
            return ErrorSeverity.HIGH
        elif any(keyword in error_msg for keyword in medium_keywords):
            return ErrorSeverity.MEDIUM
        else:
            # 根据异常类型判断
            if isinstance(exception, (MemoryError, SystemError)):
                return ErrorSeverity.CRITICAL
            elif isinstance(exception, (PermissionError, AuthenticationError)):
                return ErrorSeverity.HIGH
            elif isinstance(exception, (TimeoutError, ConnectionError)):
                return ErrorSeverity.MEDIUM
            else:
                return ErrorSeverity.LOW
    
    def _determine_retry_strategy(self, category: ErrorCategory, severity: ErrorSeverity) -> tuple:
        """确定重试策略"""
        if severity == ErrorSeverity.CRITICAL:
            return RetryStrategy.NO_RETRY, 0
        elif severity == ErrorSeverity.HIGH:
            return RetryStrategy.LINEAR_BACKOFF, 2
        elif category == ErrorCategory.REDIS:
            # Redis错误使用更积极的重试策略
            return RetryStrategy.EXPONENTIAL_BACKOFF, 5  # 增加到5次重试
        elif category == ErrorCategory.NETWORK:
            return RetryStrategy.EXPONENTIAL_BACKOFF, 3
        elif category == ErrorCategory.AI_SERVICE:
            return RetryStrategy.EXPONENTIAL_BACKOFF, 3
        elif category == ErrorCategory.TIMEOUT:
            return RetryStrategy.LINEAR_BACKOFF, 2
        else:
            return RetryStrategy.EXPONENTIAL_BACKOFF, 3

class RetryManager:
    """重试管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("RetryManager")
    
    async def execute_with_retry(self, 
                               func: Callable,
                               error_info: ErrorInfo,
                               *args, **kwargs) -> Any:
        """带重试的执行"""
        if error_info.retry_strategy == RetryStrategy.NO_RETRY:
            self.logger.warning(f"{Fore.YELLOW}⚠️  No retry strategy - executing once{Style.RESET_ALL}")
            return await func(*args, **kwargs)
        
        last_exception = None
        
        for attempt in range(error_info.max_retries + 1):
            try:
                if attempt > 0:
                    delay = self._calculate_delay(error_info, attempt)
                    self.logger.info(f"{Fore.CYAN}🔄 Retry attempt {attempt}/{error_info.max_retries} after {delay:.2f}s delay{Style.RESET_ALL}")
                    await asyncio.sleep(delay)
                
                result = await func(*args, **kwargs)
                
                if attempt > 0:
                    self.logger.info(f"{Fore.GREEN}✅ Retry successful on attempt {attempt + 1}{Style.RESET_ALL}")
                
                return result
                
            except Exception as e:
                last_exception = e
                error_info.retry_count = attempt + 1
                
                if attempt < error_info.max_retries:
                    self.logger.warning(f"{Fore.YELLOW}⚠️  Attempt {attempt + 1} failed: {str(e)}{Style.RESET_ALL}")
                else:
                    self.logger.error(f"{Fore.RED}❌ All retry attempts exhausted. Final error: {str(e)}{Style.RESET_ALL}")
        
        # 所有重试都失败了
        raise last_exception
    
    def _calculate_delay(self, error_info: ErrorInfo, attempt: int) -> float:
        """计算延迟时间"""
        if error_info.retry_strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = error_info.base_delay * (error_info.backoff_factor ** (attempt - 1))
        elif error_info.retry_strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = error_info.base_delay * attempt
        elif error_info.retry_strategy == RetryStrategy.FIXED_INTERVAL:
            delay = error_info.base_delay
        else:
            delay = error_info.base_delay
        
        return min(delay, error_info.max_delay)

class ErrorMetrics:
    """错误指标收集器"""
    
    def __init__(self):
        self.error_counts = {}
        self.error_history = []
        self.max_history = 1000
        self.logger = logging.getLogger("ErrorMetrics")
    
    def record_error(self, error_info: ErrorInfo, operation: str = "unknown"):
        """记录错误"""
        key = f"{error_info.category.value}_{error_info.severity.value}"
        
        if key not in self.error_counts:
            self.error_counts[key] = 0
        self.error_counts[key] += 1
        
        # 记录历史
        error_record = {
            "timestamp": error_info.timestamp.isoformat(),
            "operation": operation,
            "category": error_info.category.value,
            "severity": error_info.severity.value,
            "message": error_info.message,
            "retry_count": error_info.retry_count,
            "context": error_info.context
        }
        
        self.error_history.append(error_record)
        
        # 保持历史记录大小
        if len(self.error_history) > self.max_history:
            self.error_history.pop(0)
        
        # 彩色日志记录
        color = self._get_severity_color(error_info.severity)
        self.logger.error(f"{color}📊 Error recorded: {key} (total: {self.error_counts[key]}){Style.RESET_ALL}")
    
    def _get_severity_color(self, severity: ErrorSeverity) -> str:
        """获取严重程度对应的颜色"""
        color_map = {
            ErrorSeverity.LOW: Fore.BLUE,
            ErrorSeverity.MEDIUM: Fore.YELLOW,
            ErrorSeverity.HIGH: Fore.MAGENTA,
            ErrorSeverity.CRITICAL: Fore.RED
        }
        return color_map.get(severity, Fore.WHITE)
    
    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        total_errors = sum(self.error_counts.values())
        
        return {
            "total_errors": total_errors,
            "error_counts": self.error_counts.copy(),
            "recent_errors": self.error_history[-10:],  # 最近10个错误
            "error_rate_by_category": self._calculate_error_rates()
        }
    
    def _calculate_error_rates(self) -> Dict[str, float]:
        """计算各类别错误率"""
        total = sum(self.error_counts.values())
        if total == 0:
            return {}
        
        rates = {}
        for key, count in self.error_counts.items():
            rates[key] = count / total
        
        return rates

# 自定义异常类
class CircuitBreakerOpenException(Exception):
    """熔断器开启异常"""
    pass

class AuthenticationError(Exception):
    """认证错误"""
    pass

class EnhancedErrorHandler:
    """增强的错误处理器 - 主要入口类"""
    
    def __init__(self, redis_manager=None):
        self.classifier = ErrorClassifier()
        self.retry_manager = RetryManager()
        self.metrics = ErrorMetrics()
        self.circuit_breakers = {}
        self.redis_manager = redis_manager
        self.logger = logging.getLogger("EnhancedErrorHandler")
        
        # 默认熔断器配置 - 更宽松的Redis设置
        self.default_cb_configs = {
            "ai_service": CircuitBreakerConfig(failure_threshold=3, recovery_timeout=30, timeout=120.0),
            "redis": CircuitBreakerConfig(failure_threshold=8, recovery_timeout=30, timeout=60.0),  # 更宽松的Redis设置
            "network": CircuitBreakerConfig(failure_threshold=6, recovery_timeout=45, timeout=45.0),
        }
        
        # 初始化熔断器
        for name, config in self.default_cb_configs.items():
            self.circuit_breakers[name] = CircuitBreaker(name, config)
    
    async def handle_with_circuit_breaker(self,
                                        func: Callable,
                                        circuit_breaker_name: str,
                                        operation_name: str = "unknown",
                                        context: Optional[Dict] = None,
                                        *args, **kwargs) -> Any:
        """带熔断器的错误处理"""
        if circuit_breaker_name not in self.circuit_breakers:
            self.logger.warning(f"{Fore.YELLOW}⚠️  Circuit breaker '{circuit_breaker_name}' not found, creating default{Style.RESET_ALL}")
            self.circuit_breakers[circuit_breaker_name] = CircuitBreaker(
                circuit_breaker_name, 
                CircuitBreakerConfig()
            )
        
        circuit_breaker = self.circuit_breakers[circuit_breaker_name]
        
        try:
            self.logger.info(f"{Fore.CYAN}🔧 Executing {operation_name} with circuit breaker {circuit_breaker_name}{Style.RESET_ALL}")
            result = await circuit_breaker.call(func, *args, **kwargs)
            self.logger.info(f"{Fore.GREEN}✅ {operation_name} completed successfully{Style.RESET_ALL}")
            return result
            
        except Exception as e:
            # 分类错误
            error_info = self.classifier.classify(e, context)
            
            # 记录错误指标
            self.metrics.record_error(error_info, operation_name)
            
            # 彩色错误日志
            color = self.metrics._get_severity_color(error_info.severity)
            self.logger.error(f"{color}💥 {operation_name} failed: {error_info.category.value} - {error_info.severity.value} - {str(e)}{Style.RESET_ALL}")
            
            # 根据严重程度决定是否重新抛出
            if error_info.severity == ErrorSeverity.CRITICAL:
                self.logger.critical(f"{Fore.RED}🚨 CRITICAL ERROR in {operation_name} - immediate escalation required{Style.RESET_ALL}")
                raise e
            
            # 尝试重试
            if error_info.retry_strategy != RetryStrategy.NO_RETRY:
                try:
                    self.logger.info(f"{Fore.YELLOW}🔄 Attempting retry for {operation_name}{Style.RESET_ALL}")
                    return await self.retry_manager.execute_with_retry(func, error_info, *args, **kwargs)
                except Exception as retry_e:
                    self.logger.error(f"{Fore.RED}❌ Retry failed for {operation_name}: {str(retry_e)}{Style.RESET_ALL}")
                    raise retry_e
            else:
                raise e
    
    def get_system_health(self) -> Dict[str, Any]:
        """获取系统健康状态"""
        cb_statuses = {}
        for name, cb in self.circuit_breakers.items():
            cb_statuses[name] = cb.get_status()
        
        error_summary = self.metrics.get_error_summary()
        
        # 计算整体健康分数
        health_score = self._calculate_health_score(cb_statuses, error_summary)
        
        return {
            "health_score": health_score,
            "circuit_breakers": cb_statuses,
            "error_metrics": error_summary,
            "timestamp": datetime.now().isoformat()
        }
    
    def _calculate_health_score(self, cb_statuses: Dict, error_summary: Dict) -> float:
        """计算健康分数 (0-100)"""
        score = 100.0
        
        # 熔断器状态影响
        for cb_status in cb_statuses.values():
            if cb_status["state"] == "open":
                score -= 30
            elif cb_status["state"] == "half_open":
                score -= 15
        
        # 错误率影响
        total_errors = error_summary.get("total_errors", 0)
        if total_errors > 100:
            score -= 20
        elif total_errors > 50:
            score -= 10
        elif total_errors > 20:
            score -= 5
        
        return max(0.0, score)
    
    def add_circuit_breaker(self, name: str, config: CircuitBreakerConfig):
        """添加自定义熔断器"""
        self.circuit_breakers[name] = CircuitBreaker(name, config)
        self.logger.info(f"{Fore.GREEN}➕ Added circuit breaker: {name}{Style.RESET_ALL}")
    
    async def reset_circuit_breaker(self, name: str):
        """重置熔断器"""
        if name in self.circuit_breakers:
            self.circuit_breakers[name]._reset()
            self.logger.info(f"{Fore.GREEN}🔄 Reset circuit breaker: {name}{Style.RESET_ALL}")
        else:
            self.logger.warning(f"{Fore.YELLOW}⚠️  Circuit breaker not found: {name}{Style.RESET_ALL}")