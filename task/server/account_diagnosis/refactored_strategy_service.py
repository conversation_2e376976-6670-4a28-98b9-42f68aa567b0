#!/usr/bin/env python3
"""
使用通用框架重构的策略生成服务
展示如何使用AsyncTaskFramework
"""

import asyncio
import json
import time
import os
import sys
from typing import Dict, Tuple

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task import ENV, GEMINI_PRO_MODEL
from task.lib.call_claude import gemini, gpt
from task.lib.prompt_utils import clean_html_output
from task.lib.json_utils_enhanced import enhanced_validate_and_fix_ai_json
from task.lib.async_task_framework import BaseTaskProcessor, create_service, run_service

# 队列配置
STRATEGY_INPUT_QUEUE = ENV.lower() + ":q:generate:report:socialmedia:request"
STRATEGY_OUTPUT_QUEUE = ENV.lower() + ":q:generate:report:socialmedia:response"

class StrategyTaskProcessor(BaseTaskProcessor):
    """策略任务处理器"""
    
    def __init__(self):
        super().__init__("strategy")
    
    def get_task_type_from_input(self, input_data: Dict) -> str:
        """从输入数据中获取任务类型"""
        if input_data.get("socialMediaReportData"):
            return "strategy"
        return "unknown"
    
    async def process_task(self, input_data: Dict) -> Tuple[bool, Dict]:
        """处理策略生成任务"""
        social_media_data = input_data.get("socialMediaReportData")
        if not social_media_data:
            return False, {"error": "必须提供 'socialMediaReportData' 字段"}
        
        self.logger.info("开始生成策略报告...")
        
        try:
            # Step 1: 生成JSON格式策略报告
            json_status, json_result = await self._generate_strategy_json(social_media_data)
            if not json_status:
                return False, {"error": f"生成JSON报告失败: {json_result.get('error', '未知错误')}"}
            
            self.logger.info("JSON策略报告生成成功")
            
            # Step 2: 基于JSON生成HTML可视化报告
            html_status, html_result = await self._generate_strategy_html(json_result)
            if not html_status:
                self.logger.warning(f"HTML报告生成失败: {html_result}")
                return True, {
                    "reportJson": json_result, 
                    "reportHtml": None, 
                    "error": f"HTML报告生成失败: {html_result}"
                }
            
            self.logger.info("HTML策略报告生成成功")
            
            return True, {
                "reportJson": json_result,
                "reportHtml": html_result
            }
            
        except Exception as e:
            self.logger.error(f"生成策略报告时发生异常: {str(e)}", exc_info=True)
            return False, {"error": f"生成策略报告异常: {str(e)}"}
    
    async def _call_ai_async(self, ai_function, sys_prompt, user_prompt, model_or_schema=None, model_name=None, task_name="AI任务", max_retries=2):
        """异步AI调用函数"""
        def sync_ai_call():
            if model_or_schema:
                return ai_function(sys_prompt, user_prompt, json_schema=model_or_schema, model=model_name)
            else:
                return ai_function(sys_prompt, user_prompt, model=model_name)
        
        start_time = time.time()
        self.logger.info(f"开始执行{task_name}，模型：{model_name}")
        
        for attempt in range(max_retries):
            try:
                self.logger.info(f"{task_name} - 尝试 {attempt + 1}/{max_retries}")
                
                loop = asyncio.get_event_loop()
                status, result = await asyncio.wait_for(
                    loop.run_in_executor(None, sync_ai_call),
                    timeout=120
                )
                
                if status:
                    elapsed_time = time.time() - start_time
                    self.logger.info(f"{task_name}生成成功，耗时：{elapsed_time:.2f}秒")
                    return True, result
                else:
                    self.logger.warning(f"{task_name}调用失败，尝试 {attempt + 1}/{max_retries}: {str(result)}")
                    if attempt == max_retries - 1:
                        return False, f"{task_name}调用最终失败: {str(result)}"
                    await asyncio.sleep(3)
                    
            except asyncio.TimeoutError:
                self.logger.error(f"{task_name}调用超时，尝试 {attempt + 1}/{max_retries}")
                if attempt == max_retries - 1:
                    return False, f"{task_name}调用超时"
                await asyncio.sleep(3)
                    
            except Exception as e:
                self.logger.error(f"{task_name}调用异常，尝试 {attempt + 1}/{max_retries}: {str(e)}")
                if attempt == max_retries - 1:
                    return False, f"{task_name}调用异常: {str(e)}"
                await asyncio.sleep(3)
        
        return False, f"{task_name}未知错误"
    
    async def _generate_strategy_json(self, company_data: Dict) -> Tuple[bool, Dict]:
        """生成JSON格式的策略报告"""
        try:
            sys_prompt = """你是一位资深的市场策略专家，擅长根据公司信息制定社交媒体运营策略。

请根据公司信息生成社媒策略报告，必须严格按照以下JSON结构输出：

重要要求：
1. 根节点必须是 "strategyReport"
2. 包含所有指定字段，信息不足的字段设为空字符串""
3. 直接输出JSON，不要任何解释文字或markdown标记
4. 确保JSON格式正确可解析
5. 要求输出要用中文

必须包含的字段结构：
- title: 报告标题  
- executiveSummary: 执行摘要
- businessGoals: {title, primaryObjective, suggestedKPIs: {summary, phase1_Launch, phase2_Growth}, industryBenchmarks}
- situationAnalysis: {title, brandEssenceAnalysis: {title, summary}, assetAudit: {title, summary}, inferredCompetitorAnalysis: {title, summary}}
- targetAudience: {title, primaryPersona: {name, demographics, interests, painPoints, socialMediaBehavior}}
- contentStrategy: {title, coreMessage, toneOfVoice, contentPillars, contentFormats, launchContentPlan}
- measurementAndReporting: {title, reportingFrequency, keyMetricsToWatch, optimizationProcess}
- budgetAllocation: {title, summary, breakdown: {contentCreation, communitySeeding, exploratoryAds}}
- visualIdentity: {title, summary, visualStyleAndMood: {title, keywords: [], description}, colorPalette: {title, primaryColor, secondaryColors: [], accentColor}, imageAndVideoStyle: {title, photography, videography, filterAndEditing}, graphicElementsAndTemplates: {title, logoUsage, iconsAndIllustrations, layoutTemplates}}"""
            
            # 构建用户提示词
            marketing_object = company_data.get("marketingObject", "")
            company_name = company_data.get("companyName", "")
            company_industry = company_data.get("companyIndustry", "")
            brand_positioning = company_data.get("companyBrandPositioning", "")
            core_competency = company_data.get("companyCoreCompetency", "")
            target_audience = company_data.get("companyTargetAudience", "")
            brand_vision = company_data.get("companyBrandVision", "")
            
            user_prompt = f"""以下是公司信息：
营销目标：{marketing_object}
公司名称：{company_name}
所属行业：{company_industry}
品牌定位：{brand_positioning}
核心竞争力：{core_competency}
目标受众：{target_audience}
品牌愿景：{brand_vision}

请根据以上公司信息，严格按照系统提示中的JSON结构，生成一份详细的社交媒体策略报告。注意：
1. 必须严格按照指定的JSON格式输出
2. 所有字段都必须填写，如果信息不足可以填写空字符串""
3. 不要添加任何解释性文字，只输出JSON
4. 确保JSON格式正确可解析"""
            
            status, json_result = await self._call_ai_async(
                gpt, sys_prompt, user_prompt, 
                model_or_schema="flexible", 
                model_name="gpt-4.1", 
                task_name="策略报告(JSON)", 
                max_retries=3
            )

            if not status:
                return False, {"error": json_result}

            # 处理AI返回的数据类型
            if isinstance(json_result, dict):
                result = json_result.get('strategyReport', json_result)
                return True, result
            elif isinstance(json_result, str):
                success, result = enhanced_validate_and_fix_ai_json(
                    json_result, expected_root_key='strategyReport', logger=self.logger
                )
                
                if not success:
                    self.logger.error(f"AI JSON验证和修复失败: {result}")
                    return False, {"error": f"JSON处理失败: {result}"}
                
                return True, result
            else:
                return False, {"error": f"AI返回了意外的数据类型: {type(json_result)}"}
            
        except Exception as e:
            self.logger.error(f"生成策略JSON报告时发生异常: {e}", exc_info=True)
            return False, {"error": f"生成策略JSON报告异常: {str(e)}"}
    
    async def _generate_strategy_html(self, strategy_json: Dict) -> Tuple[bool, str]:
        """基于JSON策略报告生成HTML可视化报告"""
        try:
            sys_prompt = """你是一位专业的前端开发工程师，擅长将数据报告转换为美观的HTML可视化页面。

请根据提供的JSON格式策略报告，生成一个完整的HTML页面。

要求：
1. 生成完整的HTML文档（包含DOCTYPE、html、head、body标签）
2. 包含内联CSS样式，确保页面美观
3. 合理组织内容结构，突出重点信息
4. 使用现代化的设计风格，但页面必须保持静态
5. 确保内容完整展示JSON中的所有信息
6. 不要使用任何动画效果（包括CSS动画、JavaScript动画、过渡效果等）
7. 避免使用可能导致元素隐藏的CSS属性：opacity: 0, display: none, visibility: hidden
8. 页面应该是完全静态的，优雅而精美
9. 要求输出要用中文
10. 在品牌色彩体系（colorPalette）的展示中，请遵守以下两点以确保视觉效果和可读性： a) 根据背景色的深浅动态选择文字颜色（黑色或白色），例如在深色背景上使用白色文字，在浅色背景上使用黑色文字。 b) 颜色区块（如圆形）内的文字标签必须在该区块内部水平和垂直居中，确保文字完全包含在色块内，避免与外部背景重叠。
11. 重要：不要生成任何页脚（footer）、版权信息或年份信息

请直接输出HTML内容，不要包含任何markdown标记或解释文字。"""
            
            user_prompt = f"""请将以下JSON格式的策略报告转换为HTML可视化页面：

{json.dumps(strategy_json, ensure_ascii=False, indent=2)}

生成一个完整、美观的HTML报告页面。"""
            
            status, html_result = await self._call_ai_async(
                gemini, sys_prompt, user_prompt, 
                model_name=GEMINI_PRO_MODEL, 
                task_name="策略报告(HTML)"
            )

            if not status:
                return False, html_result

            html_result = clean_html_output(html_result)
            return True, html_result
            
        except Exception as e:
            self.logger.error(f"生成策略HTML报告时发生异常: {e}", exc_info=True)
            return False, f"生成策略HTML报告异常: {str(e)}"

def main():
    """主函数"""
    # 创建任务处理器
    processor = StrategyTaskProcessor()
    
    # 创建服务
    service = create_service(
        service_name="strategy",
        input_queue=STRATEGY_INPUT_QUEUE,
        output_queue=STRATEGY_OUTPUT_QUEUE,
        task_processor=processor,
        max_concurrent_tasks=5  # 可根据需要调整
    )
    
    # 运行服务
    asyncio.run(run_service(service))

if __name__ == "__main__":
    main() 