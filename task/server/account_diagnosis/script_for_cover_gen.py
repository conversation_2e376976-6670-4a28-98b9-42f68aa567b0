#!/usr/bin/env python3
"""
新版封面生成服务脚本
使用统一的异步服务框架 - 定时任务模式
适用于 Argo Cron 定时执行
"""

import os
import sys

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task.server.account_diagnosis.service_implementations import CoverService
from task.server.account_diagnosis.base_async_service import create_service_main

# 创建服务主函数 - 定时任务模式
# run_mode="daemon": 守护进程模式，持续运行处理任务
# timeout=600: 运行10分钟后自动退出，单个任务最多10分钟
# max_concurrent_tasks=10: 最大并发任务数
main = create_service_main(
    CoverService, 
    # run_mode="daemon",     # 守护进程模式，持续运行
    run_mode="cron",     # 定时任务模式，适配Argo Cron
    timeout=7080,  # 118分钟，激进设置，锁过期时间112.1分钟，缓冲7.9分钟
    use_service_lock=False,  # 禁用服务锁，允许随时重启
    max_concurrent_tasks=10
)

if __name__ == "__main__":
    print("启动封面生成服务...")
    main() 