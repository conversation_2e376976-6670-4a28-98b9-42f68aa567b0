import asyncio
import json
import time
import os
import sys
import logging
from typing import Dict, Tuple, Union, Optional, Callable, List, Any

# 添加 colorama 支持用于彩色输出
try:
    from colorama import Fore, Style, init
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    # 如果没有安装 colorama，创建空的占位符
    COLORAMA_AVAILABLE = False
    class MockFore:
        RED = YELLOW = GREEN = BLUE = CYAN = MAGENTA = WHITE = ""
    class MockStyle:
        RESET_ALL = ""
    Fore = MockFore()
    Style = MockStyle()

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

# 为Windows创建一个可重用的线程池执行器
WIN_EXECUTOR = None
if sys.platform == 'win32':
    from concurrent.futures import ThreadPoolExecutor
    WIN_EXECUTOR = ThreadPoolExecutor(max_workers=1)

from task import GEMINI_PRO_MODEL
from task.lib.call_claude import gemini, gpt, claude
from task.lib.prompt_utils import (
    get_diagnosis_prompt,
    get_enhanced_diagnosis_prompt,
    get_html_generation_prompts,
    clean_html_output,
    get_json_generation_prompts,
    get_sales_proposal_prompts,
    get_sales_proposal_html_prompts,
    clean_markdown_output
)
from task.lib.json_utils_enhanced import (
    enhanced_validate_and_fix_ai_json,
    detect_json_issues
)
from task.server.account_diagnosis.prompts.diagnosis_schema import diagnosis_schema_gpt

# 导入统一的状态管理系统
try:
    from .core.status_manager import (
        UnifiedTaskStatus as TaskStatus,
        UnifiedProgressTracker as TaskProgressTracker,
        StatusMessageManager
    )
    from .core.status_adapter import create_progress_tracker_adapter
except ImportError:
    # 支持直接运行的情况
    from core.status_manager import (
        UnifiedTaskStatus as TaskStatus,
        UnifiedProgressTracker as TaskProgressTracker,
        StatusMessageManager
    )
    from core.status_adapter import create_progress_tracker_adapter

# 导入优化的Redis管理器
try:
    from .core.optimized_redis_manager import (
        get_optimized_redis_manager,
        redis_queue_push,
        redis_queue_pop,
        redis_queue_length
    )
except ImportError:
    # 支持直接运行的情况
    from core.optimized_redis_manager import (
        get_optimized_redis_manager,
        redis_queue_push,
        redis_queue_pop,
        redis_queue_length
    )

# 导入统一的状态管理系统
try:
    from .core.status_manager import (
        UnifiedTaskStatus as TaskStatus,
        UnifiedProgressTracker as TaskProgressTracker,
        StatusMessageManager
    )
except ImportError:
    # 支持直接运行的情况
    from core.status_manager import (
        UnifiedTaskStatus as TaskStatus,
        UnifiedProgressTracker as TaskProgressTracker,
        StatusMessageManager
    )

# 导入优化的Redis管理器
try:
    from .core.optimized_redis_manager import (
        get_optimized_redis_manager,
        redis_queue_push,
        redis_queue_pop,
        redis_queue_length
    )
except ImportError:
    # 支持直接运行的情况
    from core.optimized_redis_manager import (
        get_optimized_redis_manager,
        redis_queue_push,
        redis_queue_pop,
        redis_queue_length
    )

logger = logging.getLogger(__name__)


# Redis管理器全局实例
_redis_manager_initialized = False


async def initialize_redis_manager():
    """初始化优化的Redis管理器"""
    global _redis_manager_initialized
    if not _redis_manager_initialized:
        try:
            # 初始化优化的Redis管理器
            redis_manager = get_optimized_redis_manager()
            health_check = await redis_manager.health_check()
            logger.info(f"✅ 优化的Redis管理器初始化成功: {health_check['status']}")
            _redis_manager_initialized = True
            
        except Exception as e:
            logger.error(f"❌ Redis管理器初始化失败: {e}")
            raise


async def cleanup_redis_manager():
    """清理Redis管理器"""
    global _redis_manager_initialized
    if _redis_manager_initialized:
        try:
            redis_manager = get_optimized_redis_manager()
            await redis_manager.cleanup()
            logger.info("✅ Redis管理器已清理")
            _redis_manager_initialized = False
        except Exception as e:
            logger.error(f"❌ Redis管理器清理失败: {e}")


# 统一状态管理系统已通过import别名引入


def ensure_complete_diagnosis_json(json_data: Dict) -> Dict:
    """确保诊断JSON包含所有必需的字段"""

    # 定义完整的字段结构模板
    template = {
        "summary": "",
        "tags": [
            {
                "dimension": "CURRENT STATUS",
                "status": "待分析"
            },
            {
                "dimension": "GROWTH POTENTIAL",
                "status": "待分析"
            },
            {
                "dimension": "FOCUS NEEDED",
                "status": "待分析"
            }
        ],
        "bottleneck": {
            "title": "账号瓶颈",
            "area": [
                {
                    "title": "内容质量待提升",
                    "title_en": "CONTENT QUALITY IMPROVEMENT",
                    "des": "内容质量需要进一步优化"
                },
                {
                    "title": "互动策略待完善",
                    "title_en": "ENGAGEMENT STRATEGY OPTIMIZATION",
                    "des": "互动策略需要改进"
                },
                {
                    "title": "定位需要明确",
                    "title_en": "POSITIONING CLARIFICATION",
                    "des": "账号定位需要更加明确"
                }
            ]
        },
        "content_analysis": {
            "title": "内容分析",
            "des": "内容分析结果待完善",
            "title_en": "CONTENT ANALYSIS"
        },
        "ip_analysis": {
            "title": "IP分析",
            "des": "IP分析结果待完善",
            "title_en": "IP ANALYSIS"
        },
        "optimize_dimension": {
            "title": "可优化维度",
            "areas": [
                {
                    "name": "定位",
                    "question": "账号定位需要进一步明确"
                },
                {
                    "name": "标题",
                    "question": "标题吸引力有待提升"
                },
                {
                    "name": "内容",
                    "question": "内容质量需要优化"
                },
                {
                    "name": "互动",
                    "question": "互动策略需要改进"
                }
            ]
        },
        "suggestion": [
            {
                "title": "内容优化建议",
                "content": [
                    "提升内容原创性",
                    "优化内容结构",
                    "增强内容价值",
                    "改进视觉呈现"
                ]
            },
            {
                "title": "互动策略建议",
                "content": [
                    "及时回复评论",
                    "主动与粉丝互动",
                    "优化发布时间",
                    "增加互动话题"
                ]
            },
            {
                "title": "定位优化建议",
                "content": [
                    "明确目标受众",
                    "统一内容风格",
                    "突出个人特色",
                    "建立专业形象"
                ]
            }
        ]
    }

    def merge_with_template(data, template_data):
        """递归合并数据，确保所有模板字段都存在"""
        if isinstance(template_data, dict):
            result = {}
            for key, template_value in template_data.items():
                if key in data and data[key] is not None:
                    if isinstance(data[key], dict) and isinstance(template_value, dict):
                        result[key] = merge_with_template(data[key], template_value)
                    elif isinstance(data[key], list) and isinstance(template_value, list):
                        # 对于数组，如果原数据有内容就使用原数据，否则使用模板
                        if len(data[key]) > 0:
                            result[key] = data[key]
                        else:
                            result[key] = template_value
                    else:
                        result[key] = data[key]
                else:
                    result[key] = template_value
            return result
        else:
            return data if data is not None else template_data

    # 如果输入数据为空或不是字典，返回完整模板
    if not isinstance(json_data, dict):
        logger.warning("输入的JSON数据不是字典类型，使用完整模板")
        return template

    # 合并数据，确保所有字段都存在
    complete_data = merge_with_template(json_data, template)

    return complete_data


async def call_ai_async(ai_function, sys_prompt, user_prompt, model_or_schema=None,
                        model_name=None, task_name="AI任务", max_retries=2,
                        progress_tracker: Optional[TaskProgressTracker] = None):
    """异步调用AI函数的通用包装器，支持重试和token统计"""

    def sync_ai_call():
        start_time = time.time()
        try:
            if ai_function == gpt:
                # GPT调用
                if model_or_schema and isinstance(model_or_schema, dict):
                    # 有JSON schema
                    status, result = ai_function(sys_prompt, user_prompt, model_or_schema, model=model_name)
                    return status, result, None, time.time() - start_time
                else:
                    # 没有JSON schema
                    status, result = ai_function(sys_prompt, user_prompt, model=model_name)
                    return status, result, None, time.time() - start_time
            elif ai_function == gemini:
                # Gemini调用 - 现在返回三个值：status, result, usage_metadata
                response = ai_function(sys_prompt, user_prompt, model=model_name)
                if isinstance(response, tuple) and len(response) == 3:
                    status, result, usage_metadata = response
                    return status, result, usage_metadata, time.time() - start_time
                elif isinstance(response, tuple) and len(response) == 2:
                    # 兼容旧的返回格式
                    status, result = response
                    return status, result, None, time.time() - start_time
                else:
                    return False, f"Unexpected response format: {response}", None, time.time() - start_time
            elif ai_function == claude:
                # Claude调用
                status, result = ai_function(sys_prompt, user_prompt)
                return status, result, None, time.time() - start_time
            else:
                return False, f"Unsupported AI function: {ai_function}", None, time.time() - start_time
        except Exception as e:
            logger.error(f"AI调用异常: {str(e)}")
            return False, f"AI调用异常: {str(e)}", None, time.time() - start_time

    # 尝试调用AI函数，支持重试
    for attempt in range(max_retries + 1):
        try:
            logger.info(f"开始执行{task_name}，模型：{model_name}")
            logger.info(f"{task_name} - 尝试 {attempt + 1}/{max_retries + 1}")
            
            # 发送AI任务开始状态更新
            if progress_tracker:
                progress_tracker.update_status_immediate(
                    progress_tracker.progress.current_status,
                    custom_message=f"正在执行 {task_name}..."
                )

            # 在线程池中执行同步调用
            loop = asyncio.get_event_loop()
            status, result, usage_metadata, elapsed_time = await loop.run_in_executor(None, sync_ai_call)

            if status:
                logger.info(f"{task_name}生成成功，耗时：{elapsed_time:.2f}秒")
                
                # 立即发送AI任务成功状态更新
                if progress_tracker:
                    progress_tracker.update_status_immediate(
                        progress_tracker.progress.current_status,
                        custom_message=f"{task_name} 执行成功 ({elapsed_time:.1f}s)"
                    )
                
                # 记录token使用量
                if usage_metadata:
                    prompt_tokens = usage_metadata.get('promptTokenCount', 0)
                    candidates_tokens = usage_metadata.get('candidatesTokenCount', 0)
                    total_tokens = usage_metadata.get('totalTokenCount', 0)
                    logger.info(
                        f"{task_name} Token使用量 - Prompt: {prompt_tokens}, Output: {candidates_tokens}, Total: {total_tokens}")

                    # 尝试调用全局的 token 统计函数（如果存在）
                    try:
                        if hasattr(sys.modules.get('__main__'), 'print_token_usage'):
                            main_module = sys.modules['__main__']
                            main_module.print_token_usage(task_name, usage_metadata)
                    except Exception:
                        pass  # 如果调用失败，静默忽略
                return status, result
            else:
                logger.warning(f"{task_name} - 尝试 {attempt + 1} 失败: {result}")
                
                # 发送AI任务失败状态更新（非最后一次尝试）
                if progress_tracker and attempt < max_retries:
                    progress_tracker.update_status_immediate(
                        progress_tracker.progress.current_status,
                        custom_message=f"{task_name} 重试中... ({attempt + 1}/{max_retries + 1})"
                    )
                
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
                else:
                    logger.error(f"{task_name}生成失败，所有重试都已用尽: {result}")
                    
                    # 发送AI任务最终失败状态更新
                    if progress_tracker:
                        progress_tracker.update_status_immediate(
                            progress_tracker.progress.current_status,
                            custom_message=f"{task_name} 执行失败"
                        )
                    
                    return status, result
        except Exception as e:
            logger.error(f"{task_name} 执行异常: {str(e)}")
            
            # 发送AI任务异常状态更新
            if progress_tracker:
                progress_tracker.update_status_immediate(
                    progress_tracker.progress.current_status,
                    custom_message=f"{task_name} 执行异常"
                )
            
            if attempt < max_retries:
                await asyncio.sleep(2 ** attempt)
            else:
                return False, f"{task_name} 执行异常: {str(e)}"

    return False, f"{task_name} 执行失败"


async def conduct_diagnosis_async(input_data: Dict, use_deep_research: bool = True,
                                  progress_tracker: Optional[TaskProgressTracker] = None) -> Tuple[bool, str]:
    """异步执行诊断，支持深度搜索模式"""
    try:
        # 尝试从input_data获取task_id，如果存在则使用新的统一队列系统
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId")
        
        # 创建统一的状态更新器（优先使用新的队列系统）
        async def update_status(status, message):
            if task_id and progress_tracker:
                adapter = create_progress_tracker_adapter(task_id, "diagnosis_core")
                await adapter.update_status_immediate(status, custom_message=message)
            elif progress_tracker:
                progress_tracker.update_status_immediate(status, custom_message=message)
        
        # 更新状态：开始账号分析
        await update_status(
            TaskStatus.CRAWLING_ACCOUNT,
            f"正在分析账号 \"{input_data.get('accountInfo', {}).get('nickname', '未知账号')}\" 的基础信息…"
        )

        required_fields = ["accountInfo", "noteList", "marketingGoal"]
        for field in required_fields:
            if field not in input_data:
                return False, f"缺少必需字段: {field}"

        # 深度搜索模式
        if use_deep_research:
            try:
                # 更新状态：开始深度搜索
                industry = input_data.get("industry", "相关行业")
                await update_status(
                    TaskStatus.SEARCHING,
                    f"正在搜索 \"{industry}\" 行业热点与最新动态…"
                )

                # 首先尝试使用核心诊断引擎（默认深度研究）
                try:
                    logger.info("尝试使用核心诊断引擎进行深度研究...")
                    
                    # 发送引擎初始化状态
                    if progress_tracker:
                        progress_tracker.update_status_immediate(
                            TaskStatus.INITIALIZING_RESEARCH,
                            custom_message="正在启动核心诊断引擎..."
                        )
                    
                    from .core.diagnosis_engine import DiagnosisEngine, DiagnosisContext, DiagnosisMode
                    from .core.config_manager import get_diagnosis_config
                    
                    # 创建诊断配置和上下文
                    config = get_diagnosis_config()
                    diagnosis_engine = DiagnosisEngine(
                        config=config,
                        progress_tracker=progress_tracker
                    )
                    
                    # 创建诊断上下文
                    context = DiagnosisContext(
                        task_id=progress_tracker.task_id if progress_tracker else "unknown",
                        user_id=progress_tracker.user_id if progress_tracker else None,
                        diagnosis_id=progress_tracker.diagnosis_id if progress_tracker else None,
                        account_info=input_data["accountInfo"],
                        mode=DiagnosisMode.DEEP_RESEARCH
                    )
                    
                    # 添加额外的上下文信息
                    context.account_info["noteList"] = input_data.get("noteList", [])
                    context.account_info["marketingGoal"] = input_data.get("marketingGoal")
                    context.account_info["industry"] = input_data.get("industry")
                    
                    logger.info("开始执行核心诊断引擎...")
                    diagnosis_results = await diagnosis_engine.diagnose(context)
                    
                    # 检查诊断结果
                    if diagnosis_results and diagnosis_results.get("status") == "completed":
                        # 尝试从reports中获取诊断报告
                        reports = diagnosis_results.get("reports", {})
                        diagnosis_report = reports.get("diagnosis_report")
                        
                        if diagnosis_report and len(str(diagnosis_report).strip()) > 50:
                            logger.info("✅ 核心诊断引擎成功完成")
                            
                            # 发送诊断成功状态
                            if progress_tracker:
                                progress_tracker.update_status_immediate(
                                    TaskStatus.PERFORMING_DIAGNOSIS,
                                    custom_message="核心诊断引擎执行成功"
                                )
                            
                            return True, diagnosis_report
                        else:
                            logger.warning(f"核心诊断引擎生成的报告内容不足，长度: {len(str(diagnosis_report)) if diagnosis_report else 0}")
                    else:
                        logger.warning(f"核心诊断引擎执行失败，状态: {diagnosis_results.get('status', 'unknown') if diagnosis_results else 'none'}")
                        if diagnosis_results and diagnosis_results.get("error_message"):
                            logger.error(f"核心诊断引擎错误信息: {diagnosis_results['error_message']}")
                    
                    logger.warning("核心诊断引擎返回结果无效，回退到备用方案")
                        
                except ImportError as e:
                    logger.warning(f"核心诊断引擎模块导入失败，使用备用方案: {e}")
                except Exception as e:
                    logger.warning(f"核心诊断引擎执行失败，使用备用方案: {e}")
                    import traceback
                    logger.error(f"核心诊断引擎详细错误信息: {traceback.format_exc()}")

                # 备用方案：尝试原有的快速深度搜索
                try:
                    logger.info("尝试导入快速深度搜索模块...")
                    
                    # 发送备用方案状态
                    if progress_tracker:
                        progress_tracker.update_status_immediate(
                            TaskStatus.SEARCHING,
                            custom_message="启动备用深度搜索方案..."
                        )
                    
                    from .emergency_timeout_fix import enhanced_diagnosis_with_quick_fix
                    logger.info("快速深度搜索模块导入成功，开始执行...")
                    
                    success, result = await enhanced_diagnosis_with_quick_fix(input_data, use_deep_research=True)
                    
                    if success and result and len(str(result).strip()) > 0:
                        logger.info("快速深度搜索成功完成")
                        
                        # 发送搜索成功状态
                        if progress_tracker:
                            progress_tracker.update_status_immediate(
                                TaskStatus.ANALYZING_INDUSTRY,
                                custom_message="深度搜索完成，开始分析"
                            )
                        
                        return success, result
                    else:
                        logger.warning(f"快速深度搜索返回失败或空结果，回退到基础模式: success={success}, result_length={len(str(result)) if result else 0}")
                        # 如果深度搜索失败，继续执行基础模式
                except ImportError as e:
                    logger.warning(f"快速深度搜索模块导入失败，回退到基础模式: {e}")
                except Exception as e:
                    logger.warning(f"快速深度搜索执行失败，回退到基础模式: {e}")
                    import traceback
                    logger.error(f"快速深度搜索详细错误信息: {traceback.format_exc()}")

            except Exception as e:
                logger.warning(f"深度搜索模式整体失败，回退到基础模式: {e}")
                import traceback
                logger.error(f"深度搜索模式详细错误信息: {traceback.format_exc()}")

        # 更新状态：开始基础诊断 - 立即发送
        if progress_tracker:
            progress_tracker.update_status_immediate(
                TaskStatus.PERFORMING_DIAGNOSIS,
                custom_message="正在进行多维度账号比对与诊断…"
            )

        # 基础诊断模式（原有逻辑）
        logger.info("使用基础诊断模式，开始构建账号数据...")
        goal_dict = {"引流私域": 1, "带货变现": 2, "品牌曝光": 3, "涨粉提升": 4}

        account_data = {
            "account": input_data["accountInfo"],
            "noteList": input_data.get("noteList", [])[:100] if isinstance(input_data.get("noteList"), list) else [],
            "industry": None if input_data.get("industry") == 'empty' else input_data.get("industry"),
            "marketingGoal": goal_dict.get(input_data["marketingGoal"], 1)
        }

        logger.info(f"基础诊断模式数据准备完成：账号={account_data['account'].get('nickname', 'Unknown')}, 笔记数={len(account_data['noteList'])}, 行业={account_data.get('industry', 'None')}, 目标={account_data['marketingGoal']}")

        sys_prompt = "你是一位资深的小红书运营策略专家，拥有多年针对不同行业账号进行诊断和优化的实战经验。请对目标账号进行深度分析并提供具有高度可操作性的优化方案。回复只能包含方案本身，不要有任何介绍性的开场白，要求输出要用中文"

        # 使用增强版诊断提示词，传入搜索结果（如果有的话）
        search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
        user_prompt = get_enhanced_diagnosis_prompt(account_data, search_results)
        
        logger.info("开始调用基础诊断AI模型...")

        result = await call_ai_async(gpt, sys_prompt, user_prompt, model_name="gpt-4.1",
                                   task_name="诊断报告", progress_tracker=progress_tracker)
        
        # 确保返回值类型正确
        if isinstance(result, tuple) and len(result) == 2:
            status, response = result
            if isinstance(response, str):
                return status, response
            else:
                # 如果响应不是字符串，转换为字符串
                return status, str(response)
        else:
            logger.error(f"call_ai_async 返回了意外的结果格式: {type(result)} - {result}")
            return False, f"AI调用返回了意外的结果格式: {type(result)}"

    except Exception as e:
        logger.error(f"诊断过程发生异常: {e}", exc_info=True)
        return False, f"诊断异常: {str(e)}"


async def generate_html_async(diagnosis_result: str, progress_tracker: Optional[TaskProgressTracker] = None) -> Tuple[
    bool, str]:
    """异步生成HTML报告"""
    try:
        # 尝试从上下文获取深度搜索结果
        search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
        
        sys_prompt, user_prompt = get_html_generation_prompts(diagnosis_result, search_results)
        result = await call_ai_async(gemini, sys_prompt, user_prompt, model_name=GEMINI_PRO_MODEL,
                                     task_name="HTML账号诊断报告", progress_tracker=progress_tracker)
        # result = await call_ai_async(claude, sys_prompt, user_prompt,
        #                              task_name="HTML账号诊断报告", progress_tracker=progress_tracker)

        # 确保返回值类型正确
        if isinstance(result, tuple) and len(result) == 2:
            status, response = result
            if not status:
                return False, str(response)
            
            # 确保响应是字符串类型
            if not isinstance(response, str):
                response = str(response)
        else:
            logger.error(f"HTML生成 call_ai_async 返回了意外的结果格式: {type(result)} - {result}")
            return False, f"AI调用返回了意外的结果格式: {type(result)}"

        # 清理HTML输出（已包含markdown标记清理）
        cleaned_result = clean_html_output(response)

        # 验证生成的HTML是否有效
        if not cleaned_result or len(cleaned_result.strip()) == 0:
            logger.error("生成的诊断HTML为空")
            return False, "生成的诊断HTML为空"

        # 检查HTML是否包含基本的HTML结构
        if not any(tag in cleaned_result.lower() for tag in ['<html', '<body', '<div']):
            logger.error("生成的诊断HTML缺少基本HTML结构")
            return False, "生成的诊断HTML缺少基本HTML结构"

        # 检查HTML长度是否合理（至少应该有一些内容）
        if len(cleaned_result.strip()) < 100:
            logger.error(f"生成的诊断HTML内容过短: {len(cleaned_result.strip())} 字符")
            return False, f"生成的诊断HTML内容过短: {len(cleaned_result.strip())} 字符"

        return True, cleaned_result
    except Exception as e:
        logger.error(f"生成HTML报告时发生异常: {e}", exc_info=True)
        return False, f"生成HTML报告异常: {str(e)}"


async def generate_json_async(diagnosis_result: str, progress_tracker: Optional[TaskProgressTracker] = None) -> Tuple[
    bool, Union[dict, str]]:
    """异步生成JSON格式报告"""
    try:
        # 尝试从上下文获取深度搜索结果
        search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
        
        sys_prompt, user_prompt = get_json_generation_prompts(diagnosis_result, search_results)
        
        # 第一次尝试：使用完整JSON schema（最大重试1次）
        logger.info("尝试使用完整JSON schema生成报告...")
        result = await call_ai_async(gpt, sys_prompt, user_prompt, diagnosis_schema_gpt,
                                     model_name="gpt-4.1-mini", task_name="JSON诊断报告(完整模式)",
                                     max_retries=1, progress_tracker=progress_tracker)

        # 检查第一次尝试结果
        if isinstance(result, tuple) and len(result) == 2:
            status, response = result
            
            if status and isinstance(response, (str, dict)):
                logger.info("完整JSON schema模式成功")
                # 如果是字符串，需要处理JSON
                if isinstance(response, str):
                    # 检测和修复AI响应中的JSON问题
                    issues = detect_json_issues(response)
                    if issues:
                        logger.warning(f"AI JSON响应检测到问题: {issues}")

                    # 使用增强版验证和修复AI生成的JSON，包含LLM修复
                    json_success, fixed_json = enhanced_validate_and_fix_ai_json(response, logger=logger)
                    if json_success:
                        complete_json = ensure_complete_diagnosis_json(fixed_json)
                        return True, complete_json
                else:
                    # 响应已经是字典格式
                    complete_json = ensure_complete_diagnosis_json(response)
                    return True, complete_json
            else:
                logger.warning(f"完整JSON schema模式失败: {response}")
        
        # 第二次尝试：使用灵活JSON模式（更宽松的要求）
        logger.info("回退到灵活JSON模式...")
        if progress_tracker:
            progress_tracker.update_status_immediate(
                progress_tracker.progress.current_status,
                custom_message="JSON诊断报告(灵活模式) 执行中..."
            )
        
        # 简化的提示词，要求更宽松的JSON格式
        simplified_sys_prompt = """你是一位专业的数据分析师。请基于诊断结果生成JSON格式报告。
        
要求：
1. 必须返回有效的JSON格式
2. 包含核心字段：summary, tags, bottleneck, content_analysis, ip_analysis, optimize_dimension, suggestion
3. 字段内容要具体实用"""

        simplified_user_prompt = f"""诊断结果：{diagnosis_result[:2000]}...

请生成包含以下结构的JSON报告：
{{
  "summary": "账号核心问题概述",
  "tags": [{{"dimension": "关键维度", "status": "当前状态"}}],
  "bottleneck": {{"title": "账号瓶颈", "area": [{{"title": "问题", "des": "说明"}}]}},
  "content_analysis": {{"title": "内容分析", "des": "分析结果"}},
  "ip_analysis": {{"title": "IP分析", "des": "分析结果"}},
  "optimize_dimension": {{"title": "可优化维度", "areas": [{{"name": "维度", "question": "问题"}}]}},
  "suggestion": [{{"title": "建议", "content": ["具体建议"]}}]
}}"""
        
        flexible_result = await call_ai_async(gpt, simplified_sys_prompt, simplified_user_prompt, 'flexible',
                                              model_name="gpt-4.1-mini", task_name="JSON诊断报告(灵活模式)",
                                              max_retries=1, progress_tracker=progress_tracker)
        
        if isinstance(flexible_result, tuple) and len(flexible_result) == 2:
            flex_status, flex_response = flexible_result
            
            if flex_status and isinstance(flex_response, (str, dict)):
                logger.info("灵活JSON模式成功")
                if isinstance(flex_response, str):
                    json_success, fixed_json = enhanced_validate_and_fix_ai_json(flex_response, logger=logger)
                    if json_success:
                        complete_json = ensure_complete_diagnosis_json(fixed_json)
                        return True, complete_json
                else:
                    complete_json = ensure_complete_diagnosis_json(flex_response)
                    return True, complete_json
            else:
                logger.warning(f"灵活JSON模式也失败: {flex_response}")
        
        # 第三次尝试：使用基础文本模式并手动构造JSON
        logger.info("最后尝试：基础文本模式...")
        if progress_tracker:
            progress_tracker.update_status_immediate(
                progress_tracker.progress.current_status,
                custom_message="JSON诊断报告(基础模式) 执行中..."
            )
        
        basic_sys_prompt = "你是诊断专家，请分析账号问题并提供建议。回复要简洁明了。"
        basic_user_prompt = f"请分析以下账号诊断结果，提供核心问题总结和3个关键建议：\n{diagnosis_result[:1500]}"
        
        basic_result = await call_ai_async(gpt, basic_sys_prompt, basic_user_prompt,
                                          model_name="gpt-4.1-mini", task_name="基础诊断分析",
                                          max_retries=0, progress_tracker=progress_tracker)
        
        if isinstance(basic_result, tuple) and len(basic_result) == 2:
            basic_status, basic_response = basic_result
            
            if basic_status and basic_response:
                logger.info("基础模式成功，手动构造JSON")
                # 手动构造基础JSON结构
                fallback_json = {
                    "summary": str(basic_response)[:200] + "..." if len(str(basic_response)) > 200 else str(basic_response),
                    "tags": [
                        {"dimension": "整体状态", "status": "需要优化"}
                    ],
                    "bottleneck": {
                        "title": "账号瓶颈",
                        "area": [{"title": "待诊断", "des": "系统正在分析中，请稍后查看详细报告"}]
                    },
                    "content_analysis": {
                        "title": "内容分析",
                        "des": "基于当前数据的初步分析结果"
                    },
                    "ip_analysis": {
                        "title": "IP分析",
                        "des": "账号定位和人设特征分析"
                    },
                    "optimize_dimension": {
                        "title": "可优化维度",
                        "areas": [
                            {"name": "内容质量", "question": "需要提升内容原创性和价值"},
                            {"name": "互动效果", "question": "需要改进与粉丝的互动策略"},
                            {"name": "发布节奏", "question": "需要优化内容发布时间和频率"}
                        ]
                    },
                    "suggestion": [
                        {
                            "title": "内容优化",
                            "content": ["提高原创性", "增强内容价值", "优化视觉呈现"]
                        },
                        {
                            "title": "互动提升",
                            "content": ["及时回复评论", "主动与粉丝互动", "创造互动话题"]
                        },
                        {
                            "title": "策略调整",
                            "content": ["分析最佳发布时间", "制定内容计划", "关注行业趋势"]
                        }
                    ]
                }
                
                complete_json = ensure_complete_diagnosis_json(fallback_json)
                return True, complete_json
        
        # 所有尝试都失败的情况
        logger.error("所有JSON生成尝试都失败")
        return False, "JSON报告生成失败，所有降级策略都未成功"
        
    except Exception as e:
        logger.error(f"生成JSON报告时发生异常: {e}", exc_info=True)
        return False, f"生成JSON报告异常: {str(e)}"


async def generate_sales_proposal_async(diagnosis_result: str,
                                        progress_tracker: Optional[TaskProgressTracker] = None) -> Tuple[bool, str]:
    """异步生成销售提案"""
    try:
        # 尝试从上下文获取深度搜索结果
        search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
        
        sys_prompt, user_prompt = get_sales_proposal_prompts(diagnosis_result, search_results)
        result = await call_ai_async(gpt, sys_prompt, user_prompt, model_name="gpt-4.1",
                                             task_name="销售提案", progress_tracker=progress_tracker)

        # 确保返回值类型正确
        if isinstance(result, tuple) and len(result) == 2:
            status, response = result
            if not status:
                return False, str(response)
                
            # 确保响应是字符串类型
            if not isinstance(response, str):
                response = str(response)
        else:
            logger.error(f"销售提案生成 call_ai_async 返回了意外的结果格式: {type(result)} - {result}")
            return False, f"AI调用返回了意外的结果格式: {type(result)}"

        # 清理Markdown输出
        cleaned_result = clean_markdown_output(response)

        # 验证生成的销售提案是否有效
        if not cleaned_result or len(cleaned_result.strip()) == 0:
            logger.error("生成的销售提案为空")
            return False, "生成的销售提案为空"

        # 检查销售提案长度是否合理（至少应该有一些内容）
        if len(cleaned_result.strip()) < 50:
            logger.error(f"生成的销售提案内容过短: {len(cleaned_result.strip())} 字符")
            return False, f"生成的销售提案内容过短: {len(cleaned_result.strip())} 字符"

        return True, cleaned_result
    except Exception as e:
        logger.error(f"生成销售提案时发生异常: {e}", exc_info=True)
        return False, f"生成销售提案异常: {str(e)}"


async def generate_sales_proposal_html_async(diagnosis_result: str,
                                             progress_tracker: Optional[TaskProgressTracker] = None) -> Tuple[
    bool, str]:
    """异步生成HTML格式的销售提案"""
    try:
        # 首先生成销售提案的Markdown内容
        sales_status, sales_proposal = await generate_sales_proposal_async(diagnosis_result, progress_tracker)
        if not sales_status:
            return False, f"生成销售提案失败: {sales_proposal}"

        # 然后基于销售提案生成HTML
        # 尝试从上下文获取深度搜索结果
        search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
        
        sys_prompt, user_prompt = get_sales_proposal_html_prompts(sales_proposal, search_results)
        result = await call_ai_async(gemini, sys_prompt, user_prompt, model_name=GEMINI_PRO_MODEL,
                                             task_name="销售提案HTML", progress_tracker=progress_tracker)

        # 确保返回值类型正确
        if isinstance(result, tuple) and len(result) == 2:
            status, response = result
            if not status:
                return False, str(response)
                
            # 确保响应是字符串类型
            if not isinstance(response, str):
                response = str(response)
        else:
            logger.error(f"销售提案HTML生成 call_ai_async 返回了意外的结果格式: {type(result)} - {result}")
            return False, f"AI调用返回了意外的结果格式: {type(result)}"

        # 清理HTML输出（已包含markdown标记清理）
        cleaned_result = clean_html_output(response)

        # 验证生成的销售提案HTML是否有效
        if not cleaned_result or len(cleaned_result.strip()) == 0:
            logger.error("生成的销售提案HTML为空")
            return False, "生成的销售提案HTML为空"

        # 检查HTML是否包含基本的HTML结构
        if not any(tag in cleaned_result.lower() for tag in ['<html', '<body', '<div']):
            logger.error("生成的销售提案HTML缺少基本HTML结构")
            return False, "生成的销售提案HTML缺少基本HTML结构"

        # 检查HTML长度是否合理（至少应该有一些内容）
        if len(cleaned_result.strip()) < 100:
            logger.error(f"生成的销售提案HTML内容过短: {len(cleaned_result.strip())} 字符")
            return False, f"生成的销售提案HTML内容过短: {len(cleaned_result.strip())} 字符"

        return True, cleaned_result
    except Exception as e:
        logger.error(f"生成销售提案HTML时发生异常: {e}", exc_info=True)
        return False, f"生成销售提案HTML异常: {str(e)}"


async def process_task_async(input_data: Dict, enable_deep_research: bool = True,
                             progress_callback: Optional[Callable] = None,
                             task_id: Optional[str] = None, user_id: Optional[str] = None,
                             diagnosis_id: Optional[int] = None, env: str = "dev") -> Tuple[bool, Dict]:
    """
    异步处理诊断任务，支持中间状态回调。
    返回一个元组 (status, results_dictionary)。
    
    Args:
        input_data: 输入的账号数据
        enable_deep_research: 是否启用深度搜索功能（默认True）
        progress_callback: 进度回调函数
        task_id: 任务ID
        user_id: 用户ID
        diagnosis_id: 诊断ID
        env: 环境标识
    """
    # 初始化Redis管理器
    await initialize_redis_manager()
    
    # 初始化进度跟踪器
    if not task_id:
        task_id = str(int(time.time() * 1000000))  # 生成默认任务ID

    progress_tracker = TaskProgressTracker(
        task_id=task_id,
        user_id=user_id,
        diagnosis_id=diagnosis_id,
        env=env,
        callback=progress_callback,
        enable_deep_research=enable_deep_research,
        verbose=True  # 启用详细模式
    )

    # 创建统一的状态更新器（优先使用新的队列系统）
    async def update_status(status, message):
        if task_id and progress_tracker:
            adapter = create_progress_tracker_adapter(task_id, "diagnosis_core")
            await adapter.update_status_immediate(status, custom_message=message)
        elif progress_tracker:
            progress_tracker.update_status_immediate(status, custom_message=message)

    try:
        # 账号诊断任务
        logger.info("检测到账号诊断任务，开始执行...")
        logger.info(f"深度搜索模式: {'启用' if enable_deep_research else '禁用'}")

        # 初始状态：规划任务 - 使用立即更新确保第一个状态及时发送
        progress_tracker.update_status_immediate(
            TaskStatus.PLANNING,
            custom_message="收到指令，正在规划任务路径…"
        )

        # 1. 执行诊断
        logger.info("Step 1: 开始执行诊断...")
        diag_status, diagnosis_result = await conduct_diagnosis_async(
            input_data,
            use_deep_research=enable_deep_research,
            progress_tracker=progress_tracker
        )

        # 调试信息：检查诊断结果
        logger.info(f"🔍 诊断执行结果 - 状态: {diag_status}, 结果长度: {len(str(diagnosis_result)) if diagnosis_result else 0}")
        
        if not diag_status:
            logger.error(f"🚨 诊断失败，准备返回错误: {diagnosis_result}")
            await update_status(
                TaskStatus.TASK_FAILED,
                f"诊断失败: {diagnosis_result}"
            )
            return False, {"error": f"生成诊断报告失败: {diagnosis_result}"}

        logger.info("✅ Step 1: 诊断执行完毕，准备进入报告生成阶段")

        # 更新状态：开始生成报告 - 使用立即更新确保关键阶段状态及时发送
        logger.info("🔄 正在更新状态到 GENERATING_REPORT...")
        await update_status(
            TaskStatus.GENERATING_REPORT,
            "正在整合分析结果，生成您的专属报告…"
        )
        logger.info("✅ 状态更新完成，准备开始并发生成任务")

        # 2. 优化后的并发生成：分组处理独立任务和依赖任务
        logger.info("🚀 Step 2: 开始优化的并发生成 HTML, JSON和销售提案...")

        max_retries = 2
        html_result = None
        json_result = None
        sales_result = None
        sales_html_result = None
        html_status = False
        json_status = False
        sales_status = False
        sales_html_status = False

        for retry_count in range(max_retries + 1):
            logger.info(f"第 {retry_count + 1} 次尝试生成必需的报告...")

            # 更新进度：报告生成进度 - 从70%开始，为耗时的报告生成预留充足空间
            progress_tracker.update_status(
                TaskStatus.GENERATING_REPORT,
                custom_message="正在整合分析结果，生成您的专属报告…"
            )

            # 第一阶段：并行执行独立的任务 (HTML, JSON, Sales Proposal)
            independent_tasks = []
            independent_task_names = []
            
            task_count = 0
            if not html_status or html_result is None:
                independent_tasks.append(generate_html_async(diagnosis_result, progress_tracker))
                independent_task_names.append("html")
                task_count += 1

            if not json_status or json_result is None:
                independent_tasks.append(generate_json_async(diagnosis_result, progress_tracker))
                independent_task_names.append("json")
                task_count += 1

            if not sales_status or sales_result is None:
                independent_tasks.append(generate_sales_proposal_async(diagnosis_result, progress_tracker))
                independent_task_names.append("sales")
                task_count += 1
            
            # 只更新一次进度，显示当前正在处理的任务数量
            if task_count > 0:
                progress_tracker.update_status(
                    TaskStatus.GENERATING_REPORT,
                    custom_message=f"正在并行生成 {task_count} 个报告模块..."
                )

            # 执行独立任务
            if independent_tasks:
                try:
                    logger.info(f"并行执行 {len(independent_tasks)} 个独立任务: {independent_task_names}")
                    
                    # 创建一个进度监控任务，为耗时的报告生成提供实时反馈
                    async def monitor_progress():
                        for interval in range(40):  # 最多监控4分钟
                            await asyncio.sleep(6)  # 每6秒更新一次
                            # 计算进度增量：从70%到90%，共有20%，分戅4分钟
                            progress_increment = 20 / 40  # 每6秒增加0.5%
                            tracker_progress = getattr(progress_tracker.progress, 'progress_percentage', 70)
                            updated_progress = min(tracker_progress + progress_increment, 90)  # 不超过90%
                            progress_tracker.update_status(
                                TaskStatus.GENERATING_REPORT,
                                custom_message=f"正在生成报告 ({interval*6}s)..."
                            )
                    
                    # 并行执行任务和进度监控
                    async def run_independent_tasks():
                        return await asyncio.gather(*independent_tasks, return_exceptions=True)
                    
                    gather_task = asyncio.create_task(run_independent_tasks())
                    monitor_task = asyncio.create_task(monitor_progress())
                    
                    done, pending = await asyncio.wait(
                        [gather_task, monitor_task],
                        return_when=asyncio.FIRST_COMPLETED,
                        timeout=420  # 7分钟超时
                    )
                    
                    # 取消未完成的任务
                    for task in pending:
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                    
                    # 获取结果
                    if gather_task in done and not gather_task.cancelled():
                        independent_results = await gather_task
                    else:
                        raise asyncio.TimeoutError("任务执行超时")

                    # 处理独立任务结果
                    for i, task_name in enumerate(independent_task_names):
                        if i < len(independent_results):
                            result_item = independent_results[i]
                            if isinstance(result_item, Exception):
                                error_msg = str(result_item)
                                logger.error(f"{task_name} 任务执行异常: {error_msg}")
                                if task_name == "html":
                                    html_status, html_result = False, error_msg
                                elif task_name == "json":
                                    json_status, json_result = False, error_msg
                                elif task_name == "sales":
                                    sales_status, sales_result = False, error_msg
                            else:
                                if isinstance(result_item, tuple) and len(result_item) == 2:
                                    try:
                                        status, result = result_item
                                        if task_name == "html":
                                            html_status, html_result = status, result
                                        elif task_name == "json":
                                            json_status, json_result = status, result
                                        elif task_name == "sales":
                                            sales_status, sales_result = status, result
                                            logger.info(f"Sales proposal 生成成功, 长度: {len(result) if result else 0}")
                                    except (ValueError, TypeError) as e:
                                        logger.error(f"{task_name} 任务结果解析失败: {e}")
                                        if task_name == "html":
                                            html_status, html_result = False, str(e)
                                        elif task_name == "json":
                                            json_status, json_result = False, str(e)
                                        elif task_name == "sales":
                                            sales_status, sales_result = False, str(e)

                except asyncio.TimeoutError:
                    logger.error(f"独立任务执行超时(420秒)，正在处理第{retry_count + 1}次重试")
                    for task_name in independent_task_names:
                        if task_name == "html" and not html_status:
                            html_status, html_result = False, "任务超时，将在下次重试中尝试"
                        elif task_name == "json" and not json_status:
                            json_status, json_result = False, "任务超时，将在下次重试中尝试"
                        elif task_name == "sales" and not sales_status:
                            sales_status, sales_result = False, "任务超时，将在下次重试中尝试"

            # 第二阶段：执行依赖任务 (Sales HTML 依赖于 Sales Proposal)
            if sales_status and (not sales_html_status or sales_html_result is None):
                try:
                    logger.info("执行销售提案HTML生成任务...")
                    # 由于sales proposal已成功，可以直接使用结果而不是重新生成
                    search_results = getattr(progress_tracker, 'search_results', None) if progress_tracker else None
                    # 确保sales_result是字符串类型
                    if not isinstance(sales_result, str):
                        sales_result = str(sales_result) if sales_result is not None else ""
                    sys_prompt, user_prompt = get_sales_proposal_html_prompts(sales_result, search_results)
                    result = await call_ai_async(gemini, sys_prompt, user_prompt, model_name=GEMINI_PRO_MODEL,
                                                 task_name="销售提案HTML", progress_tracker=progress_tracker)

                    if isinstance(result, tuple) and len(result) == 2:
                        sales_html_status, sales_html_result = result
                        logger.info(f"Sales HTML 生成成功: {sales_html_status}")
                    else:
                        logger.error(f"销售提案HTML生成返回了意外的结果格式: {type(result)}")
                        sales_html_status, sales_html_result = False, f"AI调用返回了意外的结果格式: {type(result)}"

                except asyncio.CancelledError:
                    logger.warning("销售提案HTML生成被取消")
                    sales_html_status, sales_html_result = False, "任务被取消"
                except Exception as e:
                    logger.error(f"销售提案HTML生成异常: {e}")
                    sales_html_status, sales_html_result = False, str(e)

            # 检查是否所有任务都成功完成
            if html_status and json_status and sales_status and sales_html_status:
                logger.info("所有必需的报告都已成功生成")
                break

            # 如果是最后一次重试但仍有失败的任务，记录错误
            if retry_count == max_retries:
                failed_tasks = []
                if not html_status:
                    failed_tasks.append("HTML")
                if not json_status:
                    failed_tasks.append("JSON")
                if not sales_status:
                    failed_tasks.append("Sales Proposal")
                if not sales_html_status:
                    failed_tasks.append("Sales HTML")
                
                if failed_tasks:
                    logger.error(f"经过 {max_retries + 1} 次尝试，以下任务仍然失败: {', '.join(failed_tasks)}")
                    # 继续处理结果，即使有失败的任务也要返回已生成的结果

            # 如果不是最后一次重试，等待一段时间再重试
            if retry_count < max_retries:
                wait_time = min(3 + retry_count * 2, 10)  # 递增等待时间，最多10秒
                logger.info(f"等待{wait_time}秒后进行第 {retry_count + 2} 次尝试...")
                await asyncio.sleep(wait_time)

        # 检查最终结果
        if not html_status or html_result is None:
            logger.error("HTML报告生成失败，经过所有重试后仍然无法生成")
            progress_tracker.update_status(
                TaskStatus.TASK_FAILED,
                custom_message="HTML报告生成失败"
            )
            return False, {"error": "HTML报告生成失败，经过所有重试后仍然无法生成"}

        if not json_status or json_result is None:
            logger.error("JSON诊断报告生成失败，经过所有重试后仍然无法生成")
            progress_tracker.update_status(
                TaskStatus.TASK_FAILED,
                custom_message="JSON诊断报告生成失败"
            )
            return False, {"error": "JSON诊断报告生成失败，经过所有重试后仍然无法生成"}

        if not sales_status or sales_result is None:
            logger.error("销售提案生成失败，经过所有重试后仍然无法生成")
            progress_tracker.update_status(
                TaskStatus.TASK_FAILED,
                custom_message="销售提案生成失败"
            )
            return False, {"error": "销售提案生成失败，经过所有重试后仍然无法生成"}

        if not sales_html_status or sales_html_result is None:
            logger.error("销售提案HTML生成失败，经过所有重试后仍然无法生成")
            progress_tracker.update_status(
                TaskStatus.TASK_FAILED,
                custom_message="销售提案HTML生成失败"
            )
            return False, {"error": "销售提案HTML生成失败，经过所有重试后仍然无法生成"}

        logger.info("Step 2: 并发生成任务执行完毕，所有必需报告都已成功生成.")

        # 更新状态：页面渲染 - 直接进入页面渲染阶段，不需要96%的中间步骤
        await update_status(
            TaskStatus.PAGE_RENDERING,
            "报告生成完毕，正在为您加载页面…"
        )

        # 稍微等待一下，模拟页面渲染过程
        await asyncio.sleep(0.3)

        # Assemble the final results into a clean dictionary with native Python types.
        # The serialization process will handle escaping correctly.
        final_results = {
            "diagnosisResult": diagnosis_result, # This is a string
            "diagnosisHtml": html_result,  # This is a string (HTML)
            "diagnosisReport": json_result,  # This is a dictionary
            "marketingProposal": sales_result,  # This is a string (Markdown)
            "marketingProposalHtml": sales_html_result,  # This is a string (HTML)
        }

        # 更新状态：任务完成 - 使用立即更新确保完成状态及时发送
        await update_status(
            TaskStatus.TASK_COMPLETED,
            "诊断完成！"
        )

        return True, final_results

    except Exception as e:
        logger.error(f"处理任务时发生异常: {e}", exc_info=True)

        # 更新状态：任务失败 - 使用立即更新确保失败状态及时发送
        await update_status(
            TaskStatus.TASK_FAILED,
            f"任务执行失败: {str(e)}"
        )

        return False, {"error": f"任务执行失败: {str(e)}"}


# 便捷函数：创建状态回调（保持向后兼容）
def create_redis_callback(redis_client, queue_name: str = "q:diagnosis:response"):
    """创建Redis队列回调函数（向后兼容，推荐使用内置Redis支持）"""

    async def redis_callback(status_info: Dict):
        try:
            # 将状态信息推送到Redis队列
            await redis_client.lpush(queue_name, json.dumps(status_info, ensure_ascii=False))
            logger.debug(f"📤 状态信息已推送到Redis队列: {queue_name}")
        except Exception as e:
            logger.error(f"💥 推送状态信息到Redis队列失败: {e}")

    return redis_callback


def create_enhanced_progress_tracker(task_id: str, user_id: Optional[str] = None,
                                     diagnosis_id: Optional[int] = None, env: str = "dev",
                                     callback: Optional[Callable] = None, redis_manager: Optional[Any] = None,
                                     verbose: bool = False) -> TaskProgressTracker:
    """创建增强版进度跟踪器（深度研究模式）
    
    Args:
        task_id: 任务ID
        user_id: 用户ID  
        diagnosis_id: 诊断ID
        env: 环境标识
        callback: 状态更新回调函数
        redis_manager: Redis管理器
        verbose: 是否启用详细输出
    
    Returns:
        配置为深度研究模式的TaskProgressTracker实例
    """
    return TaskProgressTracker(
        task_id=task_id,
        user_id=user_id,
        diagnosis_id=diagnosis_id,
        env=env,
        callback=callback,
        enable_deep_research=True,
        redis_manager=redis_manager,
        verbose=verbose
    )


def create_basic_progress_tracker(task_id: str, user_id: Optional[str] = None,
                                  diagnosis_id: Optional[int] = None, env: str = "dev",
                                  callback: Optional[Callable] = None) -> TaskProgressTracker:
    """创建基础版进度跟踪器（保持向后兼容）
    
    Args:
        task_id: 任务ID
        user_id: 用户ID
        diagnosis_id: 诊断ID
        env: 环境标识
        callback: 状态更新回调函数
    
    Returns:
        配置为基础模式的TaskProgressTracker实例（向后兼容）
    """
    return TaskProgressTracker(
        task_id=task_id,
        user_id=user_id,
        diagnosis_id=diagnosis_id,
        env=env,
        callback=callback,
        enable_deep_research=False
    )


def create_websocket_callback(websocket_manager, connection_id: str):
    """创建WebSocket回调函数"""

    async def websocket_callback(status_info: Dict):
        try:
            # 通过WebSocket发送状态信息
            await websocket_manager.send_to_connection(connection_id, status_info)
            logger.debug(f"状态信息已发送到WebSocket连接: {connection_id}")
        except Exception as e:
            logger.error(f"发送状态信息到WebSocket失败: {e}")

    return websocket_callback


def create_console_callback():
    """创建控制台输出回调函数（用于调试）"""

    async def console_callback(status_info: Dict):
        try:
            task_info = status_info.get("taskInfo", {})
            status = task_info.get("aiTaskStatus", "UNKNOWN")
            progress = task_info.get("aiTaskProgress", 1)
            message = task_info.get("aiTaskMsgCN", "")

            print(f"[{status}] {progress}% - {message}")
        except Exception as e:
            logger.error(f"控制台回调失败: {e}")

    return console_callback
