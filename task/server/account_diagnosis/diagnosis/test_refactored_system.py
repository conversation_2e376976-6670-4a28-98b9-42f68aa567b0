#!/usr/bin/env python3
"""
重构后系统的测试脚本

简单的测试脚本，用于验证重构后的诊断系统是否正常工作。
包含：
- Redis连接测试
- 状态管理器测试
- 诊断引擎测试
- 端到端流程测试
"""

import asyncio
import json
import logging
import os
import sys
import time

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..')))

# 将当前目录添加到系统路径以支持相对导入
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from core.redis_client import get_redis_client
from core.simple_status_manager import SimpleStatusManager
from core.diagnosis_engine import SimpleDiagnosisEngine
from utils.response_builder import ResponseBuilder
from utils.progress_tracker import ProgressTracker


# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class TestSuite:
    """测试套件"""
    
    def __init__(self):
        self.redis_client = get_redis_client()
        self.status_manager = None
        self.diagnosis_engine = SimpleDiagnosisEngine()
        self.response_builder = ResponseBuilder("test")
        
        # 测试数据
        self.test_input_data = {
            "taskInfo": {
                "taskId": "test_" + str(int(time.time())),
                "userId": "test_user_123",
                "diagnosisId": 1
            },
            "accountInfo": {
                "nickname": "测试账号",
                "followers": 1000,
                "liked": 5000,
                "collected": 1500,
                "posts": 50,
                "comments": 300
            },
            "noteList": [
                {
                    "title": "测试笔记1",
                    "liked": 100,
                    "collected": 50,
                    "comments": 20
                },
                {
                    "title": "测试笔记2", 
                    "liked": 150,
                    "collected": 75,
                    "comments": 30
                }
            ],
            "marketingGoal": "涨粉提升",
            "industry": "美妆护肤",
            "mode": "basic"
        }
    
    async def test_redis_connection(self):
        """测试Redis连接"""
        logger.info("🔍 测试Redis连接...")
        
        try:
            # 健康检查
            health = await self.redis_client.health_check()
            if health:
                logger.info("✅ Redis连接正常")
                return True
            else:
                logger.error("❌ Redis连接失败")
                return False
        except Exception as e:
            logger.error(f"❌ Redis连接测试异常: {e}")
            return False
    
    async def test_status_manager(self):
        """测试状态管理器"""
        logger.info("🔍 测试状态管理器...")
        
        try:
            # 创建状态管理器
            self.status_manager = SimpleStatusManager("test:output:queue", "test")
            
            # 创建进度跟踪器
            task_id = "test_status_" + str(int(time.time()))
            tracker = self.status_manager.create_progress_tracker(task_id, "test_user", 1)
            
            # 测试进度更新
            await tracker.update_progress(25, "测试进度更新")
            await tracker.update_progress(50, "继续测试")
            await tracker.update_progress(100, "测试完成")
            
            logger.info("✅ 状态管理器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 状态管理器测试失败: {e}")
            return False
    
    async def test_response_builder(self):
        """测试响应构建器"""
        logger.info("🔍 测试响应构建器...")
        
        try:
            # 测试进度响应
            progress_response = self.response_builder.build_progress_response(
                task_id="test_123",
                progress=50,
                user_id="user_123",
                diagnosis_id=1,
                custom_msg_cn="测试中..."
            )
            
            assert "taskInfo" in progress_response
            assert progress_response["taskInfo"]["aiTaskProgress"] == 50
            
            # 测试成功响应
            success_response = self.response_builder.build_success_response(
                task_id="test_123",
                user_id="user_123",
                diagnosis_id=1,
                diagnosis_html="<html><body>测试</body></html>",
                diagnosis_report={"summary": "测试摘要"}
            )
            
            assert "taskInfo" in success_response
            assert success_response["taskInfo"]["aiTaskProgress"] == 100
            assert "diagnosisHtml" in success_response
            
            # 测试错误响应
            error_response = self.response_builder.build_error_response(
                task_id="test_123",
                error_msg="测试错误",
                user_id="user_123"
            )
            
            assert "taskInfo" in error_response
            assert error_response["taskInfo"]["aiTaskStatus"] == "FAILED"
            
            logger.info("✅ 响应构建器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 响应构建器测试失败: {e}")
            return False
    
    async def test_diagnosis_engine_basic(self):
        """测试诊断引擎基础功能"""
        logger.info("🔍 测试诊断引擎基础功能...")
        
        try:
            # 创建模拟进度跟踪器
            class MockProgressTracker:
                async def update_progress(self, progress, message):
                    logger.info(f"进度更新: {progress}% - {message}")
            
            mock_tracker = MockProgressTracker()
            
            # 测试诊断提示词构建
            engine = self.diagnosis_engine
            account_data = {
                "account": self.test_input_data["accountInfo"],
                "noteList": self.test_input_data["noteList"],
                "industry": self.test_input_data["industry"],
                "marketingGoal": 4  # 涨粉提升
            }
            
            prompt = engine._build_diagnosis_prompt(account_data)
            assert len(prompt) > 100
            assert "测试账号" in prompt
            
            # 测试JSON结构确保
            test_json = {"summary": "测试"}
            complete_json = engine._ensure_complete_json(test_json)
            assert "summary" in complete_json
            assert "tags" in complete_json
            assert "suggestion" in complete_json
            
            # 测试HTML验证
            valid_html = "<html><body><div>测试</div></body></html>"
            invalid_html = "not html"
            
            assert engine._validate_html(valid_html) == True
            assert engine._validate_html(invalid_html) == False
            
            logger.info("✅ 诊断引擎基础功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 诊断引擎基础功能测试失败: {e}")
            return False
    
    async def test_queue_operations(self):
        """测试队列操作"""
        logger.info("🔍 测试队列操作...")
        
        try:
            test_queue = "test:queue:" + str(int(time.time()))
            test_data = {"test": "data", "timestamp": time.time()}
            
            # 测试推送
            push_success = await self.redis_client.push_to_queue(test_queue, test_data)
            assert push_success == True
            
            # 测试弹出
            popped_data = await self.redis_client.pop_from_queue(test_queue, timeout=5)
            assert popped_data is not None
            assert popped_data["test"] == "data"
            
            # 测试空队列
            empty_data = await self.redis_client.pop_from_queue(test_queue, timeout=1)
            assert empty_data is None
            
            logger.info("✅ 队列操作测试通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 队列操作测试失败: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始重构系统测试...")
        logger.info("=" * 50)
        
        test_results = []
        
        # 运行各项测试
        tests = [
            ("Redis连接", self.test_redis_connection),
            ("队列操作", self.test_queue_operations),
            ("响应构建器", self.test_response_builder),
            ("状态管理器", self.test_status_manager),
            ("诊断引擎基础", self.test_diagnosis_engine_basic),
        ]
        
        for test_name, test_func in tests:
            try:
                result = await test_func()
                test_results.append((test_name, result))
                if result:
                    logger.info(f"✅ {test_name}: 通过")
                else:
                    logger.error(f"❌ {test_name}: 失败")
            except Exception as e:
                logger.error(f"❌ {test_name}: 异常 - {e}")
                test_results.append((test_name, False))
            
            # 测试间隔
            await asyncio.sleep(1)
        
        # 汇总结果
        logger.info("=" * 50)
        logger.info("📊 测试结果汇总:")
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"  {test_name}: {status}")
            if result:
                passed += 1
        
        logger.info("=" * 50)
        logger.info(f"📈 总计: {passed}/{total} 通过")
        
        if passed == total:
            logger.info("🎉 所有测试通过！重构系统基础功能正常")
        else:
            logger.warning(f"⚠️  有 {total - passed} 项测试失败，请检查相关组件")
        
        # 清理资源
        try:
            await self.redis_client.cleanup()
            if self.status_manager:
                await self.status_manager.cleanup()
        except Exception as e:
            logger.warning(f"清理资源时出现警告: {e}")
        
        return passed == total


async def main():
    """主函数"""
    print("""
🔧 重构后诊断系统测试工具
============================
这个测试工具将验证重构后的诊断系统各组件是否正常工作。

包含测试项目：
- Redis连接测试
- 队列操作测试  
- 响应构建器测试
- 状态管理器测试
- 诊断引擎基础功能测试

============================
开始测试...
""")
    
    test_suite = TestSuite()
    
    try:
        success = await test_suite.run_all_tests()
        if success:
            print("\n🎉 测试完成！重构后的系统基础功能正常")
            sys.exit(0)
        else:
            print("\n⚠️  部分测试失败，请检查日志信息")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试执行异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())