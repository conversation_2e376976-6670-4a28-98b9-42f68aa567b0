#!/usr/bin/env python3
"""
重构后的诊断服务入口点

简化的服务启动脚本，支持：
- 基础诊断模式
- 深度研究模式  
- 命令行参数配置
- 优雅的错误处理
"""

import argparse
import asyncio
import logging
import os
import sys

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..')))

# 将当前目录添加到系统路径以支持相对导入
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from services.diagnosis_service import DiagnosisService


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler('logs/diagnosis_service.log', encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return logging.getLogger(__name__)


async def run_service(mode: str, max_concurrent_tasks: int, env: str, verbose: bool):
    """运行诊断服务"""
    logger = setup_logging(verbose)
    
    try:
        logger.info("=" * 60)
        logger.info("启动重构后的诊断服务")
        logger.info("=" * 60)
        logger.info(f"诊断模式: {mode}")
        logger.info(f"最大并发任务: {max_concurrent_tasks}")
        logger.info(f"环境: {env}")
        logger.info(f"详细日志: {verbose}")
        logger.info("=" * 60)
        
        # 创建诊断服务实例
        service = DiagnosisService(
            max_concurrent_tasks=max_concurrent_tasks,
            env=env,
            default_mode=mode
        )
        
        # 启动服务
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，服务正在关闭...")
    except Exception as e:
        logger.error(f"服务运行异常: {e}", exc_info=True)
        raise


def create_service_main(mode: str = "basic", max_concurrent_tasks: int = 3, 
                       env: str = "production", verbose: bool = False):
    """创建服务主函数"""
    def main():
        try:
            asyncio.run(run_service(mode, max_concurrent_tasks, env, verbose))
        except KeyboardInterrupt:
            pass
        except Exception as e:
            print(f"服务启动失败: {e}")
            sys.exit(1)
    
    return main


def main():
    """主函数 - 命令行入口"""
    parser = argparse.ArgumentParser(
        description="重构后的账号诊断服务",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                          # 使用默认配置启动
  %(prog)s --mode deep_research     # 启用深度研究模式
  %(prog)s --concurrent 8           # 设置8个并发任务
  %(prog)s --env test --verbose     # 测试环境 + 详细日志
        """
    )
    
    parser.add_argument(
        "--mode",
        choices=["basic", "deep_research"],
        default="basic",
        help="诊断模式: basic(基础) 或 deep_research(深度研究), 默认: basic"
    )
    
    parser.add_argument(
        "--concurrent",
        type=int,
        default=3,
        metavar="N",
        help="最大并发任务数, 默认: 3"
    )
    
    parser.add_argument(
        "--env",
        choices=["production", "test", "dev"],
        default="production",
        help="运行环境, 默认: production"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细日志输出"
    )
    
    parser.add_argument(
        "--version",
        action="version",
        version="重构诊断服务 v2.0.0"
    )
    
    args = parser.parse_args()
    
    # 验证参数
    if args.concurrent < 1 or args.concurrent > 20:
        parser.error("并发任务数必须在1-20之间")
    
    print(f"""
🚀 重构后的账号诊断服务 v2.0.0
================================
诊断模式: {args.mode}
并发任务: {args.concurrent}
运行环境: {args.env}
详细日志: {args.verbose}
================================
正在启动服务...
""")
    
    # 创建并运行服务
    service_main = create_service_main(
        mode=args.mode,
        max_concurrent_tasks=args.concurrent,
        env=args.env,
        verbose=args.verbose
    )
    
    service_main()


if __name__ == "__main__":
    main()