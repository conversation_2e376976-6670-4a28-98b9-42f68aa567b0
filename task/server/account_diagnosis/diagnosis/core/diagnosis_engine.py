"""
简化的诊断引擎

重构后的诊断引擎，简化了复杂的逻辑，保留核心功能：
- 账号基础诊断
- HTML报告生成  
- JSON报告生成
- 营销建议生成
- 清晰的错误处理
"""

import asyncio
import json
import logging
import os
import sys
from typing import Dict, Any, Tuple, Optional

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..', '..')))

from task import GEMINI_PRO_MODEL
from task.lib.call_claude import gemini, gpt, claude
from task.lib.prompt_utils import (
    clean_html_output, 
    clean_markdown_output,
    get_diagnosis_prompt,
    get_html_generation_prompts,
    get_json_generation_prompts,
    get_sales_proposal_prompts,
    get_sales_proposal_html_prompts
)
from task.lib.json_utils_enhanced import enhanced_validate_and_fix_ai_json


class SimpleDiagnosisEngine:
    """简化的诊断引擎"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    async def call_ai(self, ai_function, sys_prompt: str, user_prompt: str,
                     model_name: str = None, task_name: str = "AI任务",
                     max_retries: int = 2, json_mode: bool = False) -> Tuple[bool, Any]:
        """简化的AI调用方法"""
        
        for attempt in range(max_retries + 1):
            try:
                self.logger.info(f"执行 {task_name} - 尝试 {attempt + 1}/{max_retries + 1}")
                
                # 在线程池中执行同步调用
                loop = asyncio.get_event_loop()
                
                def sync_call():
                    if ai_function == gpt:
                        if json_mode:
                            return ai_function(sys_prompt, user_prompt, json_schema='flexible', model=model_name)
                        else:
                            return ai_function(sys_prompt, user_prompt, model=model_name)
                    elif ai_function == gemini:
                        response = ai_function(sys_prompt, user_prompt, model=model_name)
                        if isinstance(response, tuple) and len(response) >= 2:
                            return response[0], response[1]  # status, result
                        return response
                    elif ai_function == claude:
                        return ai_function(sys_prompt, user_prompt)
                    else:
                        return False, f"不支持的AI函数: {ai_function}"
                
                result = await loop.run_in_executor(None, sync_call)
                
                if isinstance(result, tuple) and len(result) == 2:
                    status, response = result
                    if status:
                        self.logger.info(f"{task_name} 成功完成")
                        return True, response
                    else:
                        self.logger.warning(f"{task_name} 失败: {response}")
                        if attempt < max_retries:
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                        else:
                            return False, response
                else:
                    error_msg = f"AI返回格式异常: {type(result)}"
                    self.logger.error(error_msg)
                    return False, error_msg
                    
            except Exception as e:
                error_msg = f"{task_name} 执行异常: {str(e)}"
                self.logger.error(error_msg)
                if attempt < max_retries:
                    await asyncio.sleep(2 ** attempt)
                else:
                    return False, error_msg
        
        return False, f"{task_name} 最终失败"
    
    async def conduct_diagnosis(self, input_data: Dict[str, Any], 
                               progress_tracker) -> Tuple[bool, str]:
        """执行账号诊断"""
        try:
            # 验证输入数据
            required_fields = ["accountInfo", "noteList", "marketingGoal"]
            for field in required_fields:
                if field not in input_data:
                    return False, f"缺少必需字段: {field}"
            
            # 更新进度 - 诊断阶段进度分配（25%-35%）
            await progress_tracker.update_progress(27, "正在分析您的账号数据与内容表现...")
            
            # 准备诊断数据
            goal_dict = {"引流私域": 1, "带货变现": 2, "品牌曝光": 3, "涨粉提升": 4}
            account_data = {
                "account": input_data["accountInfo"],
                "noteList": input_data.get("noteList", [])[:100],
                "industry": input_data.get("industry") if input_data.get("industry") != 'empty' else None,
                "marketingGoal": goal_dict.get(input_data["marketingGoal"], 1)
            }
            
            # 更新进度
            await progress_tracker.update_progress(32, "正在评估账号优势与待提升空间...")
            
            # 使用标准化的诊断提示模板
            user_prompt = get_diagnosis_prompt(account_data)
            
            # 保持现有的系统提示
            sys_prompt = """你是一位资深的小红书运营策略专家，拥有多年针对不同行业账号进行诊断和优化的实战经验。
请对目标账号进行深度分析并提供具有高度可操作性的优化方案。回复只能包含方案本身，不要有任何介绍性的开场白，要求输出要用中文。"""
            
            # 调用AI进行诊断
            success, result = await self.call_ai(
                gpt, sys_prompt, user_prompt,
                model_name="gpt-4.1",
                task_name="账号诊断"
            )
            
            if success:
                await progress_tracker.update_progress(35, "账号诊断分析完成，正在准备报告...")
                return True, str(result)
            else:
                return False, f"诊断失败: {result}"
                
        except Exception as e:
            self.logger.error(f"诊断执行异常: {e}", exc_info=True)
            return False, f"诊断异常: {str(e)}"
    
    async def generate_html_report(self, diagnosis_result: str, 
                                 progress_tracker=None) -> Tuple[bool, str]:
        """生成HTML诊断报告"""
        try:
            # 使用标准化的HTML生成提示模板
            sys_prompt, user_prompt = get_html_generation_prompts(diagnosis_result)
            
            success, result = await self.call_ai(
                gemini, sys_prompt, user_prompt,
                model_name=GEMINI_PRO_MODEL,
                task_name="HTML报告生成"
            )
            
            if success:
                cleaned_html = clean_html_output(str(result))
                if self._validate_html(cleaned_html):
                    return True, cleaned_html
                else:
                    return False, "生成的HTML无效"
            else:
                return False, f"HTML生成失败: {result}"
                
        except Exception as e:
            self.logger.error(f"HTML生成异常: {e}", exc_info=True)
            return False, f"HTML生成异常: {str(e)}"
    
    async def generate_json_report(self, diagnosis_result: str,
                                 progress_tracker=None) -> Tuple[bool, Dict[str, Any]]:
        """生成JSON诊断报告"""
        try:
            # 使用标准化的JSON生成提示模板
            sys_prompt, user_prompt = get_json_generation_prompts(diagnosis_result)
            
            success, result = await self.call_ai(
                gpt, sys_prompt, user_prompt,
                model_name="gpt-4o-mini",
                task_name="JSON报告生成",
                json_mode=True
            )
            
            if success:
                # 验证和修复JSON - 增强后处理
                if isinstance(result, str):
                    # 清理可能的markdown代码块包装
                    cleaned_result = self._clean_json_response(result)
                    json_success, fixed_json = enhanced_validate_and_fix_ai_json(cleaned_result)
                    if json_success:
                        return True, self._ensure_complete_json(fixed_json)
                    else:
                        # 尝试直接解析原始结果
                        try:
                            direct_json = json.loads(cleaned_result)
                            return True, self._ensure_complete_json(direct_json)
                        except json.JSONDecodeError:
                            return False, f"JSON解析失败: {fixed_json}"
                elif isinstance(result, dict):
                    return True, self._ensure_complete_json(result)
                else:
                    return False, f"AI返回格式异常: {type(result)}"
            else:
                return False, f"JSON生成失败: {result}"
                
        except Exception as e:
            self.logger.error(f"JSON生成异常: {e}", exc_info=True)
            return False, f"JSON生成异常: {str(e)}"
    
    async def generate_marketing_proposal(self, diagnosis_result: str,
                                        progress_tracker=None) -> Tuple[bool, str]:
        """生成营销建议"""
        try:
            # 使用标准化的营销建议生成提示模板
            sys_prompt, user_prompt = get_sales_proposal_prompts(diagnosis_result)
            
            success, result = await self.call_ai(
                gpt, sys_prompt, user_prompt,
                model_name="gpt-4.1",
                task_name="营销建议生成"
            )
            
            if success:
                cleaned_result = clean_markdown_output(str(result))
                if len(cleaned_result.strip()) > 50:
                    return True, cleaned_result
                else:
                    return False, "生成的营销建议内容不足"
            else:
                return False, f"营销建议生成失败: {result}"
                
        except Exception as e:
            self.logger.error(f"营销建议生成异常: {e}", exc_info=True)
            return False, f"营销建议生成异常: {str(e)}"
    
    async def generate_marketing_proposal_html(self, marketing_proposal: str,
                                             progress_tracker) -> Tuple[bool, str]:
        """生成营销建议HTML版本"""
        try:
            await progress_tracker.update_progress(92, "正在设计精美的营销策略页面...")
            
            # 使用标准化的营销建议HTML生成提示模板
            sys_prompt, user_prompt = get_sales_proposal_html_prompts(marketing_proposal)
            
            await progress_tracker.update_progress(94, "正在创建视觉化营销方案...")
            
            success, result = await self.call_ai(
                gemini, sys_prompt, user_prompt,
                model_name=GEMINI_PRO_MODEL,
                task_name="营销建议HTML生成"
            )
            
            if success:
                await progress_tracker.update_progress(96, "营销方案设计完成，即将呈现...")
                cleaned_html = clean_html_output(str(result))
                if self._validate_html(cleaned_html):
                    # 不要在这里更新进度，让上层服务统一处理完成状态
                    return True, cleaned_html
                else:
                    return False, "生成的营销建议HTML无效"
            else:
                return False, f"营销建议HTML生成失败: {result}"
                
        except Exception as e:
            self.logger.error(f"营销建议HTML生成异常: {e}", exc_info=True)
            return False, f"营销建议HTML生成异常: {str(e)}"
    
    def _build_diagnosis_prompt(self, account_data: Dict[str, Any]) -> str:
        """构建诊断提示词"""
        account = account_data["account"]
        note_list = account_data["noteList"]
        industry = account_data.get("industry", "未指定")
        marketing_goal = account_data["marketingGoal"]
        
        goal_names = {1: "引流私域", 2: "带货变现", 3: "品牌曝光", 4: "涨粉提升"}
        goal_name = goal_names.get(marketing_goal, "未知目标")
        
        prompt = f"""请对以下小红书账号进行全面诊断分析：

账号基本信息：
- 昵称：{account.get('nickname', 'Unknown')}
- 粉丝数：{account.get('followers', 0)}
- 获赞数：{account.get('liked', 0)}
- 收藏数：{account.get('collected', 0)}
- 笔记数：{account.get('posts', 0)}
- 行业：{industry}
- 营销目标：{goal_name}

近期笔记表现："""
        
        # 添加笔记信息
        for i, note in enumerate(note_list[:10]):  # 只取前10条笔记
            prompt += f"""

笔记{i+1}：
- 标题：{note.get('title', 'Unknown')}
- 点赞：{note.get('liked', 0)}
- 收藏：{note.get('collected', 0)}
- 评论：{note.get('comments', 0)}"""
        
        prompt += """

请提供详细的诊断分析，包括：
1. 账号现状分析
2. 内容质量评估
3. 互动数据分析
4. 存在的主要问题
5. 具体的优化建议
6. 执行建议和时间安排

请给出专业、具体、可操作的分析和建议。"""
        
        return prompt
    
    def _clean_json_response(self, response: str) -> str:
        """清理AI返回的JSON响应，移除可能的markdown包装和其他格式问题"""
        if not response:
            return response
        
        # 移除可能的markdown代码块包装
        response = response.strip()
        
        # 移除 ```json 和 ``` 包装
        if response.startswith('```json'):
            response = response[7:]
        elif response.startswith('```'):
            response = response[3:]
        
        if response.endswith('```'):
            response = response[:-3]
        
        # 移除前后空白字符
        response = response.strip()
        
        # 查找第一个 { 和最后一个 } 来提取纯JSON部分
        first_brace = response.find('{')
        last_brace = response.rfind('}')
        
        if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
            response = response[first_brace:last_brace + 1]
        
        return response
    
    def _validate_html(self, html_content: str) -> bool:
        """验证HTML内容"""
        if not html_content or len(html_content.strip()) < 100:
            return False
        
        # 检查基本HTML结构
        required_tags = ['<html', '<body', '<div']
        return any(tag in html_content.lower() for tag in required_tags)
    
    def _ensure_complete_json(self, json_data: Dict[str, Any]) -> Dict[str, Any]:
        """确保JSON包含所有必需字段"""
        template = {
            "summary": "",
            "tags": [
                {"dimension": "当前状态", "status": "待分析"},
                {"dimension": "增长潜力", "status": "待分析"},
                {"dimension": "关注重点", "status": "待分析"}
            ],
            "bottleneck": {
                "title": "账号瓶颈",
                "area": [
                    {"title": "内容质量", "title_en": "CONTENT_QUALITY", "des": "待分析"},
                    {"title": "互动效果", "title_en": "ENGAGEMENT", "des": "待分析"}
                ]
            },
            "content_analysis": {"title": "内容分析", "des": "待分析", "title_en": "CONTENT_ANALYSIS"},
            "ip_analysis": {"title": "IP分析", "des": "待分析", "title_en": "IP_ANALYSIS"},
            "optimize_dimension": {
                "title": "可优化维度",
                "areas": [
                    {"name": "定位", "question": "待分析"},
                    {"name": "内容", "question": "待分析"}
                ]
            },
            "suggestion": [
                {"title": "优化建议", "content": ["待分析"]}
            ]
        }
        
        # 合并数据，确保所有字段都存在
        def merge_dict(template_dict, data_dict):
            result = {}
            for key, template_value in template_dict.items():
                if key in data_dict and data_dict[key] is not None:
                    if isinstance(template_value, dict) and isinstance(data_dict[key], dict):
                        result[key] = merge_dict(template_value, data_dict[key])
                    else:
                        result[key] = data_dict[key]
                else:
                    result[key] = template_value
            return result
        
        return merge_dict(template, json_data if isinstance(json_data, dict) else {})