"""
简化的Redis客户端

替代复杂的Redis管理器，提供简单直接的Redis操作。
支持Redis Cluster配置，与原系统保持兼容。
主要特点：
- Redis Cluster支持
- 基本的重试机制
- 清晰的错误处理
- 与原系统配置兼容
"""

import asyncio
import json
import logging
import ssl
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager
import redis.asyncio as redis

# 导入安全序列化函数
try:
    import sys
    import os
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    from task.lib.json_utils_enhanced import safe_redis_serialize_with_validation
except ImportError:
    # 如果导入失败，使用双重序列化作为备用
    def safe_redis_serialize_with_validation(data):
        return json.dumps(json.dumps(data, ensure_ascii=False), ensure_ascii=False)

# 导入Redis配置
try:
    import sys
    import os
    # 添加项目根目录到路径
    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..'))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)
    
    from config import REDIS_CLUSTER_CONFIG
except ImportError:
    # 提供默认配置（与原系统相同）
    REDIS_CLUSTER_CONFIG = {
        'host': 'clustercfg.dev-dao.vahlan.apse1.cache.amazonaws.com',
        'port': 6379,
        'password': 'glpat-pyyX2SHeyGkadnmxaee',
        'ssl': True,
        'decode_responses': True,
        'socket_connect_timeout': 30,
        'socket_timeout': 60,
        'health_check_interval': 30
    }


class SimpleRedisClient:
    """简化的Redis客户端，支持Redis Cluster"""
    
    def __init__(self, use_cluster: bool = True):
        self.use_cluster = use_cluster
        self.logger = logging.getLogger(__name__)
        self._cluster = None
        self._pool = None
        self._max_retries = 3
    
    async def _get_or_create_cluster(self):
        """获取或创建Redis集群连接"""
        if self._cluster is None:
            redis_config = REDIS_CLUSTER_CONFIG.copy()
            
            self._cluster = redis.RedisCluster(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config['password'],
                ssl=redis_config.get('ssl', False),
                ssl_cert_reqs=ssl.CERT_NONE if redis_config.get('ssl') else None,
                ssl_ca_certs=None if redis_config.get('ssl') else None,
                decode_responses=redis_config.get('decode_responses', True),
                socket_connect_timeout=redis_config.get('socket_connect_timeout', 30),
                socket_timeout=redis_config.get('socket_timeout', 60),
                max_connections=20,
                require_full_coverage=False,
                health_check_interval=redis_config.get('health_check_interval', 30)
            )
        
        return self._cluster
    
    async def _create_connection_with_retry(self):
        """带重试的Redis连接创建"""
        last_exception = None
        
        for attempt in range(self._max_retries):
            try:
                if self.use_cluster:
                    cluster = await self._get_or_create_cluster()
                    await cluster.ping()
                    self.logger.debug(f"Redis Cluster连接成功 (attempt {attempt + 1})")
                    return cluster
                else:
                    # 单机Redis连接（备用）
                    conn = redis.Redis(
                        host='localhost',
                        port=6379,
                        decode_responses=True
                    )
                    await conn.ping()
                    self.logger.debug(f"Redis单机连接成功 (attempt {attempt + 1})")
                    return conn
                    
            except Exception as e:
                last_exception = e
                if attempt < self._max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 指数退避
                    error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}"
                    self.logger.warning(f"Redis连接失败 (attempt {attempt + 1}): {error_msg}, {wait_time}秒后重试")
                    await asyncio.sleep(wait_time)
                    
                    # 重置集群连接以尝试新连接
                    if self._cluster:
                        try:
                            await self._cluster.close()
                        except:
                            pass
                        self._cluster = None
        
        # 所有重试都失败
        error_msg = f"{type(last_exception).__name__}: {str(last_exception)}" if str(last_exception) else f"{type(last_exception).__name__}"
        self.logger.error(f"创建Redis连接失败(已重试{self._max_retries}次): {error_msg}")
        raise last_exception
    
    @asynccontextmanager
    async def get_connection(self):
        """获取Redis连接的上下文管理器"""
        try:
            connection = await self._create_connection_with_retry()
            yield connection
        except Exception as e:
            self.logger.warning(f"获取Redis连接失败: {e}")
            raise
    
    async def push_to_queue(self, queue_name: str, data: Dict[str, Any], 
                           max_retries: int = 3) -> bool:
        """推送数据到队列"""
        json_data = safe_redis_serialize_with_validation(data)
        
        for attempt in range(max_retries):
            try:
                async with self.get_connection() as conn:
                    await conn.rpush(queue_name, json_data)
                    queue_length = await conn.llen(queue_name)
                    self.logger.info(f"成功推送数据到队列 {queue_name}，队列长度: {queue_length}")
                    return True
            except Exception as e:
                self.logger.warning(f"推送到队列失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)  # 指数退避
        
        self.logger.error(f"推送到队列最终失败: {queue_name}")
        return False
    
    async def pop_from_queue(self, queue_name: str, timeout: int = 30) -> Optional[Dict[str, Any]]:
        """从队列弹出数据"""
        try:
            async with self.get_connection() as conn:
                result = await conn.blpop(queue_name, timeout=timeout)
                if result:
                    _, data = result
                    # 确保data是字符串，然后解析为JSON
                    if isinstance(data, bytes):
                        data = data.decode('utf-8')
                    elif not isinstance(data, str):
                        self.logger.warning(f"收到非字符串数据类型: {type(data)}, 值: {data}")
                        return None
                    
                    try:
                        # 优化JSON解析 - 减少不必要的重复解析，参考BaseAsyncService逻辑
                        if isinstance(data, str):
                            parsed_data = json.loads(data)
                            # 检查是否需要二次解析（避免不必要的解析）
                            if isinstance(parsed_data, str):
                                parsed_data = json.loads(parsed_data)
                        else:
                            parsed_data = data
                            
                        if not isinstance(parsed_data, dict):
                            raise ValueError("数据解析后不是字典格式")
                            
                        self.logger.debug(f"成功解析队列数据: {type(parsed_data)}")
                        return parsed_data
                    except (json.JSONDecodeError, ValueError) as e:
                        self.logger.error(f"JSON解析失败: {e}, 原始数据: {data[:200]}...")
                        return None
                return None
        except Exception as e:
            self.logger.error(f"从队列弹出数据失败: {e}")
            return None
    
    async def set_key(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """设置键值"""
        try:
            async with self.get_connection() as conn:
                await conn.set(key, value, ex=ex)
                return True
        except Exception as e:
            self.logger.error(f"设置键值失败: {e}")
            return False
    
    async def get_key(self, key: str) -> Optional[str]:
        """获取键值"""
        try:
            async with self.get_connection() as conn:
                return await conn.get(key)
        except Exception as e:
            self.logger.error(f"获取键值失败: {e}")
            return None
    
    async def delete_key(self, key: str) -> bool:
        """删除键"""
        try:
            async with self.get_connection() as conn:
                await conn.delete(key)
                return True
        except Exception as e:
            self.logger.error(f"删除键失败: {e}")
            return False
    
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            async with self.get_connection() as conn:
                await conn.ping()
                return True
        except Exception as e:
            self.logger.error(f"Redis健康检查失败: {e}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self._cluster:
            try:
                await self._cluster.close()
            except:
                pass
            self._cluster = None
        if self._pool:
            await self._pool.disconnect()
            self._pool = None


# 全局Redis客户端实例
_redis_client = None

def get_redis_client() -> SimpleRedisClient:
    """获取全局Redis客户端"""
    global _redis_client
    if _redis_client is None:
        _redis_client = SimpleRedisClient(use_cluster=True)
    return _redis_client