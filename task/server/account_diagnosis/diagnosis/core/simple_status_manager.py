"""
简化的状态管理器

统一的状态管理实现，替代复杂的多层状态管理系统。
特点：
- 简单的状态更新接口
- 自动Redis队列输出  
- 清晰的错误处理
- 统一的进度跟踪
"""

import logging
from typing import Optional, Dict, Any
from .redis_client import get_redis_client
from ..utils.progress_tracker import ProgressTracker
from ..utils.response_builder import ResponseBuilder


class SimpleStatusManager:
    """简化的状态管理器"""
    
    def __init__(self, output_queue: str, env: str = "production"):
        self.output_queue = output_queue
        self.env = env
        self.redis_client = get_redis_client()
        self.response_builder = ResponseBuilder(env)
        self.logger = logging.getLogger(__name__)
        self._active_trackers = {}  # task_id -> ProgressTracker
    
    def create_progress_tracker(self, task_id: str, user_id: Optional[str] = None,
                               diagnosis_id: Optional[int] = None) -> ProgressTracker:
        """创建进度跟踪器"""
        tracker = ProgressTracker(task_id, user_id, diagnosis_id, self.env)
        
        # 添加Redis输出回调
        async def redis_callback(response: Dict[str, Any]):
            await self._send_to_redis(response)
        
        tracker.add_callback(redis_callback)
        self._active_trackers[task_id] = tracker
        
        self.logger.info(f"创建进度跟踪器: {task_id}")
        return tracker
    
    def get_progress_tracker(self, task_id: str) -> Optional[ProgressTracker]:
        """获取现有的进度跟踪器"""
        return self._active_trackers.get(task_id)
    
    async def _send_to_redis(self, response: Dict[str, Any]) -> bool:
        """发送响应到Redis队列"""
        try:
            success = await self.redis_client.push_to_queue(self.output_queue, response)
            if success:
                self.logger.debug(f"成功发送响应到Redis队列: {self.output_queue}")
            else:
                self.logger.error(f"发送响应到Redis队列失败: {self.output_queue}")
            return success
        except Exception as e:
            self.logger.error(f"发送响应到Redis时发生异常: {e}")
            return False
    
    async def send_progress(self, task_id: str, progress: int,
                           custom_msg_cn: Optional[str] = None,
                           custom_msg_en: Optional[str] = None):
        """发送进度更新"""
        tracker = self.get_progress_tracker(task_id)
        if tracker:
            await tracker.update_progress(progress, custom_msg_cn, custom_msg_en)
        else:
            self.logger.warning(f"未找到任务的进度跟踪器: {task_id}")
    
    async def send_completion(self, task_id: str, 
                             diagnosis_html: Optional[str] = None,
                             diagnosis_report: Optional[Dict] = None,
                             marketing_proposal: Optional[str] = None,
                             marketing_proposal_html: Optional[str] = None):
        """发送任务完成"""
        tracker = self.get_progress_tracker(task_id)
        if tracker:
            await tracker.complete_task(
                diagnosis_html=diagnosis_html,
                diagnosis_report=diagnosis_report,
                marketing_proposal=marketing_proposal,
                marketing_proposal_html=marketing_proposal_html
            )
            # 完成后移除跟踪器
            self._active_trackers.pop(task_id, None)
        else:
            self.logger.warning(f"未找到任务的进度跟踪器: {task_id}")
    
    async def send_failure(self, task_id: str, error_msg: str):
        """发送任务失败"""
        tracker = self.get_progress_tracker(task_id)
        if tracker:
            await tracker.fail_task(error_msg)
            # 失败后移除跟踪器
            self._active_trackers.pop(task_id, None)
        else:
            self.logger.warning(f"未找到任务的进度跟踪器: {task_id}")
    
    async def send_custom_response(self, response: Dict[str, Any]):
        """发送自定义响应"""
        await self._send_to_redis(response)
    
    def remove_tracker(self, task_id: str):
        """移除进度跟踪器"""
        self._active_trackers.pop(task_id, None)
        self.logger.info(f"移除进度跟踪器: {task_id}")
    
    async def health_check(self) -> bool:
        """健康检查"""
        return await self.redis_client.health_check()
    
    async def cleanup(self):
        """清理资源"""
        self._active_trackers.clear()
        await self.redis_client.cleanup()
        self.logger.info("状态管理器清理完成")