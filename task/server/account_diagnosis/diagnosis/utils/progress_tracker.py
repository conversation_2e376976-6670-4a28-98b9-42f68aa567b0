"""
进度跟踪器

简化的进度跟踪实现，替代复杂的状态管理器。
提供清晰的进度更新和状态管理。
"""

import asyncio
import logging
from typing import Optional, Callable, Dict, Any
from .response_builder import ResponseBuilder


class ProgressTracker:
    """进度跟踪器"""
    
    def __init__(self, task_id: str, user_id: Optional[str] = None, 
                 diagnosis_id: Optional[int] = None, env: str = "production"):
        self.task_id = task_id
        self.user_id = user_id
        self.diagnosis_id = diagnosis_id
        self.env = env
        self.current_progress = 0
        self.response_builder = ResponseBuilder(env)
        self.logger = logging.getLogger(__name__)
        self._callbacks = []
    
    def add_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """添加进度回调函数"""
        self._callbacks.append(callback)
    
    async def update_progress(self, progress: int, 
                            custom_msg_cn: Optional[str] = None,
                            custom_msg_en: Optional[str] = None):
        """更新进度"""
        if progress < 0 or progress > 100:
            self.logger.warning(f"进度值超出范围: {progress}")
            return
        
        self.current_progress = progress
        
        # 构建响应
        response = self.response_builder.build_progress_response(
            task_id=self.task_id,
            progress=progress,
            user_id=self.user_id,
            diagnosis_id=self.diagnosis_id,
            custom_msg_cn=custom_msg_cn,
            custom_msg_en=custom_msg_en
        )
        
        # 调用所有回调函数
        for callback in self._callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(response)
                else:
                    callback(response)
            except Exception as e:
                self.logger.error(f"进度回调执行失败: {e}")
        
        self.logger.info(f"进度更新: {progress}% - {custom_msg_cn or '处理中...'}")
    
    async def complete_task(self, diagnosis_html: Optional[str] = None,
                           diagnosis_report: Optional[Dict] = None,
                           marketing_proposal: Optional[str] = None,
                           marketing_proposal_html: Optional[str] = None):
        """标记任务完成"""
        response = self.response_builder.build_success_response(
            task_id=self.task_id,
            user_id=self.user_id,
            diagnosis_id=self.diagnosis_id,
            diagnosis_html=diagnosis_html,
            diagnosis_report=diagnosis_report,
            marketing_proposal=marketing_proposal,
            marketing_proposal_html=marketing_proposal_html
        )
        
        # 调用所有回调函数
        for callback in self._callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(response)
                else:
                    callback(response)
            except Exception as e:
                self.logger.error(f"完成回调执行失败: {e}")
        
        self.current_progress = 100
        self.logger.info(f"任务完成: {self.task_id}")
    
    async def fail_task(self, error_msg: str):
        """标记任务失败"""
        response = self.response_builder.build_error_response(
            task_id=self.task_id,
            error_msg=error_msg,
            user_id=self.user_id,
            diagnosis_id=self.diagnosis_id
        )
        
        # 调用所有回调函数
        for callback in self._callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(response)
                else:
                    callback(response)
            except Exception as e:
                self.logger.error(f"失败回调执行失败: {e}")
        
        self.current_progress = 0
        self.logger.error(f"任务失败: {self.task_id} - {error_msg}")
    
    def get_current_progress(self) -> int:
        """获取当前进度"""
        return self.current_progress