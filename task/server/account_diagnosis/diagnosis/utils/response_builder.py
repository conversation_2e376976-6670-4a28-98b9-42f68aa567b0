"""
响应格式构建器

严格按照docs/response.md规范构建标准化的响应格式。
支持状态更新、完成响应和错误响应的构建。
"""

import time
from datetime import datetime
from typing import Dict, Any, Optional


class ResponseBuilder:
    """响应格式构建器"""
    
    # 状态码映射
    STATUS_MAPPING = {
        0: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Task created and waiting to be processed…",
            "aiTaskMsgCN": "任务已创建，正在等待处理..."
        },
        5: {
            "aiTaskStatus": "RUNNING", 
            "aiTaskMsg": "Instructions received. Planning the research path…",
            "aiTaskMsgCN": "收到指令，正在规划任务路径…"
        },
        15: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Analyzing your account information…", 
            "aiTaskMsgCN": "正在分析账号基础信息…"
        },
        30: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Performing a deep search and mining for information…",
            "aiTaskMsgCN": "正在搜索行业热点与最新动态…"
        },
        50: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Analyzing the industry to identify market trends…",
            "aiTaskMsgCN": "正在进行行业分析，洞察市场趋势…"
        },
        70: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Conducting a multi-dimensional diagnosis…",
            "aiTaskMsgCN": "正在进行多维度账号比对与诊断…"
        },
        85: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "Analysis complete. Generating your exclusive report…",
            "aiTaskMsgCN": "正在整合分析结果，生成您的专属报告…"
        },
        95: {
            "aiTaskStatus": "RUNNING",
            "aiTaskMsg": "The report is ready. Rendering the page for you…",
            "aiTaskMsgCN": "报告生成完毕，正在为您加载页面…"
        },
        100: {
            "aiTaskStatus": "FINISH",
            "aiTaskMsg": "Task completed!",
            "aiTaskMsgCN": "诊断完成！"
        }
    }
    
    def __init__(self, env: str = "production"):
        self.env = env.lower()
        self._start_time = time.time()
    
    def build_task_info(self, task_id: int, user_id: str, 
                       diagnosis_id: int, progress: int = 0,
                       custom_msg_cn: Optional[str] = None,
                       custom_msg_en: Optional[str] = None) -> Dict[str, Any]:
        """构建taskInfo部分"""
        
        # 获取状态信息
        status_info = self.STATUS_MAPPING.get(progress, self.STATUS_MAPPING[0])
        
        task_info = {
            "env": self.env,
            "taskId": task_id,
            "userId": user_id,
            "diagnosisId": diagnosis_id,
            "aiTaskStatus": status_info["aiTaskStatus"],
            "aiTaskMsg": custom_msg_en or status_info["aiTaskMsg"],
            "aiTaskMsgCN": custom_msg_cn or status_info["aiTaskMsgCN"],
            "aiTaskProgress": progress,
            "timestamp": datetime.now().isoformat(),
            "elapsed_time": time.time() - self._start_time
        }
            
        return task_info
    
    def build_progress_response(self, task_id: int, progress: int,
                               user_id: str,
                               diagnosis_id: int,
                               custom_msg_cn: Optional[str] = None,
                               custom_msg_en: Optional[str] = None) -> Dict[str, Any]:
        """构建进度更新响应"""
        return {
            "taskInfo": self.build_task_info(
                task_id=task_id,
                user_id=user_id,
                diagnosis_id=diagnosis_id,
                progress=progress,
                custom_msg_cn=custom_msg_cn,
                custom_msg_en=custom_msg_en
            )
        }
    
    def build_success_response(self, task_id: int, 
                              user_id: str,
                              diagnosis_id: int,
                              diagnosis_html: Optional[str] = None,
                              diagnosis_report: Optional[Dict] = None,
                              marketing_proposal: Optional[str] = None,
                              marketing_proposal_html: Optional[str] = None) -> Dict[str, Any]:
        """构建成功完成响应"""
        
        response = {
            "taskInfo": self.build_task_info(
                task_id=task_id,
                user_id=user_id, 
                diagnosis_id=diagnosis_id,
                progress=100
            )
        }
        
        # 添加业务数据
        if diagnosis_html:
            response["diagnosisHtml"] = diagnosis_html
        if diagnosis_report:
            response["diagnosisReport"] = diagnosis_report
        if marketing_proposal:
            response["marketingProposal"] = marketing_proposal
        if marketing_proposal_html:
            response["marketingProposalHtml"] = marketing_proposal_html
            
        return response
    
    def build_error_response(self, task_id: int, error_msg: str,
                            user_id: str,
                            diagnosis_id: int) -> Dict[str, Any]:
        """构建错误响应"""
        
        task_info = self.build_task_info(
            task_id=task_id,
            user_id=user_id,
            diagnosis_id=diagnosis_id,
            progress=0
        )
        
        # 更新为失败状态
        task_info.update({
            "aiTaskStatus": "FAILED",
            "aiTaskMsg": "Task execution failed",
            "aiTaskMsgCN": error_msg,
            "aiTaskProgress": 0
        })
        
        return {"taskInfo": task_info}
    
    def extract_task_data(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """从输入数据中提取任务信息"""
        task_info = input_data.get("taskInfo", {})
        
        return {
            "task_id": task_info.get("taskId"),
            "user_id": task_info.get("userId"),
            "diagnosis_id": task_info.get("diagnosisId"),
            "env": task_info.get("env", self.env)
        }
    
    def reset_timer(self):
        """重置计时器（用于新任务）"""
        self._start_time = time.time()