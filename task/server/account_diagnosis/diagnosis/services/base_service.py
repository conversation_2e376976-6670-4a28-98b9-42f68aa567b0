"""
简化的异步服务基类

大幅简化原有的BaseAsyncService，只保留核心功能：
- 简单的队列监听
- 基础的任务处理
- 清晰的错误处理
- 统一的状态管理
"""

import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional
from ..core.redis_client import get_redis_client
from ..core.simple_status_manager import SimpleStatusManager
from ..utils.response_builder import ResponseBuilder


class BaseService(ABC):
    """简化的异步服务基类"""
    
    def __init__(self, service_name: str, input_queue: str, output_queue: str,
                 max_concurrent_tasks: int = 3, env: str = "production"):
        self.service_name = service_name
        self.input_queue = input_queue
        self.output_queue = output_queue
        self.max_concurrent_tasks = max_concurrent_tasks
        self.env = env
        
        # 初始化组件
        self.logger = self._setup_logging()
        self.redis_client = get_redis_client()
        self.status_manager = SimpleStatusManager(output_queue, env)
        self.response_builder = ResponseBuilder(env)
        self.semaphore = asyncio.Semaphore(max_concurrent_tasks)
        self._shutdown_event = asyncio.Event()
    
    def _setup_logging(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(f'{self.service_name}_service')
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    async def process_task_logic(self, input_data: Dict[str, Any], 
                                progress_tracker) -> Tuple[bool, Dict[str, Any]]:
        """
        抽象方法：具体的任务处理逻辑
        
        Args:
            input_data: 输入数据
            progress_tracker: 进度跟踪器
            
        Returns:
            Tuple[bool, Dict]: (是否成功, 结果数据)
        """
        pass
    
    def extract_task_info(self, input_data: Dict[str, Any]) -> Dict[str, str]:
        """提取任务信息"""
        return self.response_builder.extract_task_data(input_data)
    
    async def process_single_task(self, input_data: Dict[str, Any]):
        """处理单个任务"""
        task_data = self.extract_task_info(input_data)
        task_id = task_data["task_id"]
        
        self.logger.info(f"开始处理任务: {task_id}")
        start_time = time.time()
        
        # 创建进度跟踪器
        progress_tracker = self.status_manager.create_progress_tracker(
            task_id=task_id,
            user_id=task_data.get("user_id"),
            diagnosis_id=task_data.get("diagnosis_id")
        )
        
        try:
            # 发送任务开始状态
            await progress_tracker.update_progress(0, "任务已创建，正在等待处理...")
            
            # 执行具体的任务逻辑
            success, result = await self.process_task_logic(input_data, progress_tracker)
            
            if success:
                # 处理成功，发送完成状态
                # 将camelCase字段名转换为snake_case以匹配SimpleStatusManager参数
                completion_params = {}
                if "diagnosisHtml" in result:
                    completion_params["diagnosis_html"] = result["diagnosisHtml"]
                if "diagnosisReport" in result:
                    completion_params["diagnosis_report"] = result["diagnosisReport"]
                if "marketingProposal" in result:
                    completion_params["marketing_proposal"] = result["marketingProposal"]
                if "marketingProposalHtml" in result:
                    completion_params["marketing_proposal_html"] = result["marketingProposalHtml"]
                
                await self.status_manager.send_completion(task_id, **completion_params)
                elapsed = time.time() - start_time
                self.logger.info(f"任务完成: {task_id}, 耗时: {elapsed:.2f}秒")
            else:
                # 处理失败
                error_msg = result.get("error", "任务处理失败")
                await self.status_manager.send_failure(task_id, error_msg)
                self.logger.error(f"任务失败: {task_id} - {error_msg}")
                
        except Exception as e:
            # 异常处理
            error_msg = f"任务处理异常: {str(e)}"
            await self.status_manager.send_failure(task_id, error_msg)
            self.logger.error(f"任务异常: {task_id}", exc_info=True)
    
    async def process_with_semaphore(self, input_data: Dict[str, Any]):
        """使用信号量控制并发的任务处理"""
        async with self.semaphore:
            await self.process_single_task(input_data)
    
    async def listen_queue(self):
        """监听队列并处理任务"""
        self.logger.info(f"开始监听队列: {self.input_queue}")
        self.logger.info(f"输出队列: {self.output_queue}")
        self.logger.info(f"最大并发: {self.max_concurrent_tasks}")
        
        tasks = []
        
        while not self._shutdown_event.is_set():
            try:
                # 从队列获取任务
                input_data = await self.redis_client.pop_from_queue(
                    self.input_queue, timeout=5
                )
                
                if input_data:
                    # 验证输入数据类型
                    if not isinstance(input_data, dict):
                        self.logger.error(f"收到非字典格式的数据: {type(input_data)}, 值: {input_data}")
                        continue
                    
                    # 创建任务并添加到任务列表
                    task = asyncio.create_task(
                        self.process_with_semaphore(input_data)
                    )
                    tasks.append(task)
                    
                    # 清理已完成的任务
                    tasks = [t for t in tasks if not t.done()]
                    self.logger.info(f"当前活跃任务数: {len(tasks)}")
                
                # 检查并处理已完成的任务
                done_tasks = [t for t in tasks if t.done()]
                for task in done_tasks:
                    try:
                        await task  # 获取任务结果，如果有异常会抛出
                    except Exception as e:
                        self.logger.error(f"任务执行异常: {e}")
                
            except Exception as e:
                self.logger.error(f"队列监听异常: {e}")
                await asyncio.sleep(1)
        
        # 等待所有剩余任务完成
        if tasks:
            self.logger.info(f"等待 {len(tasks)} 个任务完成...")
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.logger.info("队列监听服务已停止")
    
    async def start(self):
        """启动服务"""
        self.logger.info(f"启动 {self.service_name} 服务")
        
        try:
            # 健康检查
            if not await self.redis_client.health_check():
                raise Exception("Redis连接失败")
            
            if not await self.status_manager.health_check():
                raise Exception("状态管理器健康检查失败")
            
            self.logger.info("健康检查通过，开始监听队列")
            
            # 开始监听队列
            await self.listen_queue()
            
        except KeyboardInterrupt:
            self.logger.info("收到中断信号，正在关闭服务...")
        except Exception as e:
            self.logger.error(f"服务启动失败: {e}", exc_info=True)
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """关闭服务"""
        self.logger.info("正在关闭服务...")
        self._shutdown_event.set()
        
        # 清理资源
        await self.status_manager.cleanup()
        await self.redis_client.cleanup()
        
        self.logger.info(f"{self.service_name} 服务已关闭")
    
    def run(self):
        """运行服务（同步接口）"""
        try:
            asyncio.run(self.start())
        except KeyboardInterrupt:
            pass