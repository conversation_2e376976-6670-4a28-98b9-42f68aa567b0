"""
简化的诊断服务实现

使用新的简化架构重新实现诊断服务：
- 清晰的任务处理流程
- 统一的状态管理
- 简化的错误处理
- 标准化的响应格式
"""

import asyncio
import os
import sys
from typing import Dict, Any, Tuple

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..', '..', '..')))

from task import ENV
from .base_service import BaseService
from ..core.diagnosis_engine import SimpleDiagnosisEngine


class ProgressCoordinator:
    """进度协调器 - 确保进度单调递增"""
    def __init__(self, tracker, base_progress=0):
        self.tracker = tracker
        self.current_progress = base_progress
        self.lock = asyncio.Lock()
    
    async def update_progress(self, progress, message):
        async with self.lock:
            # 确保进度只能递增
            if progress > self.current_progress:
                self.current_progress = progress
                await self.tracker.update_progress(progress, message)
            else:
                # 如果新进度不大于当前进度，只更新消息
                await self.tracker.update_progress(self.current_progress, message)


class DiagnosisService(BaseService):
    """诊断服务实现"""
    
    def __init__(self, max_concurrent_tasks: int = 3, env: str = "production",
                 default_mode: str = "basic"):
        # 构建队列名称
        input_queue = ENV.lower() + ":q:diagnosis:request"
        output_queue = ENV.lower() + ":q:diagnosis:response"
        
        super().__init__(
            service_name="diagnosis",
            input_queue=input_queue,
            output_queue=output_queue,
            max_concurrent_tasks=max_concurrent_tasks,
            env=env
        )
        
        self.default_mode = default_mode
        self.diagnosis_engine = SimpleDiagnosisEngine()
        
        self.logger.info(f"诊断服务初始化完成 - 默认模式: {default_mode}")
    
    async def process_task_logic(self, input_data: Dict[str, Any], 
                                progress_tracker) -> Tuple[bool, Dict[str, Any]]:
        """诊断任务的具体处理逻辑"""
        try:
            # 验证输入数据
            success, processed_data = self._validate_input_data(input_data)
            if not success:
                return False, {"error": processed_data}
            
            # 提取任务信息
            task_data = self.extract_task_info(input_data)
            self.logger.info(f"开始处理诊断任务: {task_data['task_id']}")
            
            # 确定诊断模式
            mode_str = input_data.get("mode", self.default_mode)
            enable_deep_research = mode_str == "deep_research"
            
            self.logger.info(f"诊断模式: {mode_str} (深度研究: {enable_deep_research})")
            
            # 步骤1: 如果启用深度研究，先执行深度搜索
            if enable_deep_research:
                await progress_tracker.update_progress(5, "正在收集行业洞察和市场趋势...")
                
                # 为深度研究创建子进度协调器（5%-15%，因为深度研究很快）
                deep_research_coordinator = ProgressCoordinator(progress_tracker, 5)
                
                # 导入并执行简化深度研究
                try:
                    import sys
                    import os
                    # 添加项目根目录到系统路径
                    project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..', '..'))
                    if project_root not in sys.path:
                        sys.path.append(project_root)
                    
                    from task.server.account_diagnosis.simplified_deep_research import conduct_simplified_deep_research
                    
                    deep_research_success, deep_research_data = await conduct_simplified_deep_research(
                        processed_data, processed_data.get('industry'), deep_research_coordinator
                    )
                    
                    if deep_research_success:
                        # 将深度研究结果添加到诊断数据中
                        processed_data['deep_research_insights'] = deep_research_data
                        self.logger.info("深度研究完成，数据已集成到诊断流程")
                    else:
                        self.logger.warning(f"深度研究失败: {deep_research_data}")
                        # 继续正常诊断流程，不因深度研究失败而中断
                        
                except Exception as e:
                    self.logger.error(f"深度研究模块异常: {e}")
                    # 继续正常诊断流程
            
            # 步骤2: 执行诊断（根据实际耗时重新分配进度）
            await progress_tracker.update_progress(25 if enable_deep_research else 5, "正在分析账号表现与内容策略...")
            
            # 为诊断引擎创建子进度协调器（25%-35%，诊断约21秒）
            diagnosis_coordinator = ProgressCoordinator(progress_tracker, 25 if enable_deep_research else 5)
            
            diag_success, diagnosis_result = await self.diagnosis_engine.conduct_diagnosis(
                processed_data, diagnosis_coordinator
            )
            
            if not diag_success:
                return False, {"error": f"诊断失败: {diagnosis_result}"}
            
            self.logger.info("诊断执行完成，开始生成报告")
            
            # 步骤3: 有序生成报告（35%-75%，报告生成最耗时约128秒）
            await progress_tracker.update_progress(35, "正在为您生成专属诊断报告...")
            
            # 并发执行报告生成任务（不传递progress_tracker避免冲突）
            import asyncio
            tasks = [
                self.diagnosis_engine.generate_html_report(diagnosis_result),
                self.diagnosis_engine.generate_json_report(diagnosis_result),
                self.diagnosis_engine.generate_marketing_proposal(diagnosis_result)
            ]
            
            # 添加渐进式进度更新，给报告生成更多进度空间
            await progress_tracker.update_progress(40, "正在设计您的专业诊断页面...")
            await asyncio.sleep(0.1)  # 短暂延迟确保进度顺序
            
            await progress_tracker.update_progress(45, "正在组织诊断结果与数据分析...")
            await asyncio.sleep(0.1)
            
            await progress_tracker.update_progress(50, "正在制定个性化营销策略与建议...")
            await asyncio.sleep(0.1)
            
            try:
                await progress_tracker.update_progress(55, "正在整合分析结果与行业见解...")
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                await progress_tracker.update_progress(75, "报告内容已完成，正在进行最终优化...")
                
                # 处理结果
                html_success, html_result = self._handle_result(results[0], "HTML报告")
                json_success, json_result = self._handle_result(results[1], "JSON报告")
                proposal_success, proposal_result = self._handle_result(results[2], "营销建议")
                
                # 检查关键报告是否成功
                if not html_success:
                    return False, {"error": f"HTML报告生成失败: {html_result}"}
                
                if not json_success:
                    return False, {"error": f"JSON报告生成失败: {json_result}"}
                
                if not proposal_success:
                    return False, {"error": f"营销建议生成失败: {proposal_result}"}
                
                # 步骤4: 生成营销建议HTML（75%-95%，最终处理约53秒）
                await progress_tracker.update_progress(75, "正在设计营销建议与执行方案...")
                
                try:
                    proposal_html_success, proposal_html_result = await self.diagnosis_engine.generate_marketing_proposal_html(
                        proposal_result, progress_tracker
                    )
                    
                    if not proposal_html_success:
                        self.logger.warning(f"营销建议HTML生成失败: {proposal_html_result}")
                        # 营销建议HTML失败不是致命错误，使用备用内容
                        proposal_html_result = self._create_fallback_html(proposal_result)
                        
                except Exception as e:
                    self.logger.error(f"营销建议HTML生成异常: {e}", exc_info=True)
                    # 使用备用内容
                    proposal_html_result = self._create_fallback_html(proposal_result)
                
                # 构建最终结果
                final_results = {
                    "diagnosisHtml": html_result,
                    "diagnosisReport": json_result,
                    "marketingProposal": proposal_result,
                    "marketingProposalHtml": proposal_html_result
                }
                
                # 不要在这里更新进度到99%，让BaseService直接处理完成状态
                # 这样可以避免进度卡在99%的问题
                self.logger.info(f"诊断任务完成: {task_data['task_id']}")
                return True, final_results
                
            except Exception as e:
                self.logger.error(f"并发生成任务失败: {e}", exc_info=True)
                return False, {"error": f"报告生成异常: {str(e)}"}
                
        except Exception as e:
            self.logger.error(f"诊断任务处理异常: {e}", exc_info=True)
            return False, {"error": f"任务处理异常: {str(e)}"}
    
    def _validate_input_data(self, input_data: Dict[str, Any]) -> Tuple[bool, Any]:
        """验证和处理输入数据"""
        try:
            # 检查必需字段
            required_fields = ["accountInfo", "noteList", "marketingGoal"]
            for field in required_fields:
                if field not in input_data:
                    return False, f"缺少必需字段: {field}"
            
            # 处理数据
            processed_data = input_data.copy()
            
            # 确保noteList是列表
            if not isinstance(processed_data.get("noteList"), list):
                processed_data["noteList"] = []
            
            # 处理行业字段
            if "industry" not in processed_data:
                processed_data["industry"] = None
            elif processed_data["industry"] == "empty":
                processed_data["industry"] = None
            
            # 验证accountInfo
            if not isinstance(processed_data.get("accountInfo"), dict):
                return False, "accountInfo必须是字典格式"
            
            # 添加默认值
            account_info = processed_data["accountInfo"]
            defaults = {
                "nickname": "未知账号",
                "followers": 0,
                "liked": 0,
                "collected": 0,
                "posts": 0,
                "comments": 0
            }
            
            for key, default_value in defaults.items():
                if key not in account_info:
                    account_info[key] = default_value
            
            self.logger.info(f"输入数据验证通过，账号: {account_info.get('nickname')}")
            return True, processed_data
            
        except Exception as e:
            self.logger.error(f"输入数据验证异常: {e}")
            return False, f"数据验证异常: {str(e)}"
    
    def _handle_result(self, result, task_name: str) -> Tuple[bool, Any]:
        """处理任务结果"""
        if isinstance(result, Exception):
            self.logger.error(f"{task_name} 执行异常: {result}")
            return False, str(result)
        elif isinstance(result, tuple) and len(result) == 2:
            success, data = result
            if success:
                self.logger.info(f"{task_name} 生成成功")
                return True, data
            else:
                self.logger.error(f"{task_name} 生成失败: {data}")
                return False, data
        else:
            self.logger.error(f"{task_name} 返回格式异常: {type(result)}")
            return False, f"返回格式异常: {type(result)}"
    
    def _create_fallback_html(self, marketing_proposal: str) -> str:
        """创建备用HTML内容"""
        return f"""<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>营销建议</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1 {{
            color: #333;
            border-bottom: 3px solid #e74c3c;
            padding-bottom: 10px;
        }}
        .content {{
            white-space: pre-wrap;
            color: #555;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>营销建议</h1>
        <div class="content">{marketing_proposal}</div>
    </div>
</body>
</html>"""