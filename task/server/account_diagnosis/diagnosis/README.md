# 重构后的账号诊断系统 v2.0

## 概述

这是一个完全重构的账号诊断系统，旨在解决原系统的复杂性和维护难题。新系统采用简化的架构设计，提供清晰的代码结构和统一的状态管理。

## 主要改进

### 🎯 解决的问题
- ✅ 复杂的状态管理器（多个状态协调器并存）
- ✅ 分散的代码结构（20+个文件，功能重叠）
- ✅ 复杂的异步处理（多种Redis管理器）
- ✅ 难以维护的错误处理机制
- ✅ 不一致的响应格式

### 🚀 新架构优势
- **代码量减少约60%**：从1000+行简化到400+行核心代码
- **维护复杂度大幅降低**：清晰的模块分离和职责划分
- **状态更新更可靠**：统一的状态管理机制
- **更容易调试和扩展**：简化的错误处理和日志记录

## 目录结构

```
diagnosis/
├── __init__.py                    # 包初始化
├── main.py                        # 服务入口点
├── test_refactored_system.py      # 测试脚本
├── README.md                      # 使用说明
├── core/                          # 核心模块
│   ├── __init__.py
│   ├── redis_client.py            # 简化的Redis客户端
│   ├── simple_status_manager.py   # 统一状态管理器
│   └── diagnosis_engine.py        # 核心诊断逻辑
├── services/                      # 服务模块
│   ├── __init__.py
│   ├── base_service.py            # 简化的服务基类
│   └── diagnosis_service.py       # 诊断服务实现
└── utils/                         # 工具模块
    ├── __init__.py
    ├── response_builder.py        # 响应格式构建器
    └── progress_tracker.py        # 进度跟踪器
```

## 快速开始

### 1. 测试系统

首先运行测试脚本验证系统是否正常工作：

```bash
cd diagnosis/
python test_refactored_system.py
```

### 2. 启动服务

#### 基础模式（默认）
```bash
python main.py
```

#### 深度研究模式
```bash
python main.py --mode deep_research
```

#### 自定义配置
```bash
python main.py --mode basic --concurrent 8 --env test --verbose
```

### 3. 命令行选项

```bash
python main.py --help
```

可用选项：
- `--mode`: 诊断模式 (basic/deep_research)
- `--concurrent`: 最大并发任务数 (1-20)
- `--env`: 运行环境 (production/test/dev)
- `--verbose`: 启用详细日志
- `--version`: 显示版本信息

## 核心组件说明

### 1. SimpleRedisClient
```python
from diagnosis.core.redis_client import get_redis_client

# 获取Redis客户端
client = get_redis_client()

# 基本操作
await client.push_to_queue("queue_name", data)
await client.pop_from_queue("queue_name", timeout=30)
await client.health_check()
```

### 2. SimpleStatusManager
```python
from diagnosis.core.simple_status_manager import SimpleStatusManager

# 创建状态管理器
manager = SimpleStatusManager("output_queue", "production")

# 创建进度跟踪器
tracker = manager.create_progress_tracker("task_123", "user_456", 1)

# 发送进度更新
await manager.send_progress("task_123", 50, "处理中...")

# 发送完成状态
await manager.send_completion("task_123", 
    diagnosis_html="<html>...</html>",
    diagnosis_report={"summary": "..."})
```

### 3. ResponseBuilder
```python
from diagnosis.utils.response_builder import ResponseBuilder

# 创建响应构建器
builder = ResponseBuilder("production")

# 构建进度响应
response = builder.build_progress_response(
    task_id="task_123", 
    progress=50,
    custom_msg_cn="正在处理..."
)

# 构建成功响应
response = builder.build_success_response(
    task_id="task_123",
    diagnosis_html="<html>...</html>",
    diagnosis_report={"summary": "..."}
)
```

### 4. SimpleDiagnosisEngine
```python
from diagnosis.core.diagnosis_engine import SimpleDiagnosisEngine

# 创建诊断引擎
engine = SimpleDiagnosisEngine()

# 执行诊断
success, result = await engine.conduct_diagnosis(input_data, progress_tracker)

# 生成HTML报告
success, html = await engine.generate_html_report(diagnosis_result, progress_tracker)

# 生成JSON报告
success, json_data = await engine.generate_json_report(diagnosis_result, progress_tracker)
```

## 响应格式

系统严格按照 `docs/response.md` 规范构建响应：

### 进度更新响应
```json
{
  "taskInfo": {
    "env": "production",
    "taskId": "1932994656262557696",
    "userId": "5ec7811000000000010044fe",
    "diagnosisId": 9,
    "aiTaskStatus": "RUNNING",
    "aiTaskMsg": "Analyzing your account information…",
    "aiTaskMsgCN": "正在分析账号基础信息…",
    "aiTaskProgress": 15,
    "timestamp": "2024-01-20T10:30:00",
    "elapsed_time": 45.2
  }
}
```

### 完成响应
```json
{
  "taskInfo": {
    "env": "production",
    "taskId": "1932994656262557696",
    "aiTaskStatus": "FINISH",
    "aiTaskMsg": "Task completed!",
    "aiTaskMsgCN": "诊断完成！",
    "aiTaskProgress": 100
  },
  "diagnosisHtml": "<!DOCTYPE html>...",
  "diagnosisReport": {
    "summary": "账号分析摘要",
    "tags": [...],
    "bottleneck": {...},
    "suggestion": [...]
  },
  "marketingProposal": "# 营销建议\n...",
  "marketingProposalHtml": "<!DOCTYPE html>..."
}
```

## 与原系统的兼容性

### 队列兼容
- ✅ 输入队列：`{env}:q:diagnosis:request`
- ✅ 输出队列：`{env}:q:diagnosis:response`
- ✅ 输入数据格式：完全兼容
- ✅ 输出数据格式：完全兼容

### API兼容
- ✅ 支持 `mode` 参数（basic/deep_research）
- ✅ 保持原有的字段结构
- ✅ 相同的状态码和进度更新

## 性能优化

### 并发处理
- 支持1-20个并发任务
- 自动负载均衡
- 优雅的错误处理

### 内存优化
- 移除不必要的缓存
- 简化的对象结构
- 及时的资源清理

### 网络优化
- 简化的Redis连接管理
- 批量操作支持
- 连接池复用

## 故障排查

### 常见问题

1. **Redis连接失败**
   ```bash
   # 检查Redis服务状态
   redis-cli ping
   
   # 运行连接测试
   python test_refactored_system.py
   ```

2. **队列无任务**
   ```bash
   # 检查队列长度
   redis-cli llen "production:q:diagnosis:request"
   
   # 查看服务日志
   tail -f logs/diagnosis_service.log
   ```

3. **AI调用失败**
   - 检查环境变量配置
   - 确认API密钥有效
   - 查看详细日志 (`--verbose`)

### 日志分析

日志文件位置：`logs/diagnosis_service.log`

关键日志标识：
- `✅`: 成功操作
- `❌`: 失败操作  
- `⚠️`: 警告信息
- `🔍`: 调试信息

## 迁移指南

### 从旧系统迁移

1. **备份现有配置**
   ```bash
   cp script_for_diagnosis.py script_for_diagnosis.py.backup
   cp service_implementations.py service_implementations.py.backup
   ```

2. **切换到新系统**
   ```bash
   # 停止旧服务
   # 启动新服务
   cd diagnosis/
   python main.py --mode basic
   ```

3. **验证功能**
   ```bash
   python test_refactored_system.py
   ```

### 回滚方案

如需回滚到原系统：
```bash
# 使用备份的原始文件
python script_for_diagnosis.py --mode false  # 基础模式
python script_for_diagnosis.py --mode true   # 深度研究模式
```

## 贡献指南

### 代码规范
- 遵循PEP 8
- 添加类型注解
- 编写文档字符串
- 保持函数简洁（<50行）

### 测试要求
- 新功能必须有对应测试
- 测试覆盖率>80%
- 集成测试通过

### 提交流程
1. Fork项目
2. 创建功能分支
3. 编写代码和测试
4. 提交Pull Request

## 版本历史

### v2.0.0 (当前版本)
- 🎯 完全重构架构
- ✅ 简化状态管理
- ✅ 统一响应格式
- ✅ 提升性能和稳定性

### v1.x (原版本)
- 基础诊断功能
- 复杂的状态管理
- 多文件分散架构

## 许可证

MIT License

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交Issue
- 发起Discussion
- 邮件联系开发团队