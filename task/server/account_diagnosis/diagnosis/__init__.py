"""
重构后的账号诊断系统

这是一个简化的、易于维护的诊断系统重构版本。
主要改进：
- 统一的状态管理
- 简化的Redis交互
- 清晰的职责分离
- 标准化的响应格式
"""

__version__ = "2.0.0"
__author__ = "Refactored Diagnosis System"

from .core.simple_status_manager import SimpleStatusManager
from .core.redis_client import SimpleRedisClient
from .services.diagnosis_service import DiagnosisService
from .utils.response_builder import ResponseBuilder

__all__ = [
    "SimpleStatusManager",
    "SimpleRedisClient", 
    "DiagnosisService",
    "ResponseBuilder"
]