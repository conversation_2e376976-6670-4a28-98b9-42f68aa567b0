

diagnosis_schema_gpt = {
    "name": "generate_diagnosis_report",
    "description": "根据诊断结果生成报告",
    "strict": False,
    "schema": {
        "type": "object",
        "properties": {
            "summary": {
                "type": "string",
                "description": "当前账号问题和建议的完整概括，50-100字"
            },
            "tags": {
                "type": "array",
                "description": "3个固定维度的账号指标",
                "items": {
                    "type": "object",
                    "properties": {
                        "dimension": {
                            "type": "string",
                            "enum": ["CURRENT STATUS", "GROWTH POTENTIAL", "FOCUS NEEDED"]
                        },
                        "status": {"type": "string"}
                    },
                    "required": ["dimension", "status"]
                },
                "minItems": 3,
                "maxItems": 3
            },
            "bottleneck": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "area": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {"type": "string"},
                                "title_en": {"type": "string"},
                                "des": {"type": "string"}
                            },
                            "required": ["title", "title_en", "des"]
                        },
                        "minItems": 1
                    }
                },
                "required": ["title", "area"]
            },
            "content_analysis": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "des": {"type": "string", "description": "完整的内容分析，不能有省略号或占位符"},
                    "title_en": {"type": "string"}
                },
                "required": ["title", "des", "title_en"]
            },
            "ip_analysis": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "des": {"type": "string", "description": "完整的IP分析，不能有省略号或占位符"},
                    "title_en": {"type": "string"}
                },
                "required": ["title", "des", "title_en"]
            },
            "optimize_dimension": {
                "type": "object",
                "properties": {
                    "title": {"type": "string"},
                    "areas": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name": {"type": "string"},
                                "question": {"type": "string", "description": "具体问题描述，必须是完整的问题而不是占位符"}
                            },
                            "required": ["name", "question"]
                        },
                        "minItems": 1
                    }
                },
                "required": ["title", "areas"]
            },
            "suggestion": {
                "type": "array",
                "description": "优化建议列表",
                "items": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string"},
                        "content": {
                            "type": "array",
                            "items": {"type": "string"},
                            "description": "具体可执行的建议，每条建议必须完整且可操作",
                            "minItems": 1
                        }
                    },
                    "required": ["title", "content"]
                },
                "minItems": 1
            }
        },
        "required": ["summary", "tags", "bottleneck", "content_analysis", "ip_analysis", "optimize_dimension", "suggestion"]
    }
}
