{# 响应式评估HTML生成模板 #}
{# 系统提示词 #}
{% set system_prompt %}
你是一位专业的响应式网页设计师和运营分析专家，擅长将运营评估报告转化为在各种设备上都能完美展示的现代化网页。你的任务是创建一个专业、现代、富有视觉冲击力的响应式评估报告网页，确保在移动端、平板和桌面端都有出色的用户体验。

**严格输出要求：**
- 你的输出必须只包含完整、可执行的HTML代码
- 不要包含任何解释、注释或其他文本
- 输出的HTML代码必须是完整的，从<!DOCTYPE html>开始到</html>结束
- 不要有任何前缀或后缀说明
{% endset %}

{# 用户提示词 #}
{% set user_prompt %}
基于小红书的运营评估数据和生成日期:
*** 评估数据开头 ***
{{ review_result }}
*** 评估数据结尾 ***

生成日期: {{ current_date }}

请生成响应式网页，不要遗漏信息根据上面内容生成一个 HTML 动态网页。要求：

**响应式设计要求：**
1. 整体字数控制在 600 字左右，使用响应式Bento Grid风格的视觉设计，纯白色底配合小红书品牌色（#FF2442）作为高亮
2. 移动端优先设计：在小屏幕上垂直堆叠布局，在大屏幕上使用网格布局
3. 强调超大字体或数字突出核心要点，但在移动端适当缩小以保持可读性
4. 中英文混用，中文大字体粗体，英文小字作为点缀，字体大小根据屏幕尺寸自适应
5. 简洁的勾线图形化作为数据可视化或者配图元素，在小屏幕上保持清晰可见
6. 运用高亮色的透明度变化制造科技感，但避免使用opacity: 0或完全隐藏元素的样式，不同高亮色不要互相渐变
7. 数据可以引用在线的图表组件，样式需要跟主题一致且响应式适配

**技术要求：**
8. 使用HTML5、TailwindCSS 3.0+（通过CDN引入）
9. 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
10. 页面保持静态，不要使用任何动画效果（包括GSAP、CSS动画、JavaScript动画等）
11. 避免使用可能导致元素隐藏的CSS属性：opacity: 0, display: none, visibility: hidden
12. 避免使用emoji作为主要图标
13. 不要省略内容要点
14. **禁止生成交互按钮**：不要生成任何<button>、<a>标签的交互按钮或链接
15. **纯静态报告**：所有内容必须是纯展示性的，不包含任何点击、提交等交互功能
16. **报告日期使用传入参数**：使用传入的current_date参数显示报告生成日期，不要自己编制日期
17. 不要生成任何页脚（footer）、版权信息
18. **报告日期显示**：在报告顶部合适位置显示"报告生成日期: {{ current_date }}"

**响应式布局策略：**
15. 移动端（<768px）：单列布局，卡片间距适中，字体大小优化触摸操作
16. 平板端（768px-1024px）：双列或三列布局，保持内容层次
17. 桌面端（>1024px）：多列Bento Grid布局，充分利用屏幕空间
18. 使用Tailwind的响应式断点：sm:, md:, lg:, xl:, 2xl:
19. 确保触摸友好：按钮和可交互元素最小44px，间距充足
20. 文本可读性：移动端最小16px字体，行高1.5以上
21. 图片和图表：使用max-w-full确保不溢出，保持宽高比
22. 内边距和外边距：移动端使用较小间距，大屏幕逐步增加

**桌面端布局优化：**
23. 主容器宽度限制：max-w-6xl，避免内容在宽屏上过度拉伸
24. 页面居中：mx-auto 保证内容在大屏幕上居中对齐
25. 网格列数：grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4
26. 间距设置：gap-4 md:gap-6 lg:gap-8，保证视觉舒适度
27. 最小高度：min-h-[180px] 确保卡片内容区域充足
28. 重要模块跨列：使用col-span-2或col-span-3突出重点内容

**排版优化要求：**
23. 对于大段文本内容，必须进行分点处理，使用项目符号(bullets)和编号列表提升可读性
24. 讨论和复盘内容应该分解为易于阅读的要点，每个要点不超过2-3行
25. 使用HTML的<ul>、<ol>、<li>标签创建清晰的列表结构
26. 重要观点使用<strong>标签加粗突出
27. 为长段落添加适当的段落间距和行高
28. 使用小标题(<h4>、<h5>)来分割不同的讨论主题
29. 所有分析性内容必须使用大小标题以及项目符号列表格式，避免大段连续文本
30. 优化建议必须使用编号列表，每个建议包含具体的执行步骤

**评估报告内容结构和要点（请将这些内容点用响应式Bento Grid等风格巧妙地组织和呈现出来）：**

1. **评估概览与核心指标 (Review Overview & Key Metrics)**:
   - 立即展示评估期间的核心表现指标（例如：总互动量、粉丝增长、内容发布量）
   - 使用超大字体或数字突出显示2-3个最关键的数据指标（例如：平均互动率、热门内容占比、目标完成率等）
   - 此部分在移动端占据首屏全宽，在桌面端可以是较大的Bento格

2. **内容表现分析 (Content Performance Analysis)**:
   - 分析评估期间的内容类型表现、发布频率、互动数据分布
   - 识别表现最好和最差的内容，分析原因和模式
   - 移动端垂直展示，桌面端可以使用左右分栏或网格布局
   - **重要：使用项目符号列表呈现分析要点，避免大段文字**

3. **粉丝增长与互动分析 (Audience Growth & Engagement)**:
   - 基于评估数据，分析粉丝增长趋势、互动行为变化、评论质量
   - 识别高价值粉丝和互动模式
   - 这部分可以用文本描述配合小图标的响应式Bento格
   - **重要：将分析内容分解为清晰的要点，使用编号列表或项目符号**

4. **目标达成情况 (Goal Achievement Status)**:
   - 评估设定目标的完成情况，包括数量指标和质量指标
   - 分析未达成目标的原因和改进空间
   - 此部分可以用对比强烈的响应式Bento格（例如，目标 vs 实际表现）

{% if has_accounts %}
5. **账号表现详情 (Account Performance Details)**:
   - 展示各个账号的具体表现数据、数据洞察和热门内容分析
   - 使用响应式卡片布局，每个账号一个卡片
   - **重要：账号分析内容必须使用项目符号和小标题进行结构化呈现**
   - 包含以下内容：
     * 账号基本信息和核心指标
     * 数据洞察分析（使用项目符号列表）
     * 热门内容分析（使用项目符号列表）
     * 优化建议（使用编号列表）
{% endif %}

{% if has_accounts %}
6. **优化建议与行动计划 (Optimization Recommendations & Action Plan)**:
{% else %}
5. **优化建议与行动计划 (Optimization Recommendations & Action Plan)**:
{% endif %}
   - 基于评估结果，提供具体的优化建议
   - 包含短期和长期的改进措施，以及预期效果
   - 每条建议可以设计成一个独立的响应式Bento Grid单元格，包含问题、建议、预期结果
   - 移动端垂直堆叠，桌面端可以使用网格布局
   - **重要：优化建议必须使用编号列表，每个建议包含具体的执行步骤**

{% if has_accounts %}
7. **热门内容回顾 (Top Content Review)**:
{% else %}
6. **热门内容回顾 (Top Content Review)**:
{% endif %}
   - 展示评估期间表现最好的内容
   - 分析成功因素和可复制的模式
   - 以卡片形式展示，包含标题、互动数据、成功要素
   - **重要：成功要素分析使用项目符号列表呈现**

{% if has_accounts %}
8. **下阶段重点方向 (Next Phase Focus Areas)**:
{% else %}
7. **下阶段重点方向 (Next Phase Focus Areas)**:
{% endif %}
   - 基于评估结果，明确下一阶段的重点工作方向
   - 包含具体的执行建议和时间规划
   - 在各种屏幕尺寸上都保持良好的可读性
   - **重要：重点方向使用编号列表，每个方向包含具体的时间节点和执行要点**

**特别注意事项：**
- **HTML生成要求：**
  - 输出必须是纯HTML代码，不能包含任何模板变量或语法
  - 所有数据都必须是硬编码的HTML内容，不能有动态变量
  - 页面布局要自动调整，确保在各种情况下都保持良好的视觉效果
  - 确保所有章节正常显示，保持内容的完整性和连贯性
- **内容结构化要求：**
  - 所有讨论性和分析性内容都必须进行结构化处理，使用HTML列表标签
  - 避免出现超过5行的连续文本段落，将其分解为要点列表
  - 使用适当的HTML语义标签（<section>、<article>、<aside>）来组织内容结构
  - 确保每个Bento Grid单元格内的内容都有清晰的层次结构

**关键HTML布局结构（强制要求）：**
- 主容器：<div class="min-h-screen bg-white">
- 内容容器：<div class="max-w-6xl mx-auto p-4 md:p-6 lg:p-8">
- Bento Grid：<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
- 卡片基础：<div class="bg-white rounded-lg border border-gray-200 p-4 md:p-6 min-h-[180px]">

请确保评估报告不仅信息全面、分析深入，而且在所有设备上都有出色的视觉效果和用户体验。特别注意移动端的触摸操作便利性和内容的层次结构。不要遗漏【评估数据】中的任何重要信息。

**静态报告约束：**
- 绝对禁止任何<button>、<a href>、onclick等交互元素
- 不要生成"立即咨询"、"联系我们"、"获取服务"等行动召唤按钮
- 所有内容必须是纯展示性的文字、图表和数据
- 报告日期必须使用传入的current_date参数，格式为"YYYY年MM月DD日"
{% endset %}

{# 导出变量供代码使用 #}
{{ system_prompt }}
---SEPARATOR---
{{ user_prompt }}
