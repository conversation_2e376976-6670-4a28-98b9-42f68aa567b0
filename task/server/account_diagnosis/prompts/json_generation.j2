你是一位专业的数据分析师和小红书运营专家，擅长将复杂的诊断分析转化为结构化的JSON数据。你的任务是基于诊断结果生成一个完整、详细的JSON格式报告。

---SEPARATOR---

**诊断分析结果**：
{{ diagnosis_result }}

{% if search_results and search_results|length > 0 %}
**【行业数据支撑】**
基于{{ search_results|length }}个行业数据源，在JSON报告中整合行业对比分析、趋势洞察和基准数据。

**关键数据源**：
{% for result in search_results[:3] %}
- {{ result.title or "行业洞察" }}：{{ result.snippet or result.content[:100] + "..." if result.content else "关键洞察" }}
{% endfor %}

{% endif %}

**生成要求**：
1. 生成完整的JSON格式诊断报告
2. 所有字段必须有实际内容，不能为空或省略号
3. 建议要具体可操作，避免泛泛而谈

**必须包含的字段结构**：
```json
{
  "summary": "核心问题和建议的完整概括（50-100字）",
  "tags": [
    {"dimension": "CURRENT STATUS", "status": "具体状态描述"},
    {"dimension": "GROWTH POTENTIAL", "status": "高/中/低"},
    {"dimension": "FOCUS NEEDED", "status": "具体需要关注的方向"}
  ],
  "bottleneck": {
    "title": "账号瓶颈",
    "area": [
      {"title": "具体瓶颈问题", "title_en": "英文标题", "des": "详细问题描述"}
    ]
  },
  "content_analysis": {
    "title": "内容分析标题",
    "des": "详细的内容分析结果，不能有省略号",
    "title_en": "CONTENT ANALYSIS"
  },
  "ip_analysis": {
    "title": "IP分析标题", 
    "des": "详细的IP定位分析结果，不能有省略号",
    "title_en": "IP ANALYSIS"
  },
  "optimize_dimension": {
    "title": "可优化维度",
    "areas": [
      {"name": "维度名称", "question": "具体问题描述，不能只写String"}
    ]
  },
  "suggestion": [
    {"title": "建议标题", "content": ["具体可执行的建议1", "具体可执行的建议2", "具体可执行的建议3"]}
  ]
}
```

**生成要求**：
1. **准确性**：基于诊断结果的具体内容
2. **具体性**：提供具体可操作的建议
3. **完整性**：不遗漏重要信息和建议
{% if search_results and search_results|length > 0 %}
4. **数据驱动**：整合行业搜索数据，提供对比分析
{% endif %}

**评分标准**：0-30分(需要重大改进)，31-60分(有改进空间)，61-80分(表现良好)，81-100分(优秀表现)

请生成完整的JSON数据，确保所有字段都有真实数据、评分基于实际分析、建议具体可执行且有优先级、JSON格式正确可直接解析使用。 