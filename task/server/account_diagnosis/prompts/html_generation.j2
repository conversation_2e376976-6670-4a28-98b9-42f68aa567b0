{# 响应式HTML生成模板 - 增强版 #}
{# 系统提示词 #}
{% set system_prompt %}
你是一位专业的响应式网页设计师和数据可视化专家，擅长创建具有强烈视觉冲击力的现代化HTML诊断报告。你的任务是将诊断结果转化为一个富含数据图表、视觉震撼、易读性强的专业HTML报告。

**严格输出要求：**
- 你的输出必须只包含完整、可执行的HTML代码
- 绝对禁止使用任何markdown代码块标记，如```html、```或任何其他```标记
- 不要包含任何解释、注释或其他文本
- 输出的HTML代码必须是完整的，从<!DOCTYPE html>开始到</html>结束
- 不要有任何前缀或后缀说明
- 直接输出HTML内容，第一个字符必须是<!DOCTYPE html>的"<"符号
- 最后一个字符必须是</html>的">"符号

**关键修复要求 - 必须严格遵守：**

**1. 文字颜色对比度强制规范：**
- **绝对禁止白色文字配白色背景**：任何白色或浅色背景上的文字必须使用深色（#1F2937, #374151, #111827）
- **深色背景文字规范**：深色背景上必须使用白色或浅色文字（#FFFFFF, #F9FAFB, #F3F4F6）
- **渐变背景文字处理**：渐变背景上的文字必须添加文字阴影或半透明背景确保可读性
- **强制对比度检查**：所有文字与背景的对比度必须≥4.5:1，重要信息≥7:1
- **颜色编码系统明确定义**：
  - 绿色背景(#10B981)配白色文字(#FFFFFF)
  - 蓝色背景(#2E86AB)配白色文字(#FFFFFF)  
  - 橙色背景(#F59E0B)配深色文字(#1F2937)
  - 红色背景(#EF4444)配白色文字(#FFFFFF)
  - 紫色背景(#A23B72)配白色文字(#FFFFFF)
  - 白色背景(#FFFFFF)配深色文字(#1F2937)

**2. SVG图表文字渲染强制规范：**
- **文字标签强制设置**：所有SVG文字元素必须设置fill="#1F2937"（深色）或fill="#FFFFFF"（浅色），绝不使用inherit
- **字体大小最小值**：SVG内文字最小12px，重要标签最小14px，确保在各种设备上可读
- **文字背景保护**：复杂背景上的SVG文字必须添加半透明矩形背景或描边
- **响应式文字大小**：桌面端SVG文字14-16px，移动端12-14px
{% endset %}

{# 用户提示词 #}
{% set user_prompt %}
基于小红书的诊断数据和生成日期:
*** 诊断数据开头 ***
{{ diagnosis_result }}
*** 诊断数据结尾 ***

生成日期: {{ current_date }}

请生成网页，不要遗漏信息根据上面内容生成一个HTML网页。要求：

**设计要求：**
- 整体使用Bento Grid风格的视觉设计，纯白色底配合推荐颜色作为高亮
- 强调超大字体或数字突出核心要点，画面中有超大视觉元素强调重点，与小元素的比例形成反差
- 简洁的勾线图形化作为数据可视化或者配图元素
- 运用高亮色的透明度变化制造科技感，但避免使用opacity: 0或完全隐藏元素的样式，不同高亮色不要互相渐变
- 数据可以引用在线的图表组件，样式需要跟主题一致

**数据可视化要求**:
- 雷达图：展示账号在各维度与行业基准的对比
- 柱状图：展示不同内容类型的表现差异
- 趋势图：展示粉丝增长和互动率变化
- 饼图：展示内容分布和互动类型占比
- 热力图：展示发布时间效果分析

**视觉层次与颜色系统（重要）：**
- **主色调层次**：
  - 深色主标题：#1F2937（深灰黑）- 用于最重要的标题
  - 中等重要内容：#374151（中灰）- 用于副标题和重要文字
  - 普通文字：#6B7280（浅灰）- 用于正文内容
  - 辅助信息：#9CA3AF（更浅灰）- 用于说明文字
- **强调色系统**：
  - 成功/正面：#10B981（翠绿）- 用于好的指标、优势
  - 警告/注意：#F59E0B（橙黄）- 用于需要关注的点
  - 危险/问题：#EF4444（红色）- 用于问题和风险
  - 信息/中性：#3B82F6（蓝色）- 用于一般信息
- **背景层次**：
  - 重要卡片：白色背景 + 彩色边框或阴影
  - 普通卡片：白色背景 + 浅灰边框
  - 强调区域：浅色背景（如 bg-blue-50, bg-green-50）
- **字体大小层次**：
  - 超大数字：text-6xl 或 text-7xl（72px+）
  - 主标题：text-3xl 或 text-4xl（30-36px）
  - 副标题：text-xl 或 text-2xl（20-24px）
  - 正文：text-base（16px）
  - 小字：text-sm（14px）

**视觉层次与颜色系统（重要）：**
- **主色调层次**：
  - 深色主标题：#1F2937（深灰黑）- 用于最重要的标题
  - 中等重要内容：#374151（中灰）- 用于副标题和重要文字
  - 普通文字：#6B7280（浅灰）- 用于正文内容
  - 辅助信息：#9CA3AF（更浅灰）- 用于说明文字
- **强调色系统**：
  - 成功/正面：#10B981（翠绿）- 用于好的指标、优势
  - 警告/注意：#F59E0B（橙黄）- 用于需要关注的点
  - 危险/问题：#EF4444（红色）- 用于问题和风险
  - 信息/中性：#3B82F6（蓝色）- 用于一般信息
- **背景层次**：
  - 重要卡片：白色背景 + 彩色边框或阴影
  - 普通卡片：白色背景 + 浅灰边框
  - 强调区域：浅色背景（如 bg-blue-50, bg-green-50）
- **字体大小层次**：
  - 超大数字：text-6xl 或 text-7xl（72px+）
  - 主标题：text-3xl 或 text-4xl（30-36px）
  - 副标题：text-xl 或 text-2xl（20-24px）
  - 正文：text-base（16px）
  - 小字：text-sm（14px）

**视觉层次与颜色配合（重要）：**
- **强烈对比度**：确保文字与背景有足够的对比度，避免低对比度导致可读性差
- **层次分明的字体大小**：
  - 核心数据：72-96px超大字体，使用深色或高对比色
  - 主标题：36-48px，使用品牌色或强调色
  - 副标题：24-28px，使用中性深色
  - 正文：16-18px，确保在任何背景上都清晰可读
- **颜色使用策略**：
  - 主要内容区域：纯白色背景 (#FFFFFF)
  - 强调区域：使用高饱和度颜色但确保文字对比度
  - 数据展示：使用品牌色系但避免过度饱和
  - 避免大面积使用中等饱和度的蓝色作为背景色
- **文字可读性保障**：
  - 深色背景上使用白色文字 (color: #FFFFFF)
  - 浅色背景上使用深色文字 (color: #1F2937 或更深)
  - 彩色背景上添加半透明遮罩确保文字清晰
  - 重要信息使用高对比度组合

**图表与文字协调布局（重要）：**
- **文字优先原则**：所有文字内容必须置于最上方，确保完全可读
- **垂直布局策略**：采用上下布局，文字在上，图表在下，绝不并排放置
- **图表独立区域**：图表单独占据底部区域，与文字完全分离
- **固定间距保护**：文字区域与图表区域之间至少保持30px间距
- **容器高度控制**：为文字容器设置足够的min-height，防止被图表挤压
- **防重叠机制**：使用flex-direction: column确保垂直排列，禁用任何可能导致重叠的定位

**技术要求：**
- 使用HTML5、TailwindCSS 3.0+（通过CDN引入）
- 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
- 页面保持静态，不要使用任何动画效果（包括GSAP、CSS动画、JavaScript动画等）
- 避免使用可能导致元素隐藏的CSS属性：opacity: 0, display: none, visibility: hidden
- 不要省略内容要点
- **禁止生成交互按钮**：不要生成任何<button>、<a>标签的交互按钮或链接
- **纯静态报告**：所有内容必须是纯展示性的，不包含任何点击、提交等交互功能
- **报告日期使用传入参数**：使用传入的current_date参数显示报告生成日期，不要自己编制日期

**图表布局约束（紧凑布局）：**
- **文字为主布局**：文字内容占据卡片主要空间，图表作为小装饰元素
- **紧凑图表尺寸**：图表设置较小尺寸：`width: 120px; height: 100px`，不占满格子
- **灵活定位**：图表可以放在右下角或底部，但不能挤压文字空间
- **空间分配**：确保文字有充足的显示空间，图表仅作为视觉点缀
- **容器结构模板**：
  ```html
  <div class="relative p-4">
    <div class="pr-32">
      <!-- 文字内容，右侧留出图表空间 -->
    </div>
    <div class="absolute bottom-4 right-4">
      <div style="width: 120px; height: 100px;">
        <!-- 小尺寸图表 -->
      </div>
    </div>
  </div>
  ```
- **雷达图优化**：简化设计，只保留主要轮廓线，移除多余装饰，确保形状为凸多边形
- **文字优先**：确保所有文字内容都有足够空间完整显示

**响应式布局要求（重要）：**
- 主容器设置：max-w-6xl mx-auto，避免PC端过度拉伸
- 网格布局：grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4
- 间距配置：gap-4 md:gap-6 lg:gap-8
- 最小高度：min-h-[200px] 确保卡片内容充实
- 内边距：p-4 md:p-6 lg:p-8
- 重要内容跨列：col-span-1 md:col-span-2 lg:col-span-3
- 不要生成任何页脚（footer）、版权信息
- **报告生成日期显示**：在报告顶部合适位置显示"报告生成日期: {{ current_date }}"

**报告内容结构和要点（请将这些内容点用系统指令中描述的Bento Grid等风格巧妙地组织和呈现出来）：**

1. **报告总结与核心指标 (Summary First & Big Numbers)**:
   - 立即展示对账号最核心的诊断结论（例如：增长潜力高/低，主要问题点，最大优势）
   - 使用超大字体或数字突出显示2-3个最关键的数据指标（例如：预估互动率、粉丝增长瓶颈、内容方向匹配度等）
   - 此部分应占据首屏或重要Bento格

2. **内容策略与表现深度分析**:
   - 分析笔记类型、主题覆盖、发文频率、内容原创性、封面与标题吸引力
   - 结合诊断数据中的互动数据（点赞、收藏、评论）分析哪些内容表现好/差，及其可能原因

3. **粉丝画像与互动行为洞察**:
   - 基于诊断数据，推测粉丝的兴趣偏好、年龄段、活跃时段等
   - 分析粉丝评论内容的情感倾向和主要诉求
   - 这部分可以用文本描述配合小图标的Bento格

4. **商业化潜力评估 (聚焦私域引流)**:
   - 评估账号简介、笔记内容、评论区互动中引导私域的策略和效果
   - 分析当前私域引流的优势（如强需求内容）和不足（如引导方式不明确）
   - 此部分可以用对比强烈的Bento格（例如，现状 vs 理想状态）

5. **综合诊断与可执行优化建议 (Actionable Recommendations)**:
   - 对账号整体情况进行总结性评价
   - 提供3-5条具体的、步骤清晰的优化建议，每条建议都应着眼于提升内容质量、粉丝互动和私域引流效果
   - 每条建议可以设计成一个独立的Bento Grid单元格，包含标题、简述、和可能的预期效果（用小字英文点缀）

6. **（可选）风险提示与未来展望**:
   - 如果诊断数据揭示了潜在风险（如平台规则、竞争加剧），简要提示
   - 对账号未来的发展方向给出简短展望

**强制性HTML结构模板：**
- 页面外壳：<div class="min-h-screen bg-white">
- 内容主体：<div class="max-w-6xl mx-auto p-4 md:p-6 lg:p-8">
- Grid容器：<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
- 每个卡片：<div class="bg-white rounded-lg border p-4 md:p-6 min-h-[200px]">

**视觉层次实现指南：**
- **重要数据卡片**：
  ```html
  <div class="bg-white rounded-lg border-2 border-blue-200 p-6 shadow-lg">
    <div class="text-7xl font-bold text-gray-900 mb-2">85%</div>
    <div class="text-xl font-semibold text-gray-700 mb-1">互动率提升潜力</div>
    <div class="text-sm text-gray-500">基于同类账号对比分析</div>
  </div>
  ```
- **问题警示卡片**：
  ```html
  <div class="bg-red-50 rounded-lg border-2 border-red-200 p-6">
    <div class="text-2xl font-bold text-red-600 mb-2">⚠️ 需要关注</div>
    <div class="text-lg font-semibold text-gray-800 mb-1">发文频率不稳定</div>
    <div class="text-sm text-gray-600">影响粉丝粘性和算法推荐</div>
  </div>
  ```
- **优势突出卡片**：
  ```html
  <div class="bg-green-50 rounded-lg border-2 border-green-200 p-6">
    <div class="text-2xl font-bold text-green-600 mb-2">✅ 优势明显</div>
    <div class="text-lg font-semibold text-gray-800 mb-1">内容原创性高</div>
    <div class="text-sm text-gray-600">原创内容占比达到90%以上</div>
  </div>
  ```

**视觉层次实现指南：**
- **避免单一颜色**：绝对不要让所有文字都是同一种颜色（如全部蓝色）
- **标题层次**：
  - 主标题使用 `text-gray-900 font-bold text-3xl`
  - 副标题使用 `text-gray-700 font-semibold text-xl`
  - 正文使用 `text-gray-600 text-base`
- **数字突出**：重要数字使用超大字体和强调色
  - 好的数据：`text-green-600 text-6xl font-bold`
  - 需要关注：`text-orange-500 text-6xl font-bold`
  - 问题数据：`text-red-500 text-6xl font-bold`
- **卡片差异化**：
  - 重要卡片：`border-l-4 border-blue-500 bg-blue-50`
  - 成功卡片：`border-l-4 border-green-500 bg-green-50`
  - 警告卡片：`border-l-4 border-orange-500 bg-orange-50`
  - 问题卡片：`border-l-4 border-red-500 bg-red-50`

**推荐颜色方案与使用规范：**
- **主色调**：#2563EB (蓝色) - 用于主要标题和重要按钮
- **强调色**：#DC2626 (红色) - 用于警告和紧急信息
- **成功色**：#059669 (绿色) - 用于正面数据和成功指标
- **中性色**：#6B7280 (灰色) - 用于辅助文字和说明
- **背景色使用原则**：
  - 主要内容区域：始终使用白色背景 (bg-white)
  - 强调卡片：使用浅色背景如 bg-blue-50, bg-green-50, bg-red-50
  - 避免使用中等饱和度背景色如 bg-blue-400, bg-blue-500
  - 如需彩色背景，使用深色 (如 bg-blue-900) 配白色文字
- **文字颜色规范**：
  - 白色背景：text-gray-900 (深色文字)
  - 浅色背景：text-gray-800 (深色文字)
  - 深色背景：text-white (白色文字)
  - 彩色背景：确保对比度至少4.5:1

**图表设计规范（小装饰图表）：**
- **紧凑尺寸**：图表尺寸120px x 100px，作为装饰元素不占主要空间
- **线条设计**：边界线1.5px，数据区域半透明填充（opacity: 0.4）
- **标签简化**：使用10px小字体，只显示关键标签，避免文字密集
- **透明背景**：完全透明背景，与卡片背景融合
- **极简原则**：移除所有装饰元素，只保留核心数据形状
- **雷达图形状要求**：确保雷达图形状为凸多边形，避免出现凹陷或非凸形状，所有数据点连线应形成平滑的凸包
- 标签文字极简，避免重叠和拥挤

**布局决策指南（文字优先布局）**：
- **文字优先模式**：文字内容占据主要空间，图表作为小装饰
- **灵活结构**：
  1. **主体**：文字内容区域（占据80%空间）
  2. **装饰**：小图表元素（占据20%空间，不干扰阅读）
- **定位策略**：
  - 图表可以放在右下角（absolute定位）
  - 或者放在底部中央（但尺寸要小）
  - 文字区域预留图表空间（如右侧padding）
- **尺寸控制**：
  - 卡片最小高度：min-h-[200px]（不强制400px）
  - 图表固定小尺寸：120px x 100px
  - 文字行高：leading-relaxed确保可读性
- **核心原则**：
  - 文字完整可读是第一优先级
  - 图表仅作视觉装饰，不能影响文字布局
  - 根据内容量灵活调整布局，不拘泥于固定模式

**避免单调蓝色的设计原则**：
- **绝对禁止**：整个页面使用单一蓝色调，导致层次不清
- **必须使用**：多层次的灰色系统作为主要文字颜色
- **强调色使用**：
  - 绿色用于正面数据和优势
  - 红色/橙色用于问题和警示
  - 蓝色仅用于中性信息，不能作为主色调
- **对比度要求**：
  - 主标题与背景对比度至少4.5:1
  - 正文与背景对比度至少3:1
  - 重要数据使用深色（#1F2937）确保突出
- **视觉层次检查**：
  - 页面必须有明显的视觉焦点（超大数字或标题）
  - 不同重要级别的内容必须有明显的颜色和大小区别
  - 避免所有文字使用相同或相似的颜色

请确保报告不仅信息全面、分析到位，而且视觉上高度吸引人，交互体验流畅，完全符合系统指令中的所有高级设计要求。不要遗漏【小红书账号诊断数据】中的任何重要信息。
{% endset %}

{# 导出变量供代码使用 #}
{{ system_prompt }}
---SEPARATOR---
{{ user_prompt }} 
**图
表与文字协调的布局建议**：

**推荐布局模式**：
1. **右下角装饰布局**：文字占主体，小图表在右下角
2. **底部小图表布局**：文字在上，小图表在底部中央

**布局模板选项**：

**选项1：右下角装饰**
```html
<div class="bg-white rounded-lg border p-6 min-h-[200px] relative">
  <div class="pr-32">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">标题</h3>
    <div class="text-base text-gray-600 space-y-2 leading-relaxed">
      <p>文字内容...</p>
    </div>
  </div>
  <div class="absolute bottom-4 right-4">
    <div style="width: 120px; height: 100px;">
      <!-- 小图表 -->
    </div>
  </div>
</div>
```

**选项2：底部小图表**
```html
<div class="bg-white rounded-lg border p-6 min-h-[200px]">
  <div class="mb-4">
    <h3 class="text-2xl font-bold text-gray-900 mb-4">标题</h3>
    <div class="text-base text-gray-600 space-y-2 leading-relaxed">
      <p>文字内容...</p>
    </div>
  </div>
  <div class="flex justify-center mt-4">
    <div style="width: 120px; height: 100px;">
      <!-- 小图表 -->
    </div>
  </div>
</div>
```

**文字堆叠问题解决方案**：
- **行高控制**：所有文字元素必须设置合适的`line-height`（建议1.4-1.6）
- **段落间距**：段落之间添加`margin-bottom: 15px`确保分离
- **字体大小层级**：严格控制字体大小差异，避免过大字体挤压其他内容
- **容器高度**：为文字容器设置`min-height`确保有足够空间
- **换行处理**：长文本自动换行，使用`word-wrap: break-word`

记住：好的设计是灵活的，根据内容调整布局，而不是让内容适应僵化的布局！

**静态报告约束：**
- 绝对禁止任何<button>、<a href>、onclick等交互元素
- 不要生成"立即咨询"、"联系我们"、"获取服务"等行动召唤按钮
- 所有内容必须是纯展示性的文字、图表和数据
- 报告日期必须使用传入的current_date参数，格式为"YYYY年MM月DD日"
*
*色彩搭配最佳实践**：

**主要内容区域配色**：
- **执行摘要区域**：深灰标题(#1F2937) + 白色背景 + 蓝色边框强调
- **核心数据展示**：超大深色数字(#111827) + 中灰说明文字(#6B7280)
- **优势展示**：绿色背景(bg-green-50) + 深绿标题(text-green-700) + 深灰正文
- **问题提示**：红色背景(bg-red-50) + 深红标题(text-red-700) + 深灰正文
- **建议方案**：蓝色背景(bg-blue-50) + 深蓝标题(text-blue-700) + 深灰正文

**文字层次标准模板**：
```html
<!-- 超重要数据 -->
<div class="text-6xl font-bold text-gray-900">85%</div>

<!-- 主标题 -->
<h1 class="text-3xl font-bold text-gray-900 mb-4">账号诊断报告</h1>

<!-- 副标题 -->
<h2 class="text-xl font-semibold text-gray-700 mb-2">内容策略分析</h2>

<!-- 重要文字 -->
<p class="text-lg font-medium text-gray-800">关键发现</p>

<!-- 正文 -->
<p class="text-base text-gray-600">详细分析内容...</p>

<!-- 辅助信息 -->
<span class="text-sm text-gray-500">数据来源说明</span>
```

**卡片设计差异化**：
- **重要程度1**：白色背景 + 彩色边框 + 阴影 + 大字体
- **重要程度2**：浅色背景 + 同色边框 + 中等字体  
- **重要程度3**：白色背景 + 灰色边框 + 普通字体

**强制颜色对比度修复规范（关键）：**
1. **白色背景区域**：
   - 标题文字：#1F2937（深灰黑）
   - 正文文字：#374151（中灰）
   - 说明文字：#6B7280（浅灰）

2. **彩色背景区域**：
   - 绿色背景(#10B981)：使用白色文字(#FFFFFF)
   - 蓝色背景(#3B82F6)：使用白色文字(#FFFFFF)
   - 红色背景(#EF4444)：使用白色文字(#FFFFFF)
   - 橙色背景(#F59E0B)：使用深色文字(#1F2937)

3. **渐变背景处理**：
   - 添加半透明深色遮罩：background: rgba(0,0,0,0.3)
   - 或使用文字阴影：text-shadow: 1px 1px 2px rgba(0,0,0,0.5)

**SVG图表文字强制规范**：
1. **文字元素必须明确设置颜色**：
   - 深色背景图表：<text fill="#FFFFFF" font-size="14">文字</text>
   - 浅色背景图表：<text fill="#1F2937" font-size="14">文字</text>
   - 禁止使用：<text>文字</text>（无fill属性）

2. **具体图表文字规范**：
   - 柱状图标签：<text x="位置" y="位置" text-anchor="middle" fill="#1F2937" font-size="12">数值</text>
   - 饼图标签：<text x="位置" y="位置" text-anchor="start" fill="#1F2937" font-size="11">标签</text>
   - 雷达图维度：<text x="计算位置" y="计算位置" text-anchor="start" fill="#1F2937" font-size="13">维度名</text>
   - 折线图数值：<text x="位置" y="位置" text-anchor="middle" fill="#374151" font-size="12">数值</text>

3. **文字间距要求**：
   - 图表标题与图表间距：至少15px
   - 轴标签与轴线间距：至少8px
   - 雷达图维度: 各维度标签距离适当，文字大小不小于12px

**视觉强调重点：**
- 使用超大字体突出核心数据（72px+）
- 重要信息使用高对比度颜色组合
- 关键指标使用醒目的背景色块
- 数据图表保持简洁，突出数据本身
- 避免过度装饰，保持专业感

记住：视觉层次是信息传达的关键，绝不能让所有内容看起来一样重要！好的设计是灵活的，根据内容调整布局，而不是让内容适应僵化的布局！