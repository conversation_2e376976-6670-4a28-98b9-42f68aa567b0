{# 销售提案HTML生成模板 #}
{# 系统提示词 #}
{% set system_prompt %}
你是一位专业的响应式网页设计师和销售策略专家，擅长将销售提案转化为在各种设备上都能完美展示的现代化网页。你的任务是创建一个专业、现代、富有视觉冲击力的响应式销售提案网页，确保在移动端、平板和桌面端都有出色的用户体验。

**严格输出要求：**
- 你的输出必须只包含完整、可执行的HTML代码
- 绝对禁止使用任何markdown代码块标记，如```html、```或任何其他```标记
- 不要包含任何解释、注释或其他文本
- 输出的HTML代码必须是完整的，从<!DOCTYPE html>开始到</html>结束
- 不要有任何前缀或后缀说明
- 直接输出HTML内容，第一个字符必须是<!DOCTYPE html>的"<"符号
- 最后一个字符必须是</html>的">"符号
{% endset %}

{# 用户提示词 #}
{% set user_prompt %}
基于小红书的销售提案数据和生成日期:
*** 销售提案开头 ***
{{ proposal_result }}
*** 销售提案结尾 ***

生成日期: {{ current_date }}

请生成响应式网页，不要遗漏信息根据上面内容生成一个 HTML 动态网页。要求：

**视觉冲击力设计要求：**
1. **强化视觉层次**：整体字数控制在 800 字左右，使用响应式Bento Grid风格的高冲击力视觉设计
   - **主色调**：#2E86AB（商务蓝）- 用于主要标题和信任建立
   - **强调色**：#A23B72（深紫红）- 用于关键发现和紧急机会  
   - **成功色**：#10B981（翠绿色）- 用于正面预期和ROI展示
   - **警告色**：#F59E0B（橙黄色）- 用于时间紧迫和风险提示
   - **渐变背景**：重要模块使用渐变背景增强视觉吸引力
2. **超大数字与震撼标题**：
   - **核心数据用72-96px超大字体**：转化率、ROI、时间窗口等关键数字
   - **标题层次分明**：h1用48px，h2用36px，h3用24px，确保视觉冲击
   - **中英文混搭**：中文粗体大标题 + 英文小标题点缀，营造专业感
3. **数据可视化强化**：
   - **SVG图表集群**：圆环图、柱状图、趋势线、雷达图
   - **进度条动画**：机会窗口倒计时、ROI预期进度
   - **对比图表**：竞争对手vs头部KOL vs当前状态的三重对比
   - **热力图展示**：优先级、风险等级、机会大小的颜色编码
4. **科技感视觉元素**：
   - **渐变卡片**：重要内容用渐变背景突出
   - **阴影立体效果**：卡片使用多层阴影增强立体感
   - **脉动动画**：重要指标添加subtle pulse效果
   - **透明度层次**：用alpha通道创建层次感，但避免完全透明

**技术要求：**
8. 使用HTML5、TailwindCSS 3.0+（通过CDN引入）
9. 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
10. 页面保持静态，不要使用任何动画效果（包括GSAP、CSS动画、JavaScript动画等）
11. 避免使用可能导致元素隐藏的CSS属性：opacity: 0, display: none, visibility: hidden
12. 避免使用emoji作为主要图标
13. 不要省略内容要点
14. **禁止生成交互按钮**：不要生成任何<button>、<a>标签的交互按钮或链接
15. **纯静态报告**：所有内容必须是纯展示性的，不包含任何点击、提交等交互功能
16. **报告日期使用传入参数**：使用传入的current_date参数显示报告生成日期，不要自己编制日期
17. 不要生成任何页脚（footer）、版权信息

**响应式布局策略：**
15. 移动端（<768px）：单列布局，卡片间距适中，字体大小优化触摸操作
16. 平板端（768px-1024px）：双列或三列布局，保持内容层次
17. 桌面端（>1024px）：多列Bento Grid布局，充分利用屏幕空间
18. 使用Tailwind的响应式断点：sm:, md:, lg:, xl:, 2xl:
19. 确保触摸友好：按钮和可交互元素最小44px，间距充足
20. 文本可读性：移动端最小16px字体，行高1.5以上
21. 图片和图表：使用max-w-full确保不溢出，保持宽高比
22. 内边距和外边距：移动端使用较小间距，大屏幕逐步增加

**桌面端布局优化（关键修复）：**
23. 主容器最大宽度限制：max-w-7xl，在超宽屏上避免过度拉伸
24. 内容居中对齐：mx-auto 确保在大屏幕上居中显示
25. Bento Grid 响应式列数：grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5
26. 合理的间距设置：gap-4 md:gap-6 lg:gap-8，避免元素过于紧密
27. 卡片最小高度：min-h-[180px] md:min-h-[220px]，确保内容区域充足
28. 防止内容过窄：每个销售模块至少占据合理的列数

**排版优化要求：**
23. 对于大段文本内容，必须进行分点处理，使用项目符号(bullets)和编号列表提升可读性
24. 销售论证内容应该分解为易于阅读的要点，每个要点不超过2-3行
25. 使用HTML的<ul>、<ol>、<li>标签创建清晰的列表结构
26. 重要观点使用<strong>标签加粗突出
27. 为长段落添加适当的段落间距和行高
28. 使用小标题(<h4>、<h5>)来分割不同的销售主题
29. 所有分析性内容必须使用大小标题以及项目符号列表格式，避免大段连续文本
30. 优化建议必须使用编号列表，每个建议包含具体的执行步骤

**销售提案内容结构和要点（请将这些内容点用响应式Bento Grid等风格巧妙地组织和呈现出来）：**

1. **颠覆性发现与紧急机会 (Critical Insights & Urgent Opportunities)**:
   - 立即展示诊断报告中最重要的颠覆性洞察和反直觉发现
   - 使用超大字体或数字突出显示2-3个最关键的发现指标（必须包含具体数据和百分比）
   - 添加倒计时元素显示机会窗口期（如"剩余XX天关键时间窗口"）
   - 此部分在移动端占据首屏全宽，在桌面端可以是较大的Bento格
   - **重要：使用项目符号列表呈现关键发现，每个发现必须包含数据源引用和量化指标**

2. **竞争环境双维洞察 (Competitive Landscape Insights)**:
   **A. 头部KOL强项学习机会**：
   - 基于深度研究，展示行业领先者的成功密码和核心强项
   - 详细分析可学习的策略、技巧和最佳实践案例
   - 提供具体的学习路径和追赶计划，建立成长标杆
   - 移动端垂直展示，桌面端使用对比卡片布局

   **B. 竞争对手弱点突破战略**：
   - 识别同级别竞争对手的战略盲区和执行短板
   - 详细说明基于深度研究的差异化突破路径
   - 展示市场空白机会和超越策略，建立竞争信心
   - **重要：使用项目符号列表呈现分析要点，避免大段文字**

3. **受众深度洞察与内容革新 (Deep Audience Insights & Content Innovation)**:
   - 基于诊断数据，分析受众隐性需求和行为预测
   - 提出颠覆性的内容策略和互动方式
   - 这部分可以用文本描述配合小图标的响应式Bento格
   - **重要：将洞察内容分解为清晰的要点，使用编号列表或项目符号**

4. **风险预警与防护体系 (Risk Management & Protection System)**:
   - 展示诊断报告中的关键风险识别和预警信号
   - 提供危机应对方案和保护价值说明
   - 此部分可以用对比强烈的响应式Bento格（例如，风险 vs 保护方案）

5. **专业服务方案与学习成长价值 (Professional Solutions & Growth Value)**:
   **A. 头部KOL成功模式学习服务**：
   - 展示行业领先者策略解构和最佳实践分析能力
   - 提供系统性学习方案和标杆追赶路径设计
   - 说明如何将头部KOL的成功经验转化为可执行的行动计划

   **B. 竞争优势构建与差异化策略**：
   - 展示深度研究能力和竞争情报分析优势
   - 说明颠覆性策略制定和持续竞争监控支持
   - 提供基于弱点突破的差异化竞争方案
   - 使用响应式卡片布局，每个服务价值一个卡片
   - **重要：服务价值必须使用项目符号和小标题进行结构化呈现**

6. **90天转型计划与里程碑 (90-Day Transformation Plan)**:
   - 详细展示三个阶段的具体计划和关键指标
   - 每个阶段可以设计成一个独立的响应式Bento Grid单元格
   - 移动端垂直堆叠，桌面端可以使用网格布局
   - **重要：转型计划必须使用编号列表，每个阶段包含具体的执行步骤**

7. **投资回报可视化分析中心 (ROI Visual Analysis Center)**:
   - **ROI仪表盘**：用圆环图展示投资回报率，用颜色编码显示保守/可能/乐观三种情况
   - **成本效益对比图**：用柱状图对比机会成本、竞争成本和预期收益（具体数字和时间节点）
   - **投资回收期倒计时**：用进度条和数字倒计时展示回本时间窗口
   - **风险收益矩阵**：用散点图或热力图展示不同策略的风险与收益关系
   - **增长预测曲线**：用折线图展示3-6-12个月的增长预期轨迹
   - **关键KPI对比卡片**：三种情景（保守/可能/乐观）的关键指标并排对比
   - **时间价值可视化**：用时间轴图表展示延迟行动的机会成本递增
   - **重要：所有数据点用超大字体突出，配合图表说明，每个数据包含具体金额和时间**

8. **立即行动的价值与风险对比 (Immediate Action Value vs Risk Comparison)**:
   - 基于诊断结果，明确立即行动的价值和延迟风险
   - 包含具体的时间窗口和效果预期
   - 在各种屏幕尺寸上都保持良好的可读性
   - **报告日期显示**：在报告顶部合适位置显示"报告生成日期: {{ current_date }}"
   - **重要：价值对比使用对比表格或编号列表，每个要点包含具体的量化数据**

**特别注意事项：**
- **HTML生成要求：**
  - 输出必须是纯HTML代码，不能包含任何模板变量或语法
  - 严禁使用markdown代码块语法（```html、```等），直接输出HTML
  - 所有数据都必须是硬编码的HTML内容，不能有动态变量
  - 页面布局要自动调整，确保在各种情况下都保持良好的视觉效果
  - 确保所有章节正常显示，保持内容的完整性和连贯性
  - 响应内容的第一行必须是<!DOCTYPE html>，最后一行必须是</html>
- **内容结构化要求：**
  - 所有销售论证和分析性内容都必须进行结构化处理，使用HTML列表标签
  - 避免出现超过5行的连续文本段落，将其分解为要点列表
  - 使用适当的HTML语义标签（<section>、<article>、<aside>）来组织内容结构
  - 确保每个Bento Grid单元格内的内容都有清晰的层次结构
- **销售驱动设计：**
  - 使用紧急感的视觉元素（如警告色、倒计时风格）突出时间敏感性
  - 采用对比强烈的颜色方案来强调价值主张
  - 使用数字和数据来增强说服力
  - **禁止行动召唤按钮**：不要生成任何CTA按钮、链接或交互元素

**强制布局结构（必须严格遵循）：**
- 页面主容器：<div class="min-h-screen bg-white">
- 内容容器：<div class="max-w-7xl mx-auto p-4 md:p-6 lg:p-8">
- 标题区域：<div class="mb-8 md:mb-12 text-center">
- Bento Grid主体：<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-4 md:gap-6 lg:gap-8">
- 重要模块跨列：class="col-span-1 md:col-span-2 lg:col-span-3" 等

请确保销售提案不仅信息全面、逻辑清晰，而且在所有设备上都有出色的视觉效果和用户体验。特别注意移动端的触摸操作便利性和内容的层次结构。不要遗漏【销售提案数据】中的任何重要信息。

**可读性和用户体验强化要求：**
- **文本层次优化**：
  - **震撼性标题**：关键机会用72-96px超大字体展示
  - **重要数据突出**：ROI、转化率等关键数字用60-80px字体
  - **正文可读性**：18-20px字体确保内容易读
  - **注释说明**：12-14px小字提供数据来源和补充信息
- **视觉引导系统**：
  - **渐变高亮**：重要发现用渐变背景突出
  - **脉动效果**：紧急机会添加subtle pulse动画
  - **颜色编码**：绿色=机会，蓝色=稳定，橙色=注意，红色=紧急
  - **图表标注**：每个图表配备清晰的标题和数据说明
- **交互性增强**：
  - **悬停效果**：卡片悬停时显示更多详情
  - **进度动画**：数据加载时的动态展示效果
  - **响应式适配**：确保在所有设备上的最佳显示效果
- **专业性体现**：
  - **数据源标注**：每个重要数据标明来源和可信度
  - **时间节点清晰**：所有预测都标明具体时间范围
  - **风险提示**：用适当的视觉元素标注风险等级

**最终输出格式再次强调：**
直接输出完整的HTML代码，不要使用任何markdown格式或代码块标记。输出应该直接以<!DOCTYPE html>开始，以</html>结束，中间不包含任何非HTML内容。确保页面既有视觉冲击力又保持专业性和可读性。

**静态报告约束：**
- 绝对禁止任何<button>、<a href>、onclick等交互元素
- 不要生成"立即咨询"、"联系我们"、"获取服务"等行动召唤按钮
- 所有内容必须是纯展示性的文字、图表和数据
- 报告日期必须使用传入的current_date参数，格式为"YYYY年MM月DD日"
{% endset %}

{# 导出变量供代码使用 #}
{{ system_prompt }}
---SEPARATOR---
{{ user_prompt }} 