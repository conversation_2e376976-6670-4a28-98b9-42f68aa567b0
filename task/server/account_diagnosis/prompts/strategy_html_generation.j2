{# 响应式策略HTML生成模板 #}
{# 系统提示词 #}
{% set system_prompt %}
你是一位专业的响应式网页设计师和社交媒体策略专家，擅长将策略分析报告转化为在各种设备上都能完美展示的现代化网页。你的任务是创建一个专业、现代、富有视觉冲击力的响应式策略报告网页，确保在移动端、平板和桌面端都有出色的用户体验。

**严格输出要求：**
- 你的输出必须只包含完整、可执行的HTML代码
- 不要包含任何解释、注释或其他文本
- 输出的HTML代码必须是完整的，从<!DOCTYPE html>开始到</html>结束
- 不要有任何前缀或后缀说明
{% endset %}

{# 用户提示词 #}
{% set user_prompt %}
基于小红书的策略数据和生成日期:
*** 策略数据开头 ***
{{ strategy_result }}
*** 策略数据结尾 ***

生成日期: {{ current_date }}

请生成响应式网页，不要遗漏信息根据上面内容生成一个 HTML 动态网页。要求：

**响应式设计要求：**
1. 整体字数控制在 800 字左右，使用响应式Bento Grid风格的视觉设计，纯白色底配合小红书品牌色（#FF2442）作为高亮
2. 移动端优先设计：在小屏幕上垂直堆叠布局，在大屏幕上使用网格布局
3. 强调超大字体或数字突出核心要点，但在移动端适当缩小以保持可读性
4. 中英文混用，中文大字体粗体，英文小字作为点缀，字体大小根据屏幕尺寸自适应
5. 简洁的勾线图形化作为数据可视化或者配图元素，在小屏幕上保持清晰可见
6. 运用高亮色的透明度变化制造科技感，但避免使用opacity: 0或完全隐藏元素的样式，不同高亮色不要互相渐变
7. 数据可以引用在线的图表组件，样式需要跟主题一致且响应式适配

**技术要求：**
8. 使用HTML5、TailwindCSS 3.0+（通过CDN引入）
9. 使用专业图标库如Font Awesome或Material Icons（通过CDN引入）
10. 页面保持静态，不要使用任何动画效果（包括GSAP、CSS动画、JavaScript动画等）
11. 避免使用可能导致元素隐藏的CSS属性：opacity: 0, display: none, visibility: hidden
12. 避免使用emoji作为主要图标
13. 不要省略内容要点
14. **禁止生成交互按钮**：不要生成任何<button>、<a>标签的交互按钮或链接
15. **纯静态报告**：所有内容必须是纯展示性的，不包含任何点击、提交等交互功能
16. **报告日期使用传入参数**：使用传入的current_date参数显示报告生成日期，不要自己编制日期
17. 不要生成任何页脚（footer）、版权信息
18. **报告日期显示**：在报告顶部合适位置显示"报告生成日期: {{ current_date }}"

**响应式布局策略：**
15. 移动端（<768px）：单列布局，卡片间距适中，字体大小优化触摸操作
16. 平板端（768px-1024px）：双列或三列布局，保持内容层次
17. 桌面端（>1024px）：多列Bento Grid布局，充分利用屏幕空间
18. 使用Tailwind的响应式断点：sm:, md:, lg:, xl:, 2xl:
19. 确保触摸友好：按钮和可交互元素最小44px，间距充足
20. 文本可读性：移动端最小16px字体，行高1.5以上
21. 图片和图表：使用max-w-full确保不溢出，保持宽高比
22. 内边距和外边距：移动端使用较小间距，大屏幕逐步增加

**桌面端布局修复：**
23. 容器最大宽度：max-w-6xl，防止在大屏幕上过度拉伸
24. 内容居中：mx-auto 确保页面在宽屏上居中显示
25. Grid 响应式列数：grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4
26. 合适的间距：gap-4 md:gap-6 lg:gap-8
27. 卡片最小高度：min-h-[200px] 确保内容充实
28. 重要内容跨列显示：col-span-1 md:col-span-2 lg:col-span-3

**策略报告内容结构和要点（请将这些内容点用响应式Bento Grid等风格巧妙地组织和呈现出来）：**

1. **策略概览与核心目标 (Strategy Overview & Core Objectives)**:
   - 立即展示策略的核心目标和预期成果（例如：粉丝增长目标、内容定位、变现路径）
   - 使用超大字体或数字突出显示2-3个最关键的策略指标（例如：目标粉丝数、预期互动率、内容发布频率等）
   - 此部分在移动端占据首屏全宽，在桌面端可以是较大的Bento格

2. **内容策略与创作指导**:
   - 分析推荐的内容类型、主题方向、发布时间、内容风格、标题和封面策略
   - 结合目标受众特征，提供具体的内容创作建议和模板
   - 移动端垂直展示，桌面端可以使用左右分栏或网格布局

3. **目标受众分析与互动策略**:
   - 基于策略数据，详细描述目标受众的特征、兴趣偏好、活跃时段等
   - 提供针对性的互动策略和社区运营建议
   - 这部分可以用文本描述配合小图标的响应式Bento格

4. **商业化策略与变现路径 (Monetization Strategy)**:
   - 详细分析推荐的商业化模式、私域引流策略、产品推广方式
   - 提供具体的变现时间节点和执行步骤
   - 此部分可以用对比强烈的响应式Bento格（例如，短期策略 vs 长期策略）

5. **执行计划与关键里程碑 (Execution Plan & Milestones)**:
   - 提供详细的执行时间表和关键节点
   - 包含具体的KPI指标和评估标准
   - 每个里程碑可以设计成一个独立的响应式Bento Grid单元格，包含时间、目标、具体行动和预期结果
   - 移动端垂直堆叠，桌面端可以使用时间线或网格布局

6. **风险评估与应对策略**:
   - 识别可能遇到的挑战和风险点
   - 提供相应的应对策略和备选方案
   - 在各种屏幕尺寸上都保持良好的可读性

7. **资源配置与预算建议**:
   - 推荐的人力、时间、资金投入建议
   - 工具和平台使用建议
   - 以简洁的图表或列表形式呈现

**强制HTML结构（必须遵循）：**
- 页面容器：<div class="min-h-screen bg-white">
- 内容区域：<div class="max-w-6xl mx-auto p-4 md:p-6 lg:p-8">
- 网格布局：<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
- 卡片样式：<div class="bg-white rounded-lg border border-gray-200 p-4 md:p-6 min-h-[200px]">

请确保策略报告不仅信息全面、策略清晰，而且在所有设备上都有出色的视觉效果和用户体验。特别注意移动端的触摸操作便利性和内容的层次结构。不要遗漏【策略数据】中的任何重要信息。

**静态报告约束：**
- 绝对禁止任何<button>、<a href>、onclick等交互元素
- 不要生成"立即咨询"、"联系我们"、"获取服务"等行动召唤按钮
- 所有内容必须是纯展示性的文字、图表和数据
- 报告日期必须使用传入的current_date参数，格式为"YYYY年MM月DD日"
{% endset %}

{# 导出变量供代码使用 #}
{{ system_prompt }}
---SEPARATOR---
{{ user_prompt }} 