#!/usr/bin/env python3
"""
重构后的诊断服务启动脚本

这个脚本位于 account_diagnosis 目录下，可以直接运行重构后的诊断系统。
"""

import argparse
import asyncio
import logging
import os
import sys
import signal
import time

# 将项目根目录添加到系统路径 (必须在diagnosis目录之前，避免utils模块冲突)
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
sys.path.insert(0, project_root)

# 添加 diagnosis 目录到路径
diagnosis_dir = os.path.join(os.path.dirname(__file__), 'diagnosis')
sys.path.append(diagnosis_dir)

from task import ENV
from diagnosis.services.diagnosis_service import DiagnosisService


def setup_logging(verbose: bool = False):
    """设置日志配置"""
    level = logging.DEBUG if verbose else logging.INFO
    
    # 创建日志目录
    os.makedirs('logs', exist_ok=True)
    
    # 配置日志格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 文件处理器
    file_handler = logging.FileHandler('logs/diagnosis_service_v2.log', encoding='utf-8')
    file_handler.setLevel(level)
    file_handler.setFormatter(formatter)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(level)
    console_handler.setFormatter(formatter)
    
    # 根日志器配置
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    return logging.getLogger(__name__)


async def run_service(diagnosis_mode: str, run_mode: str, max_concurrent_tasks: int, verbose: bool):
    """运行诊断服务"""
    logger = setup_logging(verbose)
    env = ENV.lower()
    
    # 计算运行时长（cron模式下约99.5分钟自动退出）
    if run_mode == "cron":
        max_runtime_seconds = 6000
        start_time = time.time()
        logger.info(f"Cron模式: 将在 {max_runtime_seconds/60:.1f} 分钟后自动退出")
    else:
        max_runtime_seconds = None
        start_time = None
        logger.info("Daemon模式: 持续运行直到手动停止")
    
    try:
        logger.info("=" * 60)
        logger.info("启动重构后的诊断服务 v2.0")
        logger.info("=" * 60)
        logger.info(f"运行模式: {run_mode}")
        logger.info(f"诊断模式: {diagnosis_mode}")
        logger.info(f"最大并发任务: {max_concurrent_tasks}")
        logger.info(f"环境: {env}")
        logger.info(f"详细日志: {verbose}")
        logger.info(f"队列环境: {ENV}")
        logger.info("=" * 60)
        
        # 创建诊断服务实例
        service = DiagnosisService(
            max_concurrent_tasks=max_concurrent_tasks,
            env=env,
            default_mode=diagnosis_mode
        )
        
        # 在cron模式下，创建定时退出任务
        if run_mode == "cron":
            async def auto_shutdown():
                await asyncio.sleep(max_runtime_seconds)
                logger.info(f"Cron模式运行时间达到 {max_runtime_seconds/60:.1f} 分钟，自动退出")
                await service.shutdown()
            
            # 启动自动关闭任务
            shutdown_task = asyncio.create_task(auto_shutdown())
            
            # 同时启动服务和定时器
            try:
                await asyncio.gather(
                    service.start(),
                    shutdown_task,
                    return_exceptions=True
                )
            except Exception as e:
                logger.error(f"服务运行异常: {e}", exc_info=True)
                # 确保服务正确关闭
                await service.shutdown()
                raise
        else:
            # daemon模式，正常启动服务
            await service.start()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号，服务正在关闭...")
        if 'service' in locals():
            await service.shutdown()
    except Exception as e:
        logger.error(f"服务运行异常: {e}", exc_info=True)
        if 'service' in locals():
            await service.shutdown()
        raise


def main():
    """主函数 - 命令行入口"""
    parser = argparse.ArgumentParser(
        description="重构后的账号诊断服务 v2.0",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                    # 使用默认配置启动(cron模式)
  %(prog)s --run-mode daemon                  # daemon模式持续运行
  %(prog)s --diagnosis-mode deep_research     # 启用深度研究模式
  %(prog)s --concurrent 8                     # 设置8个并发任务
  %(prog)s --verbose                          # 详细日志

运行模式说明:
  cron: 定时任务模式，运行99.5分钟后自动退出，适合cron调度
  daemon: 守护进程模式，持续运行直到手动停止
        """
    )
    
    parser.add_argument(
        "--run-mode",
        choices=["cron", "daemon"],
        default="cron",
        help="运行模式: cron(定时任务,99.5分钟后自动退出) 或 daemon(持续运行), 默认: cron"
    )
    
    parser.add_argument(
        "--diagnosis-mode",
        choices=["basic", "deep_research"],
        default="deep_research",
        help="诊断模式: basic(基础) 或 deep_research(深度研究), 默认: deep_research"
    )
    
    parser.add_argument(
        "--concurrent",
        type=int,
        default=8,
        metavar="N",
        help="最大并发任务数, 默认: 8"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细日志输出"
    )

    args = parser.parse_args()
    
    # 验证参数
    if args.concurrent < 1 or args.concurrent > 20:
        parser.error("并发任务数必须在1-20之间")
    
    # 显示启动信息
    runtime_info = "99.5分钟后自动退出" if args.run_mode == "cron" else "持续运行"
    
    print(f"""
🚀 重构后的账号诊断服务 v2.0.0
================================
运行模式: {args.run_mode} ({runtime_info})
诊断模式: {args.diagnosis_mode}
并发任务: {args.concurrent}
详细日志: {args.verbose}
队列环境: {ENV}
================================
正在启动服务...
""")
    
    try:
        asyncio.run(run_service(
            args.diagnosis_mode, 
            args.run_mode, 
            args.concurrent, 
            args.verbose
        ))
    except KeyboardInterrupt:
        print("\n⚠️  服务已停止")
    except Exception as e:
        print(f"\n❌ 服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()