import os
import sys
import json
from typing import Dict, <PERSON><PERSON>, Optional
from datetime import datetime

# 将项目根目录添加到系统路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

from task import ENV, GEMINI_PRO_MODEL
from task.lib.call_claude import gemini, gpt, claude
from task.lib.prompt_utils import clean_html_output, clean_json_output
from task.lib.json_utils_enhanced import enhanced_validate_and_fix_ai_json, safe_redis_serialize_with_validation
from .base_async_service import BaseAsyncService
from .diagnosis_core import process_task_async
# from .core.diagnosis_engine import DiagnosisE<PERSON><PERSON>, DiagnosisContext, DiagnosisMode
# from .core.config_manager import DiagnosisConfig
# from .core.resource_manager import get_resource_manager
# from .core.status_manager import UnifiedProgressTracker


class DiagnosisService(BaseAsyncService):
    """诊断服务实现"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300, use_service_lock: bool = True, redis_connection_timeout: int = 30, redis_socket_timeout: int = 30, default_mode: str = "basic"):
        input_queue = ENV.lower() + ":q:diagnosis:request"
        output_queue = ENV.lower() + ":q:diagnosis:response"
        super().__init__("diagnosis", input_queue, output_queue, max_concurrent_tasks, run_mode, timeout, use_service_lock, redis_connection_timeout, redis_socket_timeout)
        self.default_mode = default_mode
        # 消息排序相关
        import time
        import asyncio
        self._start_time = time.time()
        self._message_sequence = 0
        self._message_lock = asyncio.Lock()
    
    async def send_progress_status(self, input_data: Dict, progress: int, status: str, msg_en: str, msg_cn: str):
        """发送进度状态的统一方法 - 中央化版本，通过GlobalStatusCoordinator"""
        try:
            from core.status_manager import GlobalStatusCoordinator, UnifiedTaskStatus
        except ImportError:
            # 备用导入路径
            try:
                from .core.status_manager import GlobalStatusCoordinator, UnifiedTaskStatus
            except ImportError:
                self.logger.error("无法导入GlobalStatusCoordinator，回退到直接Redis写入")
                # 回退到原始方法
                await self._send_progress_status_fallback(input_data, progress, status, msg_en, msg_cn)
                return
        
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId")
        user_id = task_info.get("userId")
        diagnosis_id = task_info.get("diagnosisId")
        
        # 类型转换
        try:
            if task_id is not None:
                task_id = int(task_id)
            if diagnosis_id is not None:
                diagnosis_id = int(diagnosis_id)
        except (ValueError, TypeError) as e:
            self.logger.error(f"字段类型转换失败: {e}")
            await self._send_progress_status_fallback(input_data, progress, status, msg_en, msg_cn)
            return
        
        if not task_id or not user_id or not diagnosis_id:
            self.logger.error(f"无法发送进度状态：缺少必填字段 taskId={task_id}, userId={user_id}, diagnosisId={diagnosis_id}")
            await self._send_progress_status_fallback(input_data, progress, status, msg_en, msg_cn)
            return
        
        try:
            # 使用新的统一队列系统
            from core.unified_status_queue import get_unified_queue
            
            queue = await get_unified_queue()
            
            # 提交到统一队列
            success = await queue.submit_status_update(
                task_id=task_id,
                status=status,
                progress=float(progress),
                module_name="service_implementation",
                message=msg_cn
            )
            
            if success:
                self.logger.info(f"✅ 状态已提交到统一队列: {progress}% - {msg_cn}")
            else:
                self.logger.warning(f"⚠️ 状态被统一队列拒绝: {progress}% - {msg_cn}")
                
        except Exception as e:
            self.logger.error(f"通过统一队列发送状态失败: {e}")
    
    def _convert_to_unified_status(self, status: str, progress: int) -> 'UnifiedTaskStatus':
        """将API状态和进度转换为UnifiedTaskStatus"""
        try:
            from core.status_manager import UnifiedTaskStatus
        except ImportError:
            from .core.status_manager import UnifiedTaskStatus
        
        # 根据进度范围映射到合适的状态
        if status == "RUNNING":
            if progress <= 1:
                return UnifiedTaskStatus.TASK_QUEUED
            elif progress <= 3:
                return UnifiedTaskStatus.PLANNING
            elif progress <= 8:
                return UnifiedTaskStatus.CRAWLING_ACCOUNT
            elif progress <= 15:
                return UnifiedTaskStatus.SEARCHING
            elif progress <= 30:
                return UnifiedTaskStatus.ANALYZING_INDUSTRY
            elif progress <= 70:
                return UnifiedTaskStatus.PERFORMING_DIAGNOSIS
            elif progress <= 95:
                return UnifiedTaskStatus.GENERATING_REPORT
            else:
                return UnifiedTaskStatus.PAGE_RENDERING
        elif status == "FINISH":
            return UnifiedTaskStatus.TASK_COMPLETED
        elif status == "FAILED":
            return UnifiedTaskStatus.TASK_FAILED
        else:
            return UnifiedTaskStatus.TASK_QUEUED
    
    async def _send_progress_status_fallback(self, input_data: Dict, progress: int, status: str, msg_en: str, msg_cn: str):
        """回退方法：直接写入Redis（当中央协调器不可用时）"""
        import time
        
        task_info = input_data.get("taskInfo", {})
        
        # 确保必填字段存在并转换类型
        user_id = task_info.get("userId")
        diagnosis_id = task_info.get("diagnosisId")
        task_id = task_info.get("taskId")
        
        # 类型转换
        try:
            if task_id is not None:
                task_id = int(task_id)
            if diagnosis_id is not None:
                diagnosis_id = int(diagnosis_id)
        except (ValueError, TypeError) as e:
            self.logger.error(f"字段类型转换失败: {e}")
            return
        
        if not user_id or not diagnosis_id or not task_id:
            self.logger.error(f"缺少必填字段: userId={user_id}, diagnosisId={diagnosis_id}, taskId={task_id}")
            return
        
        progress_status = {
            "taskInfo": {
                "env": ENV.lower(),
                "taskId": task_id,
                "userId": user_id,
                "diagnosisId": diagnosis_id,
                "aiTaskStatus": status,
                "aiTaskMsg": msg_en,
                "aiTaskMsgCN": msg_cn,
                "aiTaskProgress": progress,
                "timestamp": datetime.fromtimestamp(time.time()).isoformat(),
                "elapsed_time": time.time() - getattr(self, '_start_time', time.time())
            }
        }
        
        try:
            status_json = safe_redis_serialize_with_validation(progress_status)
            await self.write_to_queue(self.output_queue, status_json, self.logger)
            self.logger.info(f"✅ 回退方式发送进度状态: {progress}% - {msg_cn}")
        except Exception as e:
            self.logger.error(f"回退方式发送进度状态失败: {e}")
        
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """诊断任务的具体处理逻辑"""
        self.logger.info("检测到账号诊断任务，使用diagnosis_core.py执行...")

        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId")
        user_id = task_info.get("userId")
        diagnosis_id = task_info.get("diagnosisId")

        try:
            # 发送任务开始状态
            await self.send_progress_status(input_data, 1, "RUNNING", "Task received and processing started", "任务已接收，开始处理")
            
            # 创建进度回调函数，用于写入Redis队列 - 中央化版本
            async def progress_callback(status_info: Dict):
                try:
                    # 统一使用send_progress_status方法，通过中央协调器发送
                    if "task_id" in status_info and "taskInfo" not in status_info:
                        # 从旧格式转换
                        progress = int(status_info.get("progress", 0))
                        msg_cn = status_info.get("message", "处理中...")
                        msg_en = status_info.get("message_en", msg_cn)
                        await self.send_progress_status(input_data, progress, "RUNNING", msg_en, msg_cn)
                    elif status_info.get('taskInfo'):
                        # 提取信息后使用统一的中央化发送方法
                        task_info = status_info['taskInfo']
                        progress = int(task_info.get('aiTaskProgress', 1))
                        status = task_info.get('aiTaskStatus', 'RUNNING')
                        msg_cn = task_info.get('aiTaskMsgCN', '处理中...')
                        msg_en = task_info.get('aiTaskMsg', msg_cn)
                        await self.send_progress_status(input_data, progress, status, msg_en, msg_cn)
                except Exception as e:
                    self.logger.error(f"通过中央协调器写入进度状态失败: {e}")
            
            # 确定是否启用深度研究模式
            mode_str = input_data.get("mode", self.default_mode)
            enable_deep_research = mode_str == "deep_research"
            
            # 发送数据处理状态
            await self.send_progress_status(input_data, 2, "RUNNING", "Processing input data and initializing", "正在处理输入数据并初始化")
            
            # 检查和处理输入数据，确保包含必要的字段
            processed_input = self._process_input_data(input_data)
            self.logger.info(f"输入数据处理完成，包含字段: {list(processed_input.keys())}")
            
            # 调用diagnosis_core.py的process_task_async函数
            success, results = await process_task_async(
                input_data=processed_input,
                enable_deep_research=enable_deep_research,
                progress_callback=progress_callback,
                task_id=task_id,
                user_id=user_id,
                diagnosis_id=diagnosis_id,
                env=ENV.lower()
            )
            
            if not success:
                self.logger.error(f"诊断任务执行失败: {results.get('error', '未知错误')}")
                return False, results

            self.logger.info("诊断任务执行成功")
            return True, results
        
        except Exception as e:
            self.logger.error(f"诊断任务执行异常: {e}", exc_info=True)
            return False, {"error": f"诊断任务异常: {str(e)}"}
    
    def _process_input_data(self, input_data: Dict) -> Dict:
        """处理输入数据，确保包含diagnosis_core.py需要的必要字段"""
        processed_data = input_data.copy()
        
        # 确保包含必要字段
        if "marketingGoal" not in processed_data:
            # 尝试从其他字段推断或使用默认值
            processed_data["marketingGoal"] = "涨粉提升"  # 默认营销目标
            self.logger.info(f"缺少marketingGoal字段，使用默认值: {processed_data['marketingGoal']}")
        
        if "accountInfo" not in processed_data:
            self.logger.warning("缺少accountInfo字段")
            processed_data["accountInfo"] = {}
        
        if "noteList" not in processed_data:
            self.logger.warning("缺少noteList字段")
            processed_data["noteList"] = []
        
        # 可选字段的处理
        if "industry" not in processed_data:
            processed_data["industry"] = None
        
        self.logger.info(f"输入数据处理完成，包含字段: {list(processed_data.keys())}")
        return processed_data


class StrategyService(BaseAsyncService):
    """策略服务实现"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300, use_service_lock: bool = True, redis_connection_timeout: int = 30, redis_socket_timeout: int = 30):
        input_queue = ENV.lower() + ":q:generate:report:socialmedia:request"
        output_queue = ENV.lower() + ":q:generate:report:socialmedia:response"
        super().__init__("strategy", input_queue, output_queue, max_concurrent_tasks, run_mode, timeout, use_service_lock, redis_connection_timeout, redis_socket_timeout)
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """策略任务的具体处理逻辑"""
        return await self._generate_strategy_report(input_data)
    
    async def _generate_strategy_report(self, input_data: Dict) -> Tuple[bool, Dict]:
        """生成策略报告"""
        try:
            company_data = input_data.get("socialMediaReportData", {})
            if not company_data:
                return False, {"error": "缺少公司数据：未找到socialMediaReportData字段"}
            
            # 生成JSON格式的策略报告
            json_success, strategy_json = await self._generate_strategy_json(company_data)
            if not json_success:
                return False, {"error": f"JSON生成失败: {strategy_json}"}
            
            # 生成HTML格式的策略报告
            html_success, strategy_html = await self._generate_strategy_html(strategy_json)
            if not html_success:
                return False, {"error": f"HTML生成失败: {strategy_html}"}
            
            return True, {
                "reportJson": strategy_json,
                "reportHtml": strategy_html
            }
            
        except Exception as e:
            return False, {"error": f"策略报告生成异常: {str(e)}"}
    
    async def _generate_strategy_json(self, company_data: Dict) -> Tuple[bool, Dict]:
        """生成JSON格式的策略报告"""
        sys_prompt = """你是一位资深的市场策略专家，擅长根据公司信息制定社交媒体运营策略。

请根据公司信息生成社媒策略报告，必须严格按照以下JSON结构输出：

重要要求：
1. 根节点必须是 "strategyReport"
2. 包含所有指定字段，信息不足的字段设为空字符串""或空数组[]
3. 直接输出JSON，不要任何解释文字或markdown标记
4. 确保JSON格式正确可解析
5. 要求输出要用中文

完整的JSON结构示例：
{
  "strategyReport": {
    "title": "报告标题",
    "executiveSummary": "执行摘要",
    "businessGoals": {
      "title": "业务目标",
      "primaryObjective": "主要目标",
      "suggestedKPIs": {
        "summary": "KPI总结",
        "phase1_Launch": "启动阶段KPI",
        "phase2_Growth": "增长阶段KPI"
      },
      "industryBenchmarks": "行业基准"
    },
    "situationAnalysis": {
      "title": "现状分析",
      "brandEssenceAnalysis": {
        "title": "品牌本质分析",
        "summary": "分析总结"
      },
      "assetAudit": {
        "title": "资产盘点",
        "summary": "盘点总结"
      },
      "inferredCompetitorAnalysis": {
        "title": "竞品分析",
        "summary": "竞品分析总结"
      }
    },
    "targetAudience": {
      "title": "目标受众",
      "primaryPersona": {
        "name": "目标人群名称",
        "demographics": "人口统计学特征",
        "interests": "兴趣爱好",
        "painPoints": "痛点",
        "socialMediaBehavior": "社交媒体行为"
      }
    },
    "contentStrategy": {
      "title": "内容策略",
      "coreMessage": "核心信息",
      "toneOfVoice": "语调风格",
      "contentPillars": ["内容支柱1", "内容支柱2", "内容支柱3"],
      "contentFormats": ["格式1", "格式2", "格式3"],
      "launchContentPlan": "启动内容计划"
    },
    "measurementAndReporting": {
      "title": "效果衡量与报告",
      "reportingFrequency": "报告频率",
      "keyMetricsToWatch": ["指标1", "指标2", "指标3"],
      "optimizationProcess": "优化流程"
    },
    "budgetAllocation": {
      "title": "预算分配",
      "summary": "预算总结",
      "breakdown": {
        "contentCreation": "内容创作预算占比",
        "communitySeeding": "社区运营预算占比",
        "exploratoryAds": "探索性广告预算占比"
      }
    },
    "visualIdentity": {
      "title": "品牌视觉形象",
      "summary": "视觉形象总结",
      "visualStyleAndMood": {
        "title": "视觉风格与氛围",
        "keywords": ["关键词1", "关键词2", "关键词3"],
        "description": "风格描述"
      },
      "colorPalette": {
        "title": "品牌色彩方案",
        "primaryColor": "#主色调",
        "secondaryColors": ["#辅助色1", "#辅助色2"],
        "accentColor": "#强调色"
      },
      "imageAndVideoStyle": {
        "title": "图片与视频风格",
        "photography": "摄影风格",
        "videography": "视频风格",
        "filterAndEditing": "滤镜与编辑风格"
      },
      "graphicElementsAndTemplates": {
        "title": "图形元素与模板",
        "logoUsage": "Logo使用规范",
        "iconsAndIllustrations": "图标与插画风格",
        "layoutTemplates": "布局模板"
      }
    }
  }
}

请严格按照上述结构生成完整的JSON报告，确保所有字段都有内容填充。"""
        
        # 构建用户提示词 - 使用与script_for_strategy.py相同的字段提取逻辑
        marketing_object = company_data.get("marketingObject", "")
        company_name = company_data.get("companyName", "")
        company_industry = company_data.get("companyIndustry", "")
        brand_positioning = company_data.get("companyBrandPositioning", "")
        core_competency = company_data.get("companyCoreCompetency", "")
        target_audience = company_data.get("companyTargetAudience", "")
        brand_vision = company_data.get("companyBrandVision", "")
        
        user_prompt = f"""以下是公司信息：
营销目标：{marketing_object}
公司名称：{company_name}
所属行业：{company_industry}
品牌定位：{brand_positioning}
核心竞争力：{core_competency}
目标受众：{target_audience}
品牌愿景：{brand_vision}

请根据以上公司信息，严格按照系统提示中的JSON结构，生成一份详细的社交媒体策略报告。注意：
1. 必须严格按照指定的JSON格式输出
2. 所有字段都必须填写，如果信息不足可以基于行业常识合理推测填写
3. 不要添加任何解释性文字，只输出JSON
4. 确保JSON格式正确可解析
5. 数组字段如contentPillars、contentFormats、keyMetricsToWatch等必须包含具体的数组元素，不能为空数组
6. 颜色字段必须使用十六进制格式（如#FF5733）"""

        # 调用AI生成JSON - 使用与script_for_strategy.py相同的配置
        success, json_result = await self.call_ai_async(
            gpt, sys_prompt, user_prompt, 
            model_or_schema="flexible",
            model_name="gpt-4o-mini",
            task_name="策略JSON生成",
            max_retries=3,
            timeout=120
        )
        
        if not success:
            return False, {"error": json_result}

        # 处理AI返回的数据类型 - 可能是字符串或已解析的字典
        if isinstance(json_result, dict):
            # AI直接返回了解析后的字典，无需进一步处理
            self.logger.info("AI返回了解析后的字典数据，直接使用")
            result = json_result.get('strategyReport', json_result)
        elif isinstance(json_result, str):
            # AI返回了JSON字符串，需要解析和验证
            self.logger.info("AI返回了JSON字符串，进行解析和验证")
            success, result = enhanced_validate_and_fix_ai_json(json_result, expected_root_key='strategyReport')
            
            if not success:
                self.logger.error(f"AI JSON验证和修复失败: {result}")
                return False, {"error": f"JSON处理失败: {result}"}
        else:
            return False, {"error": f"AI返回了意外的数据类型: {type(json_result)}"}
        
        # 验证生成的JSON是否有效
        if not result or (isinstance(result, dict) and len(result) == 0):
            self.logger.error("生成的策略JSON为空")
            return False, {"error": "生成的策略JSON为空"}
        
        # 检查是否包含必要的字段
        if isinstance(result, dict):
            required_fields = ['title', 'executiveSummary', 'businessGoals', 'contentStrategy']
            missing_fields = [field for field in required_fields if field not in result or not result[field]]
            if missing_fields:
                self.logger.warning(f"策略JSON缺少部分必要字段: {missing_fields}")
                # 不返回失败，但记录警告
        
        return True, result
    
    async def _generate_strategy_html(self, strategy_json: Dict) -> Tuple[bool, str]:
        """生成HTML格式的策略报告"""
        # 加载响应式HTML生成模板
        template_content = self.load_prompt('strategy_html_generation.j2')
        if not template_content:
            return False, "无法加载策略HTML生成模板"
        
        # 使用Jinja2模板引擎处理模板
        try:
            from jinja2 import Template, Environment
            
            # 创建Jinja2环境
            env = Environment()
            template = env.from_string(template_content)
            
            # 准备模板变量
            template_vars = {
                'strategy_result': strategy_json
            }
            
            # 渲染模板
            rendered_content = template.render(**template_vars)
            
            # 解析渲染后的内容
            parts = rendered_content.split('---SEPARATOR---')
            if len(parts) != 2:
                return False, "模板格式错误"
            
            sys_prompt = parts[0].strip()
            user_prompt = parts[1].strip()
            
        except Exception as e:
            self.logger.error(f"Jinja2模板处理失败: {str(e)}")
            # 回退到原有的字符串替换方法
            parts = template_content.split('---SEPARATOR---')
            if len(parts) != 2:
                return False, "模板格式错误"
            
            sys_prompt = parts[0].strip()
            user_prompt_template = parts[1].strip()
            
            # 构建用户提示词，将策略数据注入模板
            import json
            user_prompt = user_prompt_template.replace('{{ strategy_result }}', json.dumps(strategy_json, ensure_ascii=False))
        
        # 调用AI生成响应式HTML - 使用与原来相同的配置
        success, html_result = await self.call_ai_async(
            gemini, sys_prompt, user_prompt,
            model_name=GEMINI_PRO_MODEL,
            task_name="策略HTML生成(响应式)",
            timeout=90
        )
        
        if not success:
            return False, html_result
        
        # 清理HTML输出（已包含markdown标记清理）
        cleaned_html = clean_html_output(html_result)
        
        # 验证生成的HTML是否有效
        if not cleaned_html or len(cleaned_html.strip()) == 0:
            self.logger.error("生成的HTML为空")
            return False, "生成的HTML为空"
        
        # 检查HTML是否包含基本的HTML结构
        if not any(tag in cleaned_html.lower() for tag in ['<html', '<body', '<div']):
            self.logger.error("生成的HTML缺少基本HTML结构")
            return False, "生成的HTML缺少基本HTML结构"
        
        return True, cleaned_html


class ReviewService(BaseAsyncService):
    """复盘服务实现"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300, use_service_lock: bool = True, redis_connection_timeout: int = 30, redis_socket_timeout: int = 30):
        input_queue = ENV.lower() + ":q:generate:report:operation:request"
        output_queue = ENV.lower() + ":q:generate:report:operation:response"
        super().__init__("review", input_queue, output_queue, max_concurrent_tasks, run_mode, timeout, use_service_lock, redis_connection_timeout, redis_socket_timeout)
    
    def extract_task_info(self, input_data: Dict) -> Tuple[str, str]:
        """覆盖任务信息提取逻辑，适配review的数据结构"""
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId", "未知任务")
        business_id = task_info.get("reviewId")
        return task_id, business_id
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """复盘任务的具体处理逻辑"""
        # 生成复盘报告
        success, result = await self._generate_review_report(input_data)
        
        if not success:
            return False, result
        
        # 后处理：构建符合用户期望的完整结构
        final_result = self._post_process_review_result(input_data, result)
        
        return True, final_result
    
    async def _generate_review_report(self, input_data: Dict) -> Tuple[bool, Dict]:
        """生成复盘报告"""
        try:
            # 检查accounts字段是否存在且有效
            has_valid_accounts = False
            if "accounts" in input_data and isinstance(input_data["accounts"], list) and len(input_data["accounts"]) > 0:
                # 验证账号数据结构
                try:
                    for account in input_data["accounts"]:
                        required_keys = ["accountInfo", "noteList", "industry", "marketingGoal"]
                        if all(key in account for key in required_keys):
                            has_valid_accounts = True
                            break
                except:
                    # 如果验证过程中出现异常，视为无有效账号数据
                    has_valid_accounts = False
            
            # 在input_data中添加标记，供模板使用
            input_data["_has_valid_accounts"] = has_valid_accounts
            
            if not has_valid_accounts:
                self.logger.info("accounts字段缺失或无效，将生成基于其他数据的复盘报告")
            
            # 生成JSON格式的复盘报告
            json_success, review_json = await self._generate_review_analysis(input_data)
            if not json_success:
                return False, {"error": f"JSON生成失败: {review_json}"}
            
            # 使用schema确保所有必需字段都存在
            # review_json = self._ensure_review_json_fields(review_json)
            
            # 生成HTML格式的复盘报告
            html_success, review_html = await self._generate_review_html(review_json)
            if not html_success:
                return False, {"error": f"HTML生成失败: {review_html}"}
            
            return True, {
                # "reportJson": review_json,
                "reportHtml": review_html
            }
            
        except Exception as e:
            return False, {"error": f"复盘报告生成异常: {str(e)}"}
    
    async def _generate_review_analysis(self, task_data: Dict) -> Tuple[bool, Dict]:
        """生成复盘报告的分析性内容"""
        sys_prompt = self.load_prompt('review_json_generation.j2')
        if not sys_prompt:
            return False, {"error": "无法加载JSON生成prompt"}

        # 构建更详细的用户提示，包含schedule完成情况分析
        schedule_info = task_data.get("scheduleInfo", {})
        schedule_analysis = ""
        
        if schedule_info and "scheduleSuggestions" in schedule_info:
            suggestion = schedule_info["scheduleSuggestions"]
            if suggestion:
                generated_schedule = suggestion.get("generatedScheduleCount", 0)
                completed_schedule = suggestion.get("completedScheduleCount", 0)
                generated_suggestion = suggestion.get("generatedSuggestionCount", 0)
                completed_suggestion = suggestion.get("completedSuggestionCount", 0)
                
                schedule_analysis = f"""
排期完成情况分析：
- 生成排期数量：{generated_schedule}
- 完成排期数量：{completed_schedule}
- 排期完成率：{(completed_schedule/generated_schedule*100) if generated_schedule > 0 else 0:.1f}%
- 生成建议数量：{generated_suggestion}
- 完成建议数量：{completed_suggestion}
- 建议完成率：{(completed_suggestion/generated_suggestion*100) if generated_suggestion > 0 else 0:.1f}%
"""

        user_prompt = f"""
请基于以下数据生成运营复盘报告：

{schedule_analysis}

任务数据：
{task_data}

请生成包含执行情况分析、问题识别、改进建议等内容的详细复盘报告。
"""
        
        # 调用AI生成JSON
        success, result = await self.call_ai_async(
            gpt, sys_prompt, user_prompt, 
            model_or_schema="flexible",
            model_name="gpt-4.1",
            task_name="复盘JSON生成",
            timeout=180
        )
        
        if not success:
            return False, result
        
        # 验证和修复JSON
        try:
            success, validated_json = enhanced_validate_and_fix_ai_json(result, "reviewReport")
            if not success:
                return False, {"error": f"JSON验证失败: {validated_json}"}
            
            # # 验证生成的JSON是否有效
            if not validated_json or (isinstance(validated_json, dict) and len(validated_json) == 0):
                self.logger.error("生成的复盘JSON为空")
                return False, {"error": "生成的复盘JSON为空"}
            
            # # 检查是否包含必要的字段
            # if isinstance(validated_json, dict):
            #     required_fields = ['title', 'brandOperationSummary']
            #     missing_fields = [field for field in required_fields if field not in validated_json or not validated_json[field]]
            #     if missing_fields:
            #         self.logger.warning(f"复盘JSON缺少部分必要字段: {missing_fields}")
                    # 不返回失败，但记录警告
            
            return True, validated_json
        except Exception as e:
            return False, {"error": f"JSON验证失败: {str(e)}"}
    
    async def _generate_review_html(self, review_json: Dict) -> Tuple[bool, str]:
        """生成HTML格式的复盘报告"""
        # 加载响应式HTML生成模板
        template_content = self.load_prompt('review_html_generation.j2')
        if not template_content:
            return False, "无法加载评估HTML生成模板"
        
        # 使用Jinja2模板引擎处理模板
        try:
            from jinja2 import Template, Environment
            
            # 创建Jinja2环境
            env = Environment()
            template = env.from_string(template_content)
            
            # 准备模板变量
            template_vars = {
                'review_result': review_json,
                'has_accounts': bool(review_json.get('accounts') and len(review_json.get('accounts', [])) > 0)
            }
            
            # 渲染模板
            rendered_content = template.render(**template_vars)
            
            # 解析渲染后的内容
            parts = rendered_content.split('---SEPARATOR---')
            if len(parts) != 2:
                return False, "模板格式错误"
            
            sys_prompt = parts[0].strip()
            user_prompt = parts[1].strip()
            
        except Exception as e:
            self.logger.error(f"Jinja2模板处理失败: {str(e)}")
            # 回退到原有的字符串替换方法
            parts = template_content.split('---SEPARATOR---')
            if len(parts) != 2:
                return False, "模板格式错误"
            
            sys_prompt = parts[0].strip()
            user_prompt_template = parts[1].strip()
            
            # 构建用户提示词，将评估数据注入模板
            import json
            user_prompt = user_prompt_template.replace('{{ review_result }}', json.dumps(review_json, ensure_ascii=False))
        
        # 调用AI生成响应式HTML
        success, result = await self.call_ai_async(
            gemini, sys_prompt, user_prompt,
            model_name=GEMINI_PRO_MODEL,
            task_name="复盘HTML生成",
            timeout=90
        )
        
        if not success:
            return False, result
        
        # 清理HTML输出（已包含markdown标记清理）
        cleaned_html = clean_html_output(result)
        
        # 验证生成的HTML是否有效
        if not cleaned_html or len(cleaned_html.strip()) == 0:
            self.logger.error("生成的HTML为空")
            return False, "生成的HTML为空"
        
        # 检查HTML是否包含基本的HTML结构
        if not any(tag in cleaned_html.lower() for tag in ['<html', '<body', '<div']):
            self.logger.error("生成的HTML缺少基本HTML结构")
            return False, "生成的HTML缺少基本HTML结构"
        
        return True, cleaned_html
    
    def _ensure_review_json_fields(self, review_json: Dict) -> Dict:
        """确保复盘报告JSON包含所有必需字段，缺失的字段用空字符串或空数组占位"""
        # 确保review_json是字典格式
        if not isinstance(review_json, dict):
            self.logger.warning(f"review_json不是字典格式，类型: {type(review_json)}")
            review_json = {}
        
        # 如果是包含reviewReport根节点的结构，直接返回（因为AI已经生成了正确的结构）
        if "reviewReport" in review_json:
            self.logger.info("检测到reviewReport根节点，使用AI生成的完整结构")
            return review_json
        
        # 对于直接的JSON结构，确保必需字段存在
        required_string_fields = [
            "title",
            "brandOperationSummary",
            "nextMonthOptimization"
        ]
        
        # 检查并补全缺失的字符串字段
        for field in required_string_fields:
            if field not in review_json or review_json[field] is None:
                review_json[field] = ""
                self.logger.info(f"字段 '{field}' 缺失，已设为空字符串")
            elif not isinstance(review_json[field], str):
                # 如果字段存在但不是字符串类型，转换为字符串
                review_json[field] = str(review_json[field])
                self.logger.info(f"字段 '{field}' 不是字符串类型，已转换为字符串")
        
        # 确保accounts字段存在
        if "accounts" not in review_json or not isinstance(review_json["accounts"], list):
            review_json["accounts"] = []
            self.logger.info("字段 'accounts' 缺失或格式不正确，已设为空数组")
        
        self.logger.info(f"复盘报告JSON字段验证完成，包含字段: {list(review_json.keys())}")
        return review_json
    
    def _post_process_review_result(self, input_data: Dict, result: Dict) -> Dict:
        """后处理复盘报告结果，构建符合用户期望的完整结构"""
        import time
        
        # 提取taskInfo
        task_info = input_data.get("taskInfo", {})
        
        # 获取当前UTC时间戳（毫秒）
        current_time_ms = int(time.time() * 1000)
        
        # 获取生成的reportJson
        report_json = result.get("reportJson", {})
        
        # 对reportJson进行后处理，添加必要字段
        enhanced_report_json = self._enhance_report_json(report_json, input_data, current_time_ms)
        
        # 构建最终的结果结构
        final_result = {
            "taskInfo": task_info,
            "reportHtml": result.get("reportHtml", ""),
            "reportJson": enhanced_report_json
        }
        
        self.logger.info(f"复盘报告后处理完成，包含字段: {list(final_result.keys())}")
        return final_result
    
    def _enhance_report_json(self, report_json: Dict, input_data: Dict, current_time_ms: int) -> Dict:
        """增强reportJson，添加updateTimeMs、accounts等字段"""
        
        # 直接使用AI生成的JSON（现在模板已经是正确的结构）
        enhanced_json = dict(report_json)
        self.logger.info("使用AI生成的JSON原始格式")
        
        # 添加updateTimeMs
        enhanced_json["updateTimeMs"] = current_time_ms
        
        # 添加或更新title（如果没有的话）
        if "title" not in enhanced_json or not enhanced_json["title"]:
            task_info = input_data.get("taskInfo", {})
            enhanced_json["title"] = f"运营复盘报告 - {task_info.get('taskId', 'unknown')}"
        
        # 处理accounts字段 - 合并输入数据和AI分析内容
        input_accounts = input_data.get("accounts", [])
        if input_accounts and isinstance(input_accounts, list):
            ai_accounts_analysis = enhanced_json.get("accounts", [])
            enhanced_json["accounts"] = self._merge_accounts_data(input_accounts, ai_accounts_analysis)
            self.logger.info(f"合并了 {len(input_accounts)} 个账号的原始数据和AI分析内容")
        else:
            enhanced_json["accounts"] = []
            self.logger.info("没有账号信息，设置为空数组")
        
        return enhanced_json
    
    def _merge_accounts_data(self, input_accounts: list, ai_accounts_analysis: list) -> list:
        """合并输入的原始账号数据和AI生成的分析内容"""
        merged_accounts = []
        
        for i, account in enumerate(input_accounts):
            if not isinstance(account, dict):
                continue
                
            # 提取账号基本信息（直接从输入复制，防止模型幻觉）
            account_info = account.get("accountInfo", {})
            
            # 构建基础账号数据（原始数据直接复制）
            merged_account = {
                "accountName": account_info.get("nickname", account_info.get("accountName", "")),
                "accountId": account_info.get("accountId", ""),
                "platform": account_info.get("platform", "rednote"),
                "metrics": {
                    "posts": account_info.get("posts", 0),
                    "liked": account_info.get("liked", 0),
                    "collected": account_info.get("collected", 0),
                    "comments": account_info.get("comments", 0),
                    "followers": account_info.get("followers", 0)
                }
            }
            
            # 获取对应的AI分析内容
            if i < len(ai_accounts_analysis) and isinstance(ai_accounts_analysis[i], dict):
                ai_analysis = ai_accounts_analysis[i]
                
                # 添加AI生成的分析内容
                merged_account["dataInsights"] = ai_analysis.get("dataInsights", "")
                merged_account["optimizationPlan"] = ai_analysis.get("optimizationPlan", "")
                
                # 处理hotPostAnalysis，确保contentAnalysis格式正确
                hot_post_analysis = ai_analysis.get("hotPostAnalysis", {})
                content_analysis = hot_post_analysis.get("contentAnalysis", [])
                
                # 处理contentAnalysis，确保每个项目都包含所有必需字段
                processed_content_analysis = []
                for content_item in content_analysis:
                    if isinstance(content_item, dict):
                        # 确保包含所有必需字段，特别是shares字段
                        processed_item = {
                            "displayTitle": content_item.get("displayTitle", content_item.get("title", "")),
                            "likes": content_item.get("likes", 0),
                            "collected": content_item.get("collected", 0),
                            "comments": content_item.get("comments", 0),
                            "shares": content_item.get("shares", 0),  # 确保包含shares字段
                            "createTimeMs": content_item.get("createTimeMs", int(__import__('time').time() * 1000))
                        }
                        processed_content_analysis.append(processed_item)
                
                merged_account["hotPostAnalysis"] = {
                    "contentAnalysis": processed_content_analysis,
                    "summary": hot_post_analysis.get("summary", "")
                }
            else:
                # 如果没有对应的AI分析，设置为空值
                merged_account["dataInsights"] = ""
                merged_account["optimizationPlan"] = ""
                merged_account["hotPostAnalysis"] = {
                    "contentAnalysis": [],
                    "summary": ""
                }
                self.logger.warning(f"账号 {merged_account['accountName']} 缺少AI分析内容")
            
            merged_accounts.append(merged_account)
        
        return merged_accounts
    
    def _process_content_analysis(self, content_analysis_data) -> list:
        """处理contentAnalysis数据，确保格式正确"""
        import time
        
        if not isinstance(content_analysis_data, list):
            return []
        
        processed_content = []
        for content in content_analysis_data:
            if isinstance(content, dict):
                # 确保包含所有必需的字段
                processed_item = {
                    "content": content.get("content", ""),
                    "displayTitle": content.get("displayTitle", content.get("content", "")),
                    "likes": content.get("likes", 0),
                    "collected": content.get("collected", 0),
                    "comments": content.get("comments", 0),
                    "shares": content.get("shares", 0),
                    "createTimeMs": content.get("createTimeMs", int(time.time() * 1000))
                }
                processed_content.append(processed_item)
        
        return processed_content
    
    def _process_accounts_data(self, accounts: list) -> list:
        """处理accounts数据，确保格式符合期望（保留用于兼容性）"""
        processed_accounts = []
        
        for account in accounts:
            if not isinstance(account, dict):
                continue
                
            # 提取账号基本信息
            account_info = account.get("accountInfo", {})
            
            # 构建符合期望格式的账号数据
            processed_account = {
                "accountName": account_info.get("accountName", ""),
                "accountId": account_info.get("accountId", ""),
                "platform": account_info.get("platform", "rednote"),
                "metrics": self._extract_metrics(account),
                "dataInsights": self._extract_data_insights(account),
                "hotPostAnalysis": self._extract_hot_post_analysis(account),
                "optimizationPlan": self._extract_optimization_plan(account)
            }
            
            processed_accounts.append(processed_account)
        
        return processed_accounts
    
    def _extract_metrics(self, account: Dict) -> Dict:
        """提取账号指标数据"""
        # 从account中提取指标数据
        metrics = account.get("metrics", {})
        
        # 确保包含所有必要的指标字段
        return {
            "posts": metrics.get("posts", 0),
            "liked": metrics.get("liked", 0),
            "collected": metrics.get("collected", 0),
            "comments": metrics.get("comments", 0),
            "totalFollowers": metrics.get("totalFollowers", metrics.get("followers", 0)),
            "followers": metrics.get("followers", 0)
        }
    
    def _extract_data_insights(self, account: Dict) -> str:
        """提取数据洞察"""
        return account.get("dataInsights", "")
    
    def _extract_hot_post_analysis(self, account: Dict) -> Dict:
        """提取热门内容分析"""
        import time
        
        hot_post_analysis = account.get("hotPostAnalysis", {})
        
        # 处理contentAnalysis数组
        content_analysis = hot_post_analysis.get("contentAnalysis", [])
        processed_content = []
        
        for content in content_analysis:
            if isinstance(content, dict):
                processed_item = {
                    "content": content.get("content", ""),
                    "displayTitle": content.get("displayTitle", content.get("content", "")),
                    "likes": content.get("likes", 0),
                    "collected": content.get("collected", 0),
                    "comments": content.get("comments", 0),
                    "shares": content.get("shares", 0),
                    "createTimeMs": content.get("createTimeMs", int(time.time() * 1000))
                }
                processed_content.append(processed_item)
        
        return {
            "contentAnalysis": processed_content,
            "summary": hot_post_analysis.get("summary", "")
        }
    
    def _extract_optimization_plan(self, account: Dict) -> str:
        """提取优化计划"""
        return account.get("optimizationPlan", "")


class CoverService(BaseAsyncService):
    """封面生成服务实现"""
    
    def __init__(self, max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300, use_service_lock: bool = True, redis_connection_timeout: int = 30, redis_socket_timeout: int = 30):
        input_queue = ENV.lower() + ":q:generate:cover:request"
        output_queue = ENV.lower() + ":q:generate:cover:response"
        super().__init__("cover", input_queue, output_queue, max_concurrent_tasks, run_mode, timeout, use_service_lock, redis_connection_timeout, redis_socket_timeout)
        # 加载封面样式配置
        self.cover_styles = self._load_cover_styles()
    
    def _load_cover_styles(self) -> Dict:
        """加载封面样式配置"""
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            json_file_path = os.path.join(current_dir, '..', '..', '..', 'prompts', 'style_templates', 'rednote_cover.json')
            
            with open(json_file_path, 'r', encoding='utf-8') as f:
                styles_data = json.load(f)
            
            # 创建两个映射：id->style 和 name->style
            styles_by_id = {}
            styles_by_name = {}
            
            for style_item in styles_data:
                style_id = style_item.get('id')
                style_name = style_item.get('name', '')
                style_prompt = style_item.get('prompt', '')
                
                if style_name and style_prompt:
                    style_config = {
                        "name": style_name,
                        "prompt": style_prompt
                    }
                    
                    if style_id is not None:
                        styles_by_id[style_id] = style_config
                    
                    styles_by_name[style_name] = style_config
            
            self.logger.info(f"成功加载 {len(styles_by_name)} 个封面样式配置")
            return {
                "by_id": styles_by_id,
                "by_name": styles_by_name
            }
            
        except Exception as e:
            self.logger.error(f"加载封面样式配置失败: {str(e)}")
            return {
                "by_id": {},
                "by_name": {"默认样式": {"name": "默认样式", "prompt": "现代简约风格"}}
            }
    
    def _get_style_info(self, cover_id: Optional[int] = None, style_name: Optional[str] = None) -> Tuple[str, str]:
        """根据cover_id或style_name获取样式信息
        
        Args:
            cover_id: 封面ID，优先使用
            style_name: 样式名称，作为fallback
            
        Returns:
            Tuple[str, str]: (样式名称, 样式prompt)
        """
        # 优先使用cover_id
        if cover_id is not None:
            style_config = self.cover_styles["by_id"].get(cover_id)
            if style_config:
                return style_config["name"], style_config["prompt"]
            else:
                self.logger.warning(f"未找到cover_id={cover_id}对应的样式配置")
        
        # 如果cover_id未找到或未提供，则使用style_name
        if style_name:
            style_config = self.cover_styles["by_name"].get(style_name)
            if style_config:
                return style_config["name"], style_config["prompt"]
            else:
                self.logger.warning(f"未找到style_name='{style_name}'对应的样式配置")
        
        # 都没找到，使用默认样式
        default_style = self.cover_styles["by_name"].get("默认样式", {"name": "默认样式", "prompt": "现代简约风格"})
        return default_style["name"], default_style["prompt"]
    
    def extract_task_info(self, input_data: Dict) -> Tuple[str, str]:
        """覆盖任务信息提取逻辑，适配cover的数据结构"""
        task_info = input_data.get("taskInfo", {})
        task_id = task_info.get("taskId", "未知任务")
        business_id = input_data.get("styleId")
        return task_id, str(business_id) if business_id is not None else "未知样式ID"
    
    async def process_task_logic(self, input_data: Dict) -> Tuple[bool, Dict]:
        """封面生成任务的具体处理逻辑"""
        try:
            # 检查必要的字段
            cover_id = input_data.get("styleId")
            style_name = input_data.get("style")
            content = input_data.get("content")
            
            if not content:
                return False, {"error": "缺少封面内容信息"}
            
            # 获取样式信息，优先使用cover_id
            if isinstance(cover_id, str):
                cover_id = int(cover_id)
            final_style_name, style_prompt = self._get_style_info(cover_id, style_name)
            
            self.logger.info(f"使用样式: {final_style_name} (cover_id: {cover_id}, style: {style_name})")
            
            # 调用封面生成逻辑
            success, result = await self._generate_cover_html(final_style_name, style_prompt, content, input_data)
            
            if not success:
                return False, {"error": f"封面生成失败: {result}"}
            
            return True, {
                "coverResult": result,
                "style": final_style_name,
                "content": content
            }
            
        except Exception as e:
            return False, {"error": f"封面生成异常: {str(e)}"}
    
    async def _generate_cover_html(self, style_name: str, style_prompt: str, content: str, input_data: Dict) -> Tuple[bool, str]:
        """生成封面HTML"""
        from urllib.parse import quote
        
        # 检查是否有背景图片
        media_list = input_data.get("mediaList", [])
        background_image_url = None
        
        self.logger.info(f"接收到的输入数据字段: {list(input_data.keys())}")
        self.logger.info(f"mediaList内容: {media_list}")
        
        if media_list:
            self.logger.info(f"找到 {len(media_list)} 个媒体文件")
            # 查找第一个图片类型的媒体作为背景图
            for i, media in enumerate(media_list):
                self.logger.info(f"媒体 {i}: {media}")
                if media.get("mediaType") == "image" and media.get("mediaUrl"):
                    original_url = media.get("mediaUrl")
                    # 将原始URL转换为通过代理访问的URL，解决跨域问题
                    base_url = "https://www-dev.gm8.ai"
                    background_image_url = f"{base_url}/api/v1/image-proxy?url={quote(original_url, safe='')}"
                    
                    self.logger.info(f"✅ 找到背景图片!")
                    self.logger.info(f"原始图片URL: {original_url}")
                    self.logger.info(f"代理图片URL: {background_image_url}")
                    break
                else:
                    self.logger.info(f"媒体 {i} 不符合条件: mediaType={media.get('mediaType')}, mediaUrl={media.get('mediaUrl')}")
        else:
            self.logger.info("❌ 没有找到mediaList或mediaList为空")
        
        if background_image_url:
            self.logger.info(f"✅ 将使用背景图片: {background_image_url}")
        else:
            self.logger.info("❌ 没有背景图片，将使用纯CSS背景")
        
        # 加载小红书封面生成模板
        template_content = self.load_prompt('xiaohongshu/cover_generation.j2')
        if not template_content:
            # 如果模板加载失败，返回错误
            return False, "无法加载封面生成模板"
        
        # 使用Jinja2渲染模板，传入background_image_url和style_prompt变量
        try:
            from jinja2 import Template
            template = Template(template_content)
            sys_prompt = template.render(
                background_image_url=background_image_url,
                style_prompt=style_prompt
            )
        except Exception as e:
            self.logger.error(f"模板渲染失败: {str(e)}")
            return False, f"模板渲染失败: {str(e)}"
        
        # 构建用户提示词
        if background_image_url:
            user_prompt = f"""
请为以下内容生成小红书封面HTML：

样式风格：{style_name}
封面内容：{content}
语言按照封面内容的语言生成，不要使用其他语言。

⚠️ 重要：必须使用提供的背景图片！
背景图片URL：{background_image_url}

请确保：
1. 必须在CSS中使用这个背景图片URL
2. 使用 background-size: contain 确保图片完整显示
3. 添加渐变遮罩层确保文字可读性
4. 不要忽略背景图片，这是用户上传的重要素材

请直接输出完整的HTML代码，不要任何解释文字。
"""
        else:
            user_prompt = f"""
请为以下内容生成小红书封面HTML：

样式风格：{style_name}
封面内容：{content}
语言按照封面内容的语言生成，不要使用其他语言。
背景图片URL：无（使用纯CSS背景）

请直接输出完整的HTML代码，不要任何解释文字。
"""
        
        # 调用AI生成HTML
        success, result = await self.call_ai_async(
            gemini, sys_prompt, user_prompt,
            model_name=GEMINI_PRO_MODEL,
            task_name="封面HTML生成",
            timeout=120
        )
        
        if not success:
            return False, result
        
        # 清理HTML输出（已包含markdown标记清理）
        cleaned_html = clean_html_output(result)
        
        # 验证生成的HTML是否有效
        if not cleaned_html or len(cleaned_html.strip()) == 0:
            self.logger.error("生成的封面HTML为空")
            return False, "生成的封面HTML为空"
        
        # 检查HTML是否包含基本的HTML结构
        if not any(tag in cleaned_html.lower() for tag in ['<html', '<body', '<div']):
            self.logger.error("生成的封面HTML缺少基本HTML结构")
            return False, "生成的封面HTML缺少基本HTML结构"
        
        # 检查HTML长度是否合理（至少应该有一些内容）
        if len(cleaned_html.strip()) < 50:
            self.logger.error(f"生成的封面HTML内容过短: {len(cleaned_html.strip())} 字符")
            return False, f"生成的封面HTML内容过短: {len(cleaned_html.strip())} 字符"
        
        return True, cleaned_html 