"""
简单的Redis连接管理器 - 解决execute方法问题
"""

import asyncio
import ssl
import logging
from contextlib import asynccontextmanager
from typing import Optional
import redis.asyncio as redis

# 导入Redis配置
try:
    from task import REDIS_CLUSTER_CONFIG
except ImportError:
    # 提供默认配置
    REDIS_CLUSTER_CONFIG = {
        'host': 'clustercfg.dev-dao.vahlan.apse1.cache.amazonaws.com',
        'port': 6379,
        'password': 'glpat-pyyX2SHeyGkadnmxaee',
        'ssl': True,
        'decode_responses': True,
        'socket_connect_timeout': 30,
        'socket_timeout': 60,
        'health_check_interval': 30
    }


class SimpleRedisManager:
    """简单的Redis连接管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._connection_pool = None
        self._cluster = None
        self._max_retries = 3
    
    async def _get_or_create_cluster(self):
        """获取或创建Redis集群连接(复用连接)"""
        if self._cluster is None:
            redis_config = REDIS_CLUSTER_CONFIG.copy()
            
            self._cluster = redis.RedisCluster(
                host=redis_config['host'],
                port=redis_config['port'],
                password=redis_config['password'],
                ssl=redis_config.get('ssl', False),
                ssl_cert_reqs=ssl.CERT_NONE if redis_config.get('ssl') else None,
                ssl_ca_certs=None if redis_config.get('ssl') else None,
                decode_responses=redis_config.get('decode_responses', True),
                socket_connect_timeout=redis_config.get('socket_connect_timeout', 30),
                socket_timeout=redis_config.get('socket_timeout', 60),
                max_connections=20,  # 增加连接池大小
                require_full_coverage=False,
                health_check_interval=redis_config.get('health_check_interval', 30)
            )
        
        return self._cluster
    
    async def _create_connection_with_retry(self):
        """带重试的Redis连接创建"""
        last_exception = None
        
        for attempt in range(self._max_retries):
            try:
                cluster = await self._get_or_create_cluster()
                await cluster.ping()
                self.logger.debug(f"Redis连接成功 (attempt {attempt + 1})")
                return cluster
                
            except Exception as e:
                last_exception = e
                if attempt < self._max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 指数退避
                    error_msg = f"{type(e).__name__}: {str(e)}" if str(e) else f"{type(e).__name__}"
                    self.logger.warning(f"Redis连接失败 (attempt {attempt + 1}): {error_msg}, {wait_time}秒后重试")
                    await asyncio.sleep(wait_time)
                    # 重置集群连接以尝试新连接
                    if self._cluster:
                        try:
                            await self._cluster.close()
                        except:
                            pass
                        self._cluster = None
        
        # 所有重试都失败
        error_msg = f"{type(last_exception).__name__}: {str(last_exception)}" if str(last_exception) else f"{type(last_exception).__name__}"
        if "Redis Cluster cannot be connected" in str(last_exception) or "Timeout connecting" in str(last_exception):
            self.logger.warning(f"创建Redis连接失败(已重试{self._max_retries}次): {error_msg}")
        else:
            self.logger.error(f"创建Redis连接失败(已重试{self._max_retries}次): {error_msg}")
        raise last_exception
    
    @asynccontextmanager
    async def get_connection(self):
        """获取Redis连接(复用连接池)"""
        try:
            connection = await self._create_connection_with_retry()
            yield connection
        except Exception as e:
            self.logger.warning(f"获取Redis连接失败: {e}")
            raise
    
    async def close(self):
        """关闭Redis连接"""
        if self._cluster:
            try:
                await self._cluster.close()
                self.logger.debug("Redis集群连接已关闭")
            except Exception as e:
                self.logger.debug(f"关闭Redis连接时出错: {e}")
            finally:
                self._cluster = None


# 全局管理器实例
_simple_manager: Optional[SimpleRedisManager] = None


def get_simple_redis_manager() -> SimpleRedisManager:
    """获取简单Redis管理器单例"""
    global _simple_manager
    if _simple_manager is None:
        _simple_manager = SimpleRedisManager()
    return _simple_manager


@asynccontextmanager
async def get_simple_redis_connection():
    """获取简单Redis连接的便捷函数"""
    manager = get_simple_redis_manager()
    async with manager.get_connection() as connection:
        yield connection