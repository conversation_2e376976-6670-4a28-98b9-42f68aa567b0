"""
Resource Lifecycle Manager - 资源生命周期管理器
统一管理Redis连接、AI调用、数据库连接等资源的生命周期和超时配置
"""

import asyncio
import time
import weakref
from typing import Dict, Optional, Any, List, Callable, Union, AsyncContextManager
from dataclasses import dataclass, field
from contextlib import asynccontextmanager
from colorama import Fore, Style
import logging
from enum import Enum

class ResourceType(Enum):
    """资源类型枚举"""
    REDIS = "redis"
    AI_CLIENT = "ai_client"
    DATABASE = "database"
    HTTP_CLIENT = "http_client"
    FILE_HANDLE = "file_handle"
    WEBSOCKET = "websocket"
    CUSTOM = "custom"

class ResourceStatus(Enum):
    """资源状态枚举"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    CLOSING = "closing"
    CLOSED = "closed"
    ERROR = "error"

@dataclass
class TimeoutConfig:
    """超时配置类"""
    connection_timeout: float = 30.0  # 连接超时
    operation_timeout: float = 60.0   # 操作超时
    idle_timeout: float = 300.0       # 空闲超时
    total_timeout: float = 600.0      # 总超时
    retry_timeout: float = 5.0        # 重试超时
    cleanup_timeout: float = 10.0     # 清理超时

@dataclass
class ResourceMetrics:
    """资源指标类"""
    created_at: float = field(default_factory=time.time)
    last_used_at: float = field(default_factory=time.time)
    usage_count: int = 0
    error_count: int = 0
    total_operation_time: float = 0.0
    peak_memory_usage: int = 0

class ManagedResource:
    """托管资源类"""
    
    def __init__(self, 
                 resource_id: str,
                 resource_type: ResourceType,
                 resource_obj: Any,
                 cleanup_func: Optional[Callable] = None,
                 timeout_config: Optional[TimeoutConfig] = None):
        self.resource_id = resource_id
        self.resource_type = resource_type
        self.resource_obj = resource_obj
        self.cleanup_func = cleanup_func
        self.timeout_config = timeout_config or TimeoutConfig()
        self.status = ResourceStatus.INITIALIZING
        self.metrics = ResourceMetrics()
        self._lock = asyncio.Lock()
        self._cleanup_task: Optional[asyncio.Task] = None
        
    async def mark_used(self):
        """标记资源被使用"""
        async with self._lock:
            self.metrics.last_used_at = time.time()
            self.metrics.usage_count += 1
            if self.status == ResourceStatus.IDLE:
                self.status = ResourceStatus.ACTIVE
    
    async def mark_error(self):
        """标记资源错误"""
        async with self._lock:
            self.metrics.error_count += 1
            self.status = ResourceStatus.ERROR
    
    def is_expired(self) -> bool:
        """检查资源是否过期"""
        now = time.time()
        idle_time = now - self.metrics.last_used_at
        total_time = now - self.metrics.created_at
        
        return (idle_time > self.timeout_config.idle_timeout or 
                total_time > self.timeout_config.total_timeout)
    
    async def cleanup(self):
        """清理资源"""
        if self.status in [ResourceStatus.CLOSING, ResourceStatus.CLOSED]:
            return
            
        async with self._lock:
            self.status = ResourceStatus.CLOSING
            
            try:
                if self.cleanup_func:
                    if asyncio.iscoroutinefunction(self.cleanup_func):
                        await asyncio.wait_for(
                            self.cleanup_func(self.resource_obj),
                            timeout=self.timeout_config.cleanup_timeout
                        )
                    else:
                        self.cleanup_func(self.resource_obj)
                
                self.status = ResourceStatus.CLOSED
                
            except Exception as e:
                self.status = ResourceStatus.ERROR
                raise e

class ResourceLifecycleManager:
    """资源生命周期管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger("resource_manager")
        self._resources: Dict[str, ManagedResource] = {}
        self._resource_pools: Dict[ResourceType, List[ManagedResource]] = {}
        self._global_timeout_configs: Dict[ResourceType, TimeoutConfig] = {}
        self._cleanup_task: Optional[asyncio.Task] = None
        self._shutdown_event = asyncio.Event()
        self._lock = asyncio.Lock()
        
        # 初始化默认超时配置
        self._init_default_timeouts()
        
        # 启动清理任务
        self._start_cleanup_task()
    
    def _init_default_timeouts(self):
        """初始化默认超时配置"""
        self._global_timeout_configs = {
            ResourceType.REDIS: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=30.0,
                idle_timeout=600.0,
                total_timeout=3600.0
            ),
            ResourceType.AI_CLIENT: TimeoutConfig(
                connection_timeout=30.0,
                operation_timeout=120.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            ),
            ResourceType.DATABASE: TimeoutConfig(
                connection_timeout=15.0,
                operation_timeout=60.0,
                idle_timeout=900.0,
                total_timeout=3600.0
            ),
            ResourceType.HTTP_CLIENT: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=30.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            ),
            ResourceType.FILE_HANDLE: TimeoutConfig(
                connection_timeout=5.0,
                operation_timeout=30.0,
                idle_timeout=120.0,
                total_timeout=600.0
            ),
            ResourceType.WEBSOCKET: TimeoutConfig(
                connection_timeout=10.0,
                operation_timeout=60.0,
                idle_timeout=300.0,
                total_timeout=1800.0
            ),
            ResourceType.CUSTOM: TimeoutConfig()
        }
    
    def configure_timeout(self, resource_type: ResourceType, timeout_config: TimeoutConfig):
        """配置资源类型的超时设置"""
        self._global_timeout_configs[resource_type] = timeout_config
        self.logger.info(f"{Fore.CYAN}⚙️  Updated timeout config for {resource_type.value}{Style.RESET_ALL}")
    
    def get_timeout_config(self, resource_type: ResourceType) -> TimeoutConfig:
        """获取资源类型的超时配置"""
        return self._global_timeout_configs.get(resource_type, TimeoutConfig())
    
    async def register_resource(self,
                              resource_id: str,
                              resource_type: ResourceType,
                              resource_obj: Any,
                              cleanup_func: Optional[Callable] = None,
                              custom_timeout: Optional[TimeoutConfig] = None) -> ManagedResource:
        """注册资源"""
        async with self._lock:
            if resource_id in self._resources:
                raise ValueError(f"Resource {resource_id} already registered")
            
            timeout_config = custom_timeout or self.get_timeout_config(resource_type)
            
            managed_resource = ManagedResource(
                resource_id=resource_id,
                resource_type=resource_type,
                resource_obj=resource_obj,
                cleanup_func=cleanup_func,
                timeout_config=timeout_config
            )
            
            managed_resource.status = ResourceStatus.ACTIVE
            self._resources[resource_id] = managed_resource
            
            # 添加到资源池
            if resource_type not in self._resource_pools:
                self._resource_pools[resource_type] = []
            self._resource_pools[resource_type].append(managed_resource)
            
            self.logger.info(f"{Fore.GREEN}📦 Registered resource: {resource_id} ({resource_type.value}){Style.RESET_ALL}")
            return managed_resource
    
    async def get_resource(self, resource_id: str) -> Optional[ManagedResource]:
        """获取资源"""
        resource = self._resources.get(resource_id)
        if resource:
            await resource.mark_used()
        return resource
    
    async def unregister_resource(self, resource_id: str):
        """注销资源"""
        async with self._lock:
            resource = self._resources.pop(resource_id, None)
            if resource:
                # 从资源池中移除
                if resource.resource_type in self._resource_pools:
                    try:
                        self._resource_pools[resource.resource_type].remove(resource)
                    except ValueError:
                        pass
                
                # 清理资源
                try:
                    await resource.cleanup()
                    self.logger.info(f"{Fore.YELLOW}🗑️  Unregistered resource: {resource_id}{Style.RESET_ALL}")
                except Exception as e:
                    self.logger.error(f"{Fore.RED}❌ Error cleaning up resource {resource_id}: {e}{Style.RESET_ALL}")
    
    @asynccontextmanager
    async def managed_resource(self,
                             resource_id: str,
                             resource_type: ResourceType,
                             resource_factory: Callable,
                             cleanup_func: Optional[Callable] = None,
                             custom_timeout: Optional[TimeoutConfig] = None):
        """上下文管理器，自动管理资源生命周期"""
        resource_obj = None
        try:
            # 创建资源
            if asyncio.iscoroutinefunction(resource_factory):
                resource_obj = await resource_factory()
            else:
                resource_obj = resource_factory()
            
            # 注册资源
            managed_resource = await self.register_resource(
                resource_id=resource_id,
                resource_type=resource_type,
                resource_obj=resource_obj,
                cleanup_func=cleanup_func,
                custom_timeout=custom_timeout
            )
            
            yield managed_resource
            
        finally:
            # 自动清理
            await self.unregister_resource(resource_id)
    
    async def cleanup_expired_resources(self):
        """清理过期资源"""
        expired_resources = []
        
        async with self._lock:
            for resource_id, resource in list(self._resources.items()):
                if resource.is_expired():
                    expired_resources.append(resource_id)
        
        for resource_id in expired_resources:
            try:
                await self.unregister_resource(resource_id)
                self.logger.info(f"{Fore.YELLOW}⏰ Cleaned up expired resource: {resource_id}{Style.RESET_ALL}")
            except Exception as e:
                self.logger.error(f"{Fore.RED}❌ Error cleaning expired resource {resource_id}: {e}{Style.RESET_ALL}")
    
    def _start_cleanup_task(self):
        """启动清理任务"""
        async def cleanup_loop():
            while not self._shutdown_event.is_set():
                try:
                    await self.cleanup_expired_resources()
                    await asyncio.sleep(60)  # 每分钟检查一次
                except asyncio.CancelledError:
                    break
                except Exception as e:
                    self.logger.error(f"{Fore.RED}❌ Error in cleanup loop: {e}{Style.RESET_ALL}")
                    await asyncio.sleep(10)
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
    
    async def get_resource_metrics(self) -> Dict[str, Any]:
        """获取资源指标"""
        metrics = {
            "total_resources": len(self._resources),
            "resources_by_type": {},
            "resources_by_status": {},
            "resource_details": {}
        }
        
        for resource_id, resource in self._resources.items():
            # 按类型统计
            type_name = resource.resource_type.value
            if type_name not in metrics["resources_by_type"]:
                metrics["resources_by_type"][type_name] = 0
            metrics["resources_by_type"][type_name] += 1
            
            # 按状态统计
            status_name = resource.status.value
            if status_name not in metrics["resources_by_status"]:
                metrics["resources_by_status"][status_name] = 0
            metrics["resources_by_status"][status_name] += 1
            
            # 详细信息
            metrics["resource_details"][resource_id] = {
                "type": type_name,
                "status": status_name,
                "created_at": resource.metrics.created_at,
                "last_used_at": resource.metrics.last_used_at,
                "usage_count": resource.metrics.usage_count,
                "error_count": resource.metrics.error_count,
                "is_expired": resource.is_expired()
            }
        
        return metrics
    
    async def force_cleanup_all(self):
        """强制清理所有资源"""
        self.logger.info(f"{Fore.CYAN}🧹 Starting force cleanup of all resources{Style.RESET_ALL}")
        
        resource_ids = list(self._resources.keys())
        for resource_id in resource_ids:
            try:
                await self.unregister_resource(resource_id)
            except Exception as e:
                self.logger.error(f"{Fore.RED}❌ Error force cleaning resource {resource_id}: {e}{Style.RESET_ALL}")
        
        self.logger.info(f"{Fore.GREEN}✅ Force cleanup completed{Style.RESET_ALL}")
    
    async def shutdown(self):
        """关闭资源管理器"""
        self.logger.info(f"{Fore.CYAN}🛑 Shutting down resource lifecycle manager{Style.RESET_ALL}")
        
        # 停止清理任务
        self._shutdown_event.set()
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # 清理所有资源
        await self.force_cleanup_all()
        
        self.logger.info(f"{Fore.GREEN}✅ Resource lifecycle manager shutdown completed{Style.RESET_ALL}")

# 全局资源管理器实例
_global_resource_manager: Optional[ResourceLifecycleManager] = None

def get_resource_manager() -> ResourceLifecycleManager:
    """获取全局资源管理器实例"""
    global _global_resource_manager
    if _global_resource_manager is None:
        _global_resource_manager = ResourceLifecycleManager()
    return _global_resource_manager

async def shutdown_resource_manager():
    """关闭全局资源管理器"""
    global _global_resource_manager
    if _global_resource_manager:
        await _global_resource_manager.shutdown()
        _global_resource_manager = None

# 便捷装饰器
def managed_resource_decorator(resource_type: ResourceType, 
                             resource_id_func: Optional[Callable] = None,
                             cleanup_func: Optional[Callable] = None):
    """资源管理装饰器"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            resource_id = resource_id_func(*args, **kwargs) if resource_id_func else f"{func.__name__}_{id(args)}"
            
            async with get_resource_manager().managed_resource(
                resource_id=resource_id,
                resource_type=resource_type,
                resource_factory=lambda: func(*args, **kwargs),
                cleanup_func=cleanup_func
            ) as managed_res:
                return managed_res.resource_obj
        
        return wrapper
    return decorator