"""
Resource Management Usage Examples - 资源管理使用示例
展示如何在实际项目中集成和使用资源生命周期管理系统
"""

import asyncio
import aioredis
import aiohttp
import time
from typing import Dict, Any, Optional
from colorama import Fore, Style
from resource_lifecycle_manager import (
    ResourceLifecycleManager, ResourceType, TimeoutConfig,
    get_resource_manager, managed_resource_decorator
)

class EnhancedServiceWithResourceManagement:
    """集成资源管理的增强服务示例"""
    
    def __init__(self):
        self.resource_manager = get_resource_manager()
        self.logger = None  # 在实际使用中应该配置日志
        
        # 配置自定义超时
        self._configure_custom_timeouts()
    
    def _configure_custom_timeouts(self):
        """配置自定义超时设置"""
        # Redis超时配置
        redis_timeout = TimeoutConfig(
            connection_timeout=5.0,
            operation_timeout=10.0,
            idle_timeout=300.0,
            total_timeout=1800.0
        )
        self.resource_manager.configure_timeout(ResourceType.REDIS, redis_timeout)
        
        # AI客户端超时配置
        ai_timeout = TimeoutConfig(
            connection_timeout=30.0,
            operation_timeout=180.0,
            idle_timeout=600.0,
            total_timeout=3600.0
        )
        self.resource_manager.configure_timeout(ResourceType.AI_CLIENT, ai_timeout)
        
        print(f"{Fore.GREEN}✅ 自定义超时配置已应用{Style.RESET_ALL}")
    
    async def example_redis_usage(self):
        """Redis使用示例"""
        print(f"\n{Fore.CYAN}🔴 Redis 使用示例{Style.RESET_ALL}")
        
        async def create_redis_connection():
            """创建Redis连接"""
            # 模拟Redis连接创建
            print(f"{Fore.YELLOW}📡 创建Redis连接...{Style.RESET_ALL}")
            await asyncio.sleep(0.1)  # 模拟连接时间
            return {"connection": "mock_redis_connection", "status": "connected"}
        
        async def cleanup_redis(redis_obj):
            """清理Redis连接"""
            print(f"{Fore.YELLOW}🧹 清理Redis连接...{Style.RESET_ALL}")
            redis_obj["status"] = "closed"
            await asyncio.sleep(0.05)  # 模拟清理时间
        
        # 使用上下文管理器自动管理Redis连接
        async with self.resource_manager.managed_resource(
            resource_id="redis_main",
            resource_type=ResourceType.REDIS,
            resource_factory=create_redis_connection,
            cleanup_func=cleanup_redis
        ) as redis_resource:
            redis_conn = redis_resource.resource_obj
            print(f"{Fore.GREEN}✅ Redis连接已建立: {redis_conn}{Style.RESET_ALL}")
            
            # 模拟一些Redis操作
            for i in range(3):
                await redis_resource.mark_used()
                print(f"  📝 执行Redis操作 {i+1}")
                await asyncio.sleep(0.1)
        
        print(f"{Fore.GREEN}✅ Redis连接已自动清理{Style.RESET_ALL}")
    
    async def example_ai_client_usage(self):
        """AI客户端使用示例"""
        print(f"\n{Fore.CYAN}🤖 AI客户端使用示例{Style.RESET_ALL}")
        
        # 手动注册AI客户端
        ai_client = {"client": "mock_ai_client", "model": "gpt-4"}
        
        async def cleanup_ai_client(client_obj):
            """清理AI客户端"""
            print(f"{Fore.YELLOW}🧹 清理AI客户端...{Style.RESET_ALL}")
            client_obj["status"] = "closed"
        
        ai_resource = await self.resource_manager.register_resource(
            resource_id="ai_client_main",
            resource_type=ResourceType.AI_CLIENT,
            resource_obj=ai_client,
            cleanup_func=cleanup_ai_client
        )
        
        try:
            print(f"{Fore.GREEN}✅ AI客户端已注册: {ai_client}{Style.RESET_ALL}")
            
            # 模拟AI调用
            for i in range(2):
                await ai_resource.mark_used()
                print(f"  🧠 执行AI调用 {i+1}")
                await asyncio.sleep(0.2)
        
        finally:
            # 手动清理
            await self.resource_manager.unregister_resource("ai_client_main")
            print(f"{Fore.GREEN}✅ AI客户端已清理{Style.RESET_ALL}")
    
    async def example_http_client_usage(self):
        """HTTP客户端使用示例"""
        print(f"\n{Fore.CYAN}🌐 HTTP客户端使用示例{Style.RESET_ALL}")
        
        @managed_resource_decorator(
            resource_type=ResourceType.HTTP_CLIENT,
            resource_id_func=lambda: f"http_client_{int(time.time())}"
        )
        async def create_http_session():
            """创建HTTP会话"""
            print(f"{Fore.YELLOW}🌐 创建HTTP会话...{Style.RESET_ALL}")
            await asyncio.sleep(0.1)
            return {"session": "mock_http_session", "timeout": 30}
        
        # 使用装饰器自动管理HTTP客户端
        http_session = await create_http_session()
        print(f"{Fore.GREEN}✅ HTTP会话已创建: {http_session}{Style.RESET_ALL}")
    
    async def example_database_connection_pool(self):
        """数据库连接池示例"""
        print(f"\n{Fore.CYAN}🗄️ 数据库连接池示例{Style.RESET_ALL}")
        
        # 模拟创建多个数据库连接
        connections = []
        
        for i in range(3):
            async def create_db_connection(conn_id=i):
                print(f"{Fore.YELLOW}🔗 创建数据库连接 {conn_id}...{Style.RESET_ALL}")
                await asyncio.sleep(0.1)
                return {"connection_id": conn_id, "database": "main_db"}
            
            async def cleanup_db_connection(conn_obj):
                print(f"{Fore.YELLOW}🧹 清理数据库连接 {conn_obj['connection_id']}...{Style.RESET_ALL}")
                await asyncio.sleep(0.05)
            
            resource = await self.resource_manager.register_resource(
                resource_id=f"db_conn_{i}",
                resource_type=ResourceType.DATABASE,
                resource_obj=await create_db_connection(),
                cleanup_func=cleanup_db_connection
            )
            connections.append(resource)
        
        print(f"{Fore.GREEN}✅ 创建了 {len(connections)} 个数据库连接{Style.RESET_ALL}")
        
        # 模拟使用连接
        for conn in connections:
            await conn.mark_used()
            print(f"  📊 使用连接: {conn.resource_obj['connection_id']}")
        
        # 清理所有连接
        for i in range(3):
            await self.resource_manager.unregister_resource(f"db_conn_{i}")
        
        print(f"{Fore.GREEN}✅ 所有数据库连接已清理{Style.RESET_ALL}")
    
    async def example_error_handling(self):
        """错误处理示例"""
        print(f"\n{Fore.CYAN}⚠️ 错误处理示例{Style.RESET_ALL}")
        
        async def faulty_resource_factory():
            """有问题的资源工厂"""
            print(f"{Fore.YELLOW}🔧 创建可能失败的资源...{Style.RESET_ALL}")
            await asyncio.sleep(0.1)
            # 模拟随机失败
            import random
            if random.random() < 0.5:
                raise Exception("模拟资源创建失败")
            return {"resource": "success_resource"}
        
        async def cleanup_faulty_resource(resource_obj):
            """清理有问题的资源"""
            print(f"{Fore.YELLOW}🧹 清理资源: {resource_obj}...{Style.RESET_ALL}")
        
        # 尝试创建资源，演示错误处理
        for attempt in range(3):
            try:
                async with self.resource_manager.managed_resource(
                    resource_id=f"faulty_resource_{attempt}",
                    resource_type=ResourceType.CUSTOM,
                    resource_factory=faulty_resource_factory,
                    cleanup_func=cleanup_faulty_resource
                ) as resource:
                    print(f"{Fore.GREEN}✅ 资源创建成功 (尝试 {attempt+1}): {resource.resource_obj}{Style.RESET_ALL}")
                    break
            except Exception as e:
                print(f"{Fore.RED}❌ 资源创建失败 (尝试 {attempt+1}): {e}{Style.RESET_ALL}")
                if attempt < 2:
                    print(f"{Fore.YELLOW}🔄 重试中...{Style.RESET_ALL}")
                    await asyncio.sleep(0.5)
    
    async def show_resource_metrics(self):
        """显示资源指标"""
        print(f"\n{Fore.CYAN}📊 当前资源指标{Style.RESET_ALL}")
        
        metrics = await self.resource_manager.get_resource_metrics()
        
        print(f"总资源数: {metrics['total_resources']}")
        print(f"按类型分布: {metrics['resources_by_type']}")
        print(f"按状态分布: {metrics['resources_by_status']}")
        
        if metrics['resource_details']:
            print(f"\n{Fore.YELLOW}详细信息:{Style.RESET_ALL}")
            for resource_id, details in metrics['resource_details'].items():
                print(f"  • {resource_id}: {details['type']} ({details['status']})")
    
    async def run_all_examples(self):
        """运行所有示例"""
        print(f"{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}🎯 资源管理系统使用示例{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}")
        
        try:
            # 运行各种示例
            await self.example_redis_usage()
            await self.show_resource_metrics()
            
            await self.example_ai_client_usage()
            await self.show_resource_metrics()
            
            await self.example_http_client_usage()
            await self.show_resource_metrics()
            
            await self.example_database_connection_pool()
            await self.show_resource_metrics()
            
            await self.example_error_handling()
            await self.show_resource_metrics()
            
            # 演示清理过期资源
            print(f"\n{Fore.CYAN}🧹 清理过期资源演示{Style.RESET_ALL}")
            await self.resource_manager.cleanup_expired_resources()
            await self.show_resource_metrics()
            
        except Exception as e:
            print(f"{Fore.RED}❌ 示例运行失败: {e}{Style.RESET_ALL}")
        
        finally:
            print(f"\n{Fore.GREEN}✅ 所有示例运行完成{Style.RESET_ALL}")

class IntegratedDiagnosisService:
    """集成资源管理的诊断服务示例"""
    
    def __init__(self):
        self.resource_manager = get_resource_manager()
        
        # 配置诊断服务专用的超时设置
        diagnosis_timeout = TimeoutConfig(
            connection_timeout=15.0,
            operation_timeout=120.0,
            idle_timeout=600.0,
            total_timeout=1800.0
        )
        self.resource_manager.configure_timeout(ResourceType.AI_CLIENT, diagnosis_timeout)
    
    async def diagnosis_with_resource_management(self, account_data: Dict[str, Any]):
        """带资源管理的诊断流程"""
        print(f"\n{Fore.CYAN}🏥 开始诊断流程 (带资源管理){Style.RESET_ALL}")
        
        # 创建AI客户端资源
        async with self.resource_manager.managed_resource(
            resource_id=f"diagnosis_ai_{int(time.time())}",
            resource_type=ResourceType.AI_CLIENT,
            resource_factory=self._create_ai_client,
            cleanup_func=self._cleanup_ai_client
        ) as ai_resource:
            
            # 创建Redis缓存资源
            async with self.resource_manager.managed_resource(
                resource_id=f"diagnosis_cache_{int(time.time())}",
                resource_type=ResourceType.REDIS,
                resource_factory=self._create_redis_client,
                cleanup_func=self._cleanup_redis_client
            ) as cache_resource:
                
                # 执行诊断
                result = await self._perform_diagnosis(
                    account_data, 
                    ai_resource.resource_obj, 
                    cache_resource.resource_obj
                )
                
                return result
    
    async def _create_ai_client(self):
        """创建AI客户端"""
        print(f"{Fore.YELLOW}🤖 创建AI客户端...{Style.RESET_ALL}")
        await asyncio.sleep(0.2)
        return {"client": "diagnosis_ai", "model": "gpt-4", "status": "ready"}
    
    async def _cleanup_ai_client(self, ai_client):
        """清理AI客户端"""
        print(f"{Fore.YELLOW}🧹 清理AI客户端...{Style.RESET_ALL}")
        ai_client["status"] = "closed"
        await asyncio.sleep(0.1)
    
    async def _create_redis_client(self):
        """创建Redis客户端"""
        print(f"{Fore.YELLOW}🔴 创建Redis缓存...{Style.RESET_ALL}")
        await asyncio.sleep(0.1)
        return {"cache": "diagnosis_cache", "status": "connected"}
    
    async def _cleanup_redis_client(self, redis_client):
        """清理Redis客户端"""
        print(f"{Fore.YELLOW}🧹 清理Redis缓存...{Style.RESET_ALL}")
        redis_client["status"] = "disconnected"
        await asyncio.sleep(0.05)
    
    async def _perform_diagnosis(self, account_data, ai_client, cache_client):
        """执行诊断"""
        print(f"{Fore.GREEN}🔍 执行诊断分析...{Style.RESET_ALL}")
        
        # 模拟诊断过程
        steps = ["数据预处理", "AI分析", "结果生成", "缓存存储"]
        
        for step in steps:
            print(f"  📋 {step}...")
            await asyncio.sleep(0.3)
        
        result = {
            "account_id": account_data.get("account_id", "test"),
            "diagnosis": "账号状态良好，建议继续当前策略",
            "score": 85,
            "recommendations": ["增加互动频率", "优化内容质量"],
            "timestamp": time.time()
        }
        
        print(f"{Fore.GREEN}✅ 诊断完成{Style.RESET_ALL}")
        return result

async def main():
    """主函数 - 运行所有示例"""
    print(f"{Fore.CYAN}🚀 启动资源管理示例程序{Style.RESET_ALL}")
    
    try:
        # 基础示例
        service = EnhancedServiceWithResourceManagement()
        await service.run_all_examples()
        
        # 集成诊断服务示例
        print(f"\n{Fore.CYAN}{'='*80}")
        print(f"{Fore.CYAN}🏥 集成诊断服务示例{Style.RESET_ALL}")
        print(f"{Fore.CYAN}{'='*80}")
        
        diagnosis_service = IntegratedDiagnosisService()
        test_account = {"account_id": "test_account", "nickname": "测试账号"}
        
        diagnosis_result = await diagnosis_service.diagnosis_with_resource_management(test_account)
        print(f"\n{Fore.GREEN}📋 诊断结果: {diagnosis_result}{Style.RESET_ALL}")
        
        # 最终资源状态
        print(f"\n{Fore.CYAN}📊 最终资源状态{Style.RESET_ALL}")
        final_metrics = await get_resource_manager().get_resource_metrics()
        print(f"剩余资源: {final_metrics['total_resources']}")
        
    except Exception as e:
        print(f"{Fore.RED}❌ 程序运行失败: {e}{Style.RESET_ALL}")
    
    finally:
        # 清理所有资源
        print(f"\n{Fore.CYAN}🧹 清理所有资源...{Style.RESET_ALL}")
        from resource_lifecycle_manager import shutdown_resource_manager
        await shutdown_resource_manager()
        print(f"{Fore.GREEN}✅ 程序结束{Style.RESET_ALL}")

if __name__ == "__main__":
    asyncio.run(main())