"""
优化的Prompt模板配置
专注于提升查询质量和针对性
"""

import logging
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

logger = logging.getLogger(__name__)

def get_current_year() -> str:
    """获取当前年份"""
    return str(datetime.now().year)

def get_current_date_str() -> str:
    """获取当前日期字符串（格式：YYYY年）"""
    return f"{datetime.now().year}年"

def get_time_range_keywords() -> List[str]:
    """获取时间范围关键词"""
    current_year = datetime.now().year
    return [
        str(current_year),
        str(current_year - 1),
        "最新", "最近", "当前", "热门", "前沿"
    ]

class IndustryType(Enum):
    """行业类型枚举"""
    BEAUTY = "美妆护肤"
    FASHION = "时尚穿搭"
    FOOD = "美食"
    TRAVEL = "旅游"
    TECH = "科技数码"
    LIFESTYLE = "生活方式"
    EDUCATION = "教育"
    FITNESS = "健身"
    PARENTING = "育儿"
    FINANCE = "金融理财"
    CONSTRUCTION = "建筑装修"
    GENERAL = "通用"

@dataclass
class IndustryConfig:
    """行业配置"""
    keywords: List[str]
    trending_topics: List[str]
    key_platforms: List[str]
    regulatory_focus: List[str]
    seasonal_factors: List[str]
    competition_intensity: str  # low, medium, high

# 行业特定配置 - 增强行业术语覆盖
INDUSTRY_CONFIGS = {
    IndustryType.BEAUTY: IndustryConfig(
        keywords=[
            # 基础关键词
            "护肤", "彩妆", "美妆", "护肤品", "化妆品", "美容", "保养", "美白", "抗衰",
            # 成分相关
            "玻尿酸", "烟酰胺", "维C", "视黄醇", "果酸", "水杨酸", "神经酰胺", "胶原蛋白",
            "透明质酸", "肽类", "精华", "面膜", "爽肤水", "乳液", "面霜", "防晒霜",
            # 肌肤类型
            "敏感肌", "干皮", "油皮", "混合肌", "痘痘肌", "熟龄肌", "暗沉", "毛孔粗大",
            # 品类术语
            "洁面", "卸妆", "眼霜", "精华液", "原液", "安瓶", "面部按摩", "提拉紧致"
        ],
        trending_topics=["成分党", "功效护肤", "抗衰老", "敏感肌护理", "医美", "天然有机", "早C晚A", "建立耐受"],
        key_platforms=["小红书", "抖音", "微博", "B站"],
        regulatory_focus=["化妆品新规", "成分标注", "广告法", "医美监管"],
        seasonal_factors=["换季护肤", "防晒", "保湿", "美白", "控油"],
        competition_intensity="high"
    ),
    IndustryType.FASHION: IndustryConfig(
        keywords=[
            # 基础时尚
            "时尚", "穿搭", "搭配", "服装", "配饰", "潮流", "造型", "风格",
            # 服装类别
            "连衣裙", "半身裙", "牛仔裤", "西装", "毛衣", "外套", "风衣", "羽绒服",
            "卫衣", "T恤", "衬衫", "阔腿裤", "小脚裤", "短裤", "背心", "吊带",
            # 配饰
            "包包", "鞋子", "帽子", "围巾", "项链", "耳环", "手表", "太阳镜",
            # 风格标签
            "日系", "韩系", "欧美风", "复古", "甜美", "酷女孩", "通勤", "休闲", "度假风"
        ],
        trending_topics=["可持续时尚", "个性化穿搭", "vintage", "国潮", "快时尚", "胶囊衣橱", "色彩搭配"],
        key_platforms=["小红书", "抖音", "微博", "Instagram"],
        regulatory_focus=["消费者权益", "质量标准", "环保要求"],
        seasonal_factors=["春夏", "秋冬", "节日穿搭", "职场穿搭", "度假穿搭"],
        competition_intensity="high"
    ),
    IndustryType.FOOD: IndustryConfig(
        keywords=[
            # 基础美食
            "美食", "烹饪", "食谱", "餐厅", "小吃", "烘焙", "家常菜", "下厨",
            # 烹饪方式
            "炒", "煮", "蒸", "烤", "炸", "炖", "煎", "凉拌", "腌制", "发酵",
            # 食材类别
            "蔬菜", "水果", "肉类", "海鲜", "豆制品", "谷物", "坚果", "调料",
            # 特色菜系
            "川菜", "粤菜", "湘菜", "鲁菜", "苏菜", "浙菜", "闽菜", "徽菜", "东北菜",
            # 健康饮食
            "减脂餐", "低卡", "高蛋白", "无糖", "轻食", "素食", "有机", "营养搭配"
        ],
        trending_topics=["健康饮食", "低卡食谱", "网红美食", "家常菜", "异国料理", "减脂餐", "烘焙治愈"],
        key_platforms=["小红书", "抖音", "微博", "下厨房"],
        regulatory_focus=["食品安全", "营养标签", "广告规范"],
        seasonal_factors=["时令食材", "节日美食", "夏日清爽", "冬日滋补"],
        competition_intensity="medium"
    ),
    IndustryType.TECH: IndustryConfig(
        keywords=[
            # 数码产品
            "手机", "电脑", "平板", "耳机", "音响", "相机", "智能手表", "充电器",
            "键盘", "鼠标", "显示器", "路由器", "移动电源", "数据线", "存储卡",
            # 品牌
            "苹果", "华为", "小米", "OPPO", "vivo", "三星", "索尼", "佳能", "尼康",
            # 技术术语
            "处理器", "内存", "存储", "屏幕", "摄像头", "电池", "快充", "无线充电",
            "5G", "WiFi6", "蓝牙", "AI", "算法", "芯片", "系统优化", "性能测试"
        ],
        trending_topics=["AI应用", "智能家居", "5G技术", "新品评测", "性价比推荐", "数码配件"],
        key_platforms=["小红书", "抖音", "微博", "B站", "知乎"],
        regulatory_focus=["数据安全", "隐私保护", "产品质量"],
        seasonal_factors=["新品发布", "促销活动", "学生开学", "年终换机"],
        competition_intensity="high"
    ),
    IndustryType.TRAVEL: IndustryConfig(
        keywords=[
            # 旅游类型
            "旅游", "旅行", "自由行", "跟团游", "自驾游", "徒步", "露营", "度假",
            # 目的地
            "国内游", "出境游", "海岛", "古镇", "城市", "乡村", "山区", "海滨",
            "景点", "博物馆", "寺庙", "公园", "主题乐园", "温泉", "滑雪场",
            # 旅行用品
            "行李箱", "背包", "相机", "防晒", "户外装备", "旅行攻略", "住宿", "交通",
            # 体验类型
            "美食探店", "拍照打卡", "文化体验", "户外运动", "购物", "亲子游", "情侣游"
        ],
        trending_topics=["小众目的地", "深度游", "文化旅游", "生态旅游", "康养旅游", "摄影旅行"],
        key_platforms=["小红书", "抖音", "微博", "马蜂窝"],
        regulatory_focus=["旅游安全", "消费者权益", "环境保护"],
        seasonal_factors=["春游踏青", "夏日避暑", "秋季赏叶", "冬季滑雪"],
        competition_intensity="medium"
    ),
    IndustryType.LIFESTYLE: IndustryConfig(
        keywords=[
            # 生活方式
            "生活", "日常", "分享", "VLOG", "好物推荐", "生活技巧", "居家", "收纳",
            # 兴趣爱好
            "阅读", "音乐", "电影", "摄影", "绘画", "手工", "园艺", "宠物",
            "运动", "瑜伽", "健身", "跑步", "游泳", "篮球", "羽毛球",
            # 个人成长
            "学习", "成长", "效率", "时间管理", "习惯养成", "自律", "读书笔记",
            "职场", "技能提升", "心理健康", "冥想", "正念", "情绪管理"
        ],
        trending_topics=["精致生活", "断舍离", "极简主义", "慢生活", "可持续生活", "数字极简"],
        key_platforms=["小红书", "抖音", "微博", "知乎"],
        regulatory_focus=["广告法", "消费者保护", "隐私保护"],
        seasonal_factors=["新年计划", "春季整理", "夏日活动", "秋季收获", "冬日居家"],
        competition_intensity="medium"
    ),
    IndustryType.EDUCATION: IndustryConfig(
        keywords=[
            # 教育类型
            "教育", "学习", "培训", "课程", "知识分享", "技能提升", "考试", "备考",
            # 学科分类
            "语文", "数学", "英语", "物理", "化学", "生物", "历史", "地理", "政治",
            "编程", "设计", "营销", "金融", "法律", "医学", "艺术", "音乐",
            # 学习方法
            "学习方法", "记忆技巧", "思维导图", "笔记方法", "时间管理", "专注力",
            # 考试相关
            "高考", "中考", "考研", "公务员", "雅思", "托福", "四六级", "证书考试"
        ],
        trending_topics=["在线教育", "终身学习", "职业技能", "考试技巧", "学习工具", "知识管理"],
        key_platforms=["小红书", "抖音", "微博", "B站", "知乎"],
        regulatory_focus=["教育规范", "内容审核", "未成年人保护"],
        seasonal_factors=["开学季", "考试季", "寒暑假", "毕业季"],
        competition_intensity="high"
    ),
    IndustryType.FITNESS: IndustryConfig(
        keywords=[
            # 运动类型
            "健身", "运动", "瑜伽", "跑步", "游泳", "力量训练", "有氧运动", "拉伸",
            "普拉提", "舞蹈", "攀岩", "骑行", "徒步", "瘦身", "增肌", "塑形",
            # 身体部位
            "腹肌", "马甲线", "翘臀", "美腿", "手臂", "背部", "胸肌", "核心",
            # 健身用品
            "瑜伽垫", "哑铃", "弹力带", "跑鞋", "运动服", "蛋白粉", "健身器材",
            # 营养相关
            "蛋白质", "碳水", "脂肪", "热量", "营养搭配", "运动补给", "减脂餐", "增肌餐"
        ],
        trending_topics=["居家健身", "女性力量训练", "功能性训练", "运动康复", "健身饮食", "体态纠正"],
        key_platforms=["小红书", "抖音", "微博", "Keep"],
        regulatory_focus=["健康安全", "产品质量", "广告规范"],
        seasonal_factors=["春季减脂", "夏日塑形", "秋季增肌", "冬季维持"],
        competition_intensity="high"
    ),
    IndustryType.PARENTING: IndustryConfig(
        keywords=[
            # 育儿阶段
            "育儿", "怀孕", "备孕", "新生儿", "婴儿", "幼儿", "学龄前", "小学生",
            "青少年", "亲子", "家庭教育", "早教", "启蒙", "成长发育",
            # 育儿用品
            "奶粉", "纸尿裤", "婴儿车", "安全座椅", "玩具", "绘本", "儿童服装",
            "辅食", "餐具", "洗护用品", "益智玩具", "学习用品",
            # 育儿话题
            "睡眠训练", "如厕训练", "语言发展", "运动发育", "情商培养", "性格塑造",
            "学习习惯", "社交能力", "安全教育", "健康管理", "心理健康"
        ],
        trending_topics=["科学育儿", "正面管教", "亲子阅读", "STEAM教育", "儿童心理", "家庭关系"],
        key_platforms=["小红书", "抖音", "微博", "育儿网"],
        regulatory_focus=["儿童安全", "产品质量", "教育规范"],
        seasonal_factors=["开学准备", "假期活动", "节日庆祝", "季节性疾病"],
        competition_intensity="medium"
    ),
    IndustryType.FINANCE: IndustryConfig(
        keywords=[
            # 投资理财
            "理财", "投资", "基金", "股票", "债券", "保险", "银行", "存款", "贷款",
            "P2P", "数字货币", "比特币", "以太坊", "区块链", "NFT",
            # 理财概念
            "风险", "收益", "资产配置", "分散投资", "定投", "复利", "通胀", "流动性",
            "净值", "市盈率", "分红", "手续费", "税收", "财务规划",
            # 金融工具
            "货币基金", "指数基金", "混合基金", "股票基金", "ETF", "REITs",
            "信用卡", "房贷", "车贷", "消费贷", "创业贷", "公积金"
        ],
        trending_topics=["被动投资", "价值投资", "量化投资", "ESG投资", "财商教育", "数字化理财"],
        key_platforms=["小红书", "微博", "知乎", "雪球"],
        regulatory_focus=["金融监管", "投资者保护", "风险提示", "合规经营"],
        seasonal_factors=["年终奖理财", "税务筹划", "新年规划", "市场调整"],
        competition_intensity="high"
    ),
    IndustryType.CONSTRUCTION: IndustryConfig(
        keywords=[
            # 建筑基础
            "建筑", "装修", "装饰", "设计", "施工", "工程", "建材", "材料", "家装", "公装",
            # 设计相关
            "室内设计", "建筑设计", "软装", "硬装", "全屋定制", "空间设计", "风格设计",
            "现代简约", "新中式", "欧式", "美式", "北欧", "日式", "工业风", "轻奢",
            # 施工工艺
            "水电工程", "泥瓦工程", "木工", "油漆", "防水", "吊顶", "地板", "瓷砖",
            "墙面", "门窗", "橱柜", "衣柜", "卫浴", "厨房", "阳台", "玄关",
            # 建材产品
            "瓷砖", "地板", "涂料", "壁纸", "石材", "板材", "五金", "灯具", "开关",
            "水龙头", "马桶", "淋浴房", "浴室柜", "集成吊顶", "门", "窗",
            # 技术概念
            "智能家居", "全屋智能", "新风系统", "地暖", "中央空调", "净水系统",
            "BIM技术", "装配式建筑", "绿色建筑", "节能建筑", "智慧工地", "数字建造",
            # 商业术语
            "装修预算", "工期", "验收", "保修", "环保", "甲醛", "VOC", "施工图",
            "效果图", "3D设计", "VR体验", "一站式", "整装", "拎包入住"
        ],
        trending_topics=[
            "智能家居", "全屋定制", "绿色建筑", "装配式建筑", "数字化装修", 
            "环保材料", "零甲醛", "健康家居", "适老化改造", "小户型设计",
            "收纳设计", "多功能空间", "新中式风格", "轻奢风格", "极简风格"
        ],
        key_platforms=["小红书", "抖音", "微博", "知乎", "一兜糖"],
        regulatory_focus=[
            "建筑规范", "装修标准", "环保要求", "消防安全", "质量监督",
            "合同规范", "价格监管", "资质管理", "安全生产"
        ],
        seasonal_factors=[
            "装修旺季", "材料涨价", "工人紧缺", "梅雨季节影响", "年底交房潮",
            "春节后开工", "夏季高温施工", "金九银十", "双11建材促销"
        ],
        competition_intensity="high"
    ),
    IndustryType.GENERAL: IndustryConfig(
        keywords=["生活", "分享", "经验", "推荐", "日常", "好物", "测评", "种草"],
        trending_topics=["生活方式", "个人成长", "效率提升", "心理健康", "品质生活"],
        key_platforms=["小红书", "抖音", "微博", "知乎"],
        regulatory_focus=["广告法", "消费者保护", "隐私保护"],
        seasonal_factors=["季节性话题", "节日内容", "年度总结"],
        competition_intensity="medium"
    )
}

class PromptTemplateManager:
    """Prompt模板管理器"""
    
    def __init__(self):
        self.templates = self._load_templates()
    
    def _load_templates(self) -> Dict[str, Any]:
        """加载所有模板"""
        return {
            "basic_queries": self._get_basic_queries_templates(),
            "industry_queries": self._get_industry_queries_templates(),
            "competitive_queries": self._get_competitive_queries_templates(),
            "analysis_prompts": self._get_analysis_prompts(),
            "report_templates": self._get_final_report_templates()
        }
    
    def _get_basic_queries_templates(self) -> Dict[str, str]:
        """基础查询模板"""
        return {
            "startup_stage": """
针对起步阶段账号（{follower_count}粉丝）的{industry}领域，生成3个核心查询：

重点关注：
1. 新账号冷启动策略和方法
2. 内容定位和差异化方向
3. 早期增长和用户获取

账号信息：
- 账号：{account_name}
- 行业：{industry}
- 内容主题：{content_themes}
- 目标受众：{target_audience}

查询要求：
- 包含"新账号"、"冷启动"、"初期增长"等关键词
- 搜索具体的成功案例和实操方法
- 重点关注0-1万粉丝阶段的策略

每个查询格式：
{{
    "content": "具体搜索内容",
    "focus": "重点领域",
    "recency_filter": "month",
    "priority": 1.0-2.0
}}
""",
            
            "growth_stage": """
针对成长阶段账号（{follower_count}粉丝）的{industry}领域，生成3个核心查询：

重点关注：
1. 突破增长瓶颈的方法
2. 内容质量提升和用户留存
3. 流量转化和变现准备

账号信息：
- 账号：{account_name}
- 行业：{industry}
- 内容主题：{content_themes}
- 近期表现：{recent_performance}

查询要求：
- 包含"增长瓶颈"、"用户留存"、"内容升级"等关键词
- 搜索1-10万粉丝阶段的优化策略
- 重点关注可量化的改进方法

每个查询格式：
{{
    "content": "具体搜索内容",
    "focus": "重点领域",
    "recency_filter": "month",
    "priority": 1.0-2.0
}}
""",
            
            "mature_stage": """
针对成熟阶段账号（{follower_count}粉丝）的{industry}领域，生成3个核心查询：

重点关注：
1. 品牌化和影响力扩大
2. 多元化变现和商业合作
3. 长期竞争优势建立

账号信息：
- 账号：{account_name}
- 行业：{industry}
- 内容主题：{content_themes}
- 品牌价值：{brand_value}

查询要求：
- 包含"品牌建设"、"商业化"、"影响力"等关键词
- 搜索10万+粉丝账号的高级策略
- 重点关注长期可持续发展

每个查询格式：
{{
    "content": "具体搜索内容",
    "focus": "重点领域",
    "recency_filter": "month",
    "priority": 1.0-2.0
}}
"""
        }
    
    def _get_industry_queries_templates(self) -> Dict[str, str]:
        """行业查询模板"""
        return {
            "trend_analysis": """
作为{industry}行业专家，为{account_name}生成3个行业趋势分析查询：

行业特点：
- 关键词：{industry_keywords}
- 热门话题：{trending_topics}
- 主要平台：{key_platforms}
- 竞争强度：{competition_intensity}

查询重点：
1. {current_year}年{industry}行业最新发展趋势
2. 消费者行为和偏好变化
3. 新兴技术和商业模式影响

要求：
- 包含行业专业术语和具体事件
- 关注季节性因素：{seasonal_factors}
- 搜索权威报告和数据分析

格式：
{{
    "content": "{industry}行业{current_year}年发展趋势 消费者行为变化 [具体关键词]",
    "focus": "趋势分析",
    "recency_filter": "month",
    "priority": 1.8
}}
""",
            
            "regulatory_impact": """
针对{industry}行业的监管政策影响，生成2个专业查询：

监管重点：{regulatory_focus}
账号类型：{account_stage}阶段的{industry}账号

查询方向：
1. 最新政策法规对{industry}行业的影响
2. 合规要求和机会识别

要求：
- 包含具体的政策名称和实施时间
- 关注对内容创作的影响
- 搜索官方解读和行业分析

格式：
{{
    "content": "{industry}行业{current_year}年监管政策变化 合规要求 影响分析",
    "focus": "政策影响",
    "recency_filter": "month",
    "priority": 1.6
}}
"""
        }
    
    def _get_competitive_queries_templates(self) -> Dict[str, str]:
        """竞争查询模板"""
        return {
            "competitor_strengths_analysis": """
深度分析{industry}领域头部账号的成功优势，为{account_name}生成3个学习导向的查询：

竞争环境：
- 行业：{industry}
- 竞争强度：{competition_intensity}
- 账号阶段：{account_stage}
- 粉丝规模：{follower_count}

查询重点（优势学习导向）：
1. 头部账号的核心竞争优势和成功要素分析
2. 头部品牌的创新策略和差异化定位研究
3. 成功案例的具体执行方法和可复制模式

要求：
- 重点挖掘头部账号的成功秘诀和核心优势
- 分析具体的内容创作技巧、用户运营策略、商业模式
- 搜索可学习的成功案例和最佳实践
- 关注头部账号如何建立护城河和用户粘性
- 识别可复制的成功模式和创新点

格式示例：
{{
    "content": "{industry}头部账号成功优势分析 核心竞争力 创新策略 最佳实践案例",
    "focus": "头部品牌优势学习",
    "recency_filter": "month",
    "priority": 1.9,
    "analysis_type": "strength_focused"
}},
{{
    "content": "{industry}领先品牌差异化定位策略 独特价值主张 用户忠诚度建设方法",
    "focus": "差异化策略学习",
    "recency_filter": "month", 
    "priority": 1.8,
    "analysis_type": "strategy_learning"
}},
{{
    "content": "{industry}成功账号内容创作技巧 爆款内容规律 用户互动秘诀",
    "focus": "内容创作学习",
    "recency_filter": "week",
    "priority": 1.7,
    "analysis_type": "content_mastery"
}}
""",
            
            "competitor_weakness_opportunities": """
识别{industry}领域竞争对手的薄弱环节，为{account_name}生成2个机会发现查询：

市场背景：
- 行业：{industry}
- 目标受众：{target_audience}
- 内容主题：{content_themes}

查询重点（机会挖掘导向）：
1. 竞争对手忽视的用户需求和市场空白
2. 行业痛点和未被满足的细分需求

要求：
- 识别竞争对手的服务盲区和内容空白
- 发现用户抱怨和不满意的领域
- 寻找可以立即切入的市场机会
- 关注新兴趋势和变化中的用户需求

格式示例：
{{
    "content": "{industry}市场空白机会 用户未满足需求 竞争对手薄弱环节",
    "focus": "市场机会挖掘",
    "recency_filter": "month",
    "priority": 1.6,
    "analysis_type": "opportunity_focused"
}},
{{
    "content": "{industry}用户痛点分析 服务盲区 改进机会点",
    "focus": "痛点机会分析", 
    "recency_filter": "week",
    "priority": 1.5,
    "analysis_type": "pain_point_analysis"
}}
""",
            
            "industry_benchmark_learning": """
研究{industry}行业标杆和最佳实践，为{account_name}生成2个学习标杆查询：

行业背景：
- 行业：{industry}
- 发展阶段：{account_stage}
- 学习目标：提升竞争力

查询重点（标杆学习导向）：
1. 行业标杆的成功模式和经验总结
2. 跨行业优秀案例的借鉴和应用

要求：
- 研究行业内外的成功标杆和最佳实践
- 分析成功模式的底层逻辑和关键要素
- 寻找可借鉴的创新方法和执行策略
- 关注标杆企业的核心能力建设

格式示例：
{{
    "content": "{industry}行业标杆分析 成功模式 关键成功要素 最佳实践",
    "focus": "行业标杆学习",
    "recency_filter": "month",
    "priority": 1.7,
    "analysis_type": "benchmark_learning"
}},
{{
    "content": "跨行业优秀案例 创新模式 {industry}应用借鉴",
    "focus": "跨界学习",
    "recency_filter": "month",
    "priority": 1.6,
    "analysis_type": "cross_industry_learning"
}}
"""
        }
    
    def _get_analysis_prompts(self) -> Dict[str, str]:
        """分析Prompt模板"""
        return {
            "search_results_analysis": """
你是一位具有{industry}行业专业背景的资深分析师。基于以下搜索结果，为{account_name}生成深度分析报告。

账号情况：
- 账号名称：{account_name}
- 行业：{industry}
- 发展阶段：{account_stage}
- 粉丝规模：{follower_count}
- 内容主题：{content_themes}

搜索结果：
{search_results}

分析要求：
1. 提取关键洞察和可操作建议
2. 识别具体的机会和威胁
3. 提供有时间节点的执行建议
4. 基于行业专业知识进行深度解读

分析维度：
- 行业趋势对账号的影响和机会
- 竞争格局分析和差异化策略
- 用户需求变化和内容机会
- 具体的增长和变现建议

输出格式：
{{
    "key_insights": ["洞察1", "洞察2", "洞察3"],
    "opportunities": [
        {{
            "opportunity": "具体机会",
            "timeline": "执行时间",
            "potential_impact": "预期影响",
            "action_steps": ["步骤1", "步骤2"]
        }}
    ],
    "threats": [
        {{
            "threat": "威胁描述",
            "impact_level": "高/中/低",
            "mitigation_strategy": "应对策略"
        }}
    ],
    "recommendations": [
        {{
            "category": "内容策略/增长方法/变现模式",
            "recommendation": "具体建议",
            "priority": "高/中/低",
            "timeline": "执行时间"
        }}
    ]
}}
""",
            
            "leading_brands_strengths_analysis": """
你是一位专注于头部品牌优势研究的资深分析师。基于以下搜索结果，为{account_name}生成头部品牌优势学习报告，重点关注可学习的成功要素。

账号情况：
- 账号名称：{account_name}
- 行业：{industry}
- 发展阶段：{account_stage}
- 粉丝规模：{follower_count}
- 内容主题：{content_themes}

搜索结果：
{search_results}

分析重点（优势学习导向）：
1. 深度挖掘头部品牌的核心竞争优势和成功要素
2. 分析头部品牌的创新策略和差异化定位方法
3. 总结可复制的最佳实践案例和成功模式
4. 识别头部品牌如何建立护城河和用户粘性
5. 研究头部品牌的用户运营和内容创作技巧

输出格式：
{{
    "leading_brands_insights": {{
        "core_strengths": ["核心优势1", "核心优势2", "核心优势3"],
        "success_factors": ["成功因素1", "成功因素2", "成功因素3"],
        "competitive_moats": ["护城河1", "护城河2"],
        "key_differentiators": ["差异化要素1", "差异化要素2"]
    }},
    "innovation_strategies": {{
        "positioning_methods": ["定位方法1", "定位方法2", "定位方法3"],
        "value_propositions": ["价值主张1", "价值主张2"],
        "brand_building_tactics": ["品牌策略1", "品牌策略2"]
    }},
    "best_practices": {{
        "content_creation_secrets": ["内容秘诀1", "内容秘诀2", "内容秘诀3"],
        "user_engagement_methods": ["互动方法1", "互动方法2", "互动方法3"],
        "growth_tactics": ["增长策略1", "增长策略2"],
        "monetization_approaches": ["变现方法1", "变现方法2"]
    }},
    "replicable_models": {{
        "content_frameworks": ["内容框架1", "内容框架2"],
        "operational_systems": ["运营体系1", "运营体系2"],
        "scaling_strategies": ["扩展策略1", "扩展策略2"]
    }},
    "learning_roadmap": [
        {{
            "phase": "第一阶段（1-3个月）",
            "focus": "学习重点",
            "actions": ["行动1", "行动2", "行动3"],
            "expected_outcomes": ["预期成果1", "预期成果2"]
        }},
        {{
            "phase": "第二阶段（3-6个月）",
            "focus": "学习重点",
            "actions": ["行动1", "行动2", "行动3"],
            "expected_outcomes": ["预期成果1", "预期成果2"]
        }}
    ]
}}
"""
        }
    
    def _get_final_report_templates(self) -> Dict[str, str]:
        """最终报告模板 - 专注实用价值"""
        return {
            "comprehensive_report": """
基于深度调研，为{account_name}提供{industry}领域战略发展报告：

一、成功案例分析
基于调研的头部账号成功要素：
{success_factors}

二、策略机会识别
可复制的成功模式和机会：
{strategic_opportunities}

三、竞争格局洞察
从优秀案例中提取的关键洞察：
{competitive_insights}

四、行动建议
基于成功经验的具体执行建议：
{actionable_recommendations}

注意：
- 重点关注已验证的成功策略
- 建议基于实际案例和数据
- 提供可操作的具体步骤
- 专注实用价值和可执行性
"""
        }
    
    def get_template(self, template_category: str, template_name: str) -> str:
        """获取指定模板"""
        return self.templates.get(template_category, {}).get(template_name, "")
    
    def get_industry_config(self, industry: str) -> IndustryConfig:
        """获取行业配置 - 增强匹配逻辑"""
        industry_lower = industry.lower()
        
        # 精确匹配
        for industry_type in IndustryType:
            if industry_type.value == industry or industry_type.value.lower() == industry_lower:
                return INDUSTRY_CONFIGS.get(industry_type, INDUSTRY_CONFIGS[IndustryType.GENERAL])
        
        # 建筑行业特殊匹配
        construction_keywords = ["建筑", "装修", "装饰", "设计", "施工", "工程", "建材", "家装", "公装", "室内"]
        if any(keyword in industry_lower for keyword in construction_keywords):
            return INDUSTRY_CONFIGS.get(IndustryType.CONSTRUCTION, INDUSTRY_CONFIGS[IndustryType.GENERAL])
        
        # 模糊匹配 - 检查关键词是否包含在行业描述中
        for industry_type in IndustryType:
            industry_keywords = INDUSTRY_CONFIGS.get(industry_type, IndustryConfig([], [], [], [], [], "medium")).keywords
            if any(keyword in industry_lower for keyword in [kw.lower() for kw in industry_keywords[:5]]):  # 检查前5个核心关键词
                return INDUSTRY_CONFIGS.get(industry_type, INDUSTRY_CONFIGS[IndustryType.GENERAL])
        
        # 关键词反向匹配
        for industry_type in IndustryType:
            if any(industry_lower in kw.lower() for kw in INDUSTRY_CONFIGS.get(industry_type, IndustryConfig([], [], [], [], [], "medium")).keywords[:3]):
                return INDUSTRY_CONFIGS.get(industry_type, INDUSTRY_CONFIGS[IndustryType.GENERAL])
        
        return INDUSTRY_CONFIGS[IndustryType.GENERAL]
    
    def format_template(self, template_category: str, template_name: str, **kwargs) -> str:
        """格式化模板"""
        template = self.get_template(template_category, template_name)
        if not template:
            return ""
        
        try:
            # 自动添加当前年份和日期参数
            if 'current_year' not in kwargs:
                kwargs['current_year'] = get_current_year()
            if 'current_date' not in kwargs:
                kwargs['current_date'] = get_current_date_str()
            
            return template.format(**kwargs)
        except KeyError as e:
            # 如果缺少参数，返回原模板
            logger.warning(f"模板格式化缺少参数: {e}")
            return template

# 全局实例
_template_manager = None

def get_template_manager() -> PromptTemplateManager:
    """获取模板管理器实例"""
    global _template_manager
    if _template_manager is None:
        _template_manager = PromptTemplateManager()
    return _template_manager

def format_prompt(template_category: str, template_name: str, **kwargs) -> str:
    """格式化prompt的便捷函数"""
    manager = get_template_manager()
    return manager.format_template(template_category, template_name, **kwargs)

def get_industry_keywords(industry: str) -> List[str]:
    """获取行业关键词"""
    manager = get_template_manager()
    config = manager.get_industry_config(industry)
    return config.keywords

def get_industry_trending_topics(industry: str) -> List[str]:
    """获取行业热门话题"""
    manager = get_template_manager()
    config = manager.get_industry_config(industry)
    return config.trending_topics

def enhance_query_with_industry_terms(query: str, industry: str) -> str:
    """
    动态增强查询中的行业术语
    
    Args:
        query: 原始查询
        industry: 行业类型
        
    Returns:
        str: 增强后的查询
    """
    try:
        manager = get_template_manager()
        config = manager.get_industry_config(industry)
        
        # 统计现有行业术语
        query_lower = query.lower()
        existing_terms = sum(1 for keyword in config.keywords if keyword.lower() in query_lower)
        
        # 如果术语不足，进行增强
        if existing_terms < 2:
            # 选择最相关的关键词
            relevant_keywords = config.keywords[:3]
            trending_topics = config.trending_topics[:2]
            
            enhanced_query = query
            
            # 为建筑行业特殊优化
            if industry.lower() in ["建筑", "装修", "建筑业", "装修业", "建筑装修"]:
                if "新账号" in query and "冷启动" in query:
                    enhanced_query = query.replace("建筑业", "建筑装修/室内设计/智能家居")
                    enhanced_query = enhanced_query.replace("冷启动", "从零开始打造个人IP、内容定位、差异化策略")
                    enhanced_query = enhanced_query.replace("成功案例", "头部建筑账号案例、装修博主增长方法、设计师IP打造")
                
                elif "头部账号" in query:
                    enhanced_query = query.replace("建筑业头部账号", "建筑设计/装修案例/智能家居头部KOL")
                    enhanced_query = enhanced_query.replace("成功要素", "专业内容输出、案例展示、用户互动、商业变现模式")
                
                elif "行业最新发展趋势" in query:
                    enhanced_query = query.replace("建筑业", "建筑装修/绿色建筑/智慧工地/装配式建筑")
                    enhanced_query = enhanced_query.replace("发展趋势", "数字化转型、环保材料应用、BIM技术普及")
                    enhanced_query = enhanced_query.replace("行业转型", "传统装修向智能家居转型、线下向线上营销模式转变")
                
                elif "消费者行为" in query:
                    enhanced_query = query.replace("建筑业", "家装市场/装修行业/智能家居")
                    enhanced_query = enhanced_query.replace("消费者行为", "业主装修决策、设计师选择标准、材料品牌偏好")
                    enhanced_query = enhanced_query.replace("偏好变化", "环保意识提升、个性化定制需求、全屋智能趋势")
            
            # 通用增强逻辑
            current_year = get_current_year()
            if current_year not in enhanced_query:
                enhanced_query = f"{get_current_date_str()}{enhanced_query}"
            
            # 添加缺失的关键词
            missing_keywords = [kw for kw in relevant_keywords if kw.lower() not in enhanced_query.lower()]
            if missing_keywords and len(missing_keywords) > 0:
                # 智能插入关键词
                if "案例" in enhanced_query:
                    enhanced_query = enhanced_query.replace("案例", f"{missing_keywords[0]}案例")
                elif "策略" in enhanced_query:
                    enhanced_query = enhanced_query.replace("策略", f"{missing_keywords[0]}策略")
                else:
                    enhanced_query = f"{enhanced_query} {missing_keywords[0]}"
            
            # 确保长度合理
            if len(enhanced_query) > 150:
                enhanced_query = enhanced_query[:150]
            
            return enhanced_query
        
        return query
        
    except Exception as e:
        logger.warning(f"增强查询术语时出错: {e}")
        return query

def analyze_query_industry_coverage(query: str, industry: str) -> Dict[str, Any]:
    """
    分析查询的行业术语覆盖情况
    
    Args:
        query: 查询内容
        industry: 行业类型
        
    Returns:
        Dict: 分析结果
    """
    try:
        manager = get_template_manager()
        config = manager.get_industry_config(industry)
        
        query_lower = query.lower()
        
        # 统计行业关键词
        found_keywords = [kw for kw in config.keywords if kw.lower() in query_lower]
        
        # 统计热门话题
        found_topics = [topic for topic in config.trending_topics if topic.lower() in query_lower]
        
        # 检查具体性关键词
        specific_terms = get_time_range_keywords() + ['案例', '数据', '策略', '方法', '分析', '趋势']
        found_specific = [term for term in specific_terms if term in query_lower]
        
        # 计算覆盖得分
        keyword_score = min(1.0, len(found_keywords) / 3.0)  # 最多3个关键词满分
        topic_score = 1.0 if found_topics else 0.0
        specific_score = min(1.0, len(found_specific) / 2.0)  # 最多2个具体词满分
        
        total_score = (keyword_score * 0.5 + topic_score * 0.3 + specific_score * 0.2)
        
        return {
            "total_score": total_score,
            "keyword_count": len(found_keywords),
            "found_keywords": found_keywords,
            "topic_count": len(found_topics),
            "found_topics": found_topics,
            "specific_count": len(found_specific),
            "found_specific": found_specific,
            "is_sufficient": total_score >= 0.6,  # 60%以上算充足
            "recommendations": _generate_coverage_recommendations(total_score, found_keywords, found_topics, found_specific, config)
        }
        
    except Exception as e:
        return {"error": str(e), "total_score": 0.0, "is_sufficient": False}

def _generate_coverage_recommendations(score: float, keywords: List[str], topics: List[str], specifics: List[str], config) -> List[str]:
    """生成覆盖度改进建议"""
    recommendations = []
    
    if len(keywords) < 2:
        recommendations.append(f"建议添加更多行业关键词，如：{', '.join(config.keywords[:3])}")
    
    if not topics:
        recommendations.append(f"建议包含热门话题，如：{', '.join(config.trending_topics[:2])}")
    
    if len(specifics) < 1:
        current_year = get_current_year()
        recommendations.append(f"建议添加时间性或具体性关键词，如：{current_year}年、最新、案例分析等")
    
    if score < 0.4:
        recommendations.append("查询过于泛化，建议使用更具体的专业术语")
    
    return recommendations

if __name__ == "__main__":
    # 测试模板系统
    manager = get_template_manager()
    
    # 测试行业配置
    beauty_config = manager.get_industry_config("美妆护肤")
    print(f"美妆行业关键词: {beauty_config.keywords}")
    print(f"美妆行业热门话题: {beauty_config.trending_topics}")
    
    # 测试模板格式化
    template = manager.format_template(
        "basic_queries", 
        "startup_stage",
        follower_count=5000,
        industry="美妆护肤",
        account_name="小美的护肤日记",
        content_themes="护肤知识、产品评测",
        target_audience="18-35岁女性"
    )
    print(f"格式化后的模板:\n{template}")