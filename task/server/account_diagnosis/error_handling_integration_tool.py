"""
Error Handling Integration Tool
错误处理集成工具 - 帮助逐步迁移到增强的错误处理机制
"""

import asyncio
import time
import json
import sys
import os
from typing import Dict, Any, Optional
from colorama import Fore, Style, init

# 初始化colorama
init(autoreset=True)

# 添加项目路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', '..')))

class ErrorHandlingIntegrationTool:
    """错误处理集成工具"""
    
    def __init__(self):
        self.test_results = {}
        self.integration_status = {}
        
    def print_banner(self):
        """打印工具横幅"""
        print(f"""
{Fore.CYAN}╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🚀 Enhanced Error Handling Integration Tool              ║
║                                                              ║
║    This tool helps you integrate robust error handling      ║
║    into your account diagnosis system step by step.         ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝{Style.RESET_ALL}
        """)
    
    async def test_base_error_handling(self) -> bool:
        """测试基础错误处理系统"""
        print(f"\n{Fore.YELLOW}📋 Step 1: Testing Base Error Handling System{Style.RESET_ALL}")
        print("=" * 60)
        
        try:
            # Test import
            print(f"{Fore.CYAN}🔧 Testing imports...{Style.RESET_ALL}")
            try:
                from .enhanced_error_handling import (
                    EnhancedErrorHandler, 
                    ErrorCategory, 
                    ErrorSeverity,
                    CircuitBreaker,
                    CircuitBreakerConfig
                )
            except ImportError:
                # Fallback to absolute import
                from enhanced_error_handling import (
                    EnhancedErrorHandler, 
                    ErrorCategory, 
                    ErrorSeverity,
                    CircuitBreaker,
                    CircuitBreakerConfig
                )
            print(f"{Fore.GREEN}✅ All imports successful{Style.RESET_ALL}")
            
            # Test error handler creation
            print(f"{Fore.CYAN}🔧 Creating error handler...{Style.RESET_ALL}")
            error_handler = EnhancedErrorHandler()
            print(f"{Fore.GREEN}✅ Error handler created successfully{Style.RESET_ALL}")
            
            # Test error classification
            print(f"{Fore.CYAN}🔧 Testing error classification...{Style.RESET_ALL}")
            test_errors = [
                ConnectionError("Connection refused"),
                TimeoutError("Operation timed out"),
                ValueError("Invalid JSON format"),
                Exception("Unknown error")
            ]
            
            for error in test_errors:
                error_info = error_handler.classifier.classify(error)
                print(f"  {Fore.BLUE}• {type(error).__name__}: {error_info.category.value} - {error_info.severity.value}{Style.RESET_ALL}")
            
            print(f"{Fore.GREEN}✅ Error classification working correctly{Style.RESET_ALL}")
            
            # Test circuit breaker
            print(f"{Fore.CYAN}🔧 Testing circuit breaker...{Style.RESET_ALL}")
            
            call_count = 0
            async def test_function():
                nonlocal call_count
                call_count += 1
                if call_count <= 3:
                    raise Exception(f"Test failure {call_count}")
                return "Success!"
            
            # This should succeed after retries
            try:
                result = await error_handler.handle_with_circuit_breaker(
                    test_function,
                    "test_cb",
                    "test_operation"
                )
                print(f"{Fore.GREEN}✅ Circuit breaker and retry working: {result}{Style.RESET_ALL}")
            except Exception as e:
                print(f"{Fore.YELLOW}⚠️  Circuit breaker test result: {str(e)}{Style.RESET_ALL}")
            
            # Test health monitoring
            print(f"{Fore.CYAN}🔧 Testing health monitoring...{Style.RESET_ALL}")
            health = error_handler.get_system_health()
            print(f"{Fore.GREEN}✅ Health monitoring working - Score: {health['health_score']:.1f}{Style.RESET_ALL}")
            
            self.test_results["base_error_handling"] = True
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Base error handling test failed: {str(e)}{Style.RESET_ALL}")
            self.test_results["base_error_handling"] = False
            return False
    
    async def test_enhanced_base_service(self) -> bool:
        """测试增强的基础服务"""
        print(f"\n{Fore.YELLOW}📋 Step 2: Testing Enhanced Base Service{Style.RESET_ALL}")
        print("=" * 60)
        
        try:
            print(f"{Fore.CYAN}🔧 Testing enhanced base service import...{Style.RESET_ALL}")
            try:
                from .enhanced_base_service import EnhancedBaseAsyncService
            except ImportError:
                from enhanced_base_service import EnhancedBaseAsyncService
            print(f"{Fore.GREEN}✅ Enhanced base service import successful{Style.RESET_ALL}")
            
            # Note: We can't fully test the service without Redis, but we can test the class structure
            print(f"{Fore.BLUE}ℹ️  Enhanced base service structure validated{Style.RESET_ALL}")
            
            self.test_results["enhanced_base_service"] = True
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Enhanced base service test failed: {str(e)}{Style.RESET_ALL}")
            self.test_results["enhanced_base_service"] = False
            return False
    
    async def test_enhanced_diagnosis_service(self) -> bool:
        """测试增强的诊断服务"""
        print(f"\n{Fore.YELLOW}📋 Step 3: Testing Enhanced Diagnosis Service{Style.RESET_ALL}")
        print("=" * 60)
        
        try:
            print(f"{Fore.CYAN}🔧 Testing enhanced diagnosis service import...{Style.RESET_ALL}")
            try:
                from .enhanced_diagnosis_service import EnhancedDiagnosisService
            except ImportError:
                from enhanced_diagnosis_service import EnhancedDiagnosisService
            print(f"{Fore.GREEN}✅ Enhanced diagnosis service import successful{Style.RESET_ALL}")
            
            print(f"{Fore.BLUE}ℹ️  Enhanced diagnosis service structure validated{Style.RESET_ALL}")
            
            self.test_results["enhanced_diagnosis_service"] = True
            return True
            
        except Exception as e:
            print(f"{Fore.RED}❌ Enhanced diagnosis service test failed: {str(e)}{Style.RESET_ALL}")
            self.test_results["enhanced_diagnosis_service"] = False
            return False
    
    def check_dependencies(self) -> bool:
        """检查依赖项"""
        print(f"\n{Fore.YELLOW}📋 Checking Dependencies{Style.RESET_ALL}")
        print("=" * 60)
        
        dependencies = [
            ("colorama", "pip install colorama"),
            ("asyncio", "Built-in Python module"),
            ("redis", "pip install redis"),
            ("logging", "Built-in Python module")
        ]
        
        all_good = True
        
        for dep, install_cmd in dependencies:
            try:
                __import__(dep)
                print(f"{Fore.GREEN}✅ {dep} - Available{Style.RESET_ALL}")
            except ImportError:
                print(f"{Fore.RED}❌ {dep} - Missing (Install: {install_cmd}){Style.RESET_ALL}")
                all_good = False
        
        return all_good
    
    def generate_migration_plan(self) -> Dict[str, Any]:
        """生成迁移计划"""
        print(f"\n{Fore.YELLOW}📋 Generating Migration Plan{Style.RESET_ALL}")
        print("=" * 60)
        
        plan = {
            "phase_1": {
                "name": "Core Infrastructure Setup",
                "duration": "1-2 days",
                "tasks": [
                    "✅ Install colorama dependency",
                    "✅ Deploy enhanced_error_handling.py",
                    "✅ Deploy enhanced_base_service.py", 
                    "✅ Run integration tests",
                    "✅ Verify logging output"
                ],
                "risk": "Low",
                "rollback": "Remove new files, no impact on existing services"
            },
            "phase_2": {
                "name": "Service Integration",
                "duration": "2-3 days",
                "tasks": [
                    "🔧 Deploy enhanced_diagnosis_service.py",
                    "🔧 Update service startup scripts",
                    "🔧 Configure circuit breaker thresholds",
                    "🔧 Test with real diagnosis requests",
                    "🔧 Monitor error patterns"
                ],
                "risk": "Medium",
                "rollback": "Switch back to original service_implementations.py"
            },
            "phase_3": {
                "name": "Full System Integration",
                "duration": "3-5 days", 
                "tasks": [
                    "🔧 Integrate with other services (Strategy, Review, Cover)",
                    "🔧 Setup health monitoring dashboard",
                    "🔧 Configure alerting",
                    "🔧 Performance tuning",
                    "🔧 Documentation update"
                ],
                "risk": "Medium",
                "rollback": "Gradual rollback service by service"
            }
        }
        
        for phase_name, phase_info in plan.items():
            print(f"\n{Fore.CYAN}{phase_info['name']} ({phase_info['duration']}){Style.RESET_ALL}")
            print(f"Risk Level: {Fore.YELLOW if phase_info['risk'] == 'Medium' else Fore.GREEN}{phase_info['risk']}{Style.RESET_ALL}")
            for task in phase_info['tasks']:
                if task.startswith("✅"):
                    print(f"  {Fore.GREEN}{task}{Style.RESET_ALL}")
                else:
                    print(f"  {Fore.YELLOW}{task}{Style.RESET_ALL}")
            print(f"Rollback: {Fore.BLUE}{phase_info['rollback']}{Style.RESET_ALL}")
        
        return plan
    
    def generate_deployment_commands(self):
        """生成部署命令"""
        print(f"\n{Fore.YELLOW}📋 Deployment Commands{Style.RESET_ALL}")
        print("=" * 60)
        
        commands = [
            ("Install Dependencies", "pip install colorama"),
            ("Test Base System", "cd server/account_diagnosis && python error_handling_integration_tool.py --test-base"),
            ("Test Full Integration", "cd server/account_diagnosis && python error_handling_integration_tool.py --test-all"),
            ("Deploy Enhanced Service", "# Replace DiagnosisService with EnhancedDiagnosisService in your startup script"),
            ("Monitor Health", "# Use the health check endpoints to monitor service health"),
            ("Emergency Rollback", "# Switch back to original service_implementations.py if needed")
        ]
        
        for desc, cmd in commands:
            print(f"\n{Fore.CYAN}{desc}:{Style.RESET_ALL}")
            print(f"  {Fore.GREEN}{cmd}{Style.RESET_ALL}")
    
    def generate_monitoring_guide(self):
        """生成监控指南"""
        print(f"\n{Fore.YELLOW}📋 Monitoring Guide{Style.RESET_ALL}")
        print("=" * 60)
        
        monitoring_points = [
            ("Circuit Breaker Status", "Monitor circuit breaker states - OPEN breakers indicate issues"),
            ("Error Rates", "Track error rates by category and severity"),
            ("Response Times", "Monitor average response times for each operation"),
            ("Success Rates", "Track success rates for diagnosis, deep research, etc."),
            ("Fallback Rates", "Monitor how often deep research falls back to basic mode"),
            ("Health Scores", "Overall service health scores (0-100)")
        ]
        
        for metric, description in monitoring_points:
            print(f"\n{Fore.CYAN}{metric}:{Style.RESET_ALL}")
            print(f"  {Fore.WHITE}{description}{Style.RESET_ALL}")
        
        print(f"\n{Fore.YELLOW}Key Log Patterns to Watch:{Style.RESET_ALL}")
        log_patterns = [
            ("🚨 CRITICAL ERROR", "Immediate attention required"),
            ("🚫 Circuit breaker.*OPEN", "Service degradation"),
            ("🔄 Retry attempt", "Temporary issues being handled"),
            ("⚠️  Deep research failed", "Fallback to basic mode"),
            ("✅ Completed.*duration", "Successful operations with timing")
        ]
        
        for pattern, meaning in log_patterns:
            print(f"  {Fore.YELLOW}{pattern}{Style.RESET_ALL} - {meaning}")
    
    async def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print(f"{Fore.MAGENTA}🚀 Running Comprehensive Integration Test{Style.RESET_ALL}")
        
        # Check dependencies first
        if not self.check_dependencies():
            print(f"{Fore.RED}❌ Dependency check failed. Please install missing dependencies.{Style.RESET_ALL}")
            return {"success": False, "error": "Missing dependencies"}
        
        # Run all tests
        tests = [
            ("Base Error Handling", self.test_base_error_handling),
            ("Enhanced Base Service", self.test_enhanced_base_service),
            ("Enhanced Diagnosis Service", self.test_enhanced_diagnosis_service)
        ]
        
        for test_name, test_func in tests:
            success = await test_func()
            if not success:
                print(f"{Fore.RED}❌ Test suite failed at: {test_name}{Style.RESET_ALL}")
                return {"success": False, "error": f"Failed at {test_name}", "results": self.test_results}
        
        print(f"\n{Fore.GREEN}🎉 All tests passed successfully!{Style.RESET_ALL}")
        
        # Generate migration plan
        migration_plan = self.generate_migration_plan()
        
        # Generate deployment commands
        self.generate_deployment_commands()
        
        # Generate monitoring guide
        self.generate_monitoring_guide()
        
        return {
            "success": True,
            "test_results": self.test_results,
            "migration_plan": migration_plan
        }

async def main():
    """主函数"""
    tool = ErrorHandlingIntegrationTool()
    tool.print_banner()
    
    # Parse command line arguments
    import sys
    
    if len(sys.argv) > 1:
        if "--test-base" in sys.argv:
            await tool.test_base_error_handling()
        elif "--test-all" in sys.argv:
            await tool.run_comprehensive_test()
        elif "--check-deps" in sys.argv:
            tool.check_dependencies()
        elif "--migration-plan" in sys.argv:
            tool.generate_migration_plan()
        else:
            print(f"{Fore.YELLOW}Usage: python error_handling_integration_tool.py [--test-base|--test-all|--check-deps|--migration-plan]{Style.RESET_ALL}")
    else:
        # Run comprehensive test by default
        result = await tool.run_comprehensive_test()
        
        if result["success"]:
            print(f"\n{Fore.GREEN}🎯 Next Steps:{Style.RESET_ALL}")
            print(f"1. {Fore.CYAN}Review the migration plan above{Style.RESET_ALL}")
            print(f"2. {Fore.CYAN}Start with Phase 1 - Core Infrastructure{Style.RESET_ALL}")
            print(f"3. {Fore.CYAN}Test in development environment first{Style.RESET_ALL}")
            print(f"4. {Fore.CYAN}Monitor the colorful logs for debugging{Style.RESET_ALL}")
            print(f"5. {Fore.CYAN}Use health check endpoints to monitor service health{Style.RESET_ALL}")
        else:
            print(f"\n{Fore.RED}❌ Integration test failed. Please fix the issues above before proceeding.{Style.RESET_ALL}")

if __name__ == "__main__":
    asyncio.run(main())