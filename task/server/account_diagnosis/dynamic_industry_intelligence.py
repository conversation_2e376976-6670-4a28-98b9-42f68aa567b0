"""
动态行业术语智能发现系统
通过深度搜索自动识别和扩展任意行业的专业术语
"""

import asyncio
import json
import logging
import re
import time
from typing import Dict, List, Optional, Tuple, Set, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class IndustryIntelligence:
    """动态行业智能数据"""
    industry: str
    keywords: List[str]
    trending_topics: List[str]
    key_platforms: List[str]
    regulatory_focus: List[str]
    seasonal_factors: List[str]
    competition_intensity: str
    confidence_score: float
    last_updated: datetime
    sources: List[str]

class DynamicIndustryDiscovery:
    """动态行业发现系统"""
    
    def __init__(self, gpt_model: str = "gpt-4.1", pplx_model: str = "sonar"):
        self.gpt_model = gpt_model
        self.pplx_model = pplx_model
        self.cache_duration = 86400  # 24小时缓存
        self.industry_cache = {}  # 内存缓存
        
        # 尝试使用Redis缓存
        try:
            from .cache_disabled import get_intelligent_cache_manager
            self.cache_manager = get_intelligent_cache_manager()
            logger.info("Redis缓存管理器初始化成功")
        except (ImportError, Exception) as e:
            logger.warning(f"Redis缓存不可用，使用内存缓存: {e}")
            self.cache_manager = None
    
    async def discover_industry_intelligence(self, industry: str, account_context: Optional[Dict] = None) -> IndustryIntelligence:
        """
        动态发现行业智能信息
        
        Args:
            industry: 行业名称（可以是任意行业）
            account_context: 账号上下文信息（可选）
            
        Returns:
            IndustryIntelligence: 动态发现的行业信息
        """
        try:
            logger.info(f"开始动态发现行业智能：{industry}")
            
            # 1. 检查缓存
            cached_intelligence = await self._get_cached_industry_intelligence(industry)
            if cached_intelligence:
                logger.info(f"从缓存获取{industry}行业信息")
                return cached_intelligence
            
            # 2. 执行深度搜索发现
            search_queries = self._generate_discovery_queries(industry, account_context)
            search_results = await self._execute_discovery_search(search_queries)
            
            if not search_results:
                logger.warning(f"未能获取{industry}行业的搜索结果，使用基础模板")
                return self._create_fallback_intelligence(industry)
            
            # 3. 分析搜索结果，提取行业智能
            intelligence = await self._extract_industry_intelligence(industry, search_results, account_context)
            
            # 4. 缓存结果
            await self._cache_industry_intelligence(industry, intelligence)
            
            logger.info(f"成功发现{industry}行业智能，关键词数：{len(intelligence.keywords)}")
            return intelligence
            
        except Exception as e:
            logger.error(f"动态发现行业智能失败: {e}")
            return self._create_fallback_intelligence(industry)
    
    @staticmethod
    def _generate_discovery_queries(self, industry: str, account_context: Optional[Dict] = None) -> List[Dict]:
        """生成行业发现查询"""
        queries = []
        year_ = datetime.now().year
        
        # 基础行业术语发现查询
        queries.extend([
            {
                "content": f"{industry}行业专业术语 关键词 专业名词 行业词汇 {year_}",
                "focus": "专业术语发现",
                "priority": 2.0,
                "type": "terminology"
            },
            {
                "content": f"{industry}最新发展趋势 热门话题 行业动态 创新技术 {year_}",
                "focus": "趋势话题发现", 
                "priority": 1.9,
                "type": "trends"
            },
            {
                "content": f"{industry}头部企业 知名品牌 行业领导者 成功案例",
                "focus": "行业标杆发现",
                "priority": 1.8,
                "type": "leaders"
            },
            {
                "content": f"{industry}监管政策 行业规范 合规要求 法律法规 {year_}",
                "focus": "监管环境发现",
                "priority": 1.7,
                "type": "regulatory"
            },
            {
                "content": f"{industry}季节性特点 周期性规律 市场波动 消费习惯",
                "focus": "季节性因素发现",
                "priority": 1.6,
                "type": "seasonal"
            }
        ])
        
        # 如果有账号上下文，添加针对性查询
        if account_context:
            content_themes = account_context.get('content_themes', [])
            if content_themes:
                themes_str = ' '.join(content_themes)
                queries.append({
                    "content": f"{industry} {themes_str} 专业术语 内容创作 账号运营",
                    "focus": "内容相关术语",
                    "priority": 1.8,
                    "type": "content_specific"
                })
        
        return queries
    
    async def _execute_discovery_search(self, queries: List[Dict]) -> List[Dict]:
        """执行发现搜索"""
        try:
            from task import callWattGPT
            
            # 构建PPLX搜索请求
            pplx_search_list = []
            for query in queries:
                search_request = {
                    "model": self.pplx_model,
                    "messages": [
                        {"role": "user", "content": query["content"]}
                    ],
                    "search_recency_filter": "month"
                }
                pplx_search_list.append(search_request)
            
            # 执行批量搜索
            logger.info(f"执行{len(pplx_search_list)}个行业发现搜索")
            pplx_status, code, pplx_results = callWattGPT.gcallPplxChannelChatCompletions(
                pplx_search_list, timeout=60
            )
            
            if not pplx_status:
                logger.error(f"行业发现搜索失败: {pplx_results}")
                return []
            
            # 解析搜索结果
            search_results = []
            for i, pplx_result in enumerate(pplx_results):
                status, code, result = pplx_result
                if not status:
                    logger.warning(f"搜索查询{i}失败: {result}")
                    continue
                
                # 处理结果数据
                if hasattr(result, 'json'):
                    result_data = result.json()
                elif isinstance(result, dict):
                    result_data = result
                else:
                    logger.warning(f"搜索结果{i}格式异常")
                    continue
                
                search_content = result_data.get('result', {}).get('data', {}).get('choices', [{}])[0].get('message', {}).get('content', '')
                search_citations = result_data.get('result', {}).get('data', {}).get('citations', [])
                
                if search_content:
                    search_results.append({
                        "query_type": queries[i]["type"],
                        "query_content": queries[i]["content"],
                        "result": search_content,
                        "citations": search_citations,
                        "focus": queries[i]["focus"]
                    })
            
            logger.info(f"成功获取{len(search_results)}个搜索结果")
            return search_results
            
        except Exception as e:
            logger.error(f"执行发现搜索失败: {e}")
            return []
    
    async def _extract_industry_intelligence(self, industry: str, search_results: List[Dict], account_context: Optional[Dict] = None) -> IndustryIntelligence:
        """从搜索结果中提取行业智能"""
        try:
            from task import callWattGPT
            year_ = datetime.now().year
            
            # 构建分析prompt
            analysis_prompt = f"""
你是一位行业分析专家，需要从搜索结果中提取{industry}行业的专业智能信息。

搜索结果：
{json.dumps(search_results, indent=2, ensure_ascii=False)}

请分析并提取以下信息：

1. **专业术语关键词**（30-50个）：
   - 行业核心术语
   - 技术专业词汇
   - 产品/服务名称
   - 业务流程术语
   - 质量标准术语

2. **热门趋势话题**（10-15个）：
   - {year_}年最新趋势
   - 技术创新方向
   - 市场变化趋势
   - 消费者需求变化

3. **主要平台渠道**（5-8个）：
   - 行业主要社交媒体平台
   - 专业交流平台
   - 营销推广渠道

4. **监管合规重点**（5-10个）：
   - 相关法律法规
   - 行业标准规范
   - 质量认证要求

5. **季节性因素**（5-8个）：
   - 行业周期性特点
   - 季节性波动
   - 特殊时期影响

6. **竞争强度评估**：high/medium/low

请以JSON格式返回，确保术语准确、具体、实用：
"""

            # 定义JSON schema
            body = {
                "model": self.gpt_model,
                "messages": [
                    {"role": "user", "content": analysis_prompt}
                ],
                "response_format": {
                    "type": "json_schema",
                    "json_schema": {
                        "name": "industry_intelligence",
                        "schema": {
                            "type": "object",
                            "properties": {
                                "keywords": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "行业专业术语关键词"
                                },
                                "trending_topics": {
                                    "type": "array", 
                                    "items": {"type": "string"},
                                    "description": "热门趋势话题"
                                },
                                "key_platforms": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "主要平台渠道"
                                },
                                "regulatory_focus": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "监管合规重点"
                                },
                                "seasonal_factors": {
                                    "type": "array",
                                    "items": {"type": "string"},
                                    "description": "季节性因素"
                                },
                                "competition_intensity": {
                                    "type": "string",
                                    "enum": ["high", "medium", "low"],
                                    "description": "竞争强度"
                                },
                                "confidence_score": {
                                    "type": "number",
                                    "minimum": 0.0,
                                    "maximum": 1.0,
                                    "description": "信息可信度评分"
                                }
                            },
                            "required": ["keywords", "trending_topics", "key_platforms", "regulatory_focus", "seasonal_factors", "competition_intensity", "confidence_score"]
                        }
                    }
                }
            }
            
            # 调用GPT分析
            gpt_status, code, gpt_result = callWattGPT.callOpenaiChannelChatCompletions(body, timeout=90)
            
            if not gpt_status:
                logger.error(f"行业智能分析失败: {gpt_result}")
                return self._create_fallback_intelligence(industry)
            
            # 解析GPT响应
            if isinstance(gpt_result, dict) and 'result' in gpt_result:
                result_data = gpt_result['result']
                if isinstance(result_data, dict) and 'data' in result_data:
                    data = result_data['data']
                    if isinstance(data, dict) and 'choices' in data:
                        choices = data['choices']
                        if isinstance(choices, list) and len(choices) > 0:
                            choice = choices[0]
                            if isinstance(choice, dict) and 'message' in choice:
                                message = choice['message']
                                if isinstance(message, dict) and 'content' in message:
                                    content = message['content']
                                    intelligence_data = json.loads(content)
                                    
                                    # 创建行业智能对象
                                    return IndustryIntelligence(
                                        industry=industry,
                                        keywords=intelligence_data.get('keywords', []),
                                        trending_topics=intelligence_data.get('trending_topics', []),
                                        key_platforms=intelligence_data.get('key_platforms', []),
                                        regulatory_focus=intelligence_data.get('regulatory_focus', []),
                                        seasonal_factors=intelligence_data.get('seasonal_factors', []),
                                        competition_intensity=intelligence_data.get('competition_intensity', 'medium'),
                                        confidence_score=intelligence_data.get('confidence_score', 0.7),
                                        last_updated=datetime.now(),
                                        sources=[r.get('query_content', '') for r in search_results]
                                    )
            
            logger.error("GPT行业智能分析响应格式错误")
            return self._create_fallback_intelligence(industry)
            
        except Exception as e:
            logger.error(f"提取行业智能失败: {e}")
            return self._create_fallback_intelligence(industry)
    
    def _create_fallback_intelligence(self, industry: str) -> IndustryIntelligence:
        """创建后备行业智能"""
        return IndustryIntelligence(
            industry=industry,
            keywords=[industry, "专业术语", "行业知识", "业务流程", "产品服务"],
            trending_topics=["行业创新", "数字化转型", "用户体验", "市场趋势"],
            key_platforms=["小红书", "抖音", "微博", "知乎"],
            regulatory_focus=["行业规范", "质量标准", "合规要求"],
            seasonal_factors=["市场周期", "消费习惯", "季节性波动"],
            competition_intensity="medium",
            confidence_score=0.3,
            last_updated=datetime.now(),
            sources=["后备模板"]
        )
    
    async def _get_cached_industry_intelligence(self, industry: str) -> Optional[IndustryIntelligence]:
        """获取缓存的行业智能"""
        try:
            # 优先使用Redis缓存
            if self.cache_manager:
                cache_key = f"industry_intelligence:{industry}"
                cached_data = await self.cache_manager.get_cached_data(cache_key)
                if cached_data:
                    # 检查缓存时间
                    last_updated = datetime.fromisoformat(cached_data['last_updated'])
                    if datetime.now() - last_updated < timedelta(seconds=self.cache_duration):
                        return IndustryIntelligence(**cached_data)
            
            # 回退到内存缓存
            if industry in self.industry_cache:
                cache_entry = self.industry_cache[industry]
                if time.time() - cache_entry['timestamp'] < self.cache_duration:
                    return cache_entry['intelligence']
            
            return None
            
        except Exception as e:
            logger.error(f"获取缓存行业智能失败: {e}")
            return None
    
    async def _cache_industry_intelligence(self, industry: str, intelligence: IndustryIntelligence):
        """缓存行业智能"""
        try:
            # Redis缓存
            if self.cache_manager:
                cache_key = f"industry_intelligence:{industry}"
                cache_data = {
                    'industry': intelligence.industry,
                    'keywords': intelligence.keywords,
                    'trending_topics': intelligence.trending_topics,
                    'key_platforms': intelligence.key_platforms,
                    'regulatory_focus': intelligence.regulatory_focus,
                    'seasonal_factors': intelligence.seasonal_factors,
                    'competition_intensity': intelligence.competition_intensity,
                    'confidence_score': intelligence.confidence_score,
                    'last_updated': intelligence.last_updated.isoformat(),
                    'sources': intelligence.sources
                }
                await self.cache_manager.cache_data(cache_key, cache_data, ttl=self.cache_duration)
            
            # 内存缓存
            self.industry_cache[industry] = {
                'intelligence': intelligence,
                'timestamp': time.time()
            }
            
        except Exception as e:
            logger.error(f"缓存行业智能失败: {e}")

# 全局实例
_dynamic_industry_discovery = None

def get_dynamic_industry_discovery() -> DynamicIndustryDiscovery:
    """获取动态行业发现系统实例"""
    global _dynamic_industry_discovery
    if _dynamic_industry_discovery is None:
        _dynamic_industry_discovery = DynamicIndustryDiscovery()
    return _dynamic_industry_discovery

async def discover_industry_terms(industry: str, account_context: Optional[Dict] = None) -> IndustryIntelligence:
    """
    动态发现行业术语的便捷函数
    
    Args:
        industry: 行业名称
        account_context: 账号上下文（可选）
        
    Returns:
        IndustryIntelligence: 行业智能信息
    """
    discovery = get_dynamic_industry_discovery()
    return await discovery.discover_industry_intelligence(industry, account_context)