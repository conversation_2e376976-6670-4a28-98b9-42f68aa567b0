"""
服务配置文件
统一管理异步服务的基本配置信息
"""

from typing import Dict, List, Optional
from dataclasses import dataclass


@dataclass
class ServiceConfig:
    """服务配置类"""
    name: str
    input_queue: str
    output_queue: str
    max_concurrent_tasks: int = 8
    ai_timeout: int = 120
    ai_max_retries: int = 2


@dataclass
class AIModelConfig:
    """AI模型配置类"""
    gpt_model: str = "gpt-4.1"
    pplx_model: str = "sonar"
    gemini_model: str = "gemini-2.5-flash"
    claude_model: str = "claude-sonnet-4-20250514"


@dataclass
class DeepResearchConfig:
    """深度研究配置类"""
    # 模型配置
    models: AIModelConfig
    
    # 查询生成配置
    max_queries_per_layer: int = 3
    max_concurrent_searches: int = 3
    min_search_results: int = 2
    
    # 超时配置
    search_timeout: int = 60
    analysis_timeout: int = 180
    report_timeout: int = 120
    
    # 重试配置
    max_search_retries: int = 3
    max_analysis_retries: int = 3
    max_report_retries: int = 2
    
    # 搜索超时递增配置
    search_timeouts: Optional[List[int]] = None
    analysis_timeouts: Optional[List[int]] = None
    report_timeouts: Optional[List[int]] = None
    
    # 缓存配置
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1小时
    
    def __post_init__(self):
        """初始化后处理"""
        if self.search_timeouts is None:
            self.search_timeouts = [30, 45, 60]
        if self.analysis_timeouts is None:
            self.analysis_timeouts = [90, 120, 180]
        if self.report_timeouts is None:
            self.report_timeouts = [60, 90]


def get_service_configs(env: str) -> Dict[str, ServiceConfig]:
    """获取所有服务配置"""
    env_lower = env.lower()
    
    return {
        "diagnosis": ServiceConfig(
            name="diagnosis",
            input_queue=f"{env_lower}:q:diagnosis:request",
            output_queue=f"{env_lower}:q:diagnosis:response",
            max_concurrent_tasks=8,
            ai_timeout=220
        ),
        
        "strategy": ServiceConfig(
            name="strategy",
            input_queue=f"{env_lower}:q:generate:report:socialmedia:request",
            output_queue=f"{env_lower}:q:generate:report:socialmedia:response",
            max_concurrent_tasks=8,
            ai_timeout=180
        ),
        
        "review": ServiceConfig(
            name="review",
            input_queue=f"{env_lower}:q:generate:report:operation:request",
            output_queue=f"{env_lower}:q:generate:report:operation:response",
            max_concurrent_tasks=8,
            ai_timeout=220
        ),
        
        "cover": ServiceConfig(
            name="cover",
            input_queue=f"{env_lower}:q:generate:cover:request",
            output_queue=f"{env_lower}:q:generate:cover:response",
            max_concurrent_tasks=10,
            ai_timeout=130
        )
    }


def get_service_config(service_name: str, env: str) -> ServiceConfig:
    """获取指定服务的配置"""
    configs = get_service_configs(env)
    if service_name not in configs:
        raise ValueError(f"未知的服务名称: {service_name}")
    return configs[service_name]


def get_deep_research_config() -> DeepResearchConfig:
    """获取深度研究配置"""
    return DeepResearchConfig(
        models=AIModelConfig(),
        max_queries_per_layer=3,
        max_concurrent_searches=3,
        min_search_results=2,
        search_timeout=60,
        analysis_timeout=180,
        report_timeout=120,
        max_search_retries=3,
        max_analysis_retries=3,
        max_report_retries=2,
        enable_caching=True,
        cache_ttl=3600
    )


def get_ai_model_config() -> AIModelConfig:
    """获取AI模型配置"""
    return AIModelConfig() 