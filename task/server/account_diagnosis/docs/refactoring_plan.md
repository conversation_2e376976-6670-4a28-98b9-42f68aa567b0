# 账号诊断系统重构计划

## 📋 项目概述

本文档详细规划了账号诊断系统的完整重构方案，旨在解决当前系统架构混乱、性能瓶颈和代码质量问题，构建一个高性能、易维护、可扩展的诊断系统。对于这个文件夹下的其他几个任务(cover_gen, review 和 strategy)暂时不动。

## 🎯 重构目标

- **性能提升**: 总体执行时间减少 40-50%
- **架构优化**: 统一组件设计，消除重复代码
- **并发优化**: 最大化并行执行，减少等待时间
- **资源优化**: 统一资源管理，提高资源利用率
- **可维护性**: 清晰的模块划分，标准化的接口设计

## 📊 现状问题分析

### 🔴 架构问题
- [ ] TaskProgressTracker在多个文件中重复实现
- [ ] TaskStatus枚举定义分散，缺乏统一管理
- [ ] 配置管理分散，难以统一调整
- [ ] 模块间存在循环依赖关系

### 🔴 性能问题
- [ ] 搜索查询串行执行，浪费时间
- [ ] 报告生成串行执行，效率低下
- [ ] 缺乏智能缓存机制
- [ ] 资源管理分散，连接复用率低
- [ ] 超时配置固定，无法动态调整

### 🔴 代码质量问题
- [ ] 错误处理策略不统一
- [ ] 日志记录格式混乱
- [ ] 缺乏完整的测试覆盖
- [ ] 代码重复度高

---

## 🚀 Phase 1: 核心架构重构

### 1.1 统一状态管理系统

#### 📝 任务清单
- [ ] **创建统一状态枚举** `core/status_manager.py`
  - [ ] 定义 `UnifiedTaskStatus` 枚举类
  - [ ] 包含基础状态、深度研究状态、分析状态、报告生成状态
  - [ ] 定义状态转换规则和进度映射
  - [ ] 支持中英文状态消息

- [ ] **实现统一进度跟踪器** `core/status_manager.py`
  - [ ] 创建 `UnifiedProgressTracker` 类
  - [ ] 实现状态更新接口 `update_status()`
  - [ ] 实现步骤级进度跟踪 `update_step_progress()`
  - [ ] 集成Redis状态推送功能
  - [ ] 添加性能统计和历史记录

- [ ] **状态消息国际化** `core/status_manager.py`
  - [ ] 创建状态消息映射表
  - [ ] 支持详细模式和简洁模式
  - [ ] 实现动态消息生成

#### 🎯 验收标准
- [ ] 所有状态定义集中在一个文件中
- [ ] 进度跟踪器支持Redis推送和本地回调
- [ ] 状态消息支持中英文切换
- [ ] 单元测试覆盖率 > 90%

### 1.2 统一配置管理

#### 📝 任务清单
- [ ] **创建配置管理器** `core/config_manager.py`
  - [ ] 定义 `DiagnosisConfig` 数据类
  - [ ] 包含基础配置、AI模型配置、并发配置
  - [ ] 实现动态超时计算 `get_dynamic_timeout()`
  - [ ] 支持环境变量覆盖

- [ ] **AI模型配置统一** `core/config_manager.py`
  - [ ] 整合所有AI模型配置到 `AIModelConfig`
  - [ ] 支持模型切换和降级策略
  - [ ] 添加模型性能配置

- [ ] **并发和超时配置** `core/config_manager.py`
  - [ ] 定义并发控制参数
  - [ ] 实现基于复杂度的动态超时
  - [ ] 添加重试策略配置

#### 🎯 验收标准
- [ ] 所有配置集中管理
- [ ] 支持环境变量和配置文件
- [ ] 动态超时计算准确
- [ ] 配置验证完整

### 1.3 统一资源管理

#### 📝 任务清单
- [ ] **创建资源管理器** `core/resource_manager.py`
  - [ ] 实现 `UnifiedResourceManager` 类
  - [ ] 管理Redis连接池
  - [ ] 管理AI客户端连接
  - [ ] 管理缓存实例

- [ ] **连接池优化** `core/resource_manager.py`
  - [ ] 实现Redis连接池复用
  - [ ] 添加连接健康检查
  - [ ] 实现优雅关闭机制

- [ ] **资源生命周期管理** `core/resource_manager.py`
  - [ ] 实现资源初始化 `initialize()`
  - [ ] 实现资源清理 `cleanup()`
  - [ ] 添加资源监控和告警

#### 🎯 验收标准
- [ ] 资源管理集中化
- [ ] 连接复用率 > 80%
- [ ] 资源泄漏为零
- [ ] 支持优雅关闭

## ⚡ Phase 2: 核心算法优化

### 2.1 诊断引擎重构

#### 📝 任务清单
- [ ] **创建诊断引擎** `core/diagnosis_engine.py`
  - [ ] 实现 `DiagnosisEngine` 主类
  - [ ] 设计三阶段执行流程
  - [ ] 实现条件并行执行逻辑
  - [ ] 集成错误处理和重试机制

- [ ] **数据提取优化** `core/diagnosis_engine.py`
  - [ ] 并行执行账号信息提取和数据验证
  - [ ] 实现数据预处理管道
  - [ ] 添加数据质量检查

- [ ] **分析流程优化** `core/diagnosis_engine.py`
  - [ ] 实现基础诊断和深度研究的条件分支
  - [ ] 优化分析算法的并行度
  - [ ] 添加分析结果缓存

#### 🎯 验收标准
- [ ] 三阶段流程清晰分离
- [ ] 并行执行效率提升 > 40%
- [ ] 错误处理覆盖所有异常情况
- [ ] 支持基础和深度两种模式

### 2.2 搜索管道优化

#### 📝 任务清单
- [ ] **创建搜索管道** `core/search_pipeline.py`
  - [ ] 实现 `SearchPipeline` 类
  - [ ] 设计查询生成、优化、执行三步流程
  - [ ] 实现并发控制和限流

- [ ] **查询生成优化** `core/search_pipeline.py`
  - [ ] 并行生成基础、行业、竞争三类查询
  - [ ] 实现查询去重和优化算法
  - [ ] 添加查询质量评分

- [ ] **搜索执行优化** `core/search_pipeline.py`
  - [ ] 实现信号量控制的并发搜索
  - [ ] 集成智能缓存机制
  - [ ] 添加搜索结果质量过滤

- [ ] **结果处理优化** `core/search_pipeline.py`
  - [ ] 实现搜索结果合并和去重
  - [ ] 添加结果相关性评分
  - [ ] 实现结果摘要提取

#### 🎯 验收标准
- [ ] 搜索并发度可配置
- [ ] 缓存命中率 > 30%
- [ ] 搜索结果质量评分准确
- [ ] 支持降级和容错

### 2.3 分析引擎优化

#### 📝 任务清单
- [ ] **创建分析引擎** `core/analysis_engine.py`
  - [ ] 实现 `AnalysisEngine` 类
  - [ ] 设计分阶段分析流程
  - [ ] 实现复杂度自适应算法

- [ ] **复杂度计算** `core/analysis_engine.py`
  - [ ] 实现 `_calculate_complexity_factor()` 方法
  - [ ] 基于数据量和类型计算复杂度
  - [ ] 动态调整超时和资源分配

- [ ] **并行分析优化** `core/analysis_engine.py`
  - [ ] 并行执行基础分析和行业分析
  - [ ] 实现分析结果合并算法
  - [ ] 添加分析质量评估

- [ ] **AI调用优化** `core/analysis_engine.py`
  - [ ] 实现智能重试机制
  - [ ] 添加AI响应质量检查
  - [ ] 实现模型降级策略

#### 🎯 验收标准
- [ ] 复杂度计算准确
- [ ] 分析并行度最大化
- [ ] AI调用成功率 > 95%
- [ ] 支持多模型降级

## 📊 Phase 3: 报告生成优化

### 3.1 并行报告生成器

#### 📝 任务清单
- [ ] **创建报告生成器** `core/report_generator.py`
  - [ ] 实现 `ReportGenerator` 类
  - [ ] 设计四类报告并行生成架构
  - [ ] 实现信号量控制并发度

- [ ] **HTML报告生成** `core/report_generator.py`
  - [ ] 实现 `_generate_html_report()` 方法
  - [ ] 集成缓存机制
  - [ ] 添加HTML质量验证

- [ ] **JSON报告生成** `core/report_generator.py`
  - [ ] 实现 `_generate_json_report()` 方法
  - [ ] 添加JSON结构验证
  - [ ] 实现字段完整性检查

- [ ] **销售提案生成** `core/report_generator.py`
  - [ ] 实现销售提案Markdown生成
  - [ ] 实现销售提案HTML生成
  - [ ] 添加内容质量检查

- [ ] **报告质量保证** `core/report_generator.py`
  - [ ] 实现报告内容验证
  - [ ] 添加格式标准化
  - [ ] 实现错误报告生成

#### 🎯 验收标准
- [ ] 四类报告完全并行生成
- [ ] 报告生成时间减少 > 75%
- [ ] 报告质量验证通过率 100%
- [ ] 缓存命中率 > 25%

### 3.2 报告模板优化

#### 📝 任务清单
- [ ] **模板系统重构** `core/template_manager.py`
  - [ ] 创建统一模板管理器
  - [ ] 实现模板缓存机制
  - [ ] 支持动态模板加载

- [ ] **提示词优化** `core/template_manager.py`
  - [ ] 优化AI提示词模板
  - [ ] 实现上下文自适应
  - [ ] 添加提示词版本管理

#### 🎯 验收标准
- [ ] 模板管理统一化
- [ ] 提示词效果提升 > 20%
- [ ] 支持A/B测试


## 🧪 Phase 4: 集成测试和部署 (Week 7)

### 4.1 统一入口点重构

#### 📝 任务清单
- [ ] **重构服务入口** `new_script_for_diagnosis.py`
  - [ ] 实现 `OptimizedDiagnosisService` 类
  - [ ] 集成所有核心组件
  - [ ] 实现优雅启动和关闭

- [ ] **服务生命周期管理** `new_script_for_diagnosis.py`
  - [ ] 实现服务初始化流程
  - [ ] 添加健康检查接口
  - [ ] 实现优雅关闭机制

- [ ] **配置加载优化** `new_script_for_diagnosis.py`
  - [ ] 支持多种配置源
  - [ ] 实现配置热重载
  - [ ] 添加配置验证

#### 🎯 验收标准
- [ ] 服务启动时间 < 10秒
- [ ] 支持零停机重启
- [ ] 配置变更无需重启

### 4.2 测试体系建设

#### 📝 任务清单
- [ ] **单元测试** `tests/unit/`
  - [ ] 为所有核心类编写单元测试
  - [ ] 测试覆盖率 > 90%
  - [ ] 包含边界条件和异常情况

- [ ] **集成测试** `tests/integration/`
  - [ ] 端到端流程测试
  - [ ] 外部依赖模拟测试
  - [ ] 性能基准测试

- [ ] **压力测试** `tests/stress/`
  - [ ] 并发处理能力测试
  - [ ] 内存泄漏测试
  - [ ] 长时间运行稳定性测试

#### 🎯 验收标准
- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试通过率 100%
- [ ] 压力测试满足性能要求

---

## 📁 文件结构规划

```
task/server/account_diagnosis/
├── core/                           # 核心组件
│   ├── __init__.py
│   ├── status_manager.py          # 统一状态管理
│   ├── config_manager.py          # 统一配置管理
│   ├── resource_manager.py        # 统一资源管理
│   ├── diagnosis_engine.py        # 诊断引擎
│   ├── search_pipeline.py         # 搜索管道
│   ├── analysis_engine.py         # 分析引擎
│   ├── report_generator.py        # 报告生成器
│   ├── template_manager.py        # 模板管理器
│   └── cache_manager.py           # 缓存管理器
├── tests/                          # 测试文件
│   ├── unit/                      # 单元测试
│   ├── integration/               # 集成测试
│   └── stress/                    # 压力测试
├── deployment/                     # 部署配置
│   ├── docker/
│   ├── k8s/
│   └── scripts/
├── docs/                          # 文档
│   ├── api/                       # API文档
│   ├── architecture/              # 架构文档
│   └── operations/                # 运维文档
├── legacy/                        # 旧版本代码（迁移期间保留）
│   ├── diagnosis_core.py
│   └── deep_research_integration.py
└── new_script_for_diagnosis.py    # 新版本入口
```

## 📋 验收标准

### 功能验收

- [ ] 所有原有功能正常工作
- [ ] 新增功能按需求实现
- [ ] API接口保持兼容
- [ ] 错误处理完善

### 性能验收

- [ ] 总体执行时间减少 > 40%
- [ ] 并发处理能力提升 > 50%
- [ ] 内存使用优化 > 20%
- [ ] 缓存命中率 > 30%

### 质量验收

- [ ] 单元测试覆盖率 > 90%
- [ ] 集成测试通过率 100%
- [ ] 代码质量评分 > 8.0
- [ ] 文档完整性 > 95%

---

## 📚 相关文档

- [系统架构文档](./diagnosis_system_architecture.md)
