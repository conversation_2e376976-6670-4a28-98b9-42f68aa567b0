# 深度搜索智能体集成文档

## 概述

本项目成功将 `lib/deepresearch/` 中的 langgraph 深度搜索智能体能力集成到了 `server/account_diagnosis/diagnosis_core.py` 中，使大模型能够按需进行搜索和多方信息汇总，提供更加深入和专业的账号诊断分析。

## 功能特点

### 🔍 深度搜索能力
- 集成 langgraph 构建的深度搜索智能体
- 支持多轮搜索和信息汇总
- 基于小红书营销目标的专业诊断

### 🎪 智能模式切换
- **深度搜索模式**: 使用 agentic search 进行深入分析
- **基础诊断模式**: 传统的大模型直接分析
- **自动回退机制**: 深度搜索不可用时自动切换到基础模式

### ⚙️ 灵活配置
- 通过参数控制是否启用深度搜索
- 支持运行时动态切换模式
- 完全向后兼容现有代码

## 架构设计

```
server/account_diagnosis/
├── diagnosis_core.py              # 主诊断模块（已修改）
├── deep_research_integration.py   # 深度搜索集成模块（新增）
└── README_DEEP_RESEARCH_INTEGRATION.md

lib/deepresearch/
├── src/agent/
│   ├── graph.py                   # langgraph 智能体图
│   ├── redbook_diagnostic_prompts.py  # 小红书诊断提示词
│   ├── redbook_tools.py           # 小红书分析工具
│   └── ...
└── examples/
    └── redbook_marketing_analysis_improved.py  # 参考实现
```

## 核心组件

### 1. DeepResearchIntegrator 类
负责深度搜索功能的集成和管理：

```python
class DeepResearchIntegrator:
    def __init__(self):
        self.agent = None
        self.memory = None
        self._initialize_agent()
    
    def is_available(self) -> bool:
        """检查深度搜索功能是否可用"""
        
    def format_account_data_for_research(self, input_data: Dict) -> Dict:
        """将账号数据格式化为适合深度搜索的结构"""
        
    def create_deep_research_prompt(self, formatted_data: Dict, marketing_goal_id: Optional[int] = None) -> str:
        """创建深度搜索提示词"""
        
    async def conduct_deep_research_analysis(self, input_data: Dict) -> Tuple[bool, str]:
        """执行深度搜索分析"""
```

### 2. 增强的诊断函数
修改后的核心函数支持深度搜索：

```python
async def conduct_diagnosis_async(input_data: Dict, use_deep_research: bool = True) -> Tuple[bool, str]:
    """异步执行诊断，支持深度搜索模式"""

async def process_task_async(input_data: Dict, enable_deep_research: bool = True) -> Tuple[bool, Dict]:
    """异步处理诊断任务，支持深度搜索配置"""
```

## 使用方法

### 基本使用

```python
from server.account_diagnosis.diagnosis_core import process_task_async

# 账号数据
account_data = {
    "accountInfo": {
        "nickname": "示例账号",
        "followers": 10000,
        # ... 其他账号信息
    },
    "noteList": [
        {
            "title": "示例笔记",
            "likes": 100,
            # ... 其他笔记信息
        }
    ],
    "industry": "Food & Beverage",
    "marketingGoal": "涨粉提升"
}

# 启用深度搜索（推荐）
status, result = await process_task_async(
    account_data, 
    enable_deep_research=True
)

if status:
    print("诊断成功！")
    print(f"诊断结果: {result['diagnosisResult']}")
    print(f"HTML报告: {result['diagnosisHtml']}")
    print(f"JSON报告: {result['diagnosisReport']}")
    print(f"销售提案: {result['salesProposal']}")
else:
    print(f"诊断失败: {result}")
```

### 禁用深度搜索

```python
# 使用基础诊断模式
status, result = await process_task_async(
    account_data, 
    enable_deep_research=False
)
```

### 直接使用深度搜索集成器

```python
from server.account_diagnosis.deep_research_integration import get_deep_research_integrator

integrator = get_deep_research_integrator()

if integrator.is_available():
    status, result = await integrator.conduct_deep_research_analysis(account_data)
else:
    print("深度搜索功能不可用")
```

## 深度搜索分析内容

当启用深度搜索时，系统会进行以下深入分析：

### 1. 行业趋势深度调研
- 当前行业在小红书平台的最新趋势
- 热门话题和关键词分析
- 内容格式趋势研究
- 季节性营销机会识别

### 2. 竞争对手深度调研
- 同行业头部账号识别和分析
- 竞品运营策略研究
- 成功案例和最佳实践
- 市场空白和蓝海机会

### 3. 营销目标专项研究
根据具体营销目标（引流私域、带货变现、品牌曝光、涨粉提升）进行针对性分析：
- 关键指标分析
- 优化策略研究
- 目标特定的建议

### 4. 用户需求与市场洞察
- 目标用户画像研究
- 用户痛点和需求分析
- 消费趋势和行为模式

## 配置要求

### API配置
深度搜索功能使用现有的 `lib/call_claude.py` 中的 gemini 函数，无需额外配置API密钥。

确保以下配置正常：
- `config.WATT_AI_GPT_TOKEN`: 现有的API令牌
- `config.WATT_AI_GPT_HOST`: API服务地址
- `config.GEMINI_FLASH_MODEL`: Gemini模型配置

### 可选配置
可以在代码中调整以下参数：
- 分析阶段数量（当前为4个阶段）
- 每个阶段的分析深度
- 使用的Gemini模型版本

## 回退机制

系统具有完善的回退机制，确保在以下情况下仍能正常工作：

1. **模块缺失**: 如果 deepresearch 相关模块不可用，自动使用基础模式
2. **API 调用失败**: 如果 gemini API 调用失败，回退到基础诊断
3. **执行异常**: 如果深度搜索过程中出现异常，自动切换到基础模式
4. **超时处理**: 设置合理的超时时间，避免长时间等待

## 性能考虑

### 深度搜索模式
- **优点**: 分析更深入、信息更全面、建议更具针对性
- **缺点**: 执行时间较长（通常 2-5 分钟）、消耗更多 API 调用

### 基础诊断模式
- **优点**: 执行速度快（通常 30 秒内）、资源消耗少
- **缺点**: 分析深度有限、主要依赖模型内置知识

## 监控和日志

系统提供详细的日志记录：

```python
import logging
logger = logging.getLogger(__name__)

# 关键日志点
logger.info("尝试使用深度搜索模式进行诊断")
logger.warning("深度搜索模块导入失败，回退到基础模式")
logger.info("深度搜索分析完成")
```

## 故障排除

### 常见问题

1. **"深度搜索功能不可用"**
   - 检查 `call_claude.py` 中的 API 配置
   - 确认 `config.WATT_AI_GPT_TOKEN` 和 `config.WATT_AI_GPT_HOST` 正确
   - 查看日志了解具体错误

2. **"无法导入deepresearch模块"**
   - 检查 `lib/deepresearch/src/agent/` 目录是否存在
   - 确认相关的 `.py` 文件完整
   - 查看具体的导入错误信息

3. **执行时间过长**
   - 深度搜索包含4个分析阶段，正常需要2-5分钟
   - 如需快速响应，可使用基础模式
   - 检查网络连接和API响应速度

### 调试模式

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
status, result = await process_task_async(
    account_data, 
    enable_deep_research=True
)
```

## 未来扩展

### 计划功能
1. **缓存机制**: 缓存搜索结果，提高响应速度
2. **并行处理**: 支持多个诊断任务并行执行
3. **自定义搜索**: 允许用户自定义搜索查询
4. **结果对比**: 提供深度搜索和基础模式的结果对比

### 扩展接口
```python
# 未来可能的扩展接口
async def custom_deep_research(
    input_data: Dict, 
    custom_queries: List[str] = None,
    research_depth: int = 3
) -> Tuple[bool, str]:
    """自定义深度搜索"""
    pass
```

## 总结

通过本次集成，我们成功实现了：

1. ✅ **无缝集成**: 深度搜索功能完全集成到现有诊断系统
2. ✅ **向后兼容**: 现有代码无需修改即可使用
3. ✅ **灵活配置**: 支持运行时控制功能启用
4. ✅ **稳定可靠**: 完善的回退机制确保系统稳定性
5. ✅ **专业分析**: 基于小红书营销目标的深度专业分析

这个集成方案既保持了系统的稳定性，又大大增强了分析的深度和专业性，为用户提供更有价值的账号诊断服务。