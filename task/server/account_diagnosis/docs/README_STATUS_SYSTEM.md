# 账号诊断系统状态跟踪重新梳理

## 📋 概述

本次重新梳理了账号诊断系统，添加了完整的中间状态返回机制，让用户能够实时了解任务执行进度，而不是只能等待最终结果。

## 🎯 主要改进

### 1. 状态管理系统
- **状态枚举**: 定义了10个标准状态，覆盖整个诊断流程
- **进度跟踪**: 自动计算和更新任务进度（0-100%）
- **状态历史**: 记录所有状态变化，便于问题排查
- **时间跟踪**: 记录每个状态的耗时和总耗时

### 2. 回调机制
- **Redis队列**: 支持通过Redis队列推送状态更新
- **WebSocket**: 支持实时WebSocket通信
- **自定义回调**: 支持完全自定义的回调函数
- **控制台输出**: 提供调试用的控制台输出

### 3. 错误处理
- **优雅降级**: 当深度搜索失败时自动回退到基础模式
- **重试机制**: 对临时性错误进行自动重试
- **错误状态**: 专门的错误状态和详细错误信息
- **超时处理**: 合理的超时设置和处理

### 4. 用户体验
- **实时反馈**: 用户可以看到任务的实时进度
- **中英双语**: 支持中英文状态消息
- **详细信息**: 提供时间戳、耗时、额外数据等详细信息
- **自定义消息**: 支持根据具体情况自定义状态消息

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────┐
│                    账号诊断系统                              │
├─────────────────────────────────────────────────────────────┤
│  process_task_async()  - 主要处理函数                        │
│  ├─ TaskProgressTracker - 进度跟踪器                        │
│  ├─ TaskStatus - 状态枚举                                   │
│  ├─ TaskStatusInfo - 状态信息管理                           │
│  └─ 各种回调机制                                            │
├─────────────────────────────────────────────────────────────┤
│  状态流转过程：                                              │
│  TASK_QUEUED → PLANNING → CRAWLING_ACCOUNT                  │
│  → SEARCHING → ANALYZING_INDUSTRY → PERFORMING_DIAGNOSIS     │
│  → GENERATING_REPORT → PAGE_RENDERING → TASK_COMPLETED       │
├─────────────────────────────────────────────────────────────┤
│  回调机制：                                                  │
│  ├─ Redis队列 (q:diagnosis:response)                       │
│  ├─ WebSocket实时通信                                       │
│  ├─ 自定义回调函数                                          │
│  └─ 控制台输出（调试用）                                     │
└─────────────────────────────────────────────────────────────┘
```

## 📊 状态码表

| 状态码 | 进度 | 中文描述 | 英文描述 |
|--------|------|----------|----------|
| TASK_QUEUED | 0% | 任务已创建，正在等待处理... | Task created and waiting to be processed… |
| PLANNING | 5% | 收到指令，正在规划任务路径… | Instructions received. Planning the research path… |
| CRAWLING_ACCOUNT | 15% | 正在分析账号基础信息… | Analyzing your account information… |
| SEARCHING | 25% | 正在搜索行业热点与最新动态… | Performing a deep search and mining for information… |
| ANALYZING_INDUSTRY | 35% | 正在进行行业分析，洞察市场趋势… | Analyzing the industry to identify market trends… |
| PERFORMING_DIAGNOSIS | 50% | 正在进行多维度账号比对与诊断… | Conducting a multi-dimensional diagnosis… |
| GENERATING_REPORT | 75% | 正在整合分析结果，生成您的专属报告… | Analysis complete. Generating your exclusive report… |
| PAGE_RENDERING | 85% | 报告生成完毕，正在为您加载页面… | The report is ready. Rendering the page for you… |
| TASK_COMPLETED | 100% | 诊断完成！ | Task completed! |
| TASK_FAILED | 0% | 任务执行失败 | Task execution failed |

## 🚀 快速开始

### 基本使用

```python
from diagnosis_core import process_task_async

# 基本调用
success, result = await process_task_async(
    input_data=your_data,
    enable_deep_research=True
)
```

### 使用状态回调

```python
from diagnosis_core import process_task_async, create_console_callback

# 创建控制台回调
callback = create_console_callback()

# 使用回调
success, result = await process_task_async(
    input_data=your_data,
    progress_callback=callback,
    task_id="task_123",
    user_id="user_456"
)
```

### 自定义回调

```python
async def custom_callback(status_info):
    task_info = status_info.get("taskInfo", {})
    print(f"状态: {task_info.get('aiTaskStatus')} - {task_info.get('aiTaskProgress')}%")

success, result = await process_task_async(
    input_data=your_data,
    progress_callback=custom_callback
)
```

## 📁 文件结构

```
task/server/account_diagnosis/
├── diagnosis_core.py              # 核心诊断模块（重新梳理）
├── diagnosis_usage_example.py     # 完整使用示例
├── docs/
│   └── response.md                # 状态响应文档
└── README_STATUS_SYSTEM.md        # 本文档
```

## 📋 功能清单

### ✅ 已实现功能
- [x] 完整的状态枚举系统
- [x] 进度跟踪器
- [x] 多种回调机制
- [x] 错误处理和重试
- [x] 状态历史记录
- [x] 时间统计
- [x] 中英双语消息
- [x] 自定义消息支持
- [x] 完整的使用示例
- [x] 详细的文档

### 🔄 可扩展功能
- [ ] 状态持久化到数据库
- [ ] 更多回调机制（HTTP webhook等）
- [ ] 状态可视化界面
- [ ] 性能监控和指标
- [ ] 分布式状态同步

## 🎯 使用场景

### 1. Web应用集成
```python
# 通过WebSocket实时更新前端进度条
callback = create_websocket_callback(websocket_manager, connection_id)
```

### 2. 微服务架构
```python
# 通过Redis队列在服务间传递状态
callback = create_redis_callback(redis_client, "q:diagnosis:response")
```

### 3. 批处理任务
```python
# 记录批处理任务的执行状态
async def batch_callback(status_info):
    # 保存状态到数据库
    await save_status_to_db(status_info)
```

### 4. 调试和监控
```python
# 控制台输出，便于调试
callback = create_console_callback()
```

## 🔧 配置说明

### 环境变量
- `DIAGNOSIS_ENV`: 环境标识（test/production）
- `DIAGNOSIS_TIMEOUT`: 诊断超时时间（秒）
- `DIAGNOSIS_RETRIES`: 重试次数

### 回调配置
- **Redis队列**: 队列名称默认为 `q:diagnosis:response`
- **WebSocket**: 需要提供连接ID
- **自定义回调**: 支持异步函数

## 📈 性能优化

### 1. 并发执行
- 报告生成阶段使用并发执行
- 减少总体执行时间

### 2. 智能重试
- 指数退避重试策略
- 避免资源浪费

### 3. 状态缓存
- 避免重复计算
- 提高响应速度

### 4. 优雅降级
- 深度搜索失败时自动回退
- 确保系统可用性

## 🛠️ 开发指南

### 添加新状态
1. 在 `TaskStatus` 枚举中添加新状态
2. 在 `TaskStatusInfo.STATUS_MESSAGES` 中添加消息
3. 在 `status_progress_map` 中设置进度百分比
4. 在相应的处理函数中调用 `update_status()`

### 添加新回调机制
1. 创建回调函数工厂（如 `create_xxx_callback`）
2. 实现异步回调函数
3. 在使用示例中添加演示
4. 更新文档

### 错误处理
- 使用 `try-except` 包围可能出错的代码
- 在异常处理中调用 `update_status(TaskStatus.TASK_FAILED)`
- 提供详细的错误信息

## 📞 技术支持

如果您在使用过程中遇到问题，请：
1. 查看完整的使用示例 (`diagnosis_usage_example.py`)
2. 参考状态响应文档 (`docs/response.md`)
3. 检查日志输出和错误信息
4. 联系技术支持团队

## 🔄 版本历史

### v2.0.0 (当前版本)
- 完全重新梳理状态系统
- 添加完整的回调机制
- 支持中间状态返回
- 优化错误处理
- 添加详细文档和示例

### v1.0.0 (原版本)
- 基本的诊断功能
- 只返回最终结果
- 简单的错误处理

---

**注意**: 本次重新梳理是为了提供更好的用户体验和更强的可扩展性。所有原有的功能都得到保留，同时增加了大量新功能。 