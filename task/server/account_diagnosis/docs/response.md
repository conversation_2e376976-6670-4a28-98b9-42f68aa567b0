# 账号诊断系统状态响应文档

## 概述

账号诊断系统支持实时状态更新，通过回调机制向用户返回任务执行的中间状态。系统支持多种回调方式：Redis队列、WebSocket、自定义回调函数等。

## 状态响应格式 1

```json
{
  "taskInfo": {
    "env": "production",
    "taskId": "1932994656262557696",
    "userId": "5ec7811000000000010044fe",
    "diagnosisId": 9,
    "aiTaskStatus": "RUNNING",
    "aiTaskMsg": "Instructions received. Planning the research path…",
    "aiTaskMsgCN": "收到指令，正在规划任务路径…",
    "aiTaskProgress": 20
  }
}
```

## 状态响应格式 2
```json
{
  "taskInfo": {
    "env": "test",
    "taskId": 1932994656262557696,
    "userId": "5ec7811000000000010044fe",
    "diagnosisId": 9,
    "aiTaskStatus": "FINISH",
    "aiTaskMsg": "Task completed!",
    "aiTaskMsgCN": "诊断完成!",
    "aiTaskProgress": 100 
  },
  "diagnosisHtml": "<!DOCTYPE html> <html lang=\"zh\"> <head>   <meta charset=\"UTF-8\" />   <title>小红书账号诊断报告</title> </head> <body>   <h1>小红书账号诊断报告</h1>   <div>     <h2>1. 基础表现</h2>     <p>账号近30天平均点赞在 <strong>350</strong> 左右，收藏偏低，说明内容有浏览但未形成强吸引力。整体封面风格较统一，建议保持。</p>   </div> </body> </html>", // HTML格式的报告
  "diagnosisReport": { 
    "summary": "林旻旻以中等身材女性休闲穿搭博主定位清晰，但互动率偏低，缺乏有效的私域引流策略。账号具有明确的差异化特点，传递\"不被定义\"的穿衣理念，拥有较强商业化潜力。",
    "tags": [
      {
        "dimension": "CURRENT STATUS",
        "status": "定位模糊"
      },
      {
        "dimension": "GROWTH POTENTIAL",
        "status": "高"
      },
      {
        "dimension": "FOCUS NEEDED",
        "status": "内容聚焦"
      }
    ],
    "bottleneck": {
      "title": "账号瓶颈",
      "area": [
        {
          "title": "内容质量不稳定",
          "title_en": "INCONSISTENT QUALITY",
          "des": "部分笔记内容空洞"
        },
        {
          "title": "标签使用不精准",
          "title_en": "IMPRECISE TAGGING",
          "des": "标签使用较为随意"
        },
        {
          "title": "个人IP不突出",
          "title_en": "WEEK PERSONAL BRANDING",
          "des": "账号运营者个人特色不突出"
        }
      ]
    },
    "content_analysis": {
      "title": "内容同质化",
      "des": "大量笔记以\"交朋友\"为主题，内容…",
      "title_en": "CONTENT…"
    },
    "ip_analysis": {
      "title": "定位模糊",
      "des": "账号包含旅行…",
      "title_en": "UNFOCUS…"
    },
    "optimize_dimension": {
      "title": "可优化纬度",
      "areas": [
        {
          "name": "定位",
          "question": "String"
        },
        {
          "name": "标题",
          "question": "String"
        }
      ]
    },
    "suggestion": [
      {
        "title": "标签策略优化",
        "content": [
          "围绕主题，选择精准标签",
          "使用竞争度较低的长尾标签",
          "热门标签与垂直领域标签组合使用",
          "定期更新标签策略"
        ]
      },
      {
        "title": "个人IP打造",
        "content": [
          "塑造鲜明、独特的个人IP形象",
          "在内容中展现真实的个性和情感",
          "优化头像昵称，增强记忆点",
          "持续输出与人设定位相符的内容"
        ]
      },
      {
        "title": "高效互动策略",
        "content": [
          "及时回复：xxxxx",
          "个性化回复：xxxxxxxxx",
          "追问式回复：xxxxxxxxx"
        ]
      }
    ]
  },
  "marketingProposal": "# 营销建议\n\n## 1. 内容策略\n- 聚焦优质内容创作\n- 保持稳定更新频率\n- 建立个人特色标签\n\n## 2. 引流方案\n- 设置个人微信号\n- 添加私域社群信息\n- 开展互动活动\n\n## 3. 变现途径\n- 开设付费课程\n- 接商业广告合作\n- 发展个人IP周边",
  "marketingProposalHtml": "<html>...</html>"
}
```

## 字段说明

| 字段名 | 类型 | 描述 | 必填 |
|--------|------|------|------|
| env | string | 环境标识（test/production） | 是 |
| taskId | string | 任务唯一标识符 | 是 |
| userId | string | 用户ID | 是 |
| diagnosisId | integer | 诊断ID | 是 |
| aiTaskStatus | string | 任务状态码（见下方状态码表） | 是 |
| aiTaskMsg | string | 英文状态消息 | 是 |
| aiTaskMsgCN | string | 中文状态消息 | 是 |
| aiTaskProgress | integer | 任务进度（0-100） | 是 |
| timestamp | string | 时间戳（ISO格式） | 是 |
| elapsed_time | number | 已耗时（秒） | 是 |
| total_time | number | 总耗时（仅完成时） | 否 |

## 状态码表

| aiTaskStatus | aiTaskProgress | aiTaskMsgCN | aiTaskMsg | 描述 |
| ------------ | -------------- | ----------- | --------- | ---- |
| RUNNING | 1 | 任务已创建，正在等待处理... | Task created and waiting to be processed… | 任务已创建，等待执行 |
| RUNNING | 5 | 收到指令，正在规划任务路径… | Instructions received. Planning the research path… | 任务规划阶段 |
| RUNNING | 15 | 正在分析账号基础信息… | Analyzing your account information… | 账号信息分析阶段 |
| RUNNING | 30 | 正在搜索行业热点与最新动态… | Performing a deep search and mining for information… | 深度搜索阶段 |
| RUNNING | 50 | 正在进行行业分析，洞察市场趋势… | Analyzing the industry to identify market trends… | 行业分析阶段 |
| RUNNING | 70 | 正在进行多维度账号比对与诊断… | Conducting a multi-dimensional diagnosis… | 账号诊断阶段 |
| RUNNING | 85 | 正在整合分析结果，生成您的专属报告… | Analysis complete. Generating your exclusive report… | 报告生成阶段 |
| RUNNING | 95 | 报告生成完毕，正在为您加载页面… | The report is ready. Rendering the page for you… | 页面渲染阶段 |
| FINISH | 100 | 诊断完成！ | Task completed! | 任务成功完成 |
| FAILED | 1 | 任务执行失败 | Task execution failed | 任务执行失败 |


## 状态流转图

```
TASK_QUEUED (1%)  RUNNING
    ↓
PLANNING (5%)  RUNNING
    ↓
CRAWLING_ACCOUNT (15%)  RUNNING
    ↓
SEARCHING (25%)    RUNNING
    ↓
ANALYZING_INDUSTRY (35%) RUNNING
    ↓
PERFORMING_DIAGNOSIS (50%)  RUNNING
    ↓
GENERATING_REPORT (75%) RUNNING
    ↓
PAGE_RENDERING (85%)    RUNNING
    ↓
TASK_COMPLETED (100%)   FINISH
```

## 错误处理

当任务执行失败时，系统会发送 `TASK_FAILED` 状态，包含错误信息：

```json
{
  "taskInfo": {
    "env": "production",
    "taskId": "1932994656262557696",
    "userId": "5ec7811000000000010044fe",
    "diagnosisId": 9,
    "aiTaskStatus": "FAILED",
    "aiTaskMsg": "Task execution failed",
    "aiTaskMsgCN": "缺少必需字段: noteList",
    "aiTaskProgress": 1
  }
}
```