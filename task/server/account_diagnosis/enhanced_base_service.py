"""
Enhanced Base Async Service
"""

import asyncio
import time
import logging
from typing import Dict, Tu<PERSON>, Any, Callable, Optional
from colorama import Fore, Style, init
try:
    from .base_async_service import BaseAsyncService
    from .enhanced_error_handling import (
        <PERSON>hanced<PERSON><PERSON>r<PERSON><PERSON><PERSON>, 
        CircuitBreakerConfig,
        Error<PERSON>ategory,
        <PERSON>rrorSeverity
    )
except ImportError:
    from base_async_service import BaseAsyncService
    from enhanced_error_handling import (
        EnhancedErrorHandler, 
        CircuitBreakerConfig,
        ErrorCategory,
        ErrorSeverity
    )

# Initialize colorama
init(autoreset=True)

class EnhancedBaseAsyncService(BaseAsyncService):
    """Enhanced Base Async Service with robust error handling"""
    
    def __init__(self, service_name: str, input_queue: str, output_queue: str,
                 max_concurrent_tasks: int = 3, run_mode: str = "daemon", timeout: int = 300,
                 use_service_lock: bool = True, redis_connection_timeout: int = 30, 
                 redis_socket_timeout: int = 30):
        
        # Initialize parent
        super().__init__(
            service_name, input_queue, output_queue,
            max_concurrent_tasks, run_mode, timeout,
            use_service_lock, redis_connection_timeout, redis_socket_timeout
        )
        
        # Initialize enhanced error handler
        self.error_handler = EnhancedErrorHandler(self.redis_manager)
        
        # Add service-specific circuit breakers
        self._setup_circuit_breakers()
        
        # Enhanced logging setup
        self._setup_enhanced_logging()
        
        # Performance tracking
        self.operation_metrics = {}
        
        self.logger.info(f"{Fore.GREEN}🚀 Enhanced {self.service_name} service initialized with robust error handling{Style.RESET_ALL}")
    
    def _setup_circuit_breakers(self):
        """Setup service-specific circuit breakers"""
        self.logger.info(f"{Fore.CYAN}🔧 Setting up circuit breakers for {self.service_name}{Style.RESET_ALL}")
        
        # Redis operations circuit breaker with increased timeout and more lenient settings
        redis_config = CircuitBreakerConfig(
            failure_threshold=8,  # Increased from 5 to 8 - more tolerant of failures
            recovery_timeout=30,  # Reduced from 60 to 30 - faster recovery
            timeout=60.0          # Increased from 30.0 to 60.0 - longer timeout for Redis operations
        )
        self.error_handler.add_circuit_breaker("redis_ops", redis_config)
        
        # Task processing circuit breaker
        task_config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=45,
            timeout=300.0  # 5 minute timeout for task processing
        )
        self.error_handler.add_circuit_breaker("task_processing", task_config)
        
        self.logger.info(f"{Fore.GREEN}✅ Circuit breakers configured successfully{Style.RESET_ALL}")
    
    def _setup_enhanced_logging(self):
        """Setup enhanced logging with colors"""
        # Create a custom formatter with colors
        class ColoredFormatter(logging.Formatter):
            def __init__(self, fmt=None, datefmt=None, service_name=None):
                super().__init__(fmt, datefmt)
                self.service_name = service_name
            
            def format(self, record):
                # Color mapping for log levels
                level_colors = {
                    'DEBUG': Fore.BLUE,
                    'INFO': Fore.GREEN,
                    'WARNING': Fore.YELLOW,
                    'ERROR': Fore.RED,
                    'CRITICAL': Fore.MAGENTA
                }
                
                level_color = level_colors.get(record.levelname, Fore.WHITE)
                
                # Format the message
                formatted = super().format(record)
                
                # Add service name prefix with color
                service_name = self.service_name or "UNKNOWN"
                service_prefix = f"{Fore.CYAN}[{service_name.upper()}]{Style.RESET_ALL}"
                
                return f"{service_prefix} {formatted}"
        
        # Apply the colored formatter to all handlers
        formatter = ColoredFormatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            service_name=self.service_name
        )
        
        for handler in self.logger.handlers:
            handler.setFormatter(formatter)

    async def enhanced_task_processing(self, task_func: Callable, input_data: Dict):
        """Enhanced task processing with comprehensive error handling"""
        try:
            result = await self.error_handler.handle_with_circuit_breaker(
                task_func,
                "task_processing",
                f"task_{task_func.__name__}",
                {},
                input_data
            )
            return result
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Task processing failed: {str(e)}{Style.RESET_ALL}")
            raise

    async def enhanced_redis_operation(self, operation_name: str, func: Callable, *args, **kwargs):
        """Enhanced Redis operation with circuit breaker and metrics"""
        start_time = time.time()
        
        try:
            self.log_operation_start(f"Redis {operation_name}", {"args_count": len(args), "kwargs_count": len(kwargs)})
            
            result = await self.error_handler.handle_with_circuit_breaker(
                func,
                "redis_ops",
                f"redis_{operation_name}",
                {
                    "service": self.service_name, 
                    "operation": operation_name
                },
                *args, **kwargs
            )
            
            duration = time.time() - start_time
            self.log_operation_success(f"Redis {operation_name}", duration)
            self._record_operation_metric(f"redis_{operation_name}", duration, True)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.log_operation_error(f"Redis {operation_name}", str(e))
            self._record_operation_metric(f"redis_{operation_name}", duration, False)
            raise

    def _record_operation_metric(self, operation: str, duration: float, success: bool):
        """Record operation metrics for monitoring"""
        if operation not in self.operation_metrics:
            self.operation_metrics[operation] = {
                "total_calls": 0,
                "successful_calls": 0,
                "failed_calls": 0,
                "total_duration": 0.0,
                "avg_duration": 0.0
            }
        
        metrics = self.operation_metrics[operation]
        metrics["total_calls"] += 1
        metrics["total_duration"] += duration
        
        if success:
            metrics["successful_calls"] += 1
        else:
            metrics["failed_calls"] += 1
        
        metrics["avg_duration"] = metrics["total_duration"] / metrics["total_calls"]

    async def get_enhanced_service_health(self) -> Dict[str, Any]:
        """Get comprehensive service health status"""
        try:
            # Get error handler health
            error_handler_health = self.error_handler.get_system_health()
            
            # Get operation metrics
            operation_summary = {}
            for op, metrics in self.operation_metrics.items():
                success_rate = metrics["successful_calls"] / metrics["total_calls"] if metrics["total_calls"] > 0 else 0
                operation_summary[op] = {
                    "success_rate": success_rate,
                    "avg_duration": metrics["avg_duration"],
                    "total_calls": metrics["total_calls"]
                }
            
            health_status = {
                "service_name": self.service_name,
                "error_handling": error_handler_health,
                "operation_metrics": operation_summary,
                "timestamp": error_handler_health["timestamp"]
            }
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"{Fore.RED}❌ Failed to get service health: {str(e)}{Style.RESET_ALL}")
            return {
                "service_name": self.service_name,
                "error": str(e),
                "timestamp": time.time()
            }

    def log_operation_start(self, operation: str, context: Optional[Dict] = None):
        """Log operation start with color and context"""
        context_str = ""
        if context:
            context_str = " | ".join(f"{k}={v}" for k, v in context.items())
        self.logger.info(f"{Fore.GREEN}🚀 Starting {operation} {context_str}{Style.RESET_ALL}")
    
    def log_operation_success(self, operation: str, duration: Optional[float] = None):
        """Log operation success with color and timing"""
        duration_str = f" | ⏱️  {duration:.2f}s" if duration else ""
        self.logger.info(f"{Fore.GREEN}✅ Completed {operation}{duration_str}{Style.RESET_ALL}")
    
    def log_operation_warning(self, operation: str, message: str):
        """Log operation warning with color"""
        self.logger.warning(f"{Fore.YELLOW}⚠️  {operation}: {message}{Style.RESET_ALL}")

    def log_operation_error(self, operation: str, error: str):
        """Log operation error with color"""
        self.logger.error(f"{Fore.RED}❌ {operation} failed: {error}{Style.RESET_ALL}")

    async def reset_circuit_breakers(self):
        """Reset all circuit breakers"""
        self.logger.warning(f"{Fore.YELLOW}🔄 Resetting all circuit breakers for {self.service_name}{Style.RESET_ALL}")
        
        for name in self.error_handler.circuit_breakers.keys():
            await self.error_handler.reset_circuit_breaker(name)
        
        self.logger.info(f"{Fore.GREEN}✅ All circuit breakers reset successfully{Style.RESET_ALL}")

    async def get_operation_metrics(self) -> Dict[str, Any]:
        """Get detailed operation metrics"""
        return {
            "service_name": self.service_name,
            "metrics": self.operation_metrics.copy(),
            "timestamp": time.time()
        }

    def log_performance_metric(self, metric_name: str, value: float, unit: str = ""):
        """Log performance metric with color"""
        unit_str = f" {unit}" if unit else ""
        self.logger.info(f"{Fore.BLUE}📊 {metric_name}: {value:.2f}{unit_str}{Style.RESET_ALL}")
