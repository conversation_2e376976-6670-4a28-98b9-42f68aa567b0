from task.dao.mongo.mongo_db import MongoDB
from task.lib.multimodal import MediaExtractor
from bson import ObjectId

origin_collection = MongoDB(collection="media_s3")
save_collection = MongoDB(collection="media_with_tag")

def is_profile_image(url: str) -> bool:
    path_segments = url.split('/')
    return 'profile_images' in path_segments


def edit_video_tags(batch_size=100, init_id=None):
    last_id = init_id  # 用于存储上一批次的最后一个 _id
    fetched_count = 0  # 已获取的文档总数

    while True:
        # 构造查询条件（大于 last_id）
        query = {"_id": {"$lt": last_id}} if last_id else {}
        print(query)

        # 从 MongoDB 查询数据
        cursor = save_collection.find_data(query=query, limit=batch_size)

        # 转换为列表并获取数量
        batch = list(cursor)
        batch_len = len(batch)

        if batch_len == 0:
            print("No more data to fetch.")
            break
        # 处理数据（这里只是打印数量，你可以替换为实际处理逻辑）
        print(f"Fetched {batch_len} documents (total: {fetched_count + batch_len})")

        for i in batch:
            if is_profile_image(i['url']):
                print(i['_id'])
                save_collection.delete_data(i['_id'])
            # media_type = i['media_type']
            # if media_type == 'video':
            #     if type(i['tags']) is list:
            #         print(i)
            #         tags = i['tags'][1]
            #         i['tags'] = tags
            #         save_collection.update_one(i)

        # 更新最后一个 _id 和总计数
        last_id = batch[-1]["_id"]
        fetched_count += batch_len



def fetch_data(batch_size=10, total_limit=100000, init_id=ObjectId('67885910b59c8ef1feb27bbc')):
    """
    分批次从 MongoDB 获取数据，按 _id 排序。

    :param batch_size: 每次获取的文档数量
    :param total_limit: 最多获取的文档总数
    :param init_id: 初始 _id（不包含），用于分批次获取数据
    """
    last_id = init_id # 用于存储上一批次的最后一个 _id
    fetched_count = 0  # 已获取的文档总数

    while fetched_count < total_limit:
        # 构造查询条件（大于 last_id）
        query = {"_id": {"$lt": last_id}} if last_id else {}
        print(query)

        # 从 MongoDB 查询数据
        cursor = origin_collection.find_data(query=query, limit=batch_size)

        # 转换为列表并获取数量
        batch = list(cursor)
        batch_len = len(batch)

        if batch_len == 0:
            print("No more data to fetch.")
            break
        # 处理数据（这里只是打印数量，你可以替换为实际处理逻辑）
        print(f"Fetched {batch_len} documents (total: {fetched_count + batch_len})")

        # 更新最后一个 _id 和总计数
        last_id = batch[-1]["_id"]
        print(type(last_id))
        fetched_count += batch_len

        # 如果需要处理数据，放在这里
        # multi_process(batch)

        # 如果到达总数限制，提前退出
        if fetched_count >= total_limit:
            break


def get_tags_for_media(data):
    media_model = MediaExtractor()
    if data['media_type'] == 'photo':
        tags = media_model.img_extract_keywords_key(data)
    elif data['media_type'] == 'video':
        tags = media_model.video_extract_keywords(data)
    else:
        return False, None
    data['tags'] = tags
    return True, data


def multi_process(batch_data):
    for i in batch_data:
        status, data = get_tags_for_media(i)
        if status:
            save_collection.insert_one(data)


if __name__ == "__main__":
    edit_video_tags()
    # fetch_data()
    """{'_id': ObjectId('6788d102b59c8ef1feb2893f'), 'url_md5': '1d3b994b94fcf211d129cd49810a15e9', 'url': 'https://pbs.twimg.com/media/GhPXvEEboAAjzKq.png', 's3_key': 'c2fb1c53e70e3831ab0662aba3748a0c/0d76f9a888871400825c8fbf66530c63', 'media_type': 'photo', 'created_at': 1737019650.5569673, 'server_id': '7131cf87-feab-5444-b02a-d268629b5f73', 'create_day': '2025-01-16', 'raw_file_md5': '674d49fa5904ebd8d1a32077eba7f6bc', 'metadata': {'size': {'width': 543, 'height': 900}, 'file_size_raw': 0.9383163452148438, 'file_size': '960.84 KB'}, 'time_spent': {'check_complete': 0.004138946533203125, 'update_db': 0.006373405456542969, 'download': 0.8505713939666748, 'check_file_md5': 0.002312898635864258, 'process': 0.019597530364990234, 'upload': 0.23265719413757324}, 'time_spent_total': 1}"""