import os
import sys
import argparse
import json
from bson import ObjectId
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey

monkey.patch_all()
from task import TaskLog
from task.dao.mongo.mongdb_writer import MongoDBWriter
from task.lib.multimodal import MediaExtractor
import time
from colorama import Fore

gen_media_db = MongoDBWriter(collection="media")


def read_media_files(batch_mode: bool = False):
    status, files_to_be_extracted = gen_media_db.batchSearch("is_associated", False, limit=0)
    # status, files_to_be_extracted = gen_media_db.duoBatchSearch("is_associated", [False],
    #                                                             "type", ["photo"], limit=1)
    if not status:
        TaskLog.error("Error: ", files_to_be_extracted)
        return False, 99999, str(files_to_be_extracted)
    else:
        print(Fore.YELLOW + f"number of existing tweets: {len(files_to_be_extracted)}" + Fore.RESET)

    # for file in files_to_be_extracted:
    #     file_id = ObjectId(file.get("_id", ""))
    #     print(Fore.YELLOW + f"File ID: {file_id}" + Fore.RESET)
    #     source_url = file.get("source_url", "")
    #     # source_s3_key = file.get("resource", "")
    #
    #     keywords = ["McDonald's", 'happy meal', 'toy']
    #
    #     if keywords:
    #         print(Fore.YELLOW + f"keywords: {keywords}" + Fore.RESET)
    #         # status, result = gen_media_db.updateSingleData([{"_id": file_id}, {"$set": {"ai_keywords": keywords,
    #         #                                                                             "is_associated": True}}])
    #         file["ai_keywords"] = keywords
    #         file["is_associated"] = True
    #         # status, result = gen_media_db.update_one(file)
    #         status, result = gen_media_db.update_one({"_id": file_id, "ai_keywords": keywords, "is_associated": True})
    #         if not status:
    #             TaskLog.error("Error: ", result)
    #             return False, 99999, str(result)
    #     else:
    #         TaskLog.error("Error: ", "Keywords not found")
    #         return False, 99999, "Keywords not found"


def main(args_):
    read_media_files(args_.batch_mode)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Extract keywords from Media DB. -b False")

    parser.add_argument('-b', "--batch_mode", type=bool, default=False, help="Batch mode")

    args = parser.parse_args()
    main(args)
