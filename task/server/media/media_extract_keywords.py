import os
import sys
import argparse
from bson import ObjectId

pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey

monkey.patch_all()
import config
import requests
from task import TaskLog
from task.dao.mongo.mongdb_writer import MongoDBWriter
from task.lib.multimodal import MediaExtractor
import time
from datetime import timedelta, datetime, timezone
from colorama import Fore

gen_media_db = MongoDBWriter(collection="tweet")
rec_host = config.WATT_REC_HOST
datahub_report_url = "/api/v1/dataHub/report"
app_name = "task-media-extractor"
timestamp_format = "%Y-%m-%dT%H:%M:%SZ"
query_ = {
    "ai_media_status": {"$exists": False},
    "upload_time": {"$exists": True},
    "$or": [
        {
            "s3_media_resource.photo": {
                "$exists": True,
                "$type": "array",
                "$not": {"$size": 0}
            }
        },
        {
            "s3_media_resource.video": {
                "$exists": True,
                "$type": "array",
                "$not": {"$size": 0}
            }
        }
    ]
}


def get_last_scan_time(minutes=15):
    status, last_scan = gen_media_db.singleSearch('_id', 'last_scan_time')
    if last_scan:
        last_scan_timestamp = datetime.strptime(last_scan['timestamp'], timestamp_format)
        time_duration = last_scan_timestamp - timedelta(minutes=minutes)
        time_duration = last_scan_timestamp - timedelta(minutes=minutes)
        return datetime.strftime(time_duration, timestamp_format)
    else:
        # If no previous scan, use a default (e.g., 1 hour ago)
        time_duration = datetime.now(timezone.utc) - timedelta(minutes=minutes)
        time_duration = datetime.now(timezone.utc) - timedelta(minutes=minutes)
        return datetime.strftime(time_duration, timestamp_format)


def update_last_scan_time(scan_time):
    # Update the last scan time in the separate collection or file
    data = {
        '_id': 'last_scan_time',
        'timestamp': str(scan_time)
    }
    gen_media_db.update_one(data)


def report_trace_id(current_utc_time, trace_id: str, codes: str, msg: str, level: str, batch_id: int):
    batch_id_str = trace_id + "_" + app_name + "_" + f"{batch_id:05d}"
    body_report = {
        "traceId": trace_id,
        "appName": app_name,
        "appBatchId": batch_id_str,
        "logLevel": level,
        "code": codes,
        "msg": msg,
        "timestamp": current_utc_time
    }
    try:
        response_ = requests.post(f"{rec_host}{datahub_report_url}", json=[body_report], timeout=15)
        response_.raise_for_status()
    except Exception as e:
        TaskLog.error(f"Error retrieve datahub report results: {e}")
        return False, e
    response = response_.json()
    print(Fore.YELLOW + f"Datahub report response: {response}" + Fore.RESET)
    return True, response


def change_media_status():
    status, tweets_target = gen_media_db.findData(limit=0)
    if not status:
        TaskLog.error("Error: ", tweets_target)
        return False, 99999, str(tweets_target)
    else:
        print(Fore.YELLOW + f"number of existing tweets: {len(tweets_target)}" + Fore.RESET)

    for tweet_target in tweets_target:
        media_status = tweet_target.get("ai_media_status", None)
        if not media_status:
            print(Fore.RED + "Error: ai_media_status not found" + Fore.RESET)
            continue
        if media_status == 1:
            media_status = "1"
        elif media_status == 2:
            media_status = "2"
        elif media_status == 3:
            media_status = "3"
        file_id = ObjectId(tweet_target.get("_id", ""))
        status, result = gen_media_db.update_one({"_id": file_id, "ai_media_status": media_status})
        if not status:
            TaskLog.error("Error to insert into DB: ", result)


def read_media_files(media_model, limit: int = 100, time_window: int = 30, reversed_: bool = False):
    current_utc_time = datetime.now(timezone.utc)
    formatted_time = current_utc_time.strftime(timestamp_format)
    if time_window:
        # last_scan_time = get_last_scan_time(time_window)
        last_scan_time = datetime.strftime(current_utc_time - timedelta(minutes=time_window), timestamp_format)
        print(Fore.YELLOW + f"Last scan time: {last_scan_time}" + Fore.RESET)
        print(Fore.YELLOW + f"Current time: {formatted_time}" + Fore.RESET)
        status, tweets_target = gen_media_db.findDataCondition(query_, limit, last_scan_time, formatted_time, reversed_)
        if not status:
            TaskLog.error("Error: ", tweets_target)
            return False, 99999, str(tweets_target)
        print(Fore.YELLOW + f"number of existing tweets: {len(tweets_target)}" + Fore.RESET)
        if len(tweets_target) < 60:
            # last_scan_time = get_last_scan_time(time_window)
            last_scan_time = datetime.strftime(current_utc_time - timedelta(minutes=time_window + 10), timestamp_format)
            status, tweets_target = gen_media_db.findDataCondition(query_, limit, last_scan_time,
                                                                   formatted_time, reversed_)
            if not status:
                TaskLog.error("Error: ", tweets_target)
                return False, 99999, str(tweets_target)
            print(Fore.RED + f"number of existing tweets: {len(tweets_target)}" + Fore.RESET)
    else:
        # status, tweets_target = gen_media_db.findDataFilter(["s3_media_resource", "upload_time"], limit)
        # status, tweets_target = gen_media_db.findDataCondition(query_, limit, reversed_)
        status, tweets_target = gen_media_db.findDataCondition(query_, limit, reversed_)
        if not status:
            TaskLog.error("Error: ", tweets_target)
            return False, 99999, str(tweets_target)
        print(Fore.YELLOW + f"number of existing tweets: {len(tweets_target)}" + Fore.RESET)

    # batch_list = [tweets_target[i:i + batch_size] for i in range(0, len(tweets_target), batch_size)]
    for tweet_target in tweets_target:
        batch_id = 1
        video_keywords = []
        image_keywords = []
        image_skip = False
        video_skip = False
        file_id = ObjectId(tweet_target.get("_id", ""))
        # TaskLog.info(f"File ID: {file_id}")
        trace_id = tweet_target.get("trace_id", "")
        keyword_status = tweet_target.get("ai_media_status")
        if keyword_status == "3" or keyword_status == "2":
            continue
        media_ = tweet_target.get("s3_media_resource", {})
        ai_keywords = tweet_target.get("ai_media_keywords", {})
        image_json_list = media_.get("photo", [])
        if image_json_list:
            image_existing_keywords = ai_keywords.get("photo", [])
            if len(image_existing_keywords) > 0 and len(image_existing_keywords) == len(image_json_list):
                image_skip = True
                image_keywords = image_existing_keywords
            else:
                start_time = time.time()
                image_keywords, input_key_list = media_model.image_extract_keywords_from_list(image_json_list)
                if not image_keywords:
                    tweet_id_ = tweet_target.get("id_str", "")
                    TaskLog.error(f"tweetID {tweet_id_} with {trace_id} has image resource issue: {input_key_list}")
                    report_trace_id(formatted_time, trace_id, "-1", f"tweetID {tweet_id_} with {trace_id} "
                                                                    f"has image resource issue.", "ERROR", batch_id)
                    batch_id += 1
                print(Fore.YELLOW + f"Image inference time: {time.time() - start_time}" + Fore.RESET)

        video_json_list = media_.get("video", [])
        if video_json_list:
            video_existing_keywords = ai_keywords.get("video", [])
            if len(video_existing_keywords) > 0 and len(video_existing_keywords) == len(video_json_list):
                video_skip = True
                video_keywords = video_existing_keywords
            else:
                for video_key in video_json_list:
                    start_time = time.time()
                    stat_, output_ = media_model.video_extract_keywords(video_key)
                    if stat_:
                        print(f"Video keywords: {output_}")
                        video_keywords.append(output_)
                    else:
                        tweet_id_ = tweet_target.get("id_str", "")
                        TaskLog.error(f"tweetID {tweet_id_} with {trace_id} has video resource issue: {video_key}.")
                        report_trace_id(formatted_time, trace_id, "302",
                                        f"tweetID {tweet_id_} with {trace_id}"
                                        f" has video resource issue: {video_key}.",
                                        "ERROR", batch_id)
                        batch_id += 1
                    print(Fore.YELLOW + f"Video inference time: {time.time() - start_time}" + Fore.RESET)

        if not image_skip and not video_skip:
            ai_media_keywords = {"photo": image_keywords, "video": video_keywords}

            if image_keywords or video_keywords:
                status, result = gen_media_db.update_one(
                    {"_id": file_id, "ai_media_keywords": ai_media_keywords, "ai_media_status": "2"})
                if not status:
                    TaskLog.error(f"Error to insert into DB for _id: {file_id} and trace_id: {trace_id}")
                    report_trace_id(formatted_time, trace_id, "303", f"Error to insert into DB for _id: "
                                                                     f"{file_id} and trace_id: {trace_id}",
                                    "ERROR", batch_id)
                # else:
                #     report_trace_id(formatted_time, trace_id, "0", "Success", "INFO", batch_id)
                batch_id += 1
            else:
                TaskLog.error(f"Error: Keywords not created successfully for _id: {file_id} and trace_id: {trace_id}")
        elif image_skip and not video_skip:
            if video_keywords:
                status, result = gen_media_db.update_one(
                    {"_id": file_id, "ai_media_keywords.video": video_keywords, "ai_media_status": "2"})
                if not status:
                    TaskLog.error(f"Error to insert into DB for _id: {file_id} and trace_id: {trace_id}")
                    report_trace_id(formatted_time, trace_id, "303", f"Error to insert into DB for _id: "
                                                                     f"{file_id} and trace_id: {trace_id}",
                                    "ERROR", batch_id)
                # else:
                #     report_trace_id(formatted_time, trace_id, "0", "Success", batch_id)
                batch_id += 1
            else:
                TaskLog.error(f"Error: Keywords not created successfully for _id: {file_id} and trace_id: {trace_id}")
        elif not image_skip and video_skip:
            if image_keywords:
                status, result = gen_media_db.update_one(
                    {"_id": file_id, "ai_media_keywords.photo": image_keywords, "ai_media_status": "2"})
                if not status:
                    TaskLog.error(f"Error to insert into DB for _id: {file_id} and trace_id: {trace_id}")
                    report_trace_id(formatted_time, trace_id, "303", f"Error to insert into DB for _id: "
                                                                     f"{file_id} and trace_id: {trace_id}",
                                    "ERROR", batch_id)
                # else:
                #     report_trace_id(formatted_time, trace_id, "0", "Success", batch_id)
                batch_id += 1
            else:
                TaskLog.error(f"Error: Keywords not created successfully for _id: {file_id} and trace_id: {trace_id}")
    update_last_scan_time(formatted_time)


def main(args_):
    media_model = MediaExtractor()
    while True:
        print(Fore.GREEN + "Starting read_media_files" + Fore.RESET)
        try:
            read_media_files(media_model, args_.limit, args_.time_window, args_.reversed)
        except Exception as e:
            print(Fore.RED + f"Error: {e}" + Fore.RESET)
            time.sleep(5)
        print(Fore.GREEN + "Job executed successfully" + Fore.RESET)
        time.sleep(5)


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Extract keywords from Media DB. -l 0 -t True")

    parser.add_argument('-l', "--limit", type=int, default=100, help="Limit")
    parser.add_argument('-w', "--time_window", type=int, default=20, help="Time window (minutes)")
    parser.add_argument('-r', '--reversed', type=bool, default=False, help="Reversed")

    args = parser.parse_args()
    main(args)
