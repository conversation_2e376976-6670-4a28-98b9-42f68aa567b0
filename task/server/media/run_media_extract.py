import os
import sys
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
import time
from datetime import datetime
from task import TaskLog
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.events import EVENT_JOB_ERROR, EVENT_JOB_EXECUTED
from threading import Lock
from task.server.media.media_extract_keywords import read_media_files
from task.lib.multimodal import MediaExtractor

media_model = MediaExtractor()
job_lock = Lock()


def call_media_read_with_lock():
    if job_lock.acquire(blocking=False):
        try:
            TaskLog.info("Starting read_media_files")
            read_media_files(media_model, 0)
            TaskLog.info("Finished read_media_files")
        except Exception as e:
            TaskLog.error(f"Error in read_media_files: {str(e)}")
        finally:
            job_lock.release()
    else:
        TaskLog.warning("Previous job still running, skipping this execution")


def job_listener(event):
    if event.exception:
        TaskLog.error(f"Job failed: {event.exception}")
    else:
        TaskLog.info("Job executed successfully")


def init_scheduler():
    scheduler = BackgroundScheduler(job_defaults={
        'coalesce': True,
        'max_instances': 1,
        'misfire_grace_time': 15 * 60  # 15 minutes
    })

    scheduler.add_job(
        call_media_read_with_lock,
        'interval',
        hours=1,
        id='media_understand_job',
        next_run_time=datetime.now()
    )

    scheduler.add_listener(job_listener, EVENT_JOB_EXECUTED | EVENT_JOB_ERROR)

    scheduler.start()
    return scheduler


if __name__ == "__main__":
    scheduler = init_scheduler()

    try:
        while True:
            time.sleep(1)
    except (KeyboardInterrupt, SystemExit):
        TaskLog.info("Shutting down scheduler...")
        scheduler.shutdown()
        TaskLog.info("Scheduler shut down successfully")

