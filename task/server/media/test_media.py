import os
import sys

pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey

monkey.patch_all()
import requests
from task import TaskLog
from task.lib.multimodal import MediaExtractor
import time

def main():
    media_model = MediaExtractor()
    media_prefix = "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/"
    img_list = [media_prefix + "test1.jpeg", media_prefix + "test2.jpeg", media_prefix + "test3.jpeg", media_prefix +
                "test4.png", media_prefix + "test5.jpeg", media_prefix + "test6.jpeg", media_prefix + "test7.jpeg",
                media_prefix + "test8.jpeg", media_prefix + "test9.jpeg", media_prefix + "test10.jpeg", media_prefix +
                "test11.jpeg", media_prefix + "test12.jpeg", media_prefix + "test13.jpg", media_prefix + "test14.jpg"]

    video_list = [media_prefix + "1.mp4", media_prefix + "2.mp4", media_prefix + "3.mp4", media_prefix +
                 "4.mp4", media_prefix + "5.mp4", media_prefix + "6.mp4", media_prefix + "7.mp4",
                 media_prefix + "8.mp4", media_prefix + "9.mp4", media_prefix + "10.mp4"]
    start_time = time.time()
    # try:
    #     status, output = media_model.image_test_list(img_list)
    #     if status:
    #         print(output)
    #     else:
    #         print("Error processing image")
    # except Exception as e:
    #     print(f"Error processing image: {e}")

    for video_ in video_list:
        try:
            status, output = media_model.video_test(video_)
            if status:
                print(output)
            else:
                print("Error processing video")
        except Exception as e:
            print(f"Error processing video: {e}")
    print(f"Time elapsed: {time.time() - start_time}")


if __name__ == '__main__':
    main()
