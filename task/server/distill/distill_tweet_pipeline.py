import os
import argparse
import time
import argparse
import time
import pymongo
import sys
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey
monkey.patch_all()
import config
import requests
import config
import requests
from typing import List, Dict, Any
from datetime import datetime, timedelta, timezone
from task import TaskLog
from task.dao.mongo.mongo_db import MongoDB
from task.server.create.tagging_tweets import categorize_tweet
from task.server.media.media_extract_keywords import timestamp_format
from colorama import Fore

source_topic_db = MongoDB(collection="ai_tweet_topics")
ai_tweet_db = MongoDB(collection="test_ai_tweets")
rec_host = config.WATT_REC_HOST
datahub_report_url = "/api/v1/dataHub/report"
app_name = "task-tweet-content-category"
timestamp_format = "%Y-%m-%dT%H:%M:%SZ"
pipe = [
    {
        "$match": {"upload_time": {"$exists": True}}
    },
    {
        "$sort": {"upload_time": -1}
    },
    {
        "$group": {
            "_id": "$id_str",
            "latestDocument": {"$first": "$$ROOT"}
        }
    },
    {
        "$replaceRoot": {"newRoot": "$latestDocument"}
    }
]


def report_trace_id(current_utc_time, trace_id: str, codes: str, msg: str, level: str, batch_id: int):
    batch_id_str = trace_id + "_" + app_name + "_" + f"{batch_id:05d}"
    body_report = {
        "traceId": trace_id,
        "appName": app_name,
        "appBatchId": batch_id_str,
        "logLevel": level,
        "code": codes,
        "msg": msg,
        "timestamp": current_utc_time
    }
    try:
        response_ = requests.post(f"{rec_host}{datahub_report_url}", json=[body_report], timeout=10)
        response_.raise_for_status()
    except Exception as e:
        TaskLog.error(f"Error retrieve datahub report results: {e}")
        return False, e
    response = response_.json()
    print(Fore.YELLOW + f"Datahub report response: {response}" + Fore.RESET)
    return True, response


def get_data_type(di: dict):
    """ 1. 低粉高赞：低于2w粉，当前帖子高于500赞
        2. 高赞：高于5w赞
        3. 高浏览：高于10w
        4. 高转发：高于10w
        5. 高言论：高于1w
    """
    types = []
    if di.get('favorite_count', 0) > 500:
        if di['user'].get('followers_count', 0) < 20000:
            types.append(1)
    if di.get('favorite_count', 0) > 50000:
        types.append(2)
    if int(di['views'].get('count', 0)) > 100000:
        types.append(3)
    if di.get('retweet_count', 0) > 100000:
        types.append(4)
    if di.get('reply_count', 0) > 10000:
        types.append(5)
    return types


def get_tweet_data(minutes: int = 10, batch_size: int = 30, reverse=True) -> bool:
    current_time = datetime.now(timezone.utc)
    formatted_time = current_time.strftime(timestamp_format)
    last_minutes = current_time - timedelta(minutes=minutes)
    last_minutes_str = last_minutes.strftime("%Y-%m-%d %H:%M:%S")
    query_topic_time = {"created_at": {"$gte": last_minutes_str}, "active": 1}
    projection = {"tweets_info": 1, "name": 1, "trace_id": 1}
    topic_results = source_topic_db.find_data_condition(query_topic_time, projection=projection)

    # 汇总所有需要处理的 tweet_id 列表
    all_tweet_ids = []
    all_tweets = []
    def parse_datetime(date_string):
        return datetime.strptime(date_string, "%Y-%m-%dT%H:%M:%SZ").replace(tzinfo=timezone.utc)

    for topic_ in topic_results:
        topic_id = str(topic_['_id'])
        tweet_info_list = topic_.get('tweets_info', [])
        topic_name = topic_.get('name')
        trace_id = topic_.get('trace_id')
        for tweet_info in tweet_info_list:
            tweet_info.update({
                'topic_id': topic_id,
                'topic_name': topic_name,
                'trace_id': trace_id,
                'created_at': parse_datetime(tweet_info.get('created_at'))
            })
            all_tweets.append(tweet_info)
            all_tweet_ids.append(tweet_info['tweet_id'])
    # 针对当前批次的 tweet_id 获取已存在的 tweets，而不是全表扫描
    existing_tweets = {
        doc['tweet_id']: doc.get('data_type', [])
        for doc in ai_tweet_db.get_all_fields({'tweet_id': {'$in': all_tweet_ids}}, {'tweet_id': 1, 'data_type': 1})
    }
    print(Fore.GREEN + f"total topics in last {minutes} minutes: {len(topic_results)}" + Fore.RESET)

    # Deduplicate tweets, keeping only the most recent for each unique tweet_id
    unique_tweets = {}
    for tweet_info in all_tweets:
        tweet_id = tweet_info.get('tweet_id')
        if tweet_id not in unique_tweets or tweet_info['created_at'] > unique_tweets[tweet_id]['created_at']:
            unique_tweets[tweet_id] = tweet_info

    # Convert back to list and sort by created_at in descending order
    deduplicated_tweets = sorted(unique_tweets.values(), key=lambda x: x['created_at'], reverse=reverse)
    print(Fore.YELLOW + f"Total deduplicated tweets: {len(deduplicated_tweets)}" + Fore.RESET)
    report_id = 0

    def process_batch(batch: List[Dict[str, Any]], report_id_: int) -> None:
        new_batch = []
        update_batch = []

        for tweet in batch:
            tweet_id = tweet['tweet_id']
            if tweet_id in existing_tweets:
                new_data_type = tweet.get('data_type', [])
                if new_data_type != existing_tweets[tweet_id]:
                    update_batch.append({
                        "tweet_id": tweet_id,
                        "data_type": new_data_type,
                    })
            else:
                new_batch.append(tweet)

        if new_batch:
            tweet_texts = [tweet['full_text'] for tweet in new_batch]
            status_, result_ = categorize_tweet(tweet_texts)
            if not status_:
                TaskLog.error(f"Categorize error: {result_}")
                report_trace_id(formatted_time, trace_id, "305", f"tweetID with {trace_id}"
                                                                 f" link GPT error!", "CRITICAL", report_id_)
                report_id_ += 1
                return False, report_id_
            print(result_)
            try:
                tags = eval(result_)
            except Exception as e:
                result_ += "]"
                try:
                    tags = eval(result_)
                except Exception as e:
                    TaskLog.error(f"Eval error: {e}")
                    report_trace_id(formatted_time, trace_id, "306", f"tweetID with {trace_id}"
                                                                 f" eval error!", "WARN", report_id_)
                    report_id_ += 1
                    return False, report_id_
            if isinstance(tags, int):
                tags = [tags] * len(new_batch)
            elif isinstance(tags, list):
                tags = tags
            else:
                tags = [1] * len(new_batch)

            # Prepare bulk insert
            bulk_insert = []
            for tweet, t in zip(new_batch, tags):
                target_data = {
                    'tweet_id': tweet['tweet_id'],
                    'content_type': t,
                    'data_type': tweet.get('data_type', []),
                    'update_time': formatted_time,
                    'topic_id': tweet['topic_id'],
                    'topic_name': tweet['topic_name']
                }
                bulk_insert.append(pymongo.InsertOne(target_data))

            # Bulk insert new tweets
            try:
                result = ai_tweet_db.bulk_insert(bulk_insert, ordered=False)
                print(Fore.YELLOW+f"Inserted {result.inserted_count} documents"+Fore.RESET)
            except pymongo.errors.BulkWriteError as bwe:
                TaskLog.error(f"Bulk write error: {bwe.details}")
                report_trace_id(formatted_time, trace_id, "303", f"tweetID {id_} with {trace_id}"
                                                                 f" write to database error!", "ERROR", report_id_)
                report_id_ += 1

        # Bulk update existing tweets
        if update_batch:
            bulk_update = [
                pymongo.UpdateOne(
                    {"tweet_id": tweet["tweet_id"]},
                    {"$set": {"data_type": tweet["data_type"]}}
                )
                for tweet in update_batch
            ]
            try:
                result = ai_tweet_db.bulk_insert(bulk_update, ordered=False)
                print(Fore.YELLOW+f"Updated {result.modified_count} documents"+Fore.RESET)
            except pymongo.errors.BulkWriteError as bwe:
                TaskLog.error(Fore.RED+f"Bulk update error: {bwe.details}"+Fore.RESET)
        return False, report_id_

    for i in range(0, len(deduplicated_tweets), batch_size):
        batch = deduplicated_tweets[i:i + batch_size]
        status, report_id = process_batch(batch, report_id)
    return True, report_id


def main(args_):
    while True:
        TaskLog.info("Generating new tweets.")
        status = get_tweet_data(args_.time_window, args_.batch_size, args_.reverse)
        if not status:
            TaskLog.error("Error generating new tweets.")
            continue
        TaskLog.info("Successfully generated new tweets.")
        time.sleep(60)


def init_db():
    # 为 collection 中的 tweet_id 添加索引
    ai_tweet_db.create_index([("tweet_id", pymongo.ASCENDING)], background=True)
    # 也可以创建其他需要的索引
    source_topic_db.create_index([("created_at", pymongo.ASCENDING)], background=True)

    print("Indexes created (if they didn't already exist)")


def init_db():
    # 为 collection 中的 tweet_id 添加索引
    ai_tweet_db.create_index([("tweet_id", pymongo.ASCENDING)], background=True)
    # 也可以创建其他需要的索引
    source_topic_db.create_index([("created_at", pymongo.ASCENDING)], background=True)

    print("Indexes created (if they didn't already exist)")


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Distill tweet pipeline for generating new tweets. -w 5 -b 30")

    parser.add_argument('-w', "--time_window", type=int, default=5, help="Searching time window(minutes)")
    parser.add_argument('-b', "--batch_size", type=int, default=30, help="Processing batch size")
    parser.add_argument('-r', "--reverse", type=bool, default=True, help="Sort tweets in reverse order")
    args = parser.parse_args()
    main(args)
