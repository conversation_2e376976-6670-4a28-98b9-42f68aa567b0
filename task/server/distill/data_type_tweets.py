import os
import sys
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey
monkey.patch_all()
import config
import requests
from datetime import datetime, timedelta, timezone
from task import TaskLog
from task.dao.mongo.mongo_db import MongoDB
from colorama import Fore

source_topic_db = MongoDB(collection="ai_tweet_topics")
ai_tweet_db = MongoDB(collection="test_ai_tweets")
rec_host = config.WATT_REC_HOST
datahub_report_url = "/api/v1/dataHub/report"
app_name = "task-tweet-content-category"
timestamp_format = "%Y-%m-%dT%H:%M:%SZ"


def report_trace_id(current_utc_time, trace_id: str, codes: str, msg: str, batch_id: int):
    batch_id_str = trace_id + "_" + app_name + "_" + f"{batch_id:05d}"
    body_report = {
        "traceId": trace_id,
        "appName": app_name,
        "appBatchId": batch_id_str,
        "code": codes,
        "msg": msg,
        "timestamp": current_utc_time,
        "data": {}
    }
    try:
        response_ = requests.post(f"{rec_host}{datahub_report_url}", json=[body_report], timeout=10)
        response_.raise_for_status()
    except Exception as e:
        TaskLog.error(f"Error retrieve datahub report results: {e}")
        return False, e
    response = response_.json()
    print(Fore.YELLOW + f"Datahub report response: {response}" + Fore.RESET)
    return True, response


def process_tweet_data():
    current_time = datetime.now(timezone.utc)
    formatted_time = current_time.strftime('%Y-%m-%dT%H:%M:%SZ')
    topic_results = source_topic_db.get_all_data()
    existing_ids = set(doc['tweet_id'] for doc in ai_tweet_db.get_all_fields({}, {'tweet_id': 1}))
    print(Fore.GREEN + f"total existing tweets: {len(existing_ids)}" + Fore.RESET)
    print(Fore.GREEN + f"total topics: {len(topic_results)}" + Fore.RESET)
    for topic_ in topic_results:
        topic_id = str(topic_['_id'])
        tweet_id_list = topic_.get('tweets', [])
        tweet_info_list = topic_.get('tweets_info', [])
        topic_name = topic_.get('name')
        # trace_id = topic_.get('trace_id')
        # tweet_content_list = [tweet_info.get("full_text") for tweet_info in tweet_info_list]
        tweet_type_list = [tweet_info.get("data_type", []) for tweet_info in tweet_info_list]
        # bulk_data = []
        for id_, d_dypte in zip(tweet_id_list, tweet_type_list):
            if id_ not in existing_ids:
                target_data = {'tweet_id': id_, 'content_type': 12, 'data_type': d_dypte,
                               'update_time': formatted_time,
                               'topic_id': topic_id, 'topic_name': topic_name}
                # bulk_data.append(target_data)
                try:
                    status_ = ai_tweet_db.insert_one(target_data)
                    # status_ = ai_tweet_db.insert_many(bulk_data)
                    if not status_:
                        print(Fore.YELLOW + f"Result error: {status_}" + Fore.RESET)
                except Exception as e:
                    print(Fore.RED + f"Error insert data: {e}" + Fore.RESET)
            else:
                print(Fore.YELLOW + f"Tweet already exists: {id_}" + Fore.RESET)
    return True


def main():
    TaskLog.info("Generating new tweets.")
    status = process_tweet_data()
    if not status:
        TaskLog.error("Error generating new tweets.")
        return False
    TaskLog.info("Successfully generated new tweets.")


if __name__ == '__main__':
    main()
    # try:
    #     ai_tweet_db.update_many(
    #         {"content_type": 0},
    #         {"content_type": 1},
    #         upsert=False
    #     )
    # except Exception as e:
    #     print(Fore.RED + f"Error: {e}" + Fore.RESET)
