import os
import sys
import argparse
from bson import ObjectId
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey
monkey.patch_all()
import config
import requests
import grequests
import queue
from queue import Queue
from datetime import datetime, timedelta, timezone
import time
from task.dao.mongo.mongo_db import MongoDB
from task.lib.prompt_fusion import PromptsFusion
from task.lib.text_process import truncate_paragraph
from task.server.call_gpt import CallGpt
from task.server.offline_logger import setup_logger
from colorama import Fore
import traceback

logger = setup_logger(log_file='logs/hoc-voices.log', logger_name='summary_voices')

prompts_fusion = PromptsFusion()
prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=0.2)

crawler_host = config.CRAWLER_HOST
twitter_news_url = "/api/v1/twitter/news_search"

source_topic_db = MongoDB(collection="ai_tweet_topics")
reference_event_db = MongoDB(collection="event_details")
ai_voices_db = MongoDB(collection="topic_voices")
timestamp_format = "%Y-%m-%dT%H:%M:%SZ"

retry_queue = Queue()
MAX_RETRIES = 4


def execute_post_request(task_queue, keyword_info, retry_count, use_keyword = False):
    """
    Handles the POST request logic for fetching news using either the hotspot or keyword.
    If it fails (timeout/network error), the task is re-queued.
    """
    body = {"keyword": keyword_info["hotspot_name"]} if not use_keyword else {"keyword": keyword_info["keyword"]}

    request = grequests.post(f"{crawler_host}{twitter_news_url}", json=body, timeout=80)

    try:
        # response_ = requests.post(f"{crawler_host}{twitter_news_url}", json=body, timeout=80)
        response_ = grequests.map([request])[0]
        if response_ and response_.status_code == 200:
            if response_.json().get("status", 0) == 0:
                return response_.json().get("result", {}).get("data", [])
            else:
                error = response_.json().get("error", "")
                logger.warning(f"Failed request error: {error} for keyword: {keyword_info['keyword']}")
        logger.warning(f"Failed request with status code {response_.status_code}")
    except Exception as e:
        logger.error(f"Error during request: {e}")

    if retry_count < MAX_RETRIES:
        logger.info(f"Requeuing the task for {keyword_info['keyword']} (retry count: {retry_count + 1})")

        # Sleep for 20 seconds before re-queuing
        time.sleep(20)
        task_queue.put((keyword_info, retry_count + 1))

    return []


def execute_concurrent_requests(task_queue, keyword_infos, retry_counts, use_keyword=False):
    """
    Function to handle multiple concurrent POST requests using grequests.
    """
    requests_list = []

    for keyword_info in keyword_infos:
        body = {"keyword": keyword_info["hotspot_name"]} if not use_keyword else {"keyword": keyword_info["keyword"]}
        request = grequests.post(f"{crawler_host}{twitter_news_url}", json=body, timeout=80)
        requests_list.append(request)

    # Perform concurrent requests
    responses = grequests.map(requests_list)

    results = []
    for i, response_ in enumerate(responses):
        keyword_info = keyword_infos[i]
        retry_count = retry_counts[i]
        if response_ and response_.status_code == 200:
            if response_.json().get("status", 0) == 0:
                retrieved_data = response_.json().get("result", {}).get("data", [])
                results.append(retrieved_data)
            else:
                error = response_.json().get("error", "")
                logger.warning(f"Failed request error: {error} for keyword: {keyword_info['keyword']}")
                results.append([])
        else:
            logger.warning(f"Failed request with status code {response_.status_code if response_ else 'No Response'}")

        # Handle retries
        if not response_ or response_.status_code != 200 and retry_count < MAX_RETRIES:
            logger.info(f"Requeuing the task for {keyword_info['keyword']} (retry count: {retry_count + 1})")
            time.sleep(10)
            task_queue.put((keyword_info, retry_count + 1))

    return results


def generate_summary_con(sys_prompt_list: list, user_prompt_list: list, watttraceid: str = None):
    status_, output_list = prompt_gpt.gcallOpenaiGpt(sys_prompt_list, user_prompt_list, watttraceid=watttraceid,
                                                     temperature=0.2, json_object=False)
    if status_:
        return True, output_list
    return False, output_list


def process_news_items(news_list_hotspot, news_list_keyword, max_limit):
    """
    Combine news from hotspot and keyword with unique content_links
    """
    unique_content_links, news_list_combined = set(), []

    for news_source in (news_list_hotspot, news_list_keyword):
        for news in news_source:
            if isinstance(news, str):
                print(Fore.RED + f"Error in news: {news_source}" + Fore.RESET)
                continue
            elif isinstance(news, dict):
                content_link = news.get("content_link")
            if content_link and content_link not in unique_content_links:
                unique_content_links.add(content_link)
                news_list_combined.append(news)

    return news_list_combined[:max_limit]


def handle_topic_data(keyword_info, news_list_combined):
    """
    Handles the news data, performs GPT content generation, and stores into databases using GPT result.
    """
    sys_prompt_list, user_prompt_list = [], []

    for news_info in news_list_combined:
        input_data = {"event_background": keyword_info.get("hotspot_name")}
        text_input = news_info.get("content", "")
        if not text_input:
            logger.warning(f"Empty content for news: {news_info} of hotspotId: {keyword_info['_id']}")
            continue
        status, front, back = truncate_paragraph(news_info.get("content", ""))

        if status:
            input_data["front_part"] = front
            input_data["back_part"] = back
        else:
            input_data["full_text"] = front

        input_data.update({k: v for k, v in news_info.items() if k in ["title", "description"]})

        sys_prompt_list.append(prompts_fusion.get_summary_system(input_data))
        user_prompt_list.append(prompts_fusion.get_summary_user(input_data))

    status_, generated_summary = generate_summary_con(sys_prompt_list, user_prompt_list)
    if status_ and isinstance(generated_summary, list):
        for summary, news_info in zip(generated_summary, news_list_combined):
            news_info["content"] = summary

        keyword_info["news_list"] = news_list_combined
        topic_id = ObjectId(keyword_info.get("_id", ""))

        source_topic_db.update_one({"_id": topic_id, "viewpoint_status": 2})
        ai_voices_db.insert_one(keyword_info, overwrite=True)
        logger.info(f"News list successfully processed and stored into the database with {len(news_list_combined)} "
                    f"items for hotspotId: {keyword_info['_id']}")
    else:
        logger.error(f"Failed to generate summaries: {generated_summary} with hotspotId: {keyword_info['_id']}")


def extract_views(last_minutes, max_limit):
    status, keyword_list = get_hotspot_content(last_minutes)

    if not status or not keyword_list:
        logger.info(f"No keywords found: {keyword_list}")
        return False, keyword_list

    task_queue = Queue()
    for keyword_info in keyword_list:
        task_queue.put((keyword_info, 0))

    BATCH_SIZE = 2
    while not task_queue.empty():
        keyword_infos = []
        retry_counts = []
        # 从队列中取出 BATCH_SIZE 个任务
        for _ in range(BATCH_SIZE):
            try:
                keyword_info, retry_count = task_queue.get(timeout=2)
                keyword_infos.append(keyword_info)
                retry_counts.append(retry_count)
            except queue.Empty:
                logger.warning("Task queue is empty, no more tasks to process.")
                break  # 跳出循环，因为队列已经空了
        if not keyword_infos:
            break

        news_list_hotspot = execute_concurrent_requests(task_queue, keyword_infos, retry_counts, use_keyword=False)
        if any(len(news) < 4 for news in news_list_hotspot):
            news_list_keyword = execute_concurrent_requests(task_queue, keyword_infos, retry_counts, use_keyword=True)
        else:
            news_list_keyword = [[] for _ in keyword_infos]

        # 确保 news_list_keyword 的长度与 keyword_infos 一致
        news_list_keyword += [[] for _ in range(len(keyword_infos) - len(news_list_keyword))]

        logger.debug(f"Length of keyword_infos: {len(keyword_infos)}")
        logger.debug(f"Length of news_list_hotspot: {len(news_list_hotspot)}")
        logger.debug(f"Length of news_list_keyword: {len(news_list_keyword)}")

        for keyword_info, news_hotspot, news_keyword in zip(keyword_infos, news_list_hotspot, news_list_keyword):
            if not news_hotspot and not news_keyword:
                logger.warning(
                    f"No news found for keyword: {keyword_info['keyword']} with hotspotID: {keyword_info['_id']}")
                source_topic_db.update_one({"_id": ObjectId(keyword_info["_id"]), "viewpoint_status": 4})
                continue

            # 处理并合并获取的新闻
            news_list_combined = process_news_items(news_hotspot, news_keyword, max_limit)

            # 处理合并后的新闻数据
            handle_topic_data(keyword_info, news_list_combined)

    return True, "Success"


def get_hotspot_content(minutes: int = 30):
    current_time = datetime.now(timezone.utc)
    last_minutes = current_time - timedelta(minutes=minutes)
    last_minutes_str = last_minutes.strftime(timestamp_format)

    query_topic_time = {"created_at": {"$gte": last_minutes_str}, "active": 1,
                        "$or": [{"viewpoint_status": {"$eq": 1}},
                                {"viewpoint_status": {"$exists": False}}],
                        "keyword_source": {"$ne": "ai&science"}}

    topic_results = source_topic_db.find_data_condition(query_topic_time)
    print(Fore.GREEN + f"total topics in last {minutes} minutes: {len(topic_results)}" + Fore.RESET)
    if not topic_results:
        return False, "No topic found"

    keyword_list  = []
    for topic_ in topic_results:
        trace_id = topic_.get('trace_id')
        ref_event = reference_event_db.singleSearch("trace_id", trace_id)
        if ref_event:
            ref_keyword = ref_event.get("keyword", "")
            event_source = ref_event.get("final_event_from", "")
            # if event_source in ["both", "search"]:
            keyword_list.append({
                "_id": ObjectId(topic_["_id"]),
                "hotspot_name": topic_.get("name"),
                "trace_id": trace_id,
                "keyword": ref_keyword,
                "created_at": topic_.get('created_at')
            })
            # else:
            #     print(Fore.YELLOW + f"Skipping topic: {topic_['name']} with trace_id: {trace_id}" + Fore.RESET)
            #     source_topic_db.update_one({"_id": ObjectId(topic_["_id"]), "viewpoint_status": 3})
        else:
            print(Fore.RED + f"No reference event found for topic: {topic_['name']} with trace_id: {trace_id}" + Fore.RESET)
    return True, keyword_list


def main(args_):
    while True:
        logger.info("Starting extraction process")
        try:
            status, result = extract_views(args_.minutes, args_.max_limit)
            if not status:
                logger.info(f"Error in extracting viewpoints: {result}")
            else:
                logger.info(result)
        except Exception as e:
            logger.error(f"Exception during extraction: {e} - {traceback.format_exc()}")
        logger.info("Job done, sleeping for 10 seconds")
        time.sleep(10)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Extract views from hotspots')
    parser.add_argument('--minutes', type=int, default=20, help='Minutes to extract views')
    parser.add_argument('--max_limit', type=int, default=10, help='Max limit of news to extract')
    args = parser.parse_args()
    main(args)
