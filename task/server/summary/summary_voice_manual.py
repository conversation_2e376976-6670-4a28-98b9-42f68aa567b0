import os
import sys
import argparse
from bson import ObjectId
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
from gevent import monkey
monkey.patch_all()
import config
import requests
import queue
from queue import Queue
import time
from task.dao.mongo.mongo_db import MongoDB
from task.lib.prompt_fusion import PromptsFusion
from task.lib.text_process import truncate_paragraph
from task.server.call_gpt import CallGpt
from task.server.offline_logger import setup_logger
from colorama import Fore
import traceback

logger = setup_logger(log_file='logs/hoc-voices.log', logger_name='summary_voices')

prompts_fusion = PromptsFusion()
prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=0.2)

crawler_host = config.CRAWLER_HOST
twitter_news_url = "/api/v1/twitter/news_search"

source_topic_db = MongoDB(collection="ai_tweet_topics")
reference_event_db = MongoDB(collection="event_details")
ai_voices_db = MongoDB(collection="topic_voices")
timestamp_format = "%Y-%m-%dT%H:%M:%SZ"

retry_queue = Queue()
MAX_RETRIES = 4


def execute_post_request(task_queue, keyword_info, retry_count, use_keyword = False):
    """
    Handles the POST request logic for fetching news using either the hotspot or keyword.
    If it fails (timeout/network error), the task is re-queued.
    """
    body = {"keyword": keyword_info["hotspot_name"]} if not use_keyword else {"keyword": keyword_info["keyword"]}

    try:
        response_ = requests.post(f"{crawler_host}{twitter_news_url}", json=body, timeout=80)

        if response_.status_code == 200:
            if response_.json().get("status", 0) == 0:
                return response_.json().get("result", {}).get("data", [])
            else:
                error = response_.json().get("error", "")
                logger.warning(f"Failed request error: {error} for keyword: {keyword_info['keyword']}")
        logger.warning(f"Failed request with status code {response_.status_code}")
    except requests.RequestException as e:
        logger.error(f"Error during request: {e}")

    if retry_count < MAX_RETRIES:
        logger.info(f"Requeuing the task for {keyword_info['keyword']} (retry count: {retry_count + 1})")

        # Sleep for 10 seconds before requeuing
        time.sleep(10)
        task_queue.put((keyword_info, retry_count + 1))

    return []

def generate_summary_con(sys_prompt_list: list, user_prompt_list: list, watttraceid: str = None):
    status_, output_list = prompt_gpt.gcallOpenaiGpt(sys_prompt_list, user_prompt_list, watttraceid=watttraceid,
                                                     temperature=0.2, json_object=False)
    if status_:
        return True, output_list
    return False, '调用GPT接口生成summary of voices失败'


def process_news_items(news_list_hotspot, news_list_keyword):
    """
    Combine news from hotspot and keyword with unique content_links
    """
    unique_content_links, news_list_combined = set(), []

    for news_source in (news_list_hotspot, news_list_keyword):
        for news in news_source:
            if isinstance(news, str):
                print(Fore.RED + f"Error in news: {news_source}" + Fore.RESET)
                continue
            elif isinstance(news, dict):
                content_link = news.get("content_link")
            if content_link and content_link not in unique_content_links:
                unique_content_links.add(content_link)
                news_list_combined.append(news)

    return news_list_combined[:10]


def handle_topic_data(keyword_info, news_list_combined):
    """
    Handles the news data, performs GPT content generation, and stores into databases using GPT result.
    """
    sys_prompt_list, user_prompt_list = [], []

    for news_info in news_list_combined:
        input_data = {"event_background": keyword_info.get("hotspot_name")}
        text_input = news_info.get("content", "")
        if not text_input:
            logger.warning(f"Empty content for news: {news_info} of hotspotId: {keyword_info['_id']}")
            continue
        status, front, back = truncate_paragraph(news_info.get("content", ""))

        if status:
            input_data["front_part"] = front
            input_data["back_part"] = back
        else:
            input_data["full_text"] = front

        input_data.update({k: v for k, v in news_info.items() if k in ["title", "description"]})

        sys_prompt_list.append(prompts_fusion.get_summary_system(input_data))
        user_prompt_list.append(prompts_fusion.get_summary_user(input_data))

    status_, generated_summary = generate_summary_con(sys_prompt_list, user_prompt_list)
    if status_ and isinstance(generated_summary, list):
        for summary, news_info in zip(generated_summary, news_list_combined):
            news_info["content"] = summary

        keyword_info["news_list"] = news_list_combined
        topic_id = ObjectId(keyword_info.get("_id", ""))

        source_topic_db.update_one({"_id": topic_id, "viewpoint_status": 2})
        ai_voices_db.insert_one(keyword_info, overwrite=True)
        logger.info(f"News list successfully processed and stored into the database with {len(news_list_combined)} "
                    f"items for hotspotId: {keyword_info['_id']}")
    else:
        logger.error(f"Failed to generate summaries: {generated_summary}")


def extract_view(hotspot_id):
    status, keyword_list = get_hotspot_content(hotspot_id=hotspot_id)
    if not status or not keyword_list:
        logger.info(f"No keywords found: {keyword_list}")
        return False, keyword_list

    task_queue = Queue()
    for keyword_info in keyword_list:
        task_queue.put((keyword_info, 0))

    while not task_queue.empty():
        try:
            keyword_info, retry_count = task_queue.get(timeout=2)
        except queue.Empty:
            logger.warning("Task queue is empty, no more tasks to process.")
            break  # 跳出循环，因为队列已经空了

        news_list_hotspot = execute_post_request(task_queue, keyword_info, retry_count, use_keyword=False)
        if len(news_list_hotspot) < 4:
            news_list_keyword = execute_post_request(task_queue, keyword_info, retry_count, use_keyword=True)
        else:
            news_list_keyword = []

        if not news_list_hotspot and not news_list_keyword:
            logger.warning(f"No news found for keyword: {keyword_info['keyword']} with hotspotID: {keyword_info['_id']}")
            source_topic_db.update_one({"_id": ObjectId(keyword_info["_id"]), "viewpoint_status": 2})
            continue

        # Process and combine the fetched news
        news_list_combined = process_news_items(news_list_hotspot, news_list_keyword)
        handle_topic_data(keyword_info, news_list_combined)

    return True, "Success"


def get_hotspot_content(hotspot_id):
    topic_ = source_topic_db.singleSearch("_id", ObjectId(hotspot_id))
    if not topic_:
        return False, "No topic found"

    keyword_list  = []
    # print(Fore.GREEN + f"topic: {topic_result}" + Fore.RESET)
    trace_id = topic_.get('trace_id')
    print(Fore.GREEN + f"trace_id: {trace_id}" + Fore.RESET)
    ref_event = reference_event_db.singleSearch("trace_id", trace_id)
    if ref_event:
        ref_keyword = ref_event.get("keyword", "")
        print(Fore.YELLOW + f"ref_event: {ref_keyword}" + Fore.RESET)
        event_source = ref_event.get("final_event_from", "")
        # if event_source in ["both", "search"]:
        keyword_list.append({
            "_id": ObjectId(topic_["_id"]),
            "hotspot_name": topic_.get("name"),
            "trace_id": trace_id,
            "keyword": ref_keyword,
            "created_at": topic_.get('created_at')
        })
        # else:
        #     source_topic_db.update_one({"_id": ObjectId(topic_["_id"]), "viewpoint_status": 2})
    else:
        print(Fore.RED + f"No reference event found for topic: {topic_['name']} with trace_id: {trace_id}" + Fore.RESET)
    return True, keyword_list


def main(args_):
    logger.info("Starting extraction process")
    try:
        status, result = extract_view(hotspot_id=args_.hotspot_id)
        if not status:
            logger.info(f"Error in extracting viewpoints: {result}")
        else:
            logger.info(result)
    except Exception as e:
        logger.error(f"Exception during extraction: {e} - {traceback.format_exc()}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Extract views from hotspots')
    parser.add_argument('--hotspot_id', type=str, default="", help='Hotspot ID to extract views')
    args = parser.parse_args()
    main(args)
