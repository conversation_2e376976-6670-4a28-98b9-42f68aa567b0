import time
from task.dao.mongo.mongo_db import MongoDB
import json
import re
from task.server.call_gpt import callWattGPT


mongo_event_details = MongoDB(collection="event_details")


def remove_before_bracket(s):
    match = re.search(r"\[(.*)\]", s, re.DOTALL)
    if match:
        return match.group(0)
    else:
        return s


def get_summary_from_url(urls: list):
    url_str = ''
    for i in range(len(urls)):
        url_str += "    " + str(i + 1) + '.' + urls[i]["link"] + '\n'
    sys_prompt = "You are a helpful assistant. You should completely follow users requirements, and return only with json format"
    user_prompt = ("    There are several urls below: \n" + url_str + """
    Summarize these urls in the following sections:
    url - the original url
    title - the title of the page
    summary - the summary of the page content

    Response should strictly be a JSON object with the above sections as keys and the corresponding information as values.

    example response:
    [
      {
       "url": "https://www.sail-word.com/news/12234/this-is-a-new-about...",
       "title": "This is a news about...",
       "summary": "<PERSON> has been shoot during the USA president campaign, the shooter is be killed by the FBI..."
      },
      {
       "url":xxx,
       "title":xxx,
       "summary":xxx
      }
    ]
    Please ensure that:

    There must be no introductory paragraph
    The summary focuses on the website facts and avoids speculation or unknowns""")
    msg = [{"role": "system", "content": sys_prompt},
           {"role": "user", "content": user_prompt}]
    body = {"messages": msg, "model": "llama-3-sonar-small-32k-online", "stream": False}
    error = []
    res_log = []
    for i in range(3):
        status, code, response = callWattGPT.callPplxChannelChatCompletions(body)
        if status:
            res = response["result"]['data']["choices"][0]["message"]["content"]
            res = remove_before_bracket(res)
            try:
                info = json.loads(res)
                if len(info) == len(urls):
                    return True, info
                else:
                    res_log.append(res)
            except Exception as e:
                res_log.append(res)
                error.append(e)
        else:
            error.append(response)
    return False, {"error": error, "response": res_log}


def add_summary(data):
    print(data['trace_id'])
    final_from = data['final_event_from']
    if final_from == 'tweet':
        data['summary'] = 'skip'
        print('Skpi for tweet')
        mongo_event_details.update_one(data)
        return True
    search_details = data['search_details']
    result_to_summarize = []
    len_news, len_bing = 0, 0
    if search_details['news_result']:
        result_to_summarize.extend(search_details['news_result'])
        len_news = len(search_details['news_result'])
    if search_details['bing_result']:
        result_to_summarize.extend(search_details['bing_result'])
    summary_status, summary_list = get_summary_from_url(result_to_summarize)
    if summary_status:
        if search_details['event_from'] == 'bing':
            output_summary = summary_list[len_news]['summary']
        else:
            output_summary = summary_list[0]['summary']
        data['summary'] = output_summary
        data['summary_list'] = summary_list
        mongo_event_details.update_one(data)
        print('success')
    else:
        print(summary_list)
        pass


if __name__ == '__main__':
    query = {
        'local_time': {'$ne': None},
        'summary': "",
        'summary_list': {'$ne': []},
        '$or': [
            {'search_details.bing_result': {'$ne': []}},
            {'search_details.news_result': {'$ne': []}}
        ]
    }
    while True:
        data_to_process = list(mongo_event_details.find_data(query=query, limit=50))
        if len(data_to_process) == 0:
            print('sleep')
            time.sleep(60)
        for i in data_to_process:
            add_summary(i)