import config
from task import callWattGPT
from flask import current_app
from utils.watt_service.call_watt_ai_service import CallAiService


class CallClaude:
    def __init__(self, temperature=0.8):
        self.model = "claude-3-5-sonnet-20240620"
        self.temperature = temperature
        self.callClaudeService = CallAiService(config.WATT_AI_GPT_HOST)
        self.watt_ai_gpt_token = config.WATT_AI_GPT_TOKEN

    def callClaude(self, system_prompt, user_prompt, watttraceid="", temperature=None):
        if temperature:
            self.temperature = temperature
        headers = {'watt-gpt-token': self.watt_ai_gpt_token}
        data = {
            "model": self.model,
            "max_tokens": 4096,
            "stream": False,
            "temperature": temperature if temperature else self.temperature,
            "top_k": 0,
            "system": system_prompt,
            "messages": [
                {
                    "role": "user",
                    "content": user_prompt
                }
            ]
        }
        status, code, response = self.callClaudeService.call("/v1/kunlian/messages", headers=headers,
                                                             body=data, timeout=100, watttraceid=watttraceid)
        if status:
            output_string = response['result']['data']['content'][0]['text']
            return True, output_string
        else:
            return False, f'''调用watt claude接口失败 - {code}, {response}'''


class CallGpt:
    """
    Base class for call gpt
    """

    def __init__(self,
                 model: str = "gpt-4o",
                 temperature=0.8
                 ):

        self.model = model
        self.temperature = temperature

    def callGpt(self, prompt, watttraceid="", temperature=None, json_object=False):

        if temperature:
            self.temperature = temperature

        data = {
            "model": self.model,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }
        if json_object:
            data["response_format"] = {"type": "json_object"}

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=data, timeout=300,
                                                                              watttraceid=watttraceid)

        if status:
            output_string = (response['result']['data']['choices'][0]['message']['content']
                             .replace("\n", "").replace("\\\"", ""))
            return True, output_string
        else:
            current_app.flaskLog.error(
                current_app.fmtSaveOutput(watttraceid, f'调用watt gpt接口失败 - {code}, {response}'))
            return False, f'''调用watt gpt4接口失败 - {code}, {response}'''

    def callOpenaiGpt(self, system_prompt, user_prompt, watttraceid="", model=None, temperature=None,
                      json_object=False):
        if temperature:
            self.temperature = temperature

        data = {
            "model": self.model if not model else model,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ]
        }
        if json_object:
            data["response_format"] = {"type": "json_object"}

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=data, timeout=300,
                                                                              watttraceid=watttraceid)

        if status:
            output_string = response['result']['data']['choices'][0]['message']['content']
            return True, output_string
        else:
            return False, f'''调用watt gpt接口失败 - {code}, {response}'''

    def gcallOpenaiGpt(self, system_prompt_list, user_prompt_list, watttraceid="", model=None, temperature=None,
                       json_object=False):
        body_list = []
        output_list = []
        for system_prompt, user_prompt in zip(system_prompt_list, user_prompt_list):
            if temperature:
                self.temperature = temperature

            data = {
                "model": self.model if not model else model,
                "temperature": self.temperature,
                "messages": [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            }
            if json_object:
                data["response_format"] = {"type": "json_object"}
            body_list.append(data)

        status, code, responses = callWattGPT.gCallOpenaiChannelChatCompletions(body_list=body_list, timeout=300,
                                                                                watttraceid=watttraceid)

        if status:
            for sta_, code_, response_ in responses:
                try:
                    output_string = (response_.get('result', {}).get('data', {}).get('choices', [{}])[0].
                                     get('message', {}).get('content', ''))
                except Exception as e:
                    print(f"Error: {e} {response_}")
                    continue
                    # return False, f'''调用watt gpt接口失败 - {code}, {response_}'''
                output_list.append(output_string)
            return True, output_list
        else:
            return False, f'''调用watt gpt接口失败 - {code}, {responses}'''
