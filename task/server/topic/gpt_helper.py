import re
import json
from sentence_splitter import SentenceSplitter
import pandas as pd
from tqdm import tqdm
import random

from task.lib.chat import ChatService
from task.lib.utils import log, TOP_SOURCE, upload_log
from task.dao.mongo.mongo_db import MongoDB
from task.server.topic.mongo_helper import en_zh_pattern

gpt_service = ChatService()
splitter = SentenceSplitter(language='en')
ai_events_db = MongoDB(collection="ai_events")
keyword_db = MongoDB(collection="keyword")
keyword_trend_db = MongoDB(collection="keyword")
# event_details_db = MongoDB(collection="event_details")


# 时政科技：政治/地区新闻+科技新闻
# 财经观察：金融领域的新闻
# 娱乐竞技：娱乐新闻+体育新闻
areas = {
        5: 'AI(artificial intelligence) & neuroscience',
        4: 'entertainment & sports',
        3: 'finance',
        2: 'news & technology',
    }
default_area = 2


filter_function = [{
                "name": "filter_tweets",
                "description": "identify unrelated tweets to the event and get event category",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "event_name": {
                            "type": "string",
                            "description": "rewrite event name"
                            },
                        "event_category": {
                            "type": "integer",
                            "description": f"event category from the list: {areas}",
                            },
                        "event_summary": {
                            "type": "string",
                            "description": "event summary in 50 words"
                            },
                        "associate_keywords": {
                            "type": "array",
                            "description": "list of associated keywords",
                            "items": {
                                "type": "string",
                            }},
                        "irrelevant_tweets":{
                            "type": "array",
                            "description": "list of irrelevant tweet ids",
                            "items": {
                                "type": "integer",
                            }},

                    },
                    "required": ['event_category', 'event_summary', 'associate_keywords', "irrelevant_tweets"],
            }}]


def gpt_filter_tweets(tweets_df, event, keyword='', background='', verbose=False, skip_top_n=3, return_top_n=3,
                      gpt_model='gpt-4.5-mini', trace_id=''):
    '''Filter out irrelevant tweets and generate event summary based on relevant tweets.
    Args:
        tweets_df: pd.DataFrame, tweets data
        event: str, event name
        keyword: str, keyword name
        background: str, event background
        verbose: bool, print debug info
        skip_top_n: int, skip top n tweets
        return_top_n: int, return top n tweets if all filtered out
        gpt_model: str, gpt model name
    Return:
        tweet_ids: list, tweet ids
        event: str, event name
        area: int, event category
        summary: str, event summary
    '''

    event = event or keyword
    event = event.strip('#')
    default_summary = background
    default_res = tweets_df.tweet_id.tolist(), event, default_area, background, []

    if not event or len(tweets_df) < skip_top_n:
        return default_res

    try:
        #DEFAULT SKIP top n tweets
        tweet_ids = tweets_df.iloc[:skip_top_n].tweet_id.tolist()
        tweets_df = tweets_df.iloc[skip_top_n:].reset_index()

        n_sents = 2
        tweets_df['full_text'] = tweets_df['full_text'].apply(lambda x: ' '.join(splitter.split(x)[:n_sents]))
        tweets = tweets_df.full_text.to_markdown()

        # Determine if event name needs refinement (single word keywords need better event names)
        needs_event_refinement = ' ' not in keyword.strip()

        summary_style = random.choice(['captivating', 'lively', 'engaging', 'insightful', 'informative', 'interesting', 'compelling'])
        background_section = f'# BACKGROUND\n{background}\n\n' if background else ''

        task_line2 = "Refine the event name and generate a concise event summary" if needs_event_refinement else "Generate a concise event summary"
        
        # Define the refinement section outside the f-string to avoid backslash issues
        refinement_section = """## 2. Event Name Refinement
- Create a short, descriptive event name (5 words or less) based on the keyword and relevant tweets
- Use Chinese if the keyword is in Chinese; otherwise use English
- The name should be concise but informative

"""
        
        # Define event name description outside the f-string to avoid backslash issues
        event_name_desc = "A refined name for the event (required)" if needs_event_refinement else "The current event name or leave as is"
        
        summary_section_num = "3" if needs_event_refinement else "2"
        keywords_section_num = "4" if needs_event_refinement else "3"

        prompt = f"""# ROLE
You are an expert text analyst specializing in social media content classification and summarization.

# TASK
Analyze tweets related to (keyword: "{keyword}") and (event/topic: "{event}"), then:
1. Identify relevant vs. irrelevant tweets
2. {task_line2}
3. Suggest associated keywords for marketing purposes

{background_section}# TWEETS
{tweets}

# INSTRUCTIONS
## 1. Tweet Relevance Classification
- A tweet is RELEVANT if it discusses either the keyword "{keyword}" OR the event/topic "{event}"
- A tweet is IRRELEVANT ONLY if it has NO connection to BOTH the keyword AND the event/topic
- When in doubt, consider the tweet relevant
- Return tweet indices (0-based) that should be EXCLUDED

{refinement_section if needs_event_refinement else ""}## {summary_section_num}. Event Summary
- Write a {summary_style} summary in 50-100 words
- Use Chinese if the keyword is in Chinese; otherwise use English
- Include background information (if provided) followed by key insights from relevant tweets
- Do not use the word "tweets" in the summary
- If no useful information is available, return an empty string

## {keywords_section_num}. Associated Keywords
- Generate up to 5 marketing-focused keywords related to this event/topic
- Choose keywords that could help drive e-commerce traffic or promote brands/services
- Examples by category:
  * Sports: fitness, health, nutrition, gym
  * Finance: investment, education, stocks, crypto, trading
  * Technology: education, AI, apps, electronics
  * Science: education, healthcare, environment, medicine
  * Entertainment: beauty, fashion, self-help, travel
  * Politics: safety, speech, charisma, freedom, charity

# OUTPUT FORMAT
You must return your analysis using the provided function call format with all required parameters:
- event_name: {event_name_desc}
- event_category: An integer from this list: {areas} (2=news/tech, 3=finance, 4=entertainment/sports, 5=AI/science)
- event_summary: A concise summary of the event (50-100 words)
- associate_keywords: A list of marketing-related keywords (up to 5 items)
- irrelevant_tweets: A list of indices (0-based) for tweets that should be excluded
"""

        con, msg, func = gpt_service.function_call(prompt, functions=filter_function, model=gpt_model, temperature=0.5, timeout=30)
        if not con:
            log('OpenAI error, skip filtering', level='warning')
            # return all tweets
            return default_res

        params = func.get('filter_tweets', {})
        if isinstance(params, str):
            params = json.loads(params)

        #default keep tweet indices containing keyword
        contain_keyword = tweets_df.full_text.str.contains(keyword.strip('#'), case=False)
        keep_ids = tweets_df[contain_keyword].index
        exclude_ids = [i for i in params.get('irrelevant_tweets', []) if i not in keep_ids]
        if verbose:
            log(f'Prompt: {prompt}\n\nFiltered {len(exclude_ids)}/{len(tweets_df)} tweets; \nOutput params: {params}')
            # #check exclude contents
            temp = tweets_df.iloc[exclude_ids].full_text.to_list()
            print('EXCLUDED tweets:')
            for i in temp:
                print('-', i)

        include_ids = [i for i in tweets_df.index if i not in exclude_ids]
        if include_ids:
            tweet_ids += tweets_df.iloc[include_ids].tweet_id.tolist()
        elif skip_top_n == 0:
            #if all filter out, default keep top n tweets
            tweet_ids = tweets_df.iloc[:return_top_n].tweet_id.tolist()

        # Update event name if refinement was requested and provided
        if needs_event_refinement and params.get('event_name'):
            event = params.get('event_name', event)

        area = params.get('event_category', default_area)
        summary = params['event_summary'] if params.get('event_summary') else default_summary
        keywords = params.get('associate_keywords', [])
        return tweet_ids, event, area, summary, keywords
    except Exception as e:
        err = f'Error encounter in gpt_filter_tweets function for keyword {keyword}: {e}'
        log(err, level='error')
        upload_log(traceid=trace_id, msg=err)
        return default_res


def gpt_filter_topic(tweets_df, event, keyword_info={}, verbose=False, skip_top_n=0):
    keyword=keyword_info.get('name', '')
    background = ''

    #get event background by keyword info / trace id
    # if keyword_info.get('source') != 'ai&science':
    #     data = event_details_db.find_data(
    #         {'trace_id': keyword_info.get('trace_id'), 'keyword': keyword},
    #         limit=1
    #     )
    #     if data:
    #         event_info = data[0]
    #         if event_info.get('search_and_tweet_is_related') or event_info.get('final_event_from') == 'search':
    #             #TODO if use event summary directly 'if event from search', no use tweets
    #             background = event_info.get('summary', '')


    tweet_ids, event, area, summary, tags = gpt_filter_tweets(tweets_df, event, keyword=keyword, background=background, verbose=verbose,
                                                        skip_top_n=skip_top_n, return_top_n=0, trace_id=keyword_info.get('trace_id'))


    sample_docs = []
    if tweet_ids:
        sample = tweets_df.iloc[:5]
        sample_docs = sample[sample.tweet_id.isin(tweet_ids)].full_text.to_list()

    data = {
        'count': len(tweet_ids),
        'name': event,
        'customname': event,
        'representation': [],
        'representative_docs': sample_docs,
        'area': [area],
        'keywords': [keyword, event],
        'tweets': [str(i) for i in tweet_ids],
        'summary': summary or event,
        'tags': tags,
        'event_background': background,
        'active': 1 #if summary and len(tweet_ids) else 0,
        }

    df = pd.Series(data).to_frame().T
    return df



group_function = [{
                "name": "group_events",
                "description": "identify similar events and group them together",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "groups":{
                            "type": "array",
                            "description": "list of event groups, each is a list of event ids",
                            "items": {
                                "type": "array",
                                'description': 'group of event ids',
                                "items": {
                                    "type": "integer",
                                }
                            }},
                    },
                    "required": ['groups'],
                    }
            }]

def gpt_group_events(batch_id, skip_single_group=True, max_group_item=5, overwrite=True, min_events=2, gpt_model='gpt-4.5-mini',
                     insert_mongo=True, verbose=False):
    try:
        if not overwrite:
            old = ai_events_db.find_data({'batch_id': batch_id}, limit=1)
            if old:
                err = f'Grouped events already exist for batch_id: {batch_id}, skip grouping'
                log(err, level='warning')
                return True, {}

        if verbose:
            log(f'Start grouping events for batch_id : {batch_id}', level='warning')

        err = ''
        #1. GET DATA
        not_source = ['ai&science']
        query = {'batch_id': batch_id,  'source':{'$nin': not_source},  'associated_keywords':{'$ne':[]}}
        #default new data in front
        data = keyword_db.find_data(query)
        if not data or len(data) <= min_events:
            err = f'Batch events less than {min_events}, skip grouping'
            log(err, level='warning')
            return False, err

        df = pd.DataFrame(data).drop_duplicates('name').rename(columns={'name': 'keyword'})
        df['event'] = df['associated_keywords'].apply(lambda x: x[0])
        df['event'] = df.apply(lambda row: row.event if row.keyword.lower() in row.event.lower() else f'{row.event} ({row.keyword})', axis=1)
        # df.drop_duplicates('event', inplace=True)
        df['is_en_zh'] = df['keyword'].apply(lambda x: en_zh_pattern.match(x) is not None)
        df = df[df.is_en_zh]
        df = df.sort_values('event').reset_index(drop=True)
        # print(df.head(10))

        cols = ['keyword', 'event', 'trace_id']
        df = df[cols]

        #2. GPT GROUP DATA
        events = df.event.reset_index().rename(columns={'index':'event_id'}).to_markdown(index=False)
        prompt = f'''You are an expert in social media and SEO, given below list of headlines, help me group headlines on same Subject.
<start>
{events}
<end>
requirements:
- Think step by step.
- First, go through each headline, identify the name of the Person/Subject of interest.
- Second, go through each headline/Name pairs, assign headlines on same Name to one group.
- Be very accurate, unless matched the full name, do not add to one group!
'''

        if verbose: log(prompt)
        con, msg, func = gpt_service.function_call(prompt, functions=group_function, model=gpt_model, temperature=0.0, timeout=30)
        if not con:
            err = 'OpenAI error, skip grouping'
            log(err, level='warning')
            return False, err

        params = func.get('group_events', {})
        if isinstance(params, str):
            params = json.loads(params)
        groups = params.get('groups', [])
        if not groups:
            err = 'No groups return by OPENAI, skip grouping'
            log(err, level='warning')
            return False, err


        #Check last group data for same batch, ENSURE same batch same keyword same name
        last_keyword_group = {}
        if insert_mongo:
            old = ai_events_db.find_data({'batch_id': batch_id})
            last_keyword_group = {d['keyword']:d['group_event'] for d in old}

        insert_data = []
        kw_event = {}
        for i, group in enumerate(groups):
                #SKIP single event group
                if (skip_single_group and len(group) < 2) or len(group)>max_group_item:
                    continue

                if verbose:
                    temp = df.iloc[group].event.to_list()
                    print(f'==Group {i+1}:')
                    log(temp)

                #check if any keyword belong to last group, reuse group name
                group_event = df.iloc[group[0]].event
                if last_keyword_group:
                    for idx in group:
                        kw = df.iloc[idx].keyword
                        if kw in last_keyword_group:
                            group_event = last_keyword_group[kw]
                            break

                for idx in group:
                    kw = df.iloc[idx].keyword
                    temp = {
                        'keyword': kw,
                        #TODO rename event to associate keyword, to indicate the linkage
                        'event': df.iloc[idx].event,
                        'group_event': group_event,
                        'trace_id': df.iloc[idx].trace_id,
                        'batch_id': batch_id,
                    }
                    insert_data.append(temp)
                    kw_event[kw] = group_event

        #TODO not delete, need merge with last group data
        #3. Delete old data and insert new data to mongo ai_events
        if insert_data and insert_mongo:
            for d in old:
                ai_events_db.delete_data(d['_id'])

            status = ai_events_db.insert(insert_data)
            if not status:
                err = 'Error saving grouped events to mongo db'
                log(err, level='error')
            return status, err
        return True, kw_event
    except Exception as e:
        err = f'Error encounter in gpt_group_events function: {e}'
        log(err, level='error')
        return False, err


def group_events_pipeline(refresh_all_batch=False, limit=0, verbose=False):
    filter = {'associated_keywords':{'$ne':[]}}

    if not refresh_all_batch:
        filter.update({'status': '5', 'ai_topic_status': False})

    batch_ids = keyword_trend_db.get_distinct_values('batch_id', query=filter)
    #sort from small to large
    batch_ids.sort()
    if limit:
        batch_ids = batch_ids[-limit:]
    for batch_id in tqdm(batch_ids):
        status, err = gpt_group_events(batch_id, verbose=verbose)
        if not status:
            err = f'Error grouping events for batch_id- {batch_id}: {err}'
            # upload_log(traceid=str(batch_id), success=False, msg=err)




if __name__ == '__main__':

    #get batch ids
    # keyword_trend_db = MongoDB(collection="keyword")
    # res = keyword_trend_db.get_distinct_values('batch_id', query={'status': '5', 'ai_topic_status': True})
    # print(res)

    group_events_pipeline(refresh_all_batch=False, limit=3, verbose=True)

    batches = [
        # 1724054578
    ]
    import time
    total = 0
    for batch_id in batches:
        start = time.time()
        status = gpt_group_events(batch_id, verbose=True, insert_mongo=False)
        t = time.time()-start
        total += t
        print(status, f'Elapsed time: {t:.2f}s')

    log('Avgerage time:', total//max(1, len(batches)))