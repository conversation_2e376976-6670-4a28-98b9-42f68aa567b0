import os
import sys
# add task PYTHON<PERSON>TH to import task
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
import time
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler

from task.server.topic.filter_pipeline import tweet_topics_pipeline_multi_keywords


def job():
    print(f"Running scheduled job at {time.asctime()}")
    return tweet_topics_pipeline_multi_keywords(n_keywords=20, verbose=False, insert_mongo=True)


# scheduler = BlockingScheduler()
# scheduler.add_job(job, 'interval', minutes=5, next_run_time=datetime.now())
# scheduler.start()

if __name__ == '__main__':
    while True:
        tweet_topics_pipeline_multi_keywords(n_keywords=10, n_thread=1, verbose=False, insert_mongo=True)
        
        time.sleep(5)
