
import re
import pandas as pd
import json
from tqdm import tqdm

from task.dao.mongo.mongo_db import MongoDB
from task.lib.utils import log
from task.server.topic.mongo_helper import remove_dup_topic_name

from task.lib.chat import ChatService
gpt_service = ChatService()


topics_db = MongoDB(collection="ai_tweet_topics")
tweet_db = MongoDB(collection="tweet")

topic_schema = '''
{
    tweets: [
        {
            id_str: '123',
            full_text: 'xxxx',
            score: 0.9,
            data_type: [1,2,3],
            rank: 1,
            favorite_count: 100,
            view_count: 100,
            create_time: ''
        }
    ]    
    max_keyword_score: 1,
    avg_keyword_score: 1,
    keyword_create_time: 'most recent',
    keyword_rank: 'recent rank'

    #先忽略keywords
    keywords = [
        {
            keyword_name: '<PERSON>',
            event: 'xxxx'
            create_time: ''
            rank: '1'
            score: '0.9 #twitter formula real time',
            # event_confidence: ''

        },
        {
            keyword_name: '<PERSON><PERSON><PERSON>',
            event: 'xxxx'
            create_time: ''
            rank: '1'
            score: '0.9 #twitter formula real time'
        },
    ]
}
'''


def check_topic_tweets(kw=''):
    #check topic and tweets relevancy
    
    kw = "Ashley Benefield"
    topics = topics_db.find_data({'search_keyword': kw, 'active': 1})
    df = pd.DataFrame(topics).sort_values(['avg_tweet_stats', 'n_tweets'], ascending=False)
    df.to_csv(f'data/test/{kw}_topics.csv', index=False)

    res = []
    for topic in df.to_dict(orient='records'):        
        tweets = {d['id_str']: d for d in tweet_db.find_data({'id_str': {'$in': topic['tweets']}})}
        temp = [{
            'id_str': id,
            'full_text': tweets[id]['full_text'],
            'views': int(tweets[id]['views'].get('count', 0)),
        }
         for id in topic['tweets']]
        
        tweet = {
            'topic': topic['name'],
            'n_tweets': topic['n_tweets'],
            'avg_tweet_stats': topic['avg_tweet_stats'],            
            'tweets': temp,
        }
        res.append(tweet)

    json.dump(res, open(f'data/test/{kw}_topics_tweets.json', 'w'), indent=4)     


def update_topic():
    

    data = topics_db.find_data({
        'active': 1,
    })

    ct = 0
    
    for d in tqdm(data):
        if 'tweets_info' in d:
            continue
             
        
        temp = {
            "_id": d['_id'],
            'active': 0,
        }
        status = topics_db.update_one(temp)
        if status:
            ct += 1
    print(f'Updated {ct} topics/{len(data)}')

areas = { 
        5: 'artificial intelligence & science',            
        4: 'entertainment & sports',   
        3: 'finance',
        2: 'news & technology',   
    }
function = [{
                "name": "get_category",
                "description": "identify category",
                "parameters": {
                    "type": "object",
                    "properties": {                                              
                        "category": {
                            "type": "integer",
                            "description": f"category from the list: {areas}",

                    }                    
                    },
                    "required": ['category'],
            }}]

def get_area(event='', samples=[], default=2):    
    if not samples:
        return default
    
    prompt = f'''Given below title and related tweet, please identify the title category.
title: "{event}"
Tweet: "{samples[0]}"'''


    con, msg, func = gpt_service.function_call(prompt, functions=function, model='gpt-4o-mini', temperature=0.1, timeout=10) 
    if not con:
        log(f'Error getting area for event {event}: {msg}', level='error')
        return default
    params = func.get('get_category', {})
    if isinstance(params, str):
        params = json.loads(params)

    # log(prompt, '\n', params)    
    return params.get('category', default)
    
    
def update_area():
    res = topics_db.find_data({'area': 2, 'active': 1})    
    
    i = 0
    for d in tqdm(res):
        cat = get_area(event=d['name'], samples=d['sample_contents'])
        if cat != 2:            
            temp = {
                "_id": d['_id'],
                'area': [cat],
            }
            print(d['name'], 'new category: ', cat)
            status = topics_db.update_one(temp)
            if status:
                i += 1
    log(f'Updated {i} area for {len(res)} topics', level='warning')


    # with open('data/area.json', 'w') as f:
    #     json.dump(res, f, indent=4)

from bson import ObjectId
from task.server.topic.data import clean_text
def add_favorite_topics_summary():
    with open('data/uat.txt') as f:
        uat = f.readlines()
    dev = pd.read_csv('data/dev.csv').value.to_list()
    ids = [i.strip() for i in set(uat + dev)]
    fix = []

                
    for id in tqdm(ids):
        try:
            data = topics_db.find_data({'_id': ObjectId(id)}, limit=1)
        except:
            continue
        if not data:
            continue
        data = data[0]
        if 'summary' in data and data['summary'] != data['name']:
                continue        
        keyword = data.get('search_keyword', '')
        event = data.get('name', '')
        tids = data.get('tweets', [])
        if not tids:
            continue
        tweets = tweet_db.find_data({'id_str': {'$in': tids}})
        if not tweets:
            continue
        tweets = [[clean_text(t['full_text']),
                t['id_str']                   
                ] 
                for t in tweets]
        df = pd.DataFrame(tweets, columns=['full_text', 'id_str']).drop_duplicates('id_str')
        tweets = df.full_text.to_markdown(index=False)


        prompt = f"""given below tweets searched from (keyword: "{keyword}") and (topic name: "{event}"). 
Task: Help me write a topic summary based on relevant tweets.
#Tweets:<start>
• {tweets}
<end>  
#Requirements:
write a consise but comprehensive topic summary in 50 words and captivating tone.
- Summary starts with brief introduction of topic, followed by observation of main opinions/comments/insights from relevant tweets IF any.
- Do not use words 'topic' 'tweet' explicitly in summary.
"""     
        
        con, msg = gpt_service.chat(prompt, model='gpt-4o-mini', temperature=0.1, timeout=30)    
        if not con:
            log(f'Error generating summary for {id}: {event}', level='error')
            continue
        log(f'INPUT: {prompt}')
        log(f'OUTPUT: {msg}', level='error')
        fix.append(id)

        topics_db.update_one({'_id': ObjectId(id), 'summary': msg})
    with open('data/fix.txt', 'w') as f:
        f.write('\n'.join(fix))

if __name__ == '__main__':
    # add_favorite_topics_summary()

    data = topics_db.find_data({'active': 1, 'summary': {'$exists': True}})
    i = 0
    for d in tqdm(data):
        # if i > 10:
        #     break
        if len(d['summary']) == len(d['name']):            
            i += 1
            topics_db.update_one({'_id': d['_id'], 'active': 0})
            print(d['trace_id'], d['name'], ' | ', d['search_keyword'])
                


    
    

    
    


    



        

        
