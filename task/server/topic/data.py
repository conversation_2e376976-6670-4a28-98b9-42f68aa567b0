import json
import pandas as pd
import re
from datetime import datetime, timezone
from task.lib.utils import datetime_str, log


emoji_pattern = re.compile(
        "["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002700-\U000027BF"  # Dingbats
        u"\U000024C2-\U0001F251" 
        "]+", flags=re.UNICODE
    )

def clean_text(text):
    #remove links
    text = text.replace('\n', ' ').replace('&amp;', '&').split('http')[0].strip()
    # remove Emoji/overlap with chinese chars
    # text = emoji_pattern.sub(r'', text)
    return text


def extract_hashtags(text):    
    # Regex pattern to match hashtags with at least 2 characters
    hashtag_pattern = r'#\w{2,}'

    # Find all hashtags in the tweet
    hashtags = re.findall(hashtag_pattern, text)

    #return hashtags without the '#' character
    return [h[1:] for h in hashtags]


def process_tweets_df(data=[], tweets_file='', min_words=3, verbose=False, test=False, keyword=''):    
    '''returned a processed and ranked dataframe of tweets from json file or mongo data
    '''    
    if tweets_file:
        with open(tweets_file, 'r') as f:
            data = json.load(f)
    if isinstance(data, list):
        df = pd.DataFrame(data)
    elif isinstance(data, pd.DataFrame):
        if len(data) == 0:            
            return
        df = data           
    else:
        print('Invalid tweets data format')
        return
    if test:
        cols = ['id_str', 'full_text']
        df.to_csv(f'data/tweets/{keyword}_original.csv', index=False)   

    df = df.rename(columns={'id_str': 'tweet_id'})    
    #get tweet stats as avg count of likes, retweets, replies, bookmarks...
    # count_cols = [c for c in df.columns if c.endswith('_count')]
    # df['stats'] = df[count_cols].fillna(0).astype(int).mean(axis=1).round().astype(int)    
    df['stats'] = 0

    cols = ['tweet_id', 'full_text', 'lang', 'stats', 'score', 'data_type', 'rank', 'favorite_count', 'view_count', 'created_at']
    #zh/english only
    df = df[df.lang.isin(['en', 'zh'])][cols]    

    #process text
    df['full_text'] = df['full_text'].apply(clean_text)
    df = df.dropna(subset='full_text')    
    #remove short tweets (can not use str split for zh)
    # df['n_words'] = df.full_text.apply(lambda x: len(x.split()))
    # df = df[df.n_words >= min_words].reset_index(drop=True)      
    df = df[df.full_text.str.len() > 5].reset_index(drop=True)

    #TODO add media keywords 
    
    #extract hashtags
    df['hashtags'] = df['full_text'].apply(extract_hashtags)             

    if df is not None and verbose:        
        print(df.info())
        print(df.head())
        if tweets_file:
            df.to_csv(tweets_file.replace('.json', '.csv'), index=False)
    if test:
        df.to_csv(f'data/tweets/{keyword}_processed.csv', index=False)
    return df


def process_topics_df(topics, tweets_df, keyword={}):
    '''process topics (length 1)  dataframe'''

    topics = topics[topics.columns.drop('name')].rename(columns={
                'customname': 'name',
                'count': 'n_tweets',                
                'representative_docs': 'sample_contents',
                'keywords': 'hashtags',
                })
    
    #ADD extra topics info    
    #utc time
    n = len(topics)
    topics['name'] = topics['name'].str.strip('#')
    topics['created_at'] = datetime_str()
    topics['update_at'] = topics['created_at']
    topics['search_keyword'] = keyword.get('name', '')  
    topics['trace_id'] = keyword.get('trace_id', '')
    #new
    topics['keyword_list'] = [[keyword.get('name', '')]] * n
    topics['trace_id_list'] = [[keyword.get('trace_id', '')]] * n

    topics['keyword_batch_id'] = keyword.get('batch_id')
    topics['keyword_create_time'] = keyword.get('datetime', '')
    try:
        rank = int(keyword.get('rank'))
    except:
        rank = 20
    topics['keyword_rank'] = rank
    topics['keyword_source'] = keyword.get('source', '')
    topics['associated_search_keywords'] = [keyword.get('associated_keywords', [])] * n
    topics['twitter_related_words'] = [keyword.get('twitter_related_words', [])] * n
    topics['original_tweets'] = [keyword.get('twitter_id_list', [])] * n        
    topics['total_tweet_stats'] = 0
    topics['avg_tweet_stats'] = 0
    topics['max_keyword_score'] = keyword.get('score', 0)
    topics['avg_keyword_score'] = keyword.get('score', 0)
    topics['tweets_info'] = [[]] * n    
    topics['category'] = [[]] * n    
    topics['viewpoint_status'] = 1
            
    for i, row in topics.iterrows():
        temp = tweets_df[tweets_df.tweet_id.isin(row['tweets'])]
        if len(temp) == 0:
            continue
        #stat =  retweet + like + fav +view count...
        sum_ = temp.stats.sum()
        topics.loc[i, 'total_tweet_stats'] = sum_
        topics.loc[i, 'avg_tweet_stats'] = sum_ // len(temp)            
        #concate tweets hashtags and add to topic keywords               
        kws = row['hashtags'] or row['representation']
        hashtags = [h for h in set(temp.hashtags.sum()) if h not in kws]
        topics.at[i, 'hashtags'] = kws + hashtags      

        cols = ['tweet_id', 'full_text', 'score', 'data_type', 'rank', 'favorite_count', 'view_count', 'created_at']
        tweets_info = temp[cols].to_dict(orient='records')
        topics.at[i, 'tweets_info'] = tweets_info

        #modify area
        kw_source = keyword.get('source', '')
        prefix = kw_source.split(' ')[0] if kw_source else ''
        if prefix and prefix not in ['twitter', 'google', 'ai&science']:
            topics.at[i, 'category'].append(kw_source)

        elif 'sports' in kw_source or 'entertainment' in kw_source:
            topics.at[i, 'area'] = [4]
        elif 'news' in kw_source:
            topics.at[i, 'area'] = [2]
        elif 'ai&science' in kw_source:
            topics.at[i, 'area'] = [5]
        
        area = topics.at[i, 'area']
        if area:
            topics.at[i, 'category'].append(str(area[0]))
        

    return topics


if __name__ == '__main__':
    tweets_df = process_tweets_df(tweets_file='data/tweets/apple.json', verbose=False)
    
    