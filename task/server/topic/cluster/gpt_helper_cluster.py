import re
import json
from sentence_splitter import SentenceSplitter

from task.lib.chat import ChatService


gpt_service = ChatService()
splitter = SentenceSplitter(language='en')


# 时政科技：政治/地区新闻+科技新闻
# 财经观察：金融领域的新闻
# 娱乐竞技：娱乐新闻+体育新闻
areas = { 
        5: 'AI(artificial intelligence) & neuroscience',            
        4: 'entertainment & sports',   
        3: 'finance',
        2: 'news & technology',   
    }
default_area = 2


def get_gpt_prompts(topics_df, keyword, exclude_outliers=True):
    prompts = []
    #get prompts list
    for i, row in topics_df.iterrows():
        topic = row.Topic
        if exclude_outliers and topic < 0:            
            continue
        docs = '\n• '.join(row['Representative_Docs'])
        reps = [r for r in row['Representation'] if r][:5]               
        # use minimum adjective, yet eye catching, 
        # predicate or verb
        prompt = f"""You are an expert in social media and SEO, please help me extract the main topic/event/theme and topic category covered in the following tweets {f'on #{keyword}' if keyword else ''}.
tweets:<start>
• {docs}
<end>
(most common words appeared in the topic are: {reps})        
Requirement:
- The topic should be SHORT, succinct(such as commonly seen on social media's trending topics) 
- topic is only based on tweets, dont make up content
- prefered formats: SUBJECT verb OBEJECT, ADJ SUBJECT, or SUBJECT ADV
topic:"""    
        prompts.append(prompt)   
    return prompts


topic_function = [{
                "name": "extract_topic_category",
                "description": "get topic and category of the tweets",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "topic": {
                            "type": "string",
                            "description": "main topic/event/theme covered in given tweets",
                        },   
                        "category": {
                            "type": "array",
                            "description": f"one or more categories from the list: {areas}",
                            "items": {
                                "type": "integer",
                                "enum": list(areas.keys())
                            }
                        },
                        "keywords": { #hashtags
                            "type": "string",
                            "description": f"rewrtie the top keywords/phrases list most representative of the topic, extracted from the tweets, join by comma",                            
                        },                       
                    },
                    "required": ["topic", "category", 'keywords'],
                },
            }]


def get_gpt_topics_area(topics_df, keyword, exclude_outliers=True):    
    topic_names = []
    cats = []
    keyword_ls = []
    default_name = keyword if keyword else 'Unknown'
    if exclude_outliers and -1 in topics_df.Topic.values:        
        topic_names.append(default_name)
        cats.append([default_area])
        keyword_ls.append([])

    prompts = get_gpt_prompts(topics_df, keyword, exclude_outliers=exclude_outliers)
    if prompts:
        functions = [topic_function]*len(prompts)
        #openai gcall
        status, res_list = gpt_service.function_call_parallel(prompts, functions, model='gpt-4o-mini', temperature=0.1)
        if not status:
            print('Error getting GPT topics')
            return [], []
        i = 0
        for con, msg, func in res_list:
            if not con:
                func = {}
            
            params = func.get('extract_topic_category', {})
            if params == {}:
                print(f'No params returned for Prompts:\n{prompts[i]}')
            i += 1
            if isinstance(params, str):
                params = json.loads(params)
            # print('PARAMS:', params)
            name = params.get('topic', default_name)
            cat = params.get('category', [default_area])
            kws = params.get('keywords', '')
            if kws:
                kws = [k.strip() for k in kws.split(',')]

            topic_names.append(name)
            cats.append(cat)
            keyword_ls.append(kws)
    return topic_names, cats, keyword_ls


cluster_function = [{
                "name": "extract_topics",
                "description": "extract and group topics from tweets",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "topics":{
                            "type": "array",
                            "description": "list of topics extracted from the tweets",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "topic_name": {
                                        "type": "string",
                                        "description": "topic name",
                                    },   
                                    "tweet_ids": {
                                        "type": "array",
                                        "description": f"tweet ids belong to the topic",
                                        "items": {
                                            "type": "integer",                                
                                        }
                                    },
                                    "category": {
                                        "type": "integer",
                                        "description": f"one category from the list: {areas}",                                        
                                        # "enum": list(areas.keys())                                        
                                    },
                                    # "keywords": { #hashtags
                                    #     "type": "string",
                                    #     "description": f"the top 5 keywords/phrases representative of the topic, extracted from the tweets, join by comma",                            
                                    # },                       
                                },
                                "required": ["topic_name", "tweet_ids", "category"], #keywords
                            }},                    
                    },
                    "required": ["topics"],
                },
            }]

def gpt_cluster_topics(tweets_df, keyword, min_tweets=10):    
    #shorten tweet text for gpt processing
    n_sents = 1
    tweets_df['full_text'] = tweets_df['full_text'].apply(lambda x: ' '.join(splitter.split(x)[:n_sents]))
    
    # min_tweets_req = f'\n- each topic should inclue at least {min_tweets} tweets!' if min_tweets else ''
    req = '''
- each topic should be a main event or subject covered by tweets, dont make up content.
- Each topic should be distinct and contain as much relevant tweets as possible.
- topic name should be SHORT, succinct but descriptive(such as commonly seen on social media's trending page) 
- topic name prefered formats: SUBJECT verb (OBEJECT), ADJ SUBJECT, or SUBJECT ADV
'''
    
    tweets = tweets_df[['tweet_id', 'full_text']].to_markdown(index=False)
    prompt = f"""You are an expert in social media and SEO, please help me perform below task based on following tweets {f'on #{keyword}' if keyword else ''}.
tweets:<start>
• {tweets}
<end>  

Task:
- Try to identify one main topic covered by most of the tweets!
- if exist outlier/unrelated tweets to the main topic, generate auxiliary topics accordingly.
- Generate LESS topics as possible, main topic should include as MUCH relevant tweets as possible!
Requirements:
- topic name should be SHORT, succinct but descriptive (such as commonly seen on social media's trending page) 
- topic name prefered formats: SUBJECT verb (OBEJECT), ADJ SUBJECT, or SUBJECT ADV
"""    
    
    
    print(prompt)
    con, msg, func = gpt_service.function_call(prompt, functions=cluster_function, model='gpt-4o-mini', temperature=0.1, timeout=30)
    if not con:
        print('Error getting GPT topics')
        return []
    params = func.get('extract_topics', {})
    if isinstance(params, str):
        params = json.loads(params)
    topics = params.get('topics', [])
    return topics
    

if __name__ == '__main__':
    pass