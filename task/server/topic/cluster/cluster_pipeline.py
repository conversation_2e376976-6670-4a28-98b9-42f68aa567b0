from gevent import monkey
monkey.patch_all()

import os
import sys
# add task PYTHON<PERSON>TH to import task
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
import time
import json
import traceback
from tqdm import tqdm

from task.server.topic.data import process_tweets_df, process_topics_df
from task.server.topic.cluster.model import TopicModel
from task.server.topic.mongo_helper import get_unprocessed_keywords, get_tweets_df_from_mongo, save_topics_to_mongo, mark_processed_keywords, mark_inactive_topics
from task.lib.utils import log, upload_log


def tweet_topics_pipeline_multi_keywords(n_keywords=1, keyword_name='', verbose=True, insert_mongo=True, test=False):
    log('\n0.0 Searching unprocessed keywords...')
    keywords = get_unprocessed_keywords(n_keywords, keyword_name=keyword_name, verbose=verbose)
    if not keywords:
        log('==No unprocessed keywords found.', level='warning')
        return
        
    log(f'\n\n==Starting tweets topics pipeline for {len(keywords)} keywords')
    topic_model = TopicModel(openai_embedding=True, verbose=verbose)
    for kw in tqdm(keywords):
        log(f'***Start Keyword: {kw["name"]}!!!')
        status, msg = tweet_topics_pipeline_single_keyword(topic_model, keyword=kw, verbose=verbose, insert_mongo=insert_mongo, test=test)
        upload_log(traceid=kw['trace_id'], success=status, msg=msg)
        if not test and status:
            mark_processed_keywords(keywords=[kw['name']])            
        


def tweet_topics_pipeline_single_keyword(topic_model, keyword={}, tweets_file='', keyword_name='',
                                         verbose=True, insert_mongo=True, test=False):
    try:
        start = time.time()
        #1. LOAD TWEETS DATA
        keyword_name = keyword_name if keyword_name else keyword.get('name', '')
        log(f'\n==1. Processing tweets data for keyword: {keyword_name}')                   
        tweets = []    
        if keyword:                  
            # AFTER MERGE associated keywords, need rank by VIEW_COUNT
            tweets = get_tweets_df_from_mongo(keyword=keyword, rank_by_viewcount=False)                  
        
        df = process_tweets_df(data=tweets, tweets_file=tweets_file, verbose=verbose, test=test, keyword=keyword_name)
        if df is None or len(df) == 0:    
            err = f'No relevant tweets under {keyword_name} to process, please check.'        
            log(err,level='warning')            
            return True, err     

        #2. EXTRACT TOPICS to df then to json
        log(f'\n==2. Extracting topics from {len(df)} tweets data')
        if not keyword_name and tweets_file:
            keyword_name = tweets_file.split('/')[-1].split('.')[0]
        topics = topic_model.get_topics(df, keyword=keyword_name, 
                                        gpt_topics=True, reduce_outliers=True, visualize_topics=False, 
                                        test=test)    
        # topics.to_csv('topics_format.csv', index=False)
        topics = process_topics_df(topics=topics, tweets_df=df, keyword=keyword)

        #3. INSERT topics to json to mongo db
        insert_data = topics.drop(columns=['representation']).to_dict(orient='records')    
        if test:                
            with open(f'data/topics/{keyword_name}_topics_mongo_{time.strftime("%H:%M:%S")}.json', 'w') as f:
                json.dump(insert_data, f, indent=4)

        #if test mode not insert 
        if not test and insert_mongo:
            log(f'\n==3. Saving topics data to mongo db')       
            #mark in topics db for same keyword {active: 0}     
            mark_inactive_topics(search_keyword=keyword_name, verbose=verbose)                 
            status = save_topics_to_mongo(insert_data)
            # if status:                
                #mark in tweets as processed           
            if not status:
                err = f'Error saving topics to mongo db for keyword: {keyword_name}'
                log(err, level='error')
                return False, err     
        log(f'\n==4. Pipeline completed in {time.time()-start:.2f} seconds for keyword: {keyword_name}')
        return True, ''
    
    except Exception as e:
        err = traceback.format_exc()
        msg = f'Pipeline Error: {err} for keyword: {keyword_name}'
        log(msg, level='error')
        return False, msg


if __name__ == '__main__':
    fp = 'data/tweets/trump.json'
    kw = "Tehran"
    verbose = True
    # topic_model = TopicModel(openai_embedding=True, verbose=verbose)
    # tweet_topics_pipeline_single_keyword(topic_model, keyword_name=kw, verbose=verbose, insert_mongo=False, test=True)
    tweet_topics_pipeline_multi_keywords(n_keywords=10, keyword_name=kw, verbose=verbose, insert_mongo=True, test=True)
    

    
