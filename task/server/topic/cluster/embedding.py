import numpy as np
from bertopic.backend import <PERSON><PERSON><PERSON><PERSON>

from task import callWatt<PERSON>T
from task.lib.utils import log


# from bertopic.backend import OpenAIBackend

# embedding_model = OpenAIBackend(None, "text-embedding-ada-002")


def get_openai_embeddings(text, model:str='text-embedding-3-small', dim=1536, batch_size=200)->np.array:
    '''By default, the length of the embedding vector will be 1536 for text-embedding-3-small or 3072 for text-embedding-3-large.
    '''
    default = np.array([]).reshape(-1,1)


    if isinstance(text, str):
        inputs = [text.strip()]
    elif isinstance(text, list):
        #allow empty string
        inputs = [t.strip() if t.strip() else ' ' for t in text]
    else:
        log("openai inputs error: must be a string or a list of strings")
        return default

    #Split docs to batch to avoid rate limits    
    inputs_ls = [inputs[i:i+batch_size] for i in range(0, len(inputs), batch_size)]

    embeddings = []
    for inputs in inputs_ls:
        body = {
            'inputs': inputs,
            'model': model,
            'dimensions': dim
        }
                
        status, code, res = callWattGPT.callOpenaiChannelEmbeddings(body, timeout=30)
        if status:
            try:                    
                embeddings += [data['embedding'] for data in res['result']['data']['data']]                
            except Exception as e:
                log(f"Error getting OpenAI embeddings: {e}")
                return default
        else:            
            log(f"Error getting OpenAI embeddings for inputs: {inputs}: {res}", highlight=True)
            return default
        
    return np.array(embeddings)


class OpenAIEmbedder(BaseEmbedder):
    def __init__(self, embedding_model='text-embedding-3-small', dim=1536, batch_size=100):
        super().__init__()
        self.embedding_model = embedding_model
        self.dim = dim

    def embed(self, documents, verbose=False):
        return get_openai_embeddings(documents, self.embedding_model, dim=self.dim)
    

if __name__ == '__main__':
    docs = ['I love my dog', 'I love my cat', 'I love my pet']
    embeddings = get_openai_embeddings(docs)
    print(embeddings.shape)