import os
import sys
# add task PYTHONPATH to import task
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)

import time
from gevent import monkey
monkey.patch_all()

import random
import numpy as np
import pandas as pd

# from sentence_transformers import SentenceTransformer
from umap import UMAP
from bertopic import BERTopic
from hdbscan import HDBSCAN
from sklearn.feature_extraction.text import CountVectorizer
from bertopic.dimensionality import BaseDimensionalityReduction
from bertopic.vectorizers import ClassTfidfTransformer

from task.lib.utils import log
from task.server.topic.cluster.embedding import OpenAIEmbedder
from task.server.topic.cluster.gpt_helper_cluster import get_gpt_topics_area, default_area


os.makedirs('data/topics', exist_ok=True)
os.makedirs('data/tweets', exist_ok=True)
os.makedirs('data/figs', exist_ok=True)
#reproducible random number gen
random.seed(0)
np.random.seed(0)
    

class TopicModel:
    def __init__(self, openai_embedding=True, reduce_dimension=True, verbose=True):               
        # Pre-calculate embeddings, dimension 384
        self.openai_embedding = openai_embedding
        #TODO add language
        self.embedding_model = OpenAIEmbedder(dim=786, batch_size=100) #if openai_embedding else SentenceTransformer('all-MiniLM-L6-v2')        
        #dimension reduction
        self.umap_model=UMAP(random_state=0) if reduce_dimension else BaseDimensionalityReduction()
        #clustering:  min_cluster_size = min_topic_size; -eom: has a tendency to pick one or two large clusters and then a number of small extra clusters
        #-leaf: small homogeneous clusters, more fine grained clustering than eom
        self.hdbscan_model = HDBSCAN(min_cluster_size=5, metric='euclidean', cluster_selection_method='leaf', prediction_data=True)

        # default representaion - min_df: min word frequency to be selected, -ngram_range: 2
        #TODO add ch tokenizer
        self.vectorizer_model = CountVectorizer(stop_words="english", min_df=1, ngram_range=(1, 2))
        self.ctfidf_model = ClassTfidfTransformer(bm25_weighting=True, reduce_frequent_words=True)
        self.verbose = verbose  
        self.model = self.create_model()


    def create_model(self):         
        if self.verbose:
            log(f"0. Loading Topic model with {'openai' if self.openai_embedding else 'transformer'} embeddings...")  
        return BERTopic(
                        #MODELS
                        embedding_model=self.embedding_model, 
                        umap_model = self.umap_model,
                        hdbscan_model=self.hdbscan_model,   
                        vectorizer_model=self.vectorizer_model,
                        ctfidf_model=self.ctfidf_model,
                        # representation_model=self.representation_model, 
                        
                        #HYPERPARAMS
                        # nr_topics='auto',                                                  
                        top_n_words=10,                                              
                        verbose=self.verbose
                        )
        

    def gpt_representations(self, topics_df, keyword='', exclude_outliers=False):
        topic_names, areas, keywords = get_gpt_topics_area(topics_df, keyword, exclude_outliers=exclude_outliers)
        if not topic_names:
            log('No GPT topics generated', highlight=True, level='warning')
            if topics_df is not None:
                n = len(topics_df)            
                topics_df['area'] = [default_area]*n
                topics_df['keywords'] = []*n
            return topics_df
    
        # if self.verbose:                                  
        log(f'\n==Generated topics: {topic_names}; \nareas: {areas}, \nkeywords: {keywords}', highlight=True)               
        self.model.set_topic_labels(topic_names)   
        topics_df = self.model.get_topic_info()
        # topics_df['customname'] = topic_names
        topics_df['area'] = areas
        topics_df['keywords'] = keywords
        return topics_df

    def get_default_topic(self, df, keyword=''):
        '''merge to one default topic
        '''
        ids = df.tweet_id.to_list()
        samples = df.full_text.iloc[:3].to_list()
        kws = [keyword] if keyword else []        
        data = {'count': len(df), 
                'name': keyword, 
                'customname': keyword,
                'representation': [], 
                'representation_docs': samples,
                'area': [default_area],
                'keywords': kws,
                'tweets': ids,
                }
        data = {k: [v] for k, v in data.items()}            
        return pd.DataFrame(data)


    def get_topics(self, df, keyword='', gpt_topics=True, reduce_outliers=True, visualize_topics=False, test=True):    
        '''
        Extract topics from tweets dataframe
        params:
            df: tweets dataframe
            keyword: search keyword of the tweets
        '''
        if len(df) <= 5:            
            log('Not enough tweets data to perform topic modelling, set as one default topic', level='warning')
            return self.get_default_topic(df, keyword=keyword)   
        #TODO len < 20 no need fit_transform but need go through gpt
            
        docs = df.full_text.to_list()
        # log('1.Extracting topics...', highlight=True)
        if self.openai_embedding:
            embeddings = self.embedding_model.embed(docs)
        else:
            embeddings = self.embedding_model.encode(docs, show_progress_bar=True)        
        topics, _ = self.model.fit_transform(docs, embeddings)
        topics_df = self.model.get_topic_info()
        if self.verbose: 
            print('=Topics: \n', topics_df)
        
        #process Topic = 1
        if -1 in topics:
            if len(topics_df) == 1: 
                topics_df['Topic'] = 0                       
            else:
                #reduce outliers: reassign outliers to topics
                if reduce_outliers:
                    try:
                        topics = self.model.reduce_outliers(docs, topics, strategy='embeddings', embeddings=embeddings)
                        self.model.update_topics(docs, topics=topics)
                        topics_df = self.model.get_topic_info()
                        if self.verbose:
                            print('=Reassign outliers to Topics: \n', topics_df)
                    except Exception as e:
                        log(f'Error reducing outliers for {keyword}.', level='warning')
                topics_df = topics_df[topics_df.Topic != -1]            
                        
        #TODO reduce_outliers=False logic need change topics, gpt_representaions
        #get tweets ids for each topic        
        df['topic'] = topics        
        topic_tweets = df.groupby('topic')['tweet_id'].apply(list).to_dict()    
        topic_docs = {}

        if gpt_topics:       
            # #REMOVE dup texts and update rep tweets for topic gen (# want to use most recent tweets, but tweet.create_at not standard format)
            # sample_size = 5
            # if len(df) > 2:
            #     for topic, ids in topic_tweets.items():                           
            #         sample_contents = df.set_index('tweet_id').loc[ids].drop_duplicates('full_text').full_text.to_list()[:sample_size]                                                                    
            #         topic_docs[topic] = sample_contents                
            #         topics_df.set_index('Topic', inplace=True)
            #         topics_df.at[topic, 'Representative_Docs'] = sample_contents  
            #         topics_df.reset_index(inplace=True)                   
                        
            topics_df = self.gpt_representations(topics_df, keyword=keyword, exclude_outliers=False)

        if visualize_topics:
            try:
                fig1 = self.model.visualize_topics(custom_labels=gpt_topics)
                fig2 = self.model.visualize_hierarchy(custom_labels=gpt_topics)
                fig3 = self.model.visualize_documents(docs, reduced_embeddings=embeddings, custom_labels=gpt_topics)
                if test:
                    fig1.write_html(f'data/figs/{keyword}_topics.html')
                    fig2.write_html(f'data/figs/{keyword}_hierarchy.html')
                    fig3.write_html(f'data/figs/{keyword}_docs.html')
            except Exception as e:
                log(f'Error visualizing topics', level='warning')          

        #lowercase column name
        topics_df.columns = topics_df.columns.str.lower()
        topics_df = topics_df.set_index('topic')      
        #TODO order by relevancy, not by oringal order 
        topics_df['tweets'] = topic_tweets
        if topic_docs:
            topics_df['representative_docs'] = topic_docs

        if keyword:
            topics_df['representation'] = topics_df['representation'].apply(lambda x: [keyword] + [r for r in x if r and r != keyword])
        if test:
            topics_df.to_csv(f'data/topics/{keyword}_topics_{time.strftime("%H:%M:%S")}.csv', index=False)
        
        return topics_df
    

if __name__ == '__main__':    
    from task.server.topic.data import process_tweets_df
    pt = 'data/tweets/taylor swift.json'
    df = process_tweets_df(tweets_file=pt, verbose=True)
    start = time.time()
    model = TopicModel(openai_embedding=True)
    print(f'Model load time: {time.time()-start:.2f}')
    start = time.time()
    keyword = pt.split('/')[-1].split('.')[0]
    topics_df = model.get_topics(df, keyword, gpt_topics=True, reduce_outliers=True, visualize_topics=True)    
    print(f'Get topics time: {time.time()-start:.2f}')
    
