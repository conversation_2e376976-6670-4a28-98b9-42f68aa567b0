from gevent import monkey
monkey.patch_all()

import os
import sys
# add task PY<PERSON><PERSON><PERSON>TH to import task
pythonpath = '/'.join(os.path.dirname(os.path.abspath(__file__)).split('/')[:-3])
sys.path.append(pythonpath)
import time
import json
import traceback
from tqdm import tqdm
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from task.server.topic.data import process_tweets_df, process_topics_df
from task.server.topic.mongo_helper import get_unprocessed_keywords, get_tweets_df_from_mongo, save_topics_to_mongo, \
    mark_processed_keywords, remove_dup_topic
from task.server.topic.gpt_helper import gpt_filter_topic, gpt_group_events
from task.lib.utils import log, upload_log, TOP_SOURCE



def tweet_topics_pipeline_multi_keywords(n_keywords=1, keyword_name='', n_thread=2, verbose=True, insert_mongo=True, test=False, group_events=True):
    log('\n0.0 Searching unprocessed keywords...')
    keywords = get_unprocessed_keywords(n_keywords, is_english_chinese=True, keyword_name=keyword_name, last_n_hours=6, verbose=verbose)
    if not keywords:
        log('==No unprocessed keywords found.', level='warning')
        return
    
    
    if group_events:
        #Group/update similar events for lastest batch_ids
        group_n_batch = 1   
        batch_ids = list(set([kw['batch_id'] for kw in keywords]))
        #batch id from small to large
        batch_ids = sorted(batch_ids)[-group_n_batch:]     
        log(f'\n==Group similar events for latest batch_ids:{batch_ids}')   
        for batch_id in batch_ids: 
            #every N minute refresh event group
            overwrite = datetime.now().minute % 5 == 0
            status, kw_event = gpt_group_events(batch_id=batch_id, overwrite=overwrite, verbose=verbose, min_events=2)
            #above function ard got error log        

    
    # log(f'\n\n==Starting tweets topics pipeline for {len(keywords)} keywords')    
    # for kw in tqdm(keywords):        
    #     kw_name = kw.get('name', '')
    #     log(f'***Start Keyword: {kw_name}!!!')
    #     status, msg = tweet_topics_pipeline_single_keyword(keyword=kw, verbose=verbose, insert_mongo=insert_mongo, test=test)        
    #     if not test and status:
    #         mark_processed_keywords(keywords=[kw['name']])       
    #     if not status:
    #         mark_processed_keywords(kw_ids=[kw['_id']], custom_status='error') 
    #         upload_log(traceid=kw['trace_id'], success=status, msg=msg, data={'keyword': kw_name}, level='critical')   


    def tweet_topics_pipeline_single_keyword_task(kw):
        return tweet_topics_pipeline_single_keyword(keyword=kw, verbose=verbose, insert_mongo=insert_mongo, test=test)
    
    with ThreadPoolExecutor(max_workers=n_thread) as executor:
        try:            
            results = executor.map(tweet_topics_pipeline_single_keyword_task, keywords)
            
        except:
            err = traceback.format_exc()            
            log(err, level='error')
            upload_log(traceid=0, success=False, msg=err, level='critical')   
            

def tweet_topics_pipeline_single_keyword(keyword={}, tweets_file='', keyword_name='',
                                         verbose=True, insert_mongo=True, test=False):
    try:
        start = time.time()
        #1. LOAD TWEETS DATA
        keyword_name = keyword_name if keyword_name else keyword.get('name', '')
        event = keyword.get('associated_keywords')[0] if keyword.get('associated_keywords') else ''

        log(f'\n==1. Processing tweets data for keyword: {keyword_name}, event: {event}')                   
        tweets = []    
        if keyword:                  
            # AFTER MERGE associated keywords, need rank by VIEW_COUNT
            tweets = get_tweets_df_from_mongo(keyword=keyword, rank_by_viewcount=False)                  
        
        df = process_tweets_df(data=tweets, tweets_file=tweets_file, min_words=2, verbose=verbose, test=test, keyword=keyword_name)
        if df is None or len(df) == 0:    
            err = f'No relevant tweets under {keyword_name} to process, please check.'        
            log(err,level='warning')  
            upload_log(traceid=keyword.get('trace_id'), success=True, msg=err, level='warn')    
            mark_processed_keywords(keywords=[keyword_name])         
            return True, err     

        #2. EXTRACT TOPICS to df then to json
        log(f'\n==2. Filtering topics from {len(df)} tweets data')
        if not keyword_name and tweets_file:
            keyword_name = tweets_file.split('/')[-1].split('.')[0]      

        topics = gpt_filter_topic(df, event, keyword_info=keyword, verbose=verbose, skip_top_n=30)   
        if topics.iloc[0]['count'] == 0:
            msg=f'Warning: No tweets exists for \"{keyword_name}\" after GPT filtering'
            log(msg, level='warning')
            upload_log(traceid=keyword.get('trace_id'), success=True, msg=msg, level='warn')                   
        
        #if n_tweets == 0, acitve = 0
        topics = process_topics_df(topics=topics, tweets_df=df, keyword=keyword)          
        
        #3. INSERT topics to json to mongo db
        insert_data = topics.drop(columns=['representation']).to_dict(orient='records')    
        
        if test:                
            with open(f'data/topics/{keyword_name}_topics_mongo.json', 'w') as f:
                json.dump(insert_data, f, indent=4)


        if insert_mongo:
            log(f'\n==3. Saving topic data ({len(insert_data[0]["tweets"])} tweets) to mongo db')       
            #mark topics inactive for same keyword/name and previous batch
            topic_name = insert_data[0]['name']      
            is_top_source = insert_data[0].get('keyword_source') == TOP_SOURCE
            current_batch_id = insert_data[0].get('keyword_batch_id', 0) 
                                               
            #remove dup, keep latest            
            insert_new_a, tweets_a, tweets_info_a = remove_dup_topic(keyword=keyword_name, current_batch_id=current_batch_id, 
                                                 keep_top_source=not is_top_source)
            insert_new_b, tweets_b, tweets_info_b = remove_dup_topic(name=topic_name, current_batch_id=current_batch_id, 
                                                 keep_top_source=not is_top_source)
            if not insert_new_a or not insert_new_b:
                insert_data[0]['active'] = 0
                msg = f'Exists topic from [{TOP_SOURCE}] for Keyword: ({keyword_name}) and name: ({topic_name}), set new data active to 0'
                log(msg, level='warning')
                upload_log(traceid=keyword.get('trace_id'), success=True, msg=msg, level='warn')

            #merge prev tweets and tweets info to new data            
            for i, tid in enumerate(tweets_a):
                if tid not in insert_data[0]['tweets']:
                    insert_data[0]['tweets'].append(tid)
                    insert_data[0]['tweets_info'].append(tweets_info_a[i])
            for i, tid in enumerate(tweets_b):
                if tid not in insert_data[0]['tweets']:
                    insert_data[0]['tweets'].append(tid)
                    insert_data[0]['tweets_info'].append(tweets_info_b[i])                    


            status = save_topics_to_mongo(insert_data)            
            #mark processed keywords and log
            if not test and status:
                mark_processed_keywords(keywords=[keyword_name])                                    
            elif not status:
                err = f'Error saving topics to mongo db for keyword: {keyword_name}'
                log(err, level='error')
                mark_processed_keywords(kw_ids=[keyword['_id']], custom_status='error') 
                upload_log(traceid=keyword['trace_id'], success=status, msg=err, data={'keyword': keyword_name}, level='critical')   
                return False, err     
                     
        log(f'\n==4. Pipeline completed in {time.time()-start:.2f} seconds for keyword: {keyword_name}')
        return True, ''
    
    except Exception as e:        
        err = f'Pipeline Error: {traceback.format_exc()} for keyword: {keyword_name}'
        log(err, level='error')
        upload_log(traceid=keyword['trace_id'], success=status, msg=err, data={'keyword': keyword_name}, level='critical')   
        return False, err


if __name__ == '__main__':
    fp = 'data/tweets/trump.json'
    kw = ''
    verbose = True
    
    tweet_topics_pipeline_multi_keywords(n_keywords=1, keyword_name=kw, verbose=verbose, insert_mongo=True, test=True, group_events=False)
    

    
