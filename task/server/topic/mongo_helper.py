import traceback
import re
import pandas as pd
from tqdm import tqdm
from datetime import datetime, timezone
import warnings
warnings.filterwarnings("ignore")

from task.dao.mongo.mongo_db import MongoDB
from task.lib.utils import log, upload_log, datetime_str, TOP_SOURCE


topics_db = MongoDB(collection="ai_tweet_topics")
tweet_db = MongoDB(collection="tweet")
keyword_db = MongoDB(collection="keyword")
ai_keywords_db = MongoDB(collection="ai_keywords")
ai_events_db = MongoDB(collection="ai_events")

en_zh_pattern = re.compile(r'^[\u4E00-\u9FFF\u3400-\u4DBF\uF900-\uFAFF\u2E80-\u2EFF，a-zA-ZÁÉÍÓÚÝÀÈÌÒÙÂÊÎÔÛÃÕÑÄËÏÖÜŸáéíóúýàèìòùâêîôûãõñäëïöüÿ0-9!@#$%^&*()_+=\-{}[\]:;"\'<>,.?/\\|`~ ]+$')
INT64_MAX = 9223372036854775807
default_rank = 10



def check_time_diff(time_str=''):
    try:
        utc = time_str.endswith('Z')

        # Convert the given time string to a datetime object
        pat = '%Y-%m-%dT%H:%M:%S' + ('Z' if utc else '')
        given_time = datetime.strptime(time_str, pat)        

        # Get the current time in UTC    
        if utc:
            given_time = given_time.replace(tzinfo=timezone.utc)
            current_time = datetime.now(timezone.utc)
        else:
            current_time = datetime.now()

        # Check if the difference is over one hour
        diff = current_time - given_time
        diff_hr = diff.total_seconds() / 3600

        return diff_hr
    except Exception as e:
        #time string pass error
        return 100


def get_keyword_score(keyword_df):
    '''【(100 - 当前rank) * 20】  *  【(100 - 历史rank) * 10】 *  【(72 - 热点持续时间)*5 or 为负数则 * 0.5】
    *compute this component afterwards【hashtag下所有帖子 * 0.1】 
    '''
    try:
        score = 1        

        # keyword_df['rank'] = keyword_df['rank'].fillna(100).astype(int)
        # 当前rank
        temp = 100 - int(keyword_df.iloc[0]['rank'])
        if temp > 0:
            score *= temp * 20

        #历史rank
        temp = 100 - int(keyword_df['rank'].max())
        if temp > 0:
            score *= temp * 10
        
        #热点持续时间
        temp = check_time_diff(keyword_df.iloc[-1]['datetime'])
        if temp > 72:
            score *= 0.5
        else:
            score *= (72 - temp) * 5

        return min(int(score), INT64_MAX)
    except Exception as e:        
        err = traceback.format_exc()
        log(f'Error getting keyword score: {err}', level='warning')
        return 0


def get_unprocessed_keywords(n_keywords=1, is_english_chinese=True, keyword_name='', last_n_hours=12, verbose=True)->str:    
    try:
        #status: "1.初始化，2.只有原生词，3.有联想词, 4.进行中， 5.已完成"    
        if keyword_name:
            query = {'name': keyword_name, 'status': '5'}
        else:
            query = {'ai_topic_status': False, 'status': '5'}
        
        #GET all keywords and identify latest batch, remove old batch, check if new batch all processed, add to keywords list.
        # names = keyword_db.get_distinct_values('name', query)        

        kw_data = keyword_db.find_data(query, limit=n_keywords*10, last_n_hours=170 if keyword_name else last_n_hours) #7 days for score
        if not kw_data:
            return [] 
        
        #ORDER keywords by top source, batch_id, rank
        df = pd.DataFrame(kw_data)
        df['rank'] = df['rank'].replace({'': None}).fillna(default_rank).astype(int)
        df['is_top_source'] = df['source'] == TOP_SOURCE
        df = df.sort_values(['is_top_source','batch_id','rank'], ascending=[False, False, True])
        # df.to_csv('data/test_keywords.csv', index=False)
        df = df.drop_duplicates('name', keep='first')
        names = df.name
        
        keywords = []    
        for name in tqdm(names):
            if len(keywords) >= n_keywords:
                break
            
            #skip non english keyword
            if is_english_chinese and en_zh_pattern.match(name) is None:                 
                log(f'- Skipped non-english keyword: {name}')       
                mark_processed_keywords(keywords=[name], verbose=verbose, custom_status='skip')            
                continue
            
            temp_query = query if keyword_name else {'name': name} #'ai_topic_status': False, 
            temp = keyword_db.find_data(temp_query, last_n_hours=24) #score computation
            if not temp:
                continue
            df = pd.DataFrame(temp)               
            #for compute score and rank
            df['rank'] = df['rank'].replace({'': None}).fillna(default_rank).astype(int)   
            #sort by top source, batch_id and only keep top 1      
            df['batch_id'] = df['batch_id'].fillna(0).astype(int)
            df['is_top_source'] = df['source'] == TOP_SOURCE
            df = df.sort_values(['is_top_source','batch_id'], ascending=False)               
            

            #get keyword ranking score   
            score = get_keyword_score(df)        
            if not keyword_name:
                df = df[df.ai_topic_status == False]  

            if df is None or len(df) < 1:
                continue
            #remove older batch for same keyword
            latest_trace_id = df.iloc[0]['trace_id']                       
            trace_ids = list(df.trace_id.unique())
            if len(trace_ids) > 1:
                #merge all tweet ids for same keyword and remove duplicate
                merge_ids = df.twitter_id_list.explode().tolist()
                seen = set()
                unique_ids = [x for x in merge_ids if not (x in seen or seen.add(x))]
                unique_ids = [x for x in unique_ids if isinstance(x, str)]
                
                if unique_ids:
                    # log(unique_ids, level='test')
                    df.at[0, 'twitter_id_list'] = unique_ids
                trace_ids.remove(latest_trace_id)
                # print(trace_ids)
                if verbose: 
                    log(f'- Skipped older traceids for keyword: {name}')
                mark_processed_keywords(query={'trace_id': {'$in': trace_ids}}, verbose=verbose, custom_status='skip')
            
            
            #if any unprocessed keyword, continue if expired otherwise wait for whole batch
            if any(df.status != '5'):            
                temp = df[df.status != '5']
                temp['expire'] = df.datetime.apply(lambda x: check_time_diff(x) > 0.5)
                #if all unprocess keywords is expired, ignore them
                if temp.expire.all():
                    df = df[df.status == '5']                
                else:                
                    log(f'- Skipped uncompleted batch for keyword: {name}')
                    continue
                        
            #merge id, and keywords          
            cols = ['twitter_id_list', 'associated_keywords', 'twitter_related_words']        
            df.reset_index(drop=True, inplace=True)
            for col in cols:
                if col in df.columns:
                    # val = list(set(sum(df[col].to_list(), [])))    
                    val = list(set(sum([x for x in df[col] if isinstance(x, list)], [])))            
                    df.at[0, col] = val
            
            tweet_rank = {}
            for ls in df['twitter_id_list']:
                for i, id in enumerate(ls):
                    tweet_rank[id] = i + 1

            
            # score add【hashtag下所有帖子 * 0.1】 
            n_tweet = len(df.iloc[0].twitter_id_list)
            if n_tweet:
                score *= n_tweet * 0.1
            df['score'] = min(int(score), INT64_MAX)

            if n_tweet < 1:
                #still need mark as processed
                n_keywords += 1

            keyword = df.iloc[0].to_dict()
            keyword['tweet_rank'] = tweet_rank
            keywords.append(keyword)
                                 
        return keywords
    except Exception as e:
        err = traceback.format_exc()
        log(f'Error getting unprocessed keywords: {err}', level='error')
        return []


def get_keywords_info(keywords=''):

    keyword = {}
    return keyword


def mark_processed_keywords(kw_ids=[], keywords=[], query={}, verbose=True, custom_status=''):    
    '''kw_ids: [objectId]'''

    #mark keywords as processed
    #by query condition
    if query:
        data = keyword_db.find_data(query, last_n_hours=24)
        if data:
            kw_ids = [d['_id'] for d in data]            

    #by id
    success_count = 0
    for id in kw_ids:
        temp = {
                "_id": id,
                'ai_topic_status': custom_status if custom_status else True
            }       
        
        status = keyword_db.update_one(temp)
        if status:
            success_count += 1
    if kw_ids and verbose:
        log(f'Marked {success_count}/{len(kw_ids)} keywords as processed')

    #by name
    success_count = 0    
    for kw in keywords:
        #mark all keyword as processed no matter status
        data = keyword_db.find_data({'name': kw, 'ai_topic_status': False}, last_n_hours=24) #'status': '5', 
        for d in data:
            temp = {
                "_id": d['_id'],
                'ai_topic_status': custom_status if custom_status else True
            }
            status = keyword_db.update_one(temp)
            if status:
                success_count += 1
    if keywords and verbose:
        log(f'Marked {success_count}/ {len(keywords)} keywords as processed: {keywords}')


def mark_inactive_topics(search_keyword='', name='', current_batch_id='', verbose=True):    
    '''mark topics as inactive for same keyword/name and previous batch'''    
    success_count = 0        
    queries = []    

    #remove characters with meaning in regex: $ *
    if search_keyword:
        search_keyword = search_keyword.strip('#').replace('$', '\$').replace('*', '\*')
        queries += [{'search_keyword': re.compile(word, re.IGNORECASE), 'active': 1} for word in [search_keyword, f'#{search_keyword}']]     
    if name:
        name = name.strip('#').replace('$', '\$').replace('*', '\*')
        queries += [{'name': re.compile(word, re.IGNORECASE), 'active': 1} for word in [name, f'#{name}']]  

    for query in queries:
        query.update({'keyword_batch_id': {'$ne': current_batch_id}})
        data = topics_db.find_data(query)
        for d in data:
            temp = {
                "_id": d['_id'],
                'active': 0
            }
            status = topics_db.update_one(temp)
            if status:
                success_count += 1
    if verbose:
        log(f'Marked {success_count} topics under keyword: "{search_keyword}" or name: "{name}" as processed')



def get_data_type(di: dict):
    '''1. 低粉高赞:低于2w粉 当前帖子高于500赞
2. 高赞:高于5w赞
3. 高浏览:高于10w
4. 高转发:高于10w
5. 高言论:高于1w
    '''
    types = []
    if di.get('favorite_count', 0) > 500:
        if di['user'].get('followers_count', 0) < 20000:
            types.append(1)
    if di.get('favorite_count', 0) > 50000:
        types.append(2)
    if int(di['views'].get('count', 0)) > 100000:
        types.append(3)
    if di.get('retweet_count', 0) > 100000:
        types.append(4)
    if di.get('reply_count', 0) > 10000:
        types.append(5)
    return types


def get_tweet_score(di:dict, data_type=[]):
    '''【点赞 * 30】 * 【转发 * 20】 * 【回复 * 1】 * 【(有图 or 有视频) * 1 无则 * 0.5】 * 
      【(72 - 热点持续时间)*5 or 为负数则 * 0.5】*【符合低分高赞 * 1 不符合则*0.5】'''
    
    try:
        score = 1

        fav = int(di.get('favorite_count', 0))
        if fav:
            score *= fav * 30

        retweet = int(di.get('retweet_count', 0))
        if retweet:
            score *= retweet * 20

        reply = int(di.get('reply_count', 0))
        if reply:
            score *= reply

        media = di.get('media')
        if not media:
            score *= 0.5

        #热点持续时间 改成 发帖时间
        create_time = di.get('created_at', '')        
        hr_since_create = check_time_diff(create_time)
        if hr_since_create < 72:
            score *= (72 - hr_since_create) * 5

        if 1 not in data_type:
            score *= 0.5

        return min(int(score), INT64_MAX)
    except Exception as e:
        err = traceback.format_exc()
        log(f'Error getting tweet score: {err}', level='warning')
        return 0

    


def get_tweets_df_from_mongo(keyword={}, rank_by_viewcount=False):
    '''
    Get tweets data from mongo db for keyword, merge keyword and simple rank
    '''

    if not keyword:
        return
    
    keyword_name = keyword.get('name', '')
    try:
        #1. find mongo data where search_keyword is keyword and crawl status is 5/completed
        tweets = []                         
        rank_ids = keyword.get('twitter_id_list', [])                
        if rank_ids:
            #allow processes tweets for other keywords to reuse here
            tweets = tweet_db.find_data({'id_str': {'$in': rank_ids}}, last_n_hours=24) #  {ai_topic_status': False}          
        if not tweets:
            log(f'No tweets found for keyword: {keyword_name}', level='warning')
            return
        
        #2. process tweets data
        processed = []
        view_counts = {}    
        tweet_rank = keyword.get('tweet_rank', {})
        default_rank = len(rank_ids) + 1
        for t in tweets:   
            #standardize time str to utc, same as keyword.datetime
            t['created_at'] = datetime.strptime(t['created_at'], '%a %b %d %H:%M:%S %z %Y').strftime('%Y-%m-%dT%H:%M:%SZ')

            #default use last insert tweet for same id str
            try:
                view_count = int(t.get('views', {}).get('count', 0))
            except:
                view_count = 0
            
            if t['id_str'] in view_counts:
                #each id use max view count
                view_counts[t['id_str']] = max(view_counts[t['id_str']], view_count)
                #TODO get media des
                continue

            view_counts[t['id_str']] = view_count
            tweet = {k:v for k,v in t.items() if v is not None and not isinstance(v, dict)}
            media = t.get('ai_media_keywords', {})
            media_des = media.get('photo', ['']) or media.get('video', [''])        

            #add new fileds: score and type
            data_type = get_data_type(t)
            tweet.update(
                {                
                    'media_description': media_des[0],
                    'data_type': data_type,
                    'score': get_tweet_score(t, data_type),
                    'rank': tweet_rank.get(t['id_str'], default_rank)
                }
            )
            processed.append(tweet)

        #3. RANK tweets       
        df = pd.DataFrame(processed).set_index('id_str')
        df['view_count'] = view_counts
        ct = int((df.view_count>0).sum())
        # if ct != len(df):
        #     err = f'Warning: tweets for keyword [{keyword_name}] {ct}/{len(df)} view count > 0.'
        #     log(err, level='warning')

        if rank_by_viewcount and ct/len(df) > 0.5:        
            df = df.sort_values('view_count', ascending=False)        
        #if original id_list contain twitter order
        elif rank_ids:        
            use_rank_ids = [i for i in rank_ids if i in df.index]
            df = df.loc[use_rank_ids]    
        df.reset_index(inplace=True)
            
        return df
    except Exception as e:
        err = f'Error getting tweets data for keyword "{keyword_name}": {traceback.format_exc()}'
        log(err, level='error')
        upload_log(traceid=keyword.get('trace_id'), success=False, msg=err, level='warn')
        return 

    

def save_topics_to_mongo(topics=[]):
    # topics 1 item only
    try:
        for topic in topics:
            #skip group check for certain source
            # if topic['keyword_source'] == TOP_SOURCE:
            #     status = topics_db.insert([topic])
            #     return status 
            
            batch_id = topic['keyword_batch_id']
            keyword = topic['search_keyword']        

            #1. check if KEYWORD & BATCH belong to a event GROUP in ai_events
            query = {
                'batch_id': batch_id,
                'keyword': keyword,            
            }
            
            group_events = ai_events_db.find_data(query=query)                        
            if group_events:          
                ##skipped: update topic name                      
                # group_event = group_events[0]['group_event']
                # topic['name'] = group_event
            
                # 2. if exist topic for keywords in same group
                group_keywords = [i['keyword'] for i in group_events]
                query = {
                    'keyword_batch_id': topic['keyword_batch_id'],
                    'keyword_list': {'$in': group_keywords},
                    'active': 1,
                }
                same_topic = topics_db.find_data(query, limit=1) #assume/use one such data
                if same_topic:                    
                    old = same_topic[0]                            
                    log(f"Merging keyword: '{keyword}' & new topic: '{topic['name']}' to existing Topic: '{old['name']}'...")
                    #merge old and new topic into one topic
                    old['hashtags'] = old.get('hashtags', []) + topic['hashtags']                
                    old['tweets'] = list(set(old['tweets'] + topic['tweets']))
                    tweets_info = {t['tweet_id']: t for t in old['tweets_info']+topic['tweets_info']}
                    old['tweets_info'] = [v for k,v in tweets_info.items()]

                    old['n_tweets'] = len(old['tweets'])
                    #update created_at
                    old['update_at'] = datetime_str()
                    
                    #NEW fields           
                    old['keyword_list'] = old.get('keyword_list', []) + topic['keyword_list']
                    old['trace_id_list'] = old.get('trace_id_list', []) + topic['trace_id_list']

                    old['keyword_create_time'] = topic['keyword_create_time']    

                    # #if new source is TOP_SOURCE, update info; else ignore
                    # if topic['keyword_source'] == TOP_SOURCE:
                    #     old['keyword_source'] = topic['keyword_source']
                    #     #if old also 'top source', compare rank and update
                    #     if old['keyword_source'] == TOP_SOURCE:     
                    #         #if new rank higher, update                       
                    #         if topic['keyword_rank'] < old['keyword_rank']:
                    #             old['search_keyword'] = topic['search_keyword']
                    #             old['keyword_rank'] = topic['keyword_rank']
                    #     else:
                    #         old['search_keyword'] = topic['search_keyword']
                    #         old['keyword_rank'] = topic['keyword_rank']
                    if topic['keyword_rank'] < old['keyword_rank']:
                        old['search_keyword'] = topic['search_keyword']
                        old['keyword_rank'] = topic['keyword_rank']
                        old['keyword_source'] = topic['keyword_source']

                    old['associated_search_keywords'] = old['associated_search_keywords'] + topic['associated_search_keywords']
                    old['twitter_related_words'] = old['twitter_related_words'] + topic['twitter_related_words']
                    old['original_tweets'] = list(set(old['original_tweets'] + topic['original_tweets']))  

                    old['avg_tweet_stats'] = old['avg_tweet_stats'] + topic['avg_tweet_stats'] // 2
                    old['total_tweet_stats'] = old['avg_tweet_stats'] * old['n_tweets']
                    old['max_keyword_score'] = max(old['max_keyword_score'], topic['max_keyword_score'])
                    old['avg_keyword_score'] = max(old['avg_keyword_score'], topic['avg_keyword_score'])                
                    
                    status = topics_db.update_one(old)
                    if not status:
                        err = f'Error updating topic to mongo, keyword:{keyword}, batch_id:{batch_id}'
                        log(err, level='error')
                        upload_log(traceid=topic.get('trace_id'), success=False, msg=err, level='warn')
                        return False
                    return True

            #3. if not satisfy above, insert new topic
            status = topics_db.insert([topic])
            return status
    except Exception as e:
        err = f'Error encountered in save_topics_to_mongo: {e}'
        log(err, level='error')
        return False      
        
    
    if not status:
        log('Error saving topics to mongo', level='error')
        return False    
    return True

def get_re_queries(text, field='name', add_hash=False):   
    if not text:
        return [] 
    
    text = text.strip('#')
    options = [text, f'#{text}'] if add_hash else [text]
    #if contain special char no regex; otherwise full match          
    if text.isalnum():                
        #word boundary is \b{}\b
        return [{field: re.compile(f'^{t}$', re.IGNORECASE), 'active': 1} for t in options]
    else:
        return [{field: t, 'active': 1} for t in options] 
        

def remove_dup_topic(name='', keyword='', current_batch_id=0, keep_top_source=False):    
    '''remove duplicate topic for same name OR keyword (regex contain and case insensitive)
    Args:
        name: str, name of the topic
        keyword: str, keyword of the topic
        keep_top_source: int, keep EXISTING result if source is top source, dont insert new as active topic
    '''
    insert_new = True
    tweets = []
    unique_tweets = set()
    tweets_info = []
    try:        
        res = []                                
        if name:        
            queries = get_re_queries(text=name, field='name', add_hash=False)                  
        elif keyword:
            queries = get_re_queries(text=keyword, field='keyword_list', add_hash=True)    
        else:
            return insert_new, tweets, tweets_info
        
        # log(queries)
        for query in queries:            
            res += topics_db.find_data(query, last_n_hours=24)            

        #if duplicate, keep latest batch (in source = top)
        if len(res) > 0:
            keep_id = 0
            if keep_top_source:
                sort_data = [[r['_id'], 
                                r.get('keyword_source')==TOP_SOURCE, 
                                r.get('keyword_batch_id', 0),
                                r.get('keyword_rank', 0),
                                ] 
                                for r in res]
                sort = pd.DataFrame(sort_data, columns=['id', 'source', 'batch_id', 'rank']).\
                    sort_values(['source', 'batch_id', 'rank'], ascending=[False, False, True])
                keep_id = sort.id.iloc[0]
            i = 0
            
            for r in res:                                
                # # if exist source is top source and within 1hr, keep it; new data active=0 instead
                if r['_id'] == keep_id and r.get('keyword_source')==TOP_SOURCE and abs(current_batch_id - r.get('keyword_batch_id', 0)) < 3600:
                    insert_new = False
                    continue
                temp = {
                    "_id": r['_id'],
                    'update_at': datetime_str(),
                    'active': 0
                }
                topics_db.update_one(temp)
                i += 1
                #merge all tweets and tweets_info
                for i, t in enumerate(r['tweets']):
                    if t not in unique_tweets:
                        tweets.append(t)
                        unique_tweets.add(t)
                        tweets_info.append(r['tweets_info'][i])

            log(f'Mark inactive {i}/{len(res)} duplicate topic for name: "{name}"; keyword: "{keyword}".')
            
    except Exception as e:
        log(f'Error removing duplicate topic: {traceback.format_exc()}', level='error')
    
    return insert_new, tweets, tweets_info


if __name__ == '__main__':
    kws = [
        '#방시혁_2주준다_민희진_복귀시켜라'.strip('#$'),
        '你好嘛', 
        '人工智能',
        '习近平',
        '繁体',
        '在这里输入需要转换的简体字，即可自动进行繁体字在线转换',
        'hello你好'

           ]


    for kw in kws:
        res = en_zh_pattern.match(kw)
        print(res)