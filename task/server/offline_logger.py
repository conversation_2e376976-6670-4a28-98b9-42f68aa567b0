import logging

def setup_logger(log_file='hoc.log', logger_name = 'ai-hoc', log_level=logging.INFO):
    # Create a logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)

    # Avoid adding duplicate handlers if the logger is already configured
    if not logger.hasHandlers():
        # Create handlers: one for file and one for console
        file_handler = logging.FileHandler(log_file)
        console_handler = logging.StreamHandler()

        # Set the log level for each handler
        file_handler.setLevel(log_level)
        console_handler.setLevel(log_level)

        # Create a formatter and set it for both handlers
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)

        # Add the handlers to the logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    return logger

