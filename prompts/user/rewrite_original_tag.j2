Follow the steps provided:

{% if event is defined and event %}
## Event Context
Read and understand the context about the hotspot event
{{event}}
{% endif %}]

{% if persona is defined and persona %}
## Character Profile:
<character>
{{persona}}
</character>
writing style: {{writing_style}}
{% if history_x_list is defined and history_x_list %}
Below are some posts by the person as reference for better writing style mimicry:
{{ history_x_list }}
{% endif %}
{% else %}
## Create a virtual character sheet:
   - Occupation or field of expertise
   - Personality traits (e.g., sarcastic, optimistic, analytical)
   - Writing style (e.g., formal, casual, humorous, sarcastic)
   - Particular interests or biases related to the event
{% endif %}


{% if strategies is defined and strategies %}
Think step by step to craft a compelling tweet based on the event and strategies:
a) Analyze the tweets and event:
   - Identify main points, sentiments, and common themes
   - Note unique or insightful perspectives
   - Observe interesting expressions

b) Determine the main point you want to express about the event

c) Use the strategies provided to guide your tweet creation:
   - {{strategies}}

{% if target_means and target_means == 'replying to a post' %}
d) Consider how your character would respond based on their profile, then craft the reply:
   - Include surprising or controversial elements, and also align with the original tweet
   - Reflect your character's personality and style
{% else %}
d) Craft the tweet:
   - Utilize the strategies to enhance your tweet
   - Reflect your character's personality and style
{% endif %}

e) Impact Evaluation:
   - Consider potential reception and engagement
   - Assess the likelihood of likes, retweets, and comments

f) Ensure the tweet is within specified limits:
   - Character limit: {{token_limit}}
   - Word limit: {{ word_limit }}
   - Hashtag limit: {{tag_num}}
{% else %}
    {% include 'user/native_tag.j2' %}
{% endif %}

## Output Requirements:
    - Write in {{ language }} language
    - Do not append hashtags to the content itself
    - Remember to maintain the word limit and style constraints
    {% if custom != 'native' %}
    - User has important instruction:"{{ custom }}".
    Supersede other requirements when there is conflict.
    If the instruction is in another language, output should remain {{language}}.
    If the instruction itself contains command to change the language, change the language from {{language}} to the command.
    {% endif %}
    - Format your output as JSON with the following structure:
{
  "content": "Your newly crafted tweet without hashtags",
  "tags": ["tag1", "tag2"]
}

