{% if custom == 'native' %}
{% include 'user/native_tag.j2' %}
{% else %}
{% include 'user/custom.j2' %}
{% endif %}

- Write in {{ language }} language.
- Remember to maintain the persona's voice and style throughout the rewritten post, ensuring it feels authentic and true to the given characteristics.
- DO NOT add hashtags or any other additional content to the post.
{% if custom != 'native' %}
- User has important instruction:"{{ custom }}".
Supersede other requirements when there is conflict.
If the instruction is in another language, output should remain {{language}}.
If the instruction itself contains command to change the language, change the language from {{language}} to the command.
{% endif %}

Output your rewritten post in JSON format:

{
  "content": "Your rewritten post here"
}

- Generate the content with a strict character count between {{ token_limit }}, including spaces.
- If the original post is long, shorten your rewritten post to fit both the character and word limit.
