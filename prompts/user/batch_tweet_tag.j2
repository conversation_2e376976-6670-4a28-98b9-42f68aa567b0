Here are the details you need to consider:
## Original post:
<original_post>
{{tweet_content}}
</original_post>

## Writing styles:
You need to rewrite the original post for all the writing styles below and remember the choices. Make 3 diverse versions. One of it could be adverse or negative.
writing_styles_mapping = {{ writing_style }}

Instructions for rewriting the post:
1. Carefully read the original post and identify its core entry point or jumping-off point that makes the post interesting and engaging.
2. Study the given writing style. Create a fictional persona, and the typical way of expressing themselves.
3. Maintain the core idea but adapting it to match the persona's characteristics. Consider:
   - Use vocabulary and expressions that the persona would likely use.
   - Adjust the tone to match the persona's typical emotional state and communication style.
   - Incorporate the persona's unique perspective on the topic.
4. Remove the links from the original post and never put them in the rewritten posts.
5. If the writing style suggests a more expressive or emotional approach, consider using emojis to enhance the post's emotional impact and visual appeal. Use emojis sparingly and appropriately to match the persona's style.
6. Write in the language {{ language }} as the original post.

Generate 2-4 relevant hashtags (without # symbol) based on each of the content of the rewritten posts. These hashtags should be suitable for social media promotion.
Remember to maintain the persona's voice and style throughout the rewritten posts, ensuring authentic and true to the given characteristics.
Output your rewritten post in JSON format:
{
  "textList": [
    {
      "content": "Rewritten post",
      "writeStyle":[1],
      "tags": ["tag1", "tag2""]
    },
    {
      "content": "Rewritten post",
      "writeStyle":[3],
      "tags": ["tag1", "tag2", "tag3"]
    },
    {
      "content": "Rewritten post",
      "writeStyle":[5],
      "tags": ["tag1", "tag2", "tag3"]
    }
  ]
}

- DO NOT append hashtags to the content.
- writeStyle should be the same as the given writing style number, not the name.
- You will generate 3 diversified versions of the rewritten post based on these styles.
- Generate the content value with a strict character count between {{ token_limit }}, including spaces. Ensure the response adheres strictly to this character range. If it exceeds or falls short, it will be considered invalid.
- If the original post is long, shorten your rewritten post to fit the character limit.
