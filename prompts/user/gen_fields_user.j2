Here is the user query you need to analyze: <user_query> {{user_query}} </user_query>

Please analyze the query according to the following instructions:

- Identify the specific field of interest that best represents query.
- Generate a list of highly relevant and impactful associated keywords and phrases for each field [e.g., 'dynamic object tracking' for 'autonomous driving'].
- The keywords should capture both specific, meaningful (or technical) and popular terms, including trending topics, cutting-edge technologies, commonly discussed issues, and frequently searched queries in this field. Ensure the keywords are suitable for finding high-traffic articles, blogs and tweets.
E.g., for the field 'NBA', superstar player names > trending topics like 'injuries'/'career night'/'buzzer beater' > popular team names > game terminologies.
- The number of keywords for each field should not exceed 40. If the field name is narrow, fewer keywords (like 10-15) preferred.
- If user query is in Chinese(no matter simplified or traditional), please generate output in simplified Chinese; Otherwise, output English.
- Ensure the output is in valid JSON format, following the specified structure and guidelines.

{%  if existing_fields %}
Here is the existing fields of the user: {{ existing_fields }}
Check if there is duplicate field in user query
{%  endif %}

Here is an example of successful extraction. Your response should be similar to:
```
{
    "resCode": 200,
    "field": "Multimodal Large Language Models",
    "keywords": [
        "LLaVA",
        "MLLM",
        "Multimodal Learning",
        "Vision-Language Models",
        "VLMs",
        "Multimodal Transformers",
        "Visual Question Answering",
        "VQA",
        "Visual Dialog",
        "Modality Alignment",
        "Cross-Modal Retrieval",
        "Image Captioning",
        "Visual Commonsense Reasoning",
        "Contrastive Learning"
    ]
}
```
