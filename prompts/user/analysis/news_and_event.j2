Your current task is to evaluate and sort the relevance of a given company profile{% if plan_topic %}, marketing goals (topic and description),{% endif %} and a list of materials, and finally output the relevant materials as required, while providing sufficient reasons.

Input:
Company Profile
   {% if name is defined and name %}
   **Name: {{name}}
   {% endif %}
   {% if introduction is defined and introduction %}
   **Introduction: {{introduction}}
   {% endif %}
   {% if keywords is defined and keywords %}
   **Keywords: {{keywords}}.
   {% endif %}
   {% if businessArea is defined and businessArea %}
   **Business Area: {{businessArea}}.
   {% endif %}
   {% if productFeatures is defined and productFeatures %}
   **Product Features: {{productFeatures}}.
   {% endif %}
   {% if brandValues is defined and brandValues %}
   **Brand Values: {{brandValues}}.
   {% endif %}
   {% if targetAudience is defined and targetAudience %}
   **Target Audience:
   {{targetAudience}}.
   {% endif %}
   {% if marketPosition is defined and marketPosition %}
   **Maret Position:
   {{marketPosition}}.
   {% endif %}
   {% if recentNews is defined and recentNews %}
   **Recent News:
   {{recentNews}}.
   {% endif %}
   {% if marketCases is defined and marketCases %}
   **Market Cases:
   {{marketCases}}.
   {% endif %}
   {% if competitor is defined and competitor %}
   **Competitor:
   {{competitor}}.
   {% endif %}

{% if plan_topic is defined and plan_topic and topic_definition is defined and topic_definition %}
Marketing Goals
   Plan topic: {{plan_topic}}({{topic_definition}})
   {% endif %}
   {% if plan_description is defined and plan_description %}
   Description: {{plan_description}}
   {% endif %}

Material List To Be Evaluated
{% if information is defined and information %}
{{information}}
{% endif %}

Analyze the given material list with the above information step by step, following these instructions:

{% if plan_topic %}
Part One: Material assessment and sorting based on Company profile and Marketing goals
    Step 1: for material in the list, conduct relevance assessment in the following dimensions:
            Business fit: The fit between the material content and the company's main business.
            Brand image fit: The fit between the material content and the company's brand image.
            Target audience likelihood: The likelihood that the material attracts the company's target audience.
            Recent news relevance: The relevance of the material content to the company's recent news.
            Marketing goals relevance: The fit between the material and the marketing goals(topic and specific description).
            Timeliness: Whether the popularity and timeliness of the material are in line with the rhythm of the marketing activities.
            Risk assessment: Whether the material has potential negative impact or controversy.
    Step 2: score and sort each material based on the evaluation of the above dimensions(1-100 points, 100 points is the highest).
    Step 3: choose the top 1 material with the highest average score into 'Schedule' and provide detailed reasons for the selection. Use the index number provided in the material list.
    Step 4: {% if source is defined and source==1 %}generate a title(less than 40 words) for the selected material based on its content.{% endif %}{% if source is defined and source==2 %}generate a title in format as: publish a post about xxx(specific event) for xxx(marketing goals) .{% endif %}

Part Two: Material relevance assessment and sorting based on ONLY Company profile
    Step 5: for material list except the selected one in Step 3, conduct relevance assessment with only the company file in the following dimensions:
            Business fit: The fit between the material content and the company's main business.
            Brand image fit: The fit between the material content and the company's brand image.
            Target audience likelihood: The likelihood that the material attracts the company's target audience.
            Recent news relevance: The relevance of the material content to the company's recent news.
            Risk assessment: Whether the material has potential negative impact or controversy.
    Step 6: score and sort each material based on the evaluation of the above dimensions(1-100 points, 100 points is the highest).
    Step 7: choose the top 1 material with the highest average score into 'Suggestion' and provide detailed reasons for the selection. Use the index number provided in the material list.
    Step 8: {% if source is defined and source==1 %}generate a title(less than 40 words) for the selected material based on its content.{% endif %}{% if source is defined and source==2 %}generate a title in format as: publish a post about xxx(specific event).{% endif %}
{% endif %}
{% if not plan_topic %}
Material relevance assessment and sorting based on Company profile
    Step 1: for each material conduct relevance assessment with the company file in the following dimensions:
            Business fit: The fit between the material content and the company's main business.
            Brand image fit: The fit between the material content and the company's brand image.
            {% if targetAudience %}Target audience likelihood: The likelihood that the material attracts the company's target audience.{% endif %}
            {% if recentNews %}Recent news relevance: The relevance of the material content to the company's recent news.{% endif %}
            Risk assessment: Whether the material has potential negative impact or controversy.
    Step 2: score each material based on the average evaluation of the above dimensions(1-100 points, 100 points is the highest) and sort them from high to low.
    Step 3: choose the top 2 materials into 'Suggestion' and provide detailed reasons for the selection. Use the index number provided in the material list.
    Step 4: {% if source is defined and source==1 %}generate a title(less than 40 words) for each of the selected materials based on its content.{% endif %}{% if source is defined and source==2 %}generate a title in format as: Publish a post about xxx(specific event).{% endif %}
{% endif %}

- Ignore the material which is not in English or Chinese.
{% if recentNews and marketCases %}-Output can not contain content from **Recent News and **Market Cases.
{% elif recentNews %}-Output can not contain content from **Recent News
{% elif marketCases %}-Output can not contain content from **Market Cases{% endif %}
- For each selected material, check its index and content to make sure it is correctly from the provided material list.
- For each title, capitalize only the first letter of the first word.

Finally, compile your analysis into a JSON format as required.