Here are the inputs you'll be working with:

Strategies for a successful tweet:
<heat_attribution>
{{heat_attribute}}
</heat_attribution>

{% if context is defined and context %}
Background information of the heat attribution:
<context>
{{context}}
</context>
{% endif %}

The heat attribution is the analysis that indicates why a tweet was popular. It consists of several factors, each with a reason and a score.Higher scores indicate more significant contributions to the tweet's popularity.

You are provided with a target tweet, and you will build on top of it:
<tweet>
{{tweet_content}}
</tweet>

1. Analyze the heat attribution. They are the strategies that made the tweet popular.

2. Consider the compatibility of the heat attribution with the target tweet content, and provide a score based on the compatibility (0-100). 70 or above is considered highly compatible, and can use to craft the tweet.

3. Create an outputPrompt. This should be a detailed prompt to guide LLM to create a popular tweet with similar heat factors. Consider the following:
    a. Create a random virtual character sheet:
       - Personality traits (e.g., sarcastic, optimistic, analytical,etc)
       - Particular interests or biases related to the event
    Reflect the character's personality and style. You could add emojis or other creative elements to enhance the tweet engagement
    b. Incorporates the top-scoring factors from the heat attribution
    c. Analyze the target tweet content:
       - Identify main points, sentiments, and common themes
       - Note unique or insightful perspectives
       - Observe interesting expressions
    d. After considering these, output a detailed prompt on how to create a compelling tweet on the base of the target tweet and the heat attribution

4. Create an outputTweet. This should be a tweet that follows the guidance of the outputPrompt and incorporates the successful elements identified in the heat attribution. Make sure it:

    1. Follow the guidance of the outputPrompt
    2. Within 20-word limit
    3. Ignore the original hashtags and urls

Finally, format your output as a JSON object with two fields: outputPrompt and outputTweet. Your response should be structured as follows:

{
  "score": 0-100, (heat factors compatibility score with the input tweet content)
  "outputPrompt": "Your suggested writing prompt. Informative enough to gain as  much popularity as possible. Within 50 words limit.",
  "outputTweet": "Your example tweet content here (no hashtags)",
  "reason": "Why the heat attributes can or cannot be integrated into the target tweet.",
  "tags": ["hashtag1", "hashtag2"]
}

Ensure that the JSON is properly formatted and that all fields are included.