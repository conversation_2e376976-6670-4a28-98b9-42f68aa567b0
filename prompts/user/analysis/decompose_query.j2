Here is the user's query:
{{ input }}

Task:
Analyze the user's query and break it down into distinct search conditions. Identify the core query content and, if mentioned, any media types or platforms. If the query does not specify a media type or platform, leave those fields empty. 

Keep in mind that there may be multiple media types, authors and platforms.

Current time: {{current_time}}, so if query mentions relative time, you should use this time as the reference. If not date required, do not use it.

Return a JSON object.

Instructions:

- Decompose the query into its constituent parts step by step.
- Clearly identify and extract the main query content into the queryContent field.
- If query is 'all content', 'everything' without mentioning other clues, 'queryContent' is empty.
- Only include entries in mediaType, author, date range and platform if they are explicitly mentioned in the user's query.
- Remember that both mediaType, author and platform can include multiple entries.
- Twitter/tweet is the same as X.
- If date is not mentioned, startDate and endDate are empty.

For example, "retrieve all videos about travelchina in Yunnan on TikTok"

{
  "queryContent": "travelchina in Yunnan",
  "mediaType": ["video"],
  "platform": ["tiktok"],
  "startDate": "",
  "endDate": "",
  "author": []
}

Another example, "retrieve everything about <PERSON> from 2 years ago to 1 year ago"

{
  "queryContent": "<PERSON>",
  "mediaType": [],
  "platform": [],
  "startDate": "2023-04-01T06:29:11Z",
  "endDate": "2024-04-01T06:29:11Z",
  "author": []
}
