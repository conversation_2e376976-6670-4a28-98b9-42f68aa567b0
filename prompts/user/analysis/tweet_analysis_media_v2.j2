You are given the following information:

<tweet_content>
{{tweet_content}}
</tweet_content>

{% if context is defined and context %}
Background information:
<context>
{{context}}
</context>
{% endif %}

{% if video_list is defined and video_list %}
Video content:
<video>
{{video_list}}
</video>
{% endif %}

{% if image_list is defined and image_list %}
Image content:
<image>
{{image_list}}
</image>
{% endif %}

Engagement data:
<stats>
{{stats}}
</stats>

{% if reply_list is defined and reply_list %}
Sample Replies:
<replies>
{{reply_list}}
</replies>
{% endif %}

Author information:
- followers: {{follow_count}}
- Self introduction: {{author_desc}}

Analyze the above information step by step, following these instructions:

1. Content Deconstruction (Chain of Thought)
Analyze the following elements:
- Core topic/emotion/viewpoint
- Expression style and narrative structure
- Media quality and appeal
- Timing and social context
- Interaction characteristics

Note: if tweet content is empty or simple emojis or gibberish, then just focus on the visual content.
- Provide your conclusion of the relationship between producer and audience in the tweet:
The author generated 'xxx' content, to absorb the audience with 'xxx' emotion/viewpoint/request. And successfully drives audience conversion for this product (if there is product to promote in the tweet).

2. Virality Drivers Analysis
The total heat score is {{ heat_score }}. Identify the 6 critical success dimensions that are generally applicable to other tweets: "trending", "emotion contagion", "engagement-driven", "cultural resonance", "clear presentation", "KOL impact".
For each factor, provide:
- dimension name (must be one of the 6 dimensions)
- Impact score (Assign the total score to the 6 dimensions based on the performance, so the sum of the 6 scores should equal the total heat score)
- Explanation and reasoning of the scoring (limit to 15 words)

Note if the tweet content quality is poor, it's likely that the posted video or image is either brainwashing or have some particular appeal; or due to the author's own inertia influence.
If the author's influence is low, the key factor is the content value. If both are low, the tweet is unlikely to go viral.

3. Event context integration

If there is a background context provided, and the author has a specific viewpoint on the event, evaluate the impact of the viewpoint on the tweet. Provide a brief explanation for the viewpoint's impact.
If the author does not have a specific viewpoint, evaluate the impact of the event itself on the tweet. Provide a brief explanation for the event's impact.

4. Tag Analysis
Identify the key tags in the dimensions. Tag types are provided as below. Evaluate the impact of each tag on the tweet's virality on a 1-10 scale. Provide a brief explanation for each tag's impact.
a. Audience Profile
Overall demographic, behavioral, and psychographic characteristics of the audience.
You can define the audience based on political identity(like Moderate Liberal, Conservative Traditionalist, Tech Libertarians, etc),
demographics(Gen Z Explorers,boomers,Coastal Elite,Digital Immigrants,Immigrant Youth, Rural Boomers, etc),
interests(Gaming Enthusiasts, Fitness Enthusiasts,Political Memers, Pet Lovers, Indie Music Fans, Pop Culture Fans, Fanfiction Readers, DIY Hobbyists, etc),
and fans identity(Star Core Fan, Star Casual Fan, Brand Loyalist, Tech Geek, Virtual Idol Fan, Anti-Fan, Challenge Enthusiast Fan, Satirical Hater, Viral Controversy Hater,etc).

One tweet could have multiple audience profiles based on your analysis.

b. Niche Account
Tweet accounts who continuously post a typical type of content that attracts audience under certain need instead of being KOL.
At most 1 tag identified as under "Niche Account".
Some examples: Investment Guru, Entrepreneur Insider, Funny Video Curators,Music News Accounts,Sports News Accounts,Sports Celebrity Aggregators,Tech News Accounts,Movie and TV News Accounts,
AI News Aggregators,Blockchain Aggregators,Dark Humor Meme Aggregators,Meme Aggregators,Cat/Dog Collectors, Meme Curators, etc.

c. Celebrity
You should try to identify the celebrity's identity in the tweet, like "Musician", "Actor", "Influencer", "Politician", "Athlete", "Entrepreneur", "Tech Guru", "Fashion Icon", "Comedian", "Travel Blogger", "Streamer", "Reality Star", "Model", "Writer", "Artist", "Chef", "Designer", "Photographer", "Director", "Author", "Journalist", "TV Host","Food Critic",etc.
There might be multiple celebrities in one tweet.
d. Attitude
Conveyed attitude in social media content, can be either emotional or analytical.
Some examples: "Empathetic", "Provocative", "Inspirational", "Educational", "Entertaining", "Sarcastic", "Critical", "Supportive", "Humorous", "Motivational", "Nostalgic", "Optimistic", "Pessimistic", “Cynical", "Compassionate", etc.
If the tweet has multiple attitudes, just choose the most prominent one.
If the tweet attitude is casual or neutral, just omit this tag.

e. Emotional resonance
Refers to the shared emotional connection or understanding between individuals, evoking mutual feelings, empathy, and deep engagement.
Some examples: Admiration,Humor,Curiosity,Anger,Shock,Agreement,Disagreement,Inspiration,Sarcasm,Irony,Confusion,Anxiety,Pride,Universal Experience,Value Alignment,Inspirational Story,Cultural Memory,Local Resonance,Life Stage,Interest-based

f. Rhetorics & Narratives
Persuasive language techniques and storytelling frameworks to enhance communication and audience engagement.
Some examples: Metaphor,Parallelism,Rhetorical Question,Hypophora,Antithesis,Inversion,Symmetrical Structure,Suspense Sentence,Ambiguity & Pun,Descriptive Adjectives,Rhyme & Rhythm,Simplified Expression,Contextual Language,
Focus Guidance,Authority Building,Credibility Building,Agenda Setting and Diversion,Resonance and Identification,Storytelling Combination

g. Visual-Textual Collaboration
A method combining visuals and text to create engaging, cohesive, and impactful social media content.
Some examples: Attention Grabbing,Memory Enhancement,Persuasiveness Boost,Aesthetic Appeal,Context Enrichment,Focus Guidance,Abstract Clarification,Emotional Amplification,Attention-Grabbing Start,
Core Message Delivery,Data and Facts Support,Time Node Alignment,Multimodal Storytelling,Interactive Design

- Note that the tweet may have multiple tags in each tag type
- Omit the weak tags and focus on the most prominent ones. Limit the total tag number to 3-10.

4. Media Analysis
Focus specifically on the strategy and techniques used in selecting images and videos for the tweet. So you can evaluate the visual content's importance versus text content.

5. Output JSON
Finally, compile your analysis into a JSON format with the following structure:

{
    "heatDimensions": [
        {
            "dimension": Limited to provided 6 dimension names,
            "score": number (Round to one decimal place),
            "reason": string (limit to 15 words)
        }
    ],
    "trendingFactors": [ # aggregation list of all tags
        {
            "tag_name": tag under the tag type,
            "tag_type": limited to the provided tag types,
            "tag_description": the reason the content has such a tag (limit to 15 words)
        }
    ],
    "paradigmDescription": {
        "contentConsumption": the description between producer and audience (limit to 20 words),
        "eventSummary": the context (limit to 20 words), empty if no specific context,
        "opinionStance": the author's viewpoint on the event (limit to 20 words) Please use concise but informative description instead of simple sentiment labels (e.g., positive, negative). Also can indicate that the author represents a certain group, resonating with them deeply. Empty if no clear sentiment or perspective is present
    },
    "visualScore": number (0-100, score of visual content importance versus text content. If text is more important, the percentage cannot exceed 50.)
}

Note:
- Please evaluate tweet in conservative manner when assigning high scores. Only exceptional quality should receive such high ratings.