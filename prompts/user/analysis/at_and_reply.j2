Your current task is to evaluate and sort the relevance of a given company profile{% if plan_topic is defined and plan_topic%}, marketing goals (topic and description),{% endif %} and a series of social media replies, and finally generate a marketing strategy for the most relevant replies, while providing sufficient reasons.

Input:
Company profile
   {% if name is defined and name %}
   Name: {{name}}
   {% endif %}
   {% if introduction is defined and introduction %}
   Introduction: {{introduction}}
   {% endif %}
   {% if keywords is defined and keywords %}
   Keywords: {{keywords}}.
   {% endif %}
   {% if businessArea is defined and businessArea %}
   Business Area: {{businessArea}}.
   {% endif %}
   {% if productFeatures is defined and productFeatures %}
   Product Features: {{productFeatures}}.
   {% endif %}
   {% if brandValues is defined and brandValues %}
   Brand Values: {{brandValues}}.
   {% endif %}
   {% if targetAudience is defined and targetAudience %}
   Target Audience:
   {{targetAudience}}.
   {% endif %}
   {% if marketPosition is defined and marketPosition %}
   Maret Position:
   {{marketPosition}}.
   {% endif %}
   {% if competitor is defined and competitor %}
   Competitor:
   {{competitor}}.
   {% endif %}
   {% if recentNews is defined and recentNews %}
   Recent News:
   {{recentNews}}.
   {% endif %}
   {% if marketCases is defined and marketCases %}
   Market Cases:
   {{marketCases}}.
   {% endif %}

{% if plan_topic is defined and plan_topic and topic_definition is defined and topic_definition %}
Marketing goals
   Plan topic: {{plan_topic}}({{topic_definition}})
   {% if plan_description is defined and plan_description %}
   Description: {{plan_description}}
   {% endif %}
{% endif %}

Reply list to be evaluated
{% if comment_list is defined and comment_list %}
{{comment_list}}
{% endif %}

Analyze the replies with the above information step by step, following these instructions:

Part One: Material relevance assessment and sorting based on Company profile
    Step 1: For each reply, conduct relevance assessment with the company file in the following dimensions:
            Business fit: The fit between the reply content and the company's main business.
            Brand image fit: The fit between the reply content and the company's brand image.
            Target audience likelihood: The likelihood that the reply comes from the company's target audience.
            Recent news relevance: The relevance of the reply content to the company's recent news.
            Risk assessment: Whether the reply has potential negative impact or controversy.
{% if plan_topic is not defined or not plan_topic%}
    Step 2: Score each reply based on the evaluation of the above dimensions(1-100 points, 100 points is the highest), calculate an average score.
    Step 3: Sort the replies from high to low with the score. Use the index number provided in the original reply list.
    step 4: Add the top 5 replies into the 'Suggestion' list, and provide a detailed reasons for the selection.
{% endif %}
{% if plan_topic is defined and plan_topic%}
    Step 2: Score each reply based on the evaluation of the above dimensions(1-100 points, 100 points is the highest), calculate an average score as 'suggestion score'.

Part Two: Further assessment based on Marketing goals
    Step 3: For each reply, further conduct two relevance assessment in the following dimensions:
        Marketing goals relevance: The fit between the reply and the marketing goals(topic and specific description).
        Timeliness: Whether the reply are in line with the rhythm of the marketing activities.
    Step 4: Score each reply based on the evaluation of the two dimensions(1-100 points, 100 points is the highest), then use these two scores and 'suggestion score' to calculate a new average score as 'schedule score'.

Part Three: Final sorting and output
    Step 5: For each reply, compare the tow scores. If the 'schedule score' is higher, add the reply into the 'Schedule', use 'schedule score' as its final score, otherwise, add it into the 'Suggestion' and use 'suggestion score' as its final score.
    Step 6: Sort the 'Schedule' and 'Suggestion' from high to low with the final score. Use the index number provided in the original reply list.
    Step 7: Take three from each of the two lists('Schedule' and 'Suggestion') as final output list, and provide a detailed reasons for the selection.

- If the total number of replies is greater than two, make sure both 'schedule' and 'suggestion' have at least one reply(which means you should make a trade-off when one is empty).{% endif %}

Finally, compile your analysis into a JSON format as required.