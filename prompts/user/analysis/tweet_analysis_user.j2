You are given the following information:

<tweet_content>
{{tweet_content}}
</tweet_content>

{% if context is defined and context %}
potential background information:
<context>
{{context}}
</context>
{% endif %}

tweet engagement data:
<stats>
{{stats}}
</stats>

{% if reply_list is defined and reply_list %}
Here are some replies to the tweet as reference:
<replies>
{{reply_list}}
</replies>
{% endif %}

- author followers count: {{follow_count}}
- author self description: {{author_desc}}

Analyze the above information step by step, following these instructions:

1. Content Deconstruction (Chain of Thought)
Analyze the following elements:
- Core topic/emotion/viewpoint
- Expression style and narrative structure
- Media quality and appeal
- Timing and social context
- Interaction characteristics

2. Virality Drivers Analysis
Evaluate the contribution of each element on a 1-10 scale:
- Content value (informational/entertainment/emotional)
- Expression form (creativity/clarity/resonance)
- Author influence
- Topic relevance
- Interaction quality

Note if the tweet content quality is poor, it's likely due to the author's influence.
If the author's influence is low, the key factor is the content value. If both are low, the tweet is unlikely to go viral.

3. Key Viral Factors
Extract the most critical success factors (at most 4). The factors should be general and applicable to other tweets, not specific to the current tweet.
For each factor, provide:
- Detailed description
- Impact score (1-10)
- Specific contribution to virality

4. Creation Guidelines
Provide the following for new creators:
1. Core element replication tips
2. Common pitfalls to avoid
3. Step-by-step creation guide
4. Optimization suggestions

5. Output JSON
Finally, compile your analysis into a JSON format with the following structure:

{
    "heatAttribution": [
        {
            "factor": string,
            "score": number,
            "reason": string (limit to 20 words)
        }
    ],
    "opinionStance": string
}

Where:
- heatAttribution: List the top factors contributing to the tweet's virality (at most 4 factors).
- opinionStance: The overall sentiment of the tweet. Please use concise but informative description instead of simple sentiment labels (e.g., positive, negative). Also can indicate that the author represents a certain group, resonating with them deeply. Empty if no clear sentiment or perspecitve is present.
Note: 
- Please evaluate tweet in conservative manner when assigning high scores. Only exceptional quality should receive such high ratings.
