A tiktok video are given by its visual frames and audio transcription with the following information:
{% if description is defined and description %}
<video_description>
{{description}}
</video_description>{% endif %}

{% if reply_list is defined and reply_list %}Here are some replies to the tiktok video as reference:
<replies>
{{reply_list}}
</replies>{% endif %}

{% if bgmName is defined and bgmName %}Its background music is named: 《{{bgmName}}》{% endif %}

Author information:
- followers: {{fansNums}}
{% if author_intro is defined and author_intro %}- Self introduction: {{author_intro}}{% endif %}

Analyze the video with above information step by step, following these instructions:

1. Content Deconstruction (Chain of Thought)
Analyze the following elements:
- Core topic/emotion/viewpoint
- Expression style and narrative structure
- Timing and social context
- Interaction characteristics
- Summarize the video content with 2~3 sentences
- Provide your conclusion of the relationship between producer and audience in the video:
The author generated 'xxx' content, to absorb the audience with 'xxx' emotion/viewpoint/request. And successfully drives audience conversion for this product (if there is product to promote in the video).

2. Audience Profile Analysis
Overall demographic, behavioral, and psychographic characteristics of the audience.
You can define the audience based on political identity(like <PERSON><PERSON> Liberal, Conservative Traditionalist, Tech Libertarians, etc),
demographics(Gen Z Explorers,boomers,Coastal Elite,Digital Immigrants,Immigrant Youth, Rural Boomers, etc),
interests(Gaming Enthusiasts, Fitness Enthusiasts,Political Memers, Pet Lovers, Indie Music Fans, Pop Culture Fans, Fanfiction Readers, DIY Hobbyists, etc),
and fans identity(Star Core Fan, Star Casual Fan, Brand Loyalist, Tech Geek, Virtual Idol Fan, Anti-Fan, Challenge Enthusiast Fan, Satirical Hater, Viral Controversy Hater,etc).
One video could have multiple audience profiles based on your analysis.

3. Niche Account Analysis
Video accounts who continuously post a typical type of content that attracts audience under certain need instead of being KOL.
At most 1 tag identified as under "Niche Account".
Some examples: Investment Guru, Entrepreneur Insider, Funny Video Curators,Music News Accounts,Sports News Accounts,Sports Celebrity Aggregators,Tech News Accounts,Movie and TV News Accounts,
AI News Aggregators,Blockchain Aggregators,Dark Humor Meme Aggregators,Meme Aggregators,Cat/Dog Collectors, Meme Curators, etc.
This analysis is based on the Author information. If not enough information, it is empty.

4. Virality Drivers Analysis
The total heat score is {{ heat_score }}. Identify the 6 critical success dimensions that are generally applicable to other videos: "trending topic", "emotion contagion", "language clarity", "strong hook", "Visual appeal", "fitting bgm".
For each factor, provide:
- dimension name (must be one of the 6 dimensions)
- Impact score (Assign the total score to the 6 dimensions based on the performance, so the sum of the 6 scores should equal the total heat score)
- Explanation and reasoning of the scoring (limit to 15 words)

Note if the video content quality is poor, it's likely due to the author's influence.
If the author's influence is low, the key factor is the content value. If both are low, the video is unlikely to go viral.
For bgm fitness, you can try to look over the replies to find if anyone commented on the bgm. If no reply or commented negatively, the bgm fitness is 0.

5. Video Category Analysis
Determine the category of the video based on its content. You should understand the visual and voiceover content of the video to determine its category.
There are categories about performance and creative showcase(like Vibrant choreography, Dance moves, Lip-sync battle, Music cover, Talent showcase, Time-lapse art, Street art, Cosplay transformation, Magic trick reveal),
about fashion and beauty(Creative fashion haul, Glow-up), about humor and entertainment(Comedy skit, Prank reaction, Epic fail, Couple goals), about emotions and stories(Heartfelt story, Emotional reunion, Nostalgia moment),
about pets and kids(Pet antics), about food and challenges(Culinary masterpiece, Food challenge), about exploration and sports(Parkour stunts, Fitness routine, Nature exploration, Travel tour), and about creativity and tips(Slow-motion effect, Life hack demo)
A video should belong to only one category.

6. Event context integration
If there is a background context provided, and the author has a specific viewpoint on the event, evaluate the impact of the viewpoint on the video. Provide a brief explanation for the viewpoint's impact.
If the author does not have a specific viewpoint, evaluate the impact of the event itself on the video. Provide a brief explanation for the event's impact.
Otherwise, just omit this section.

7. Voice-over text Analysis
If the voice-over text provided, you should first checkout the text to sort out the its structure with abstracted phrases(max to 6 phrases) like: ["Dramatic Hook", "Product introduction", "Surprise", "Call for Action"] etc.
Then try to extract the text attitude, text emotion, and text writing style of the voiceover text. Attitude and emotion are one word. Writing style is one sentence in format as 'Use xxxx writing styles'. Noted that they can be empty.
If no voice-over text, just omit this section.

8. Hook Analysis
A hook in a video is a compelling element at the beginning designed to immediately capture the viewer's attention for continue watching.
Analyze the video in both visual and voice-over text ways to find out and summarize its hooks. If there is no hook, just omit this section.

9. Tag Analysis
Identify the key tags in the dimensions. Tag types are provided as below. Evaluate the impact of each tag on the video's virality on a 1-10 scale. Provide a brief explanation for each tag's impact.
a. Celebrity
You should try to identify the celebrity's identities in the video, like "Musician", "Actor", "Influencer", "Politician", "Athlete", "Entrepreneur", "Tech Guru", "Fashion Icon", "Comedian", "Travel Blogger", "Streamer", "Reality Star", "Model", "Writer", "Artist", "Chef", "Designer", "Photographer", "Director", "Author", "Journalist", "TV Host","Food Critic",etc.
There might be multiple celebrities in one video.

b. Attitude
Conveyed attitude in the video, can be either emotional or analytical.
Some examples: "Empathetic", "Provocative", "Inspirational", "Educational", "Entertaining", "Sarcastic", "Critical", "Supportive", "Humorous", "Motivational", "Nostalgic", "Optimistic", "Pessimistic", “Cynical", "Compassionate", etc.
If the video has multiple attitudes, just choose the most prominent one.
If the video attitude is casual or neutral, just omit this tag.

c. Emotional Resonance
Refers to the shared emotional connection or understanding between individuals, evoking mutual feelings, empathy, and deep engagement.
Some examples: Admiration,Humor,Curiosity,Anger,Shock,Agreement,Disagreement,Inspiration,Sarcasm,Irony,Confusion,Anxiety,Pride,Universal Experience,Value Alignment,Inspirational Story,Cultural Memory,Local Resonance,Life Stage,Interest-based

d. Visual Elements
Special spots of the video.
Some examples:  Stunning visual effects, Creative animation, High-energy dance, Jaw-dropping stunt, Challenging trick, Funny moment, Epic fail, Funny challenge, Emotional reaction, Inspiring story, Heartwarming gesture, Nostalgic reference,
Bold fashion choice, Glamorous makeup look, Cute animal moment, Cute couple moments, Wild adventure, Beautiful nature shots, Hidden talent reveal, Impressive skill display, Unbelievable talent, Trending challenge, Unexpected collaboration, Unexpected guest appearance

e. Filming Techniques
The unique filming techniques.
Some examples: Unexpected twist, Eye-catching visuals, Fast-paced editing, Shocking reveal, Emotional punch, Fun choreography, Incredible transformation, etc.

f. Verbal Expression
The special expression of the voice-over text.
Some examples: Catchy Catchphrases，Engaging Dialogue, Snappy One-Liners, Quick-Wit Moments, Bite-Sized Wisdom, Viral Soundbites, Charming Banter, Savvy Wordplay, Pithy Remarks, Catchy Taglines, Expressive Tone, Funny Comebacks, Engaging Storytelling, Pop Culture References, Playful Exaggerations, etc.

g. High BGM Match
Check the replies if anyone mentioned the background music. If so, some examples are: Perfect Beat Sync, BGM Vibes, Epic Drops, Sound Boost, Groove Fit, Viral Beat, etc.
If no one mentioned the bgm in replies, just omit this tag.


- Note that the video may have multiple tags in some tag type
- Omit the weak tags and focus on the most prominent ones. Limit the total tag number to 3-10.

10. Output JSON
Finally, compile your analysis into a JSON format as required.

Note:
- Please evaluate video in conservative manner when assigning high scores. Only exceptional quality should receive such high ratings.