You are given the following information:

<tweet_content>
{{tweet_content}}
</tweet_content>

{% if context is defined and context %}
Background information:
<context>
{{context}}
</context>
{% endif %}

{% if video_list is defined and video_list %}
Video content:
<video>
{{video_list}}
</video>
{% endif %}

{% if image_list is defined and image_list %}
Image content:
<image>
{{image_list}}
</image>
{% endif %}

Engagement data:
<stats>
{{stats}}
</stats>

{% if reply_list is defined and reply_list %}
Sample Replies:
<replies>
{{reply_list}}
</replies>
{% endif %}

Author information:
- followers: {{follow_count}}
- Self introduction: {{author_desc}}

Please follow these steps to analyze the tweet::

1. Content Deconstruction
Analyze the following elements:
- Core topic/emotion/viewpoint
- Expression style and narrative structure
- Media quality and appeal
- Timing and social context
- Interaction characteristics
Note: if tweet content is empty, then just focus on the visual content.

2. Virality Drivers Analysis
Evaluate the contribution of each element on a 1-10 scale:
- Content value (informational/entertainment/emotional)
- Expression form (creativity/clarity/resonance)
- Media impact (visual appeal/storytelling)
- Author influence
- Topic relevance
- Interaction quality

Note if the tweet content quality is poor, it's likely that the posted video or image is either brainwashing or have some particular appeal; or due to the author's influence.
If the author's influence is low, the key factor is the content value. If both are low, the tweet is unlikely to go viral.

3. Key Viral Factors
Identify up to 4 critical success factors that are generally applicable to other tweets. For each factor, provide:
- Detailed description
- Impact score (1-10)
- Specific contribution to virality

4. Media Analysis
Focus specifically on the strategy and techniques used in selecting images and videos for the tweet. Identify up to 4 critical success factors that are generally applicable. For each factor, provide:
- Detailed description
- Impact score (1-10)

 Note: If there isn't much to say about the media content, it's good to have fewer factors. Quality is more important than quantity.

5. Creation Guidelines
Provide the following for new creators:
1. Core element replication tips
2. Common pitfalls to avoid
3. Step-by-step creation guide
4. Optimization suggestions

5. Output JSON
Finally, compile your analysis into a JSON format with the following structure:

{
    "heatAttribution": [
        {
            "factor": string,
            "score": number,
            "reason": string (limit to 20 words)
        }
    ],
    "opinionStance": string,
    "visualScore": number (0-100, score of visual content importance versus text content),
    "mediaContribution": [
        {
            "uuid": string,
            "score": number (how important each media is to the tweet virality),
            "description": string (limit to 30 words)
        }
    ],
    "contentAnalysis": [
        {
            "factor": string,
            "score": number,
            "description": string (detailed analysis of the specific media content, limit to 30 words)
        }
    ]
}

Where:
- heatAttribution: List the top factors contributing to the tweet's virality (at most 4 factors). Note that quality is more important than quantity. It is good to have fewer factors if there isn't much to say.
- opinionStance: The overall sentiment of the tweet. Please use concise but informative description instead of simple sentiment labels (e.g., positive, negative). Also can indicate that the author represents a certain group, resonating with them deeply. Empty if no clear sentiment or perspective is present.
- visualScore: The percentage of visual content contribution (0-100). If text is more important, the percentage cannot exceed 50.
- contentAnalysis: Focus on the strategy of selecting or designing the visual content. List the top factors contributing to the tweet's virality. These factors should be general and applicable to other tweets. Avoid talking about the text content.
- mediaContribution: Analysis of each media contribution to the tweet (use the same uuid for each media resource as provided in the input).

Note: 
- Please evaluate tweet in conservative manner when assigning high scores. Only exceptional quality should receive such high ratings.
- contentAnalysis should focus on the visual content, not the text content.