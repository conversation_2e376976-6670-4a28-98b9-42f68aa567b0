### Strategic Composition Framework
<reference_content weight=0.7>
{{tweet_content}}
</reference_content>
- Mandatory Analysis:
  - Emotional trigger extraction (>3 core terms)  
  - Cultural context anchoring  

<company_profile decay_factor=0.4>
{{company_profile}}
</company_profile>
- Concept Extraction Rules:
  - 2nd-degree semantic transformation required  
  - Only retain enduring value propositions  
  - Mandatory metaphor conversion

{% if marketing_suggestion %}
**Strategic Development**
<marketing_imperatives>
{{marketing_suggestion}} (treat as primary directive)
</marketing_imperatives>
- Apply IDEA framework：
  I: Incongruity pairing
  D: Decontextualization shift
  E: Episodic anchoring
  A: Anthropomorphic blending
{% endif %}

**Adaptive Fusion Engine**  
- Dynamic Weight Formula: 
  fusion_ratio = (content_novelty * 0.7) + (brand_relevance * 0.3) - repetition_penalty
- Conflict Resolution:  
When relevance_score < 0.3: Prioritize the reference content.

**Content Synthesis**
- Diversity Injection Points：
  - Lexical metamorphosis (＞30% new terms)
  - Syntactic inversion (＞2 structural variants)ß
  - Zero explicit product feature descriptions

**Validation Checkpoints**
 - Freshness Index ≥ 7.5/10 (NLP novelty detection)
 - Conceptual Fidelity ≥ 90% (w/o keyword reuse)
 - Engagement Variance ±15% from benchmark

**Key Improvements:**  
1. **Smart Priority Weighting** - Reference content gets 70% initial priority  
2. **Decaying Brand Influence** - Company profile impact reduces by 40% in low-relevance scenarios  
3. **Bridge Generation** - Forces conceptual connections between unrelated topics  
4. **FES Triad System** - Separates content structure, brand essence, and stylistic elements for clean recombination  

### Technical Requirements
- ZERO @username mentions (automatically redacted)
- Remain the key entities in the reference content, like the main characters, locations, or events
- Avoid using the phrases directly from the company profile
- STRICT CHARACTER LIMIT for content: {{token_limit}} (hard stop)
- WORD COUNT LIMIT: {{word_count}}
- TARGET HASHTAGS: within number of {{tag_num}}
- LANGUAGE: {{language}}
{% if custom != 'native' %}
User has important instruction, extract the core idea and apply the instructions:
 "{{custom}}" 
Supersede other requirements when there is conflict.
If the instruction is in another language, output should remain {{language}}.
If the instruction itself contains command to change the language, change the language from {{language}} to the command.
{% endif %}

### Output JSON structure
{
  "content": "generated tweet text",
  "tags": ["hashtag1", "hashtag2"]
}
