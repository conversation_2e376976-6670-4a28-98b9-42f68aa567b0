**Current Company Profile:**
{{ company_profile | toj<PERSON>(indent=2) }}

**User Input:**
{{ user_input }}

{% if url_content %}
**URL Content Analysis:**
The following content was extracted from the provided URL and should be used as valuable context for understanding user intent:

```
{{ url_content }}
```

**URL Content Usage Guidelines:**
- **For Type 1 (Keywords/Marketing)**: Extract relevant keywords, topics, industry terms, marketing concepts, and competitive insights from the URL content
- **For Type 3 (Brand Profile)**: Extract company information, brand values, positioning statements, target audience insights, business descriptions, and competitive analysis from the URL content
- **Integration Strategy**: Combine insights from URL content with user input to provide comprehensive analysis and updates
- **Context Enhancement**: Use URL content to better understand the user's business context, industry landscape, and specific needs

{% endif %}

{% if file_content %}
**Additional Context from Files:**
{{ file_content }}
{% endif %}

{% if detected_urls %}
**Detected URLs:**
{% for url in detected_urls %}
- {{ url }}
{% endfor %}

**SPECIAL HANDLING FOR SINGLE URL INPUTS:**
If user provides only a URL without clear intent or context, you should trigger ALL THREE types:
- **Type 1**: Extract keywords/insights from the URL content for marketing purposes
- **Type 2**: Save the URL to material library for future reference
- **Type 3**: Use URL content to enhance company profile understanding

Only be specific about types when user explicitly states their intent.
{% endif %}

**Instructions:**
Analyze the user input{% if url_content %} and URL content{% endif %} against the current company profile and determine what the user wants to update:

1. **Type 1**: If user wants to update keywords/wordCloud (affects marketing strategies)
   - **CRITICAL**: SMART MERGE with existing data, don't replace completely
   - Current keywords: {{ company_profile.keywords | join(', ') }}
   - Current wordCloud: {% for item in company_profile.wordCloud %}{{ item.name }}({{ item.intensity }}){% if not loop.last %}, {% endif %}{% endfor %}
   - **MERGE LOGIC**: Keep existing + add new + merge similar + remove only if explicitly requested
   {% if url_content %}
   - **URL CONTENT ANALYSIS**: Extract marketing keywords, industry terms, competitive insights, and relevant topics from the URL content to enrich keyword analysis
   {% endif %}

2. **Type 2**: If URLs should be saved to material library
   - **IMPORTANT**: For ambiguous single URL inputs, ALWAYS include this type
   - **Context Clues**: Look for terms like "素材", "资料", "参考", "收藏", "保存", "创作", "灵感" (optional)
   - **Default Behavior**: When in doubt about URL intent, include type 2
   - **Structure**: Must include profileUpdates with ONLY urlList field containing the URLs

3. **Type 3**: If user wants to update other profile fields
   - **CRITICAL**: SMART FUSION with existing data, prioritize new while preserving valuable old context
   - Current profile fields:
     * targetAudience: {{ company_profile.targetAudience }}
     * brandPositioning: {{ company_profile.brandPositioning }}
     * brandValues: {{ company_profile.brandValues }}
     * brandVision: {{ company_profile.brandVision }}
     * coreCompetency: {{ company_profile.coreCompetency }}
     * companyDesc: {{ company_profile.companyDesc }}
     * websiteUrl: {{ company_profile.websiteUrl }}
   - **FUSION LOGIC**: Merge new information with existing context, expand rather than replace
   {% if url_content %}
   - **URL CONTENT ANALYSIS**: Extract company information, brand attributes, target audience insights, competitive positioning, and business descriptions from the URL content to enhance profile updates
   {% endif %}

**Important**: 
- Only include fields that need updating in profileUpdates
- Set unchanged fields to empty values (empty string "", empty list [], empty object {})
- Never update companyName or industry
- Multiple types can be returned if applicable
- **For keywords/wordCloud**: Always merge intelligently with existing data unless user explicitly says to remove something
- **For URLs**: Analyze user context and intent to determine appropriate type(s) - URLs can serve multiple purposes
{% if url_content %}
- **URL Content Integration**: Use the extracted URL content as rich context to better understand user intent and provide more comprehensive updates
{% endif %}

**Reply Field Requirements:**
- **If NO types detected (unclear input)**: Generate comprehensive guidance explaining AI capabilities and how to provide input for each type
- **If types detected**: Generate detailed explanations for each detected type about AI processing and next steps
- **Style**: Professional, detailed, technology-focused, with clear paragraph breaks using \n\n
- **Length**: Comprehensive but engaging, emphasizing AI capabilities and expected outcomes

Return your response in the NEW JSON format with both typeList and reply fields using {{ language }}:

```json
{
  "typeList": [
    {
      "type": 1/2/3,
      "response": "...",
      "profileUpdates": {...}
    }
  ],
  "reply": "详细的转场文案，解释AI处理能力和后续操作建议"
}
``` 