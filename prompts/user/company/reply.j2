## Strategic Decision Architecture
**Brand Contextualization**
<company_profile decay_factor=0.4>
{{company_profile}}
</company_profile>

**Reference reply to your post**
<reference_content weight=0.7>
{{tweet_content}}
</reference_content>
- Mandatory Analysis:
  - Emotional trigger extraction (>3 core terms)  
  - Rhetorical device mapping  
  - Cultural context anchoring  
  - Narrative tension curve cloning

{% if marketing_suggestion %}
**Strategic Development**
<marketing_imperatives>
{{marketing_suggestion}} (treat as primary directive)
</marketing_imperatives>
{% endif %}

**Adaptive Fusion Engine**  
- Dynamic Weight Formula: 
  fusion_ratio = (content_novelty * 0.7) + (brand_relevance * 0.3) - repetition_penalty
- Conflict Resolution:  
When relevance_score < 0.3: Activate BRIDGE GENERATION: "`trending_event` mirrors our `brand_principle` in the physical world"  

**Content Generation Protocol**
- Apply AIDA Model: Attention hook(15s curiosity trigger) × Interest(pain point illustration) × Desire(scenario value) × Action × Adaptation
- Innovation Dimensions Control:
  - Sentence Structure: Question(30%) + anchoring data(25%) + story sparks(45%)
  - Emotional Temperature: Select a level between Professional(1)-Approachable(10) scale based on brand persona

**Compliance Validation**
- Virality Assurance: VIRAL Model Score ≥ 8.2 (Viral/Interesting/Relatable/Actionable/Layered)
- Brand Integrity: Semantic lock (90% core keyword alignment)

### Technical Requirements
- ZERO @username mentions (automatically redacted)ß
- Remain the key entities in the reference content, like the main characters, locations, or events
- STRICT CHARACTER LIMIT for content: {{token_limit}} (hard stop)
- WORD COUNT LIMIT: {{word_count}}
- TARGET HASHTAGS: within number of {{tag_num}}
- LANGUAGE: {{language}}
{% if custom != 'native' %}
User has additional instruction, extract the core content and apply the instructions:
 "{{custom}}" 
Supersede other requirements when there is conflict.
If the instruction is in another language, output should remain {{language}}.
If the instruction itself contains command to change the language, change the language from {{language}} to the command.
{% endif %}

### Output JSON structure
{
  "content": "generated tweet reply",
  "tags": ["hashtag1", "hashtag2"]
}

