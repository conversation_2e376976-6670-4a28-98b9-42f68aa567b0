请根据以下信息，为我的小红书账号生成一个从明天开始，为期30天的发布计划。请综合考虑节假日和品牌特殊日期，并为我规划相应的主题推广活动。

- 当天日期: {{current_date}}
- 需要发布的笔记总数: {{rednote_num}}
- 核心营销方向: {{plan_topic}}
{% if plan_description is defined and plan_description %}
- 具体的运营目标描述: {{plan_description}}
{% endif %}
- 计划周期: {{plan_cycle}}
- 账号诊断报告和营销提案:
{{audit_input}}

请严格按照要求的JSON格式输出, 不能包含任何JSON格式之外的解释性文字、代码块标记或注释。
{
    "rednoteSchedule": [
        {
            "todoTaskDate": "YYYY-MM-DD",
            "todoTaskTitle": "任务标题，不多于30个汉字"
        }
    ]
}
数组元素数量必须严格等于用户输入的rednote_num {{rednote_num}}。