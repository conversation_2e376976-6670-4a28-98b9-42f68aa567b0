Create a comprehensive, structured video script designed to attract and engage a company's target clients through persuasive storytelling and brand alignment. Follow these guidelines precisely:

## VIDEO SPECIFICATIONS

Pace: {{pace}}
Background Music Style: {{bgm_style}}
Narration Tone: {{narration_tone}}
Narration Pace: {{narration_pace}}
Script Style: {{script_style}}
Core Themes: {{core_themes}}
{% if call_to_action is defined and call_to_action %}- Call to Action: {{call_to_action}}{% endif %}

{% if has_reason is defined and has_reason %}

## EVALUATION SUMMARY

Overall Score: {{overall_score}}/10 - {{overall_score_description}}
Brand Fit: {{brand_fit_score}}/10 - {{brand_fit_description}}
Industry Relevance: {{industry_relevance_score}}/10 - {{industry_relevance_description}}
Audience Match: {{audience_match_score}}/10 - {{audience_match_description}}
Risk Assessment: {{risk_assessment_score}}/10 - {{risk_assessment_description}}
Content Timeliness: {{content_timeliness_score}}/10 - {{content_timeliness_description}}
{% endif %}

## SCRIPT DETAILS

Duration: {{duration}} seconds
Video Style: {{video_style}}
Content Overview: {{video_content_description}}
Brand Positioning: {{brand_positioning}}
Core Competency: {{core_competency}}
Target Audience: {{target_audience}}
Desired Viewer Action: {{action}}

## COMPANY PROFILE
<company_profile decay_factor=0.4>
{{company_profile}}
</company_profile>

### Concept Extraction Rules:

Employ 2nd-degree semantic transformations
Highlight enduring value propositions
Integrate metaphorical narratives to reinforce concepts

Deliver the video script strictly in JSON format, structured as follows:
{
  "title": "Catchy and Relevant Video Title (max 140 chars)",
  "desc": "Concise compelling description",
  "shots": [
    {
      "shotId": "1",
      "shotTitle": "Descriptive title (max 140 chars)",
      "shotContent": "Visual elements",
      "transcript": "Narration or text",
      "soundEffect": "Relevant SFX",
      "environment": "Detailed setting",
      "shotType": "Shot type"
    }
    // Additional shots...
  ]
}

## RULES
- Each shot must add unique visual variety, smooth transitions, and reinforce brand positioning, core competencies, and audience resonance.  
- All values in the JSON are required and must be in {{language}}.
- Restrict shot number within 8.