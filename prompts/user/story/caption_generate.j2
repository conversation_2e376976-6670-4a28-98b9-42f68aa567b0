Here are the product highlights you should incorporate into your story:

<product_sellpoints>
{{product_sellpoints}}
</product_sellpoints>

Please create a {{ num }}-scene story based on these product highlights. Each scene should follow a consistent character while showcasing the product's key features. The story should be catchy and have good logic to follow sequentially.

Consider the following:
1. Feature highlight: Highlight a specific product feature in each scene, incorporating it naturally into the story without losing emotional or narrative depth
2. Narrative progression: How does this scene logically follow from the previous one and lead into the next? It's better to have an intriguing short story, with conflict, climax and resolution.
3. If the target of marketing is not a physical product but rather a website or service, it's essential to positively highlight specific selling points through interactions within a scenario and between characters, rather than just presenting a scene. For example, an ERP software ad could show a character resolving an issue using a form on a screen, followed by a relieved smile, or depict a collaborative online interaction among multiple characters.

The final output JSON example with {{ num }} scenes:
{
   "storyTitle": "Ace of Elegance",
   "generatedCaptions":[ # captions for each scene
        "<PERSON><PERSON> discovers perfection with <PERSON><PERSON>'s tennis racket",
        "<PERSON><PERSON>'s swift moves with <PERSON><PERSON>'s lightweight design",
        "<PERSON><PERSON>'s victory with <PERSON><PERSON>'s powerful grip",
        "<PERSON><PERSON>'s triumph with <PERSON><PERSON>'s durable frame",
        "<PERSON><PERSON>'s celebration with <PERSON><PERSON>'s stylish design"
    ]
}
- Don't output the story itself, only the captions for each scene
- Ensure every scene contributes to the overall story arc while highlighting a unique product feature.
- The generated captions should be compelling 9-word phrase of the scene's emotion and purpose
