Here are the product highlights you should incorporate into your story:

<product_sellpoints>
{{product_sellpoints}}
</product_sellpoints>

Please create a {{ num }}-scene story based on these product highlights. Each scene should follow a consistent art style and maintain the same visual character while showcasing the product's key features. The story should be catchy and have good logic to follow sequentially.

Consider the following:
1. Visual continuity: How can you maintain consistency with previous scenes in terms of character appearance, setting, and overall style?
2. Feature highlight: Highlight a specific product feature in each scene, incorporating it naturally into the story without losing emotional or narrative depth
3. Narrative progression: How does this scene logically follow from the previous one and lead into the next? It's better to have an intriguing short story, with conflict, climax and resolution.
4. Story arc: How does this scene contribute to the overall narrative arc of the {{ num }}-scene story?
5. Creative visualization: Brainstorm unique ways to visually represent the product feature in an engaging, eye-catching, or humorous manner. Feature a memorable hook that is simple and infectious for each scene.
6. Scene Composition:

    Main Subject: Describe the protagonist or focus, ensuring visual consistency throughout the story. The character could be chosen from {{ character_list }} and please describe the appearance and style in detail for figure consistency. Cartoon and animal characters preferred.
    Clothing: Specify attire, accessories, or notable physical traits, updating slightly to match the narrative’s progression.
    Environment: Paint a vivid picture of the setting, including time, mood, and weather.
    Action: Specify the protagonist’s action in the scene, highlighting the use of the product.
    Details: Add extra objects, textures, or interactions to enrich the scene. Include shot composition and camera perspective for clarity.

Requirements:
- Each scene will be fed into image generation model separately (without knowing the other scene introduction). So you need to specify main subject,environment and clothing in each scene (slightly change according to scene) to ensure the consistency of the character's appearance.
- If the target of marketing is not a physical product but rather a website or service, it's essential to positively highlight specific selling points through interactions within a scenario and between characters, rather than just presenting a scene. For example, an ERP software ad could show a character resolving an issue using a form on a screen, followed by a relieved smile, or depict a collaborative online interaction among multiple characters.

Here's an example of the JSON structure for one scene (note that this is a generic example and your scenes should be much more detailed and specific to the product):
 {
    "index": [number, from 1 to {{ num }}],
    "caption": [compelling 9-word phrase of the scene's emotion and purpose]
    "prompt": {
        "Main Subject": [Description of the main character or focus],
        "Clothing": [Detailed description of attire and accessories],
        "Environment": [Vivid depiction of the setting],
        "Action": [Specific action highlighting product use],
        "Details": [Additional elements adding richness to the scene]
    }
}

The final output JSON example with {{ num }} scenes:
{
   "storyTitle": "Ace of Elegance",
   "videoList":[
        {
            "index": 1,
            "caption": "Viera discovers perfection with Senston's tennis racket",
            "prompt": {
                "Main Subject": "A Pixar-style anthropomorphic white cat, exuding sensuality, with a tall, slender figure, large chest, and humanoid physique."
                "Clothing": "Wearing a white sports mini skirt paired with gold-trimmed sneakers, and a blue cap on her head."
                "Environment": "A luxurious private tennis court with a lush green lawn in the background."
                "Action": "Elegantly standing at the center of the tennis court, holding a brand-new Senston tennis racket with both hands."
                "Details": "The racket's strings glimmer with a metallic sheen under the sunlight, and nearby is a basket full of tennis balls. Mid-range shot, backlit photography."
            }
        },
        {
            "index": 2,
            ...
        }, ...
    ]
}
- Focus on creating a story that feels immersive and cinematic, as if the viewer is reading a screenplay.
- All script and captions should be in English.
- Ensure every scene contributes to the overall story arc while highlighting a unique product feature.
- Keep the descriptions concise, ensuring each scene's character count does not exceed 450 characters for the “prompt” section.
