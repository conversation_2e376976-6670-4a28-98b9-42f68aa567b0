{% if basic_idea is defined and basic_idea %}
Here's the basic idea for the short video:
<basic_idea>
{{basic_idea}}
</basic_idea>

Before writing the script, take a moment to brainstorm and plan your approach. Consider the following:
1. What is the core message or story you want to convey?
2. Who is the target audience for this video?
3. What trendy elements or popular memes can you incorporate?
4. Each scene should have keywords for image generation. How can you make these keywords engaging and visually appealing?

{% else %}
请用以下风格写一个有趣的叙事剧本，包含{{ num }}个分镜脚本：

主体：拟人化动物角色，参考皮克斯画风，添加情感特质与身份设定。服装具有设计感和功能性的服装描述，强调细节。
配角：同样拟人化动物角色，与主角形成对比或互动，要有细节的脸部和服装描述。
环境：带有戏剧冲突的环境描写，融入动态光影效果。
动作：主角的具体行为动作，并辅以心理活动，展现角色特性。
细节：小物件或场景细节突出叙事核心，营造丰富画面感。
每个分镜附带关键词用于生成图像（如‘皮克斯画风，黑猫，身穿红色战斗服，飞跃霓虹灯下的街道’）。
{% endif %}


Note that the script has no conversational elements, so convey sentiments and information through visuals and the facial/posture expressions of the character on screen.

When creating your script, follow this general structure:
1. Hook: Start with a captivating opening to grab the viewer's attention
2. Introduction: Briefly introduce the topic or main idea, together with the main character on screen
3. Main content: Develop your key points or story, incorporating humor, facts, or engaging visuals
4. Plot twist or surprise element: Add a twist, reveal, or unexpected element to maintain interest
5. Climax: Build up to a high point or emotional peak in the video
6. Call to action or conclusion: End with a memorable closing or prompt for engagement

Remember to:
- Keep the language concise and engaging, suitable for a short video format
- Use your professional knowledge to add depth and credibility to the content
- Balance entertainment value with informative content
- Consider visual elements that could enhance the script, such as camera angles, transitions, or special effects

Based on the script above, Please create a {{ num }}-scene story. Each scene should follow a consistent art style and maintain the same visual character.

Consider the following:
1. Character appearance: A stunning white cat, rendered in the iconic Pixar style, boasts a tall, curvaceous humanoid figure with ample chest. Eye color is a striking blue, and the fur pattern is a sleek, solid white. The character exudes sensuality and elegance.
2. Visual continuity: Maintain consistency with previous scenes in terms of character appearance, setting, and overall style.
3. Narrative progression: How does this scene logically follow from the previous one and lead into the next?
4. Story arc: How does this scene contribute to the overall narrative arc of the {{ num }}-scene story?
5. Creative visualization: Brainstorm unique ways to visually represent the story in an engaging, eye-catching, or humorous manner. Feature a memorable hook that is simple and infectious for each scene.
6. Scene Composition:

    Main Character: Describe the protagonist , ensuring visual consistency throughout the story. Also specify attire, accessories, or notable physical traits.
    Supporting Character: Describe the other character (secondary character, sidekick or antagonist), ensuring visual consistency throughout the story. Specify attire, accessories, or notable physical traits
    Environment: Paint a vivid picture of the setting, including time, mood, and weather.
    Action: Specify the protagonist’s action in the scene, highlighting the use of the product.
    Details: Add extra objects, textures, or interactions to enrich the scene. Include shot composition and camera perspective for clarity.

Here's an example of the JSON structure for one scene (note that this is a generic example and your scenes should be much more detailed and specific to the product):
 {
    "index": [number, from 1 to {{ num }}],
    "caption": [compelling 9-word phrase of the scene's emotion and purpose]
    "prompt": {
        "Main Character": [Description of the main character or focus],
        "Other Character": [Detailed description of the other characters] (empty if not applicable),
        "Environment": [Vivid depiction of the setting],
        "Action": [Specific action highlighting product use],
        "Details": [Additional elements adding richness to the scene]
    }
}

The final output JSON example with {{ num }} scenes:
{
   "storyTitle": "Ace of Elegance",
   "storyList":[
        {
            "index": 1,
            "caption": "Viera discovers perfection with Senston's tennis racket",
            "prompt": {
                "Main Character": "A Pixar-style anthropomorphic white cat, exuding sensuality, with a tall, slender figure, large chest, and humanoid physique. Blue eyes and sleek white fur.Wearing a white sports mini skirt paired with gold-trimmed sneakers, and a blue cap on her head.",
                "Supporting Character": "A small, energetic squirrel with a mischievous grin, wearing a red tennis headband and matching wristbands.",
                "Environment": "A luxurious private tennis court with a lush green lawn in the background.",
                "Action": "Elegantly standing at the center of the tennis court, holding a brand-new Senston tennis racket with both hands.",
                "Details": "The racket's strings glimmer with a metallic sheen under the sunlight, and nearby is a basket full of tennis balls. Mid-range shot, backlit photography."
            }
        },
        {
            "index": 2,
            ...
        }, ...
    ]
}

Requirements:
- Each scene will be fed into image generation model separately (without knowing the other scene introduction).
So you need to repeat the main character description (expression and posture could update), and specify environment and supporting characters in details in each scene (slightly change according to scene) to ensure the consistency of the character's appearance.
- If only main character is present in the scene, please leave the "Other Character" field empty.
- Focus on creating a story that feels immersive and cinematic, as if the viewer is reading a screenplay.
- All script and captions should be in langugae {{ language }}.
- Keep the descriptions concise, ensuring each scene's character count does not exceed 450 characters for the “prompt” section.
