我们通过搜索特定关键词找到了一批相关的小红书帖子
{% if keywords is defined and keywords %}
关键词：{{keywords}}
{% endif %}
# 任务

分析下面提供的小红书帖子列表:
{{ note_content }}
对于列表中的每一个帖子，请执行以下操作：

1. 识别卖家身份与意图:
- 核心判断: 该帖子的发布者是否在利用小红书进行商业活动或具有明确的商业意图
- 考虑维度(请放宽标准):
  - 直接销售: 是否直接展示产品/服务并引导购买？
  - 软性种草/引流: 是否看似分享，但频繁、集中地围绕特定产品、服务或领域展开，暗示其背后有商业目的（即使文案质量不高）？
  - 商业标签/话题: 是否使用指向特定行业、产品或服务的商业化标签（如 #XX招代理, #XX定制, #XX探店, #XX公司 等）？
判断“是卖家/有商业意图” 或 “非卖家/无商业意图”。
2.  评估帖子质量(寻找"破冰点"):
审视帖子的文案，判断是否存在一些明显的优化空间或小缺陷（例如：文案吸引力不足、未有效引导互动、缺乏明确的营销转化路径等）。营销策略: 是否缺乏明确的行动号召 (CTA)？互动引导是否无效？是否未使用热门话题或有效标签？转化路径是否不清晰？是否不懂利用平台工具（如商品笔记、抽奖等）？
核心理念: 内容质量的不足，尤其是当发布者有明显商业意图时，恰恰是我们提供价值、切入合作的关键机会点。 不要因为帖子质量差而否定其潜力，反而要将其视为需要帮助的信号。
3.  **潜力评分(Score)**：基于以上分析，给该用户成为我们潜在客户的可能性打分，范围 0-10。
- 0分: 明确不是卖家，纯粹个人生活记录，或消费者吐槽帖且无任何带货/商业暗示。
- 1-3分: 身份模糊，商业意图极不明显，或内容与商业关联度极低（即使带了商业标签，但内容完全无关或质量极差到无法判断意图，如纯粹复制粘贴的低质广告）。
- 4-6分:(重点关注 - 潜力区)
识别为卖家/有商业意图，但内容质量一般或较差，存在明显的优化空间。 (例如：产品图不吸引人，文案简单堆砌，互动少，但能看出是在卖东西/提供服务)。
只要能判断出是商家在发帖引流，即使内容简单、质量不高，也应归入此或更高分数段，因为他们是典型的需要优化指导的目标客户。
- 7-9分: (重点关注 - 高潜力区)
非常明确是卖家，商业意图清晰。并且，帖子/账号现状存在显著的、可提升的营销或内容短板，显示出对专业服务的迫切需求。 (例如：产品不错但笔记流量差，有一定内容基础但转化低）。也包括那些内容尚可，但明显可以通过专业运营或广告投放实现更大规模增长的卖家。
- 10分: 顶级潜力客户。明确是卖家，产品/品牌有潜力，展现了强烈的增长雄心或明确表达了合作需求（如正在寻找代运营/投手），且其业务模式与我方服务高度匹配。
4.  用户画像(User Profile)：根据帖子内容，简要推测并描述该用户的画像。包括但不限于：
    - 业务类型: 卖什么产品/服务？（如：美妆、服饰、家居、课程、本地生活服务等）
    - 可能规模: 个人小卖家、初创品牌、成熟品牌地方店等
    - 内容风格: 专业严谨、生活化分享、可爱治愈、高级冷淡、粗放直接等
    - 可能痛点:  (基于内容现状和评分)
    - 流量匮乏: (若互动数据差，内容质量低)
    - 转化困难: (若内容尚可但缺乏引导，或产品展示不清)
    - 内容创作能力不足: (若文案质量差，风格混乱)
    - 平台规则不熟: (若使用无效标签，排版混乱，甚至有违规风险)
    - 缺乏系统营销策略: (若内容零散，无持续性，无明确目标)
5.  钩子文案(Hook Message)：针对该用户及其帖子，生成一段简短、友好、个性化且有吸引力的“破冰”私信文案初稿。文案应：
    -   提及来源: 表明你看到了他们的某篇帖子（可以模糊提及，避免过于侵入）。
    -   表达认可: 对他们的产品/内容/努力表示一定的赞赏（即使有缺陷，也要找到闪光点）。
    -   点出价值/痛点: 巧妙地暗示你的服务可以帮助他们解决某个潜在问题或实现更好的增长（例如：“内容很有潜力，稍加优化可能曝光会更好哦”、“看到您在用心经营，想没想过让更多人看到您的好产品？”）。
    -   引发兴趣/提问: 以开放式问题结束，引导对方回复，表达进一步沟通的意愿（例如：“对小红书增长策略感兴趣吗？”、“或许我们可以聊聊如何提升帖子的互动效果？”）。
    -   语气: 专业、真诚、乐于助人，避免过于推销的口吻。
{% if welfare is defined and welfare %}
    - 福利追加：在前面生成的私信钩子文案末尾，追加一句福利引导。
请从以下两个模板中选择一个或构造一个类似的句子，并**务必使用 `[WELFARE_PLACEHOLDER]` 这个精确的占位符字符串来代表福利的名称**。我之后会用实际的福利名称替换掉这个占位符。
        * 模板1: “另外，如果想提升下账号运营能力，可以私信我领取[WELFARE_PLACEHOLDER]哦！”
        * 模板2: “对了，看你对[用户行业/内容方向]很用心，我这里正好有一份[WELFARE_PLACEHOLDER]，或许对你有帮助，感兴趣可以回复我领取哈！”
        * 可对模版做适当调整，以确保文案与前文自然衔接，语气友好。但是占位符要保持不变。
        * **重要指令**: 你的任务是生成包含 `[WELFARE_PLACEHOLDER]` 这个占位符的完整句子。不要尝试替换或解释 `[WELFARE_PLACEHOLDER]`。确保输出的文本中包含这个原始的占位符。
{% endif %}


# 输出要求

以 JSON 格式输出一个列表，列表中的每个对象代表对一个帖子的分析结果，包含以下字段：

*   `index` (integer): 帖子在列表中的索引，保持与原始帖子列表的顺序一致
*   `potentialScore` (integer): 0-10分的潜力评分
*   `userProfile` (string): 用户的简要画像描述,字数在150字以内
*   `hookMessage` (string): 生成的个性化钩子破冰文案,字数在150字以内。如果 `potentialScore` 为 0,为空字符串

输出 JSON 结构示例:

[
  {
    "index": 0,
    "potentialScore": 7,
    "userProfile": "个人手工饰品卖家，风格清新文艺。内容较用心，但文案引导购买路径不清晰。可能痛点：流量获取和转化优化。",
    "hookMessage": "哈喽，刷到你分享的手工饰品帖子，设计得好别致！感觉很有潜力呢。稍微优化下图文呈现和引导，也许能吸引到更多喜欢你风格的朋友哦~ 对小红书增长这块有什么想法吗？"
  },
  {
    "index": 1,
    "potentialScore": 0,
    "userProfile": "普通用户分享周末烘焙食谱，无明显商业意图。",
    "hookMessage": ""
  },
  {
    "index": 2,
    "potentialScore": 5,
    "userProfile": "小众护肤品牌代理或经销商，内容偏产品介绍，专业度尚可但略显生硬，互动较少。可能痛点：内容吸引力不足，粉丝互动弱。",
    "hookMessage": "你好呀，看到你在小红书推荐护肤品蛮用心的。好产品也需要有趣的内容来衬托，更容易被大家看到和喜欢呢！不知道你有没有考虑过怎么让帖子互动更热烈一些？"
  }
]


请根据以上要求，分析提供的帖子列表，并生成 JSON 输出。