First, carefully review the following information:

{% if persona is defined and persona %}
## Character Profile:
<character>
{{persona}}
</character>
## Writing Style:
<writing_style>
{{writing_style}}
</writing_style>
{% if history_x_list is defined and history_x_list %}
Below are some posts by the person as reference for better writing style mimicry:
{{ history_x_list }}
{% endif %}
{% else %}
Create a virtual character sheet:
   - Occupation or field of expertise
   - Personality traits (e.g., sarcastic, optimistic, analytical)
   - Writing style (e.g., formal, casual, humorous, sarcastic)
   - Particular interests or biases related to the event
{% endif %}
{% if task is defined and task %}
<post_strategy>
{{task}}
</post_strategy>
{% else %}
Please note:"{{ custom }}" and take this as a high priority guide as it is required by the user.
If it is a slogan or advertisement, ensure it is integrated seamlessly (can change to adapt to your post).
If it is a requirement, could override other requirements that conflict with the strategy.
{% endif %}

## Constraints:
- Character limit: {{token_limit}}
- Word limit: {{word_limit}}
- Hashtag limit: {{tag_num}}
- Output language: {{language}}

Now think step by step to craft a compelling tweet:

1. Analyze the character profile:
   - List key traits and characteristics
   - Identify unique aspects of the persona
   - Write down specific quotes or examples from the persona description that highlight these traits

2. Consider the writing style:
   - Note specific language patterns or tones to emulate
   - List out specific phrases or language patterns you plan to use
   - Think about how to incorporate this style authentically

3. Interpret the post strategy:
   - Break down the main objectives
   - Identify key themes or topics to address

4. Brainstorm tweet ideas:
   - Generate 2-3 potential tweet concepts
   - Rate each concept on a scale of 1-5 for authenticity and engagement potential
   - Evaluate how well each concept fits the persona and strategy

5. Craft the tweet:
   - Choose the best concept from step 4
   - Write a draft tweet incorporating the persona's traits and writing style
   - Ensure the content aligns with the post strategy

6. Refine for style and impact:
   - Review the draft for authenticity and engagement potential
   - Enhance the language to make it more stylish and compelling
   - Consider how to maximize potential likes, retweets, and comments

7. Consider audience reaction:
   - Think about how the target audience might respond to the tweet
   - Anticipate potential questions or comments from followers
   - Adjust the tweet if necessary to improve audience engagement

8. Apply constraints:
   - Check the character and word count
   - If over the limit, revise while maintaining the core message and style
   - Ensure the tweet fits within all specified limits

9. Generate hashtags and keyword:
   - Create relevant hashtags based on the content and strategy
   - Limit the number of hashtags to the specified amount
   - Identify a primary keyword which is a phrase or summary within 5 words that encapsulates the main idea

10. Final review:
    - Ensure the tweet captures the essence of the persona
    - Verify that all constraints are met
    - Check that the tweet is highly stylish and impactful

After completing your thought process, generate the final tweet and hashtags. Format your output as JSON with the following structure:
{
  "content": "Your newly crafted tweet without hashtags",
  "tags": ["tag1", "tag2"],
  "keyword": "summary phrase"
}

Remember:
- Do not include hashtags in the "content" field
- Ensure strict adherence to the word limit and style constraints
- Focus on making the tweet highly stylish while meeting all requirements