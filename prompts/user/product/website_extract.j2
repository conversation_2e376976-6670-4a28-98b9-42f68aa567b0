{% if type is defined and type %}
The website type IS {{type}}

{% else %}

You first need to classify the website into one of the following categories (forget your prior knowledge on the input URL):

1. "ecommerce": a product page on ecommerce website or online store, with detailed information and price of one or multiple products, with the function to sell.

2. "brand portal": A brand portal aims to showcase the brand and its offerings. It often includes brand / company history, mission and news / blogs. Its products or services differ from traditional ecommerce products, such as training, consultancy, etc. Purchase of such products or services may not be directly available on the website, usually requires further contact or consultation.

3. "saas": it may sell a software as a service, with subscription model, like YouTube, Spotify or GitHub.com. Or it is a platform for some web services.

4. "app": APPs are normally on Android/Play store or IoS Apple Store. If "play.google.com/store/apps" or "apps.apple.com" in the URL, it is an app. Otherwise, it is not an app.

5. "group": User communities (strictly only WhatsAPP, Discord, Telegram and Facebook Groups) for communication, user engagement, and marketing purposes. These groups are used for long-term relationship building, personalized interaction, and conversion strategies.

6. "illegal": If the website is involved with porn, violence, drugs, or other activities skirting the edge of the law.
{% endif %}

{% if description is defined and description %}
Here is the brief description of the website: {{description}}
{% endif %}

Here's the detailed information of the website:

<website_description>
{{context}}
</website_description>

Please follow these steps to create your summary:

1. Carefully read and analyze the website description.
2. Perform the following analysis steps:
   a. There might be some other products or services on the website. Identify and state the main product or purpose of the website. Filter out the unrelated information and other products.
   b. Extract and list key information about features, target audience, and unique selling points.
   c. Write an initial description (aim for 150-200 words). Remove unnecessary details and focus on the core aspects.
   d. Count the words in your description. If over 200, revise and recount until it's within the limit.
   e. Generate 5-8 most relevant keywords based on the description and purpose. The keywords should either be the specific domain or closely related category terms.  Cannot be too generic. At least 5 keywords are required.
   f. Refine and restructure the content to make it concise, organized, and easy to read. Aim for a social media-friendly style that's conversational and inviting, avoiding any technical jargon. Adjust the structure to be clear and engaging, ensuring it stands apart from the original..
3. Classify the website into one of the categories mentioned above.
Note that a facebook page type is SaaS, not group unless the suffix is "/groups/" or "/share/", and the descriptions says it's a group.

{% if type is defined and type=="SaaS" %}
The website type IS {{type}}
return the following JSON structure:
   {
     "name": "Website Name",
     "type": "website",
     "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
     "description": "Brief description",
     "OtherInfo":{
       "features": "product features",
       "targetAudience": "",
       "scenarios": "user scenarios"
     }
   }
{% else %}
If the website is an ecommerce site, return the following JSON structure:
   {
     "name": "Website Name",
     "type": "ecommerce",
     "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
     "description": "Brief description",
     "OtherInfo":{
       "features": "product features",
       "targetAudience": "",
       "scenarios": "user scenarios",
       "productList": [
             {
                "originalPrice": "",
                "currentPrice": "",
                "discount": "",
                "description": "Product specifications"
             }
          ]
     }
   }
where productList must match the 'Product Information' in the context. If 'Product Information' is not provided, make the list empty.

If the website is a brand portal or SaaS, we classify them as 'website', return the following JSON structure:
   {
     "name": "Website Name (in original language)",
     "type": "website",
     "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
     "description": "Brief description",
     "OtherInfo":{
       "features": "product features",
       "targetAudience": "",
       "scenarios": "user scenarios"
     }
   }
If the website is an app, return the following JSON structure:
   {
     "name": "APP Name (in original language)",
     "type": "app",
     "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
     "description": "Brief description",
     "OtherInfo":{
       "features": "product features",
       "targetAudience": "",
       "scenarios": "user scenarios"
     }
   }
  
If the website is a social media group, return the following JSON structure:
{
    "name": "Group Name (in original language)",
    "type": "group",
    "description": "Brief description",
    "keywords": [keywords extracted from 'topic' field, 5-7 keywords], (empty if topics field is empty)
    "OtherInfo":{
        "community": "The features and sell-points of the community, not the platform itself",
        "targetAudience": "",
        "topics": "The main topics discussed in the community, not the platform itself", (empty if not enough information)
        "platform": "whatsapp, discord, facebook, or telegram"
    }
}

If the website is illegal, return the following JSON structure:
   {
     "name": "Website Name",
     "type": "illegal",
     "description": "Reason for being illegal"
   }
{% endif %}
Important constraints:
- The output should be in language {{ language }}.
- The "name" field must be 24 characters or fewer.
- The "name" should be the specific name of the deepest page, not the global domain or platform. For example, 'amazon.com/iphone' should be 'iPhone', not 'amazon', because amazon is the platform that hosts the product.
- The "description" and other fields must be 200 words or fewer.
- If the description is not informative enough, leave the field empty instead of providing invalid information or talk about the platform itself.

Please ensure your JSON output is properly formatted and all fields are filled based on your analysis of the provided description.
