Please analyze the following company information from multiple sources to create a comprehensive company profile:

{% if website_info is defined and website_info %}
<website_information>
{% if website_info.title %}Website Title: {{website_info.title}}{% endif %}
{% if website_info.url %}Website URL: {{website_info.url}}{% endif %}
{% if website_info.description %}Website Description: {{website_info.description}}{% endif %}
{% if website_info.content %}Website Content: {{website_info.content}}{% endif %}
</website_information>
{% endif %}

{% if brochure_info is defined and brochure_info %}
<company_brochure>
{{brochure_info}}
</company_brochure>
{% endif %}

{% if account_info is defined and account_info %}
<social_media_account>
Platform: 小红书 (Xiaohongshu/RedNote)
{% if account_info.nickname %}Account Name: {{account_info.nickname}}{% endif %}
{% if account_info.profileDesc %}Profile Description: {{account_info.profileDesc}}{% endif %}
{% if account_info.profilePageLink %}Profile Link: {{account_info.profilePageLink}}{% endif %}
{% if account_info.ipLocation %}Location: {{account_info.ipLocation}}{% endif %}
{% if account_info.followsCnt %}Follows: {{account_info.followsCnt}}{% endif %}
{% if account_info.fansCnt %}Fans: {{account_info.fansCnt}}{% endif %}
{% if account_info.likedCnt %}Total Likes: {{account_info.likedCnt}}{% endif %}
{% if account_info.collectedCnt %}Collections: {{account_info.collectedCnt}}{% endif %}
{% if account_info.notesCnt %}Notes Published: {{account_info.notesCnt}}{% endif %}
{% if account_info.isVerified %}Verified Account: {{account_info.isVerified}}{% endif %}
</social_media_account>
{% endif %}

{% if description is defined and description %}
<additional_information>
{{description}}
</additional_information>
{% endif %}

Your task is to create a structured JSON summary by synthesizing information from all available sources.

Analysis Guidelines:
- **Cross-reference information**: Compare and validate information across website, brochure, and social media account data
- **Prioritize official sources**: Website and brochure information typically carry more weight than social media profiles
- **Name Preservation**: Please keep the original name of the company as well as terminologies, do not change them to other languages, no matter what the output language requirement is.
- **Handle Garbled Text**: If you encounter text that appears to be garbled or incorrectly encoded (mojibake), especially for critical fields like `companyName`, you MUST prioritize information from other sources that are clean. For example, if the website title is garbled, use the social media account name for `companyName`. If no clean alternative is available, it is better to leave the field empty than to populate it with corrupted data.
- **Leverage social media insights**: Use account metrics (fan count, engagement) and profile description to understand target audience and market positioning
- **Fill gaps intelligently**: If exact information is not available, make educated estimates based on available context clues
- **Industry classification**: Determine the industry of the company's core product or service itself, not the industry of its target customers. For example, a company that sells software to architects is in the "Software" industry, not the "Construction" industry.
- **Target audience inference**: Consider social media followers, content themes, and business description to identify target demographics

Your output must strictly adhere to the JSON schema provided. Do not include any properties that are not explicitly defined in the schema.
The output should be in {{ language }} language.
return the following JSON schema:
{
    "type": "object",
    "properties": {
        "companyName": {
            "type": "string"
        },
        "companyIndustry": {
            "type": "enum",
            "enum": [
                "Retail",
                "Healthcare",
                "E-commerce",
                "Manufacturing",
                "Technology",
                "Software",
                "IT services",
                "Hardware",
                "Education",
                "Finance",
                "Real Estate",
                "Hospitality",
                "Transportation",
                "Media",
                "Construction",
                "Food & Beverage",
                "Entertainment",
                "Consulting",
                "Marketing Agency",
                "Energy",
                "Agriculture",
                "Legal",
                "Non-profit",
                "Telecommunications",
                "Fashion",
                "Automotive",
                "Pharmaceutical",
                "Sports",
                "Insurance",
                "Beauty",
                "Other",
            ]
        },
        "companyBrandPositioning": {
            "type": "string"
        },
        "companyCoreCompetency": {
            "type": "string"
        },
        "companyTargetAudience": {
            "type": "string"
        },
        "companyOtherInfo": {
            "type": "object",
            "properties": {
                "subscribeKeywords": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "Exactly 5 relevant keywords. Prioritize specific, niche topics over general ones.",
                    "numberOfItems": 5
                },
                "foundingTime": {
                    "type": "string",
                    "description": "The year-month the company was founded (month is optional)"
                },
                "description": {
                    "type": "string",
                    "description": "A brief description of the company's products or services. Aim for 150-200 words."
                },
                "features": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "scenarios": {
                    "type": "string"
                }
            },
            "required": [
                "subscribeKeywords",
                "description",
                "features"
            ],
            "additionalProperties": false
        }
    },
    "required": [
        "companyName",
        "companyIndustry",
        "companyBrandPositioning",
        "companyCoreCompetency",
        "companyTargetAudience",
        "companyOtherInfo"
    ]
}

Ensure that your final JSON output is accurate and keep values empty if the field is not mandatory and information is not provided.