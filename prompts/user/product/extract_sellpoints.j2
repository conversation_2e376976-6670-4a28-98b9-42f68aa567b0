Here's the product description from a website you need to analyze:

<website_description>
{{context}}
</website_description>

{% if description is defined and description %}
Here is additional description: {{description}}
{% endif %}

Please follow these steps to create your summary:

1. Carefully read and analyze the website description.
2. Generate exactly 6 most intriguing or unique sell points (in English) based on the description and purpose. Each sell point should be within 5 words.
3. Make sure the 6 sell points are distinct, attractive and not repetitive. If there is not enough information to generate 6 sell points, create unique sell points with your imagination and knowledge of this type of product or service.
4. Make sure the output is in language {{ language }} and follows the JSON format below.

{
     "name": "product name",
     "sellPoints": ["sellpoint1", "sellpoint2", "sellpoint3", "sellpoint4", "sellpoint5", "sellpoint6"]
}
