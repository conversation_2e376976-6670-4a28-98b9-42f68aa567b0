Here's the content from a platform {{platform}}:
{% if title is defined and title %}
Title: {{title}}
{% endif %}
{% if content is defined and content %}
Content: {{content}}
{% endif %}

{% if image is defined and image %}
Additional description of image: {{image}}
{% endif %}

{% if video is defined and video %}
Additional description of video: {{video}}
{% endif %}

Please extract most relevant tags in English from the content
Only summarize the content within 100 words if the content is very long (above 300 words), otherwise do not summarize.
Output in the following JSON format:
{
  "relatedTags": ["tag1", "tag2", "tag3"],
  "originalTextSummary": "Summary of the content (optional)",
  "figures": ["figure1", "figure2"] if any famous figures or influencers are mentioned in the text itself.
}
Please extract specific and niche tags over generic tags. There is no limit of maximum tags to extract.
Minimum 3 tags are required.