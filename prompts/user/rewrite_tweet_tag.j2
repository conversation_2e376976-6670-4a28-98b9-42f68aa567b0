{% if persona is defined and persona %}
Your character profile is as follows:
<character>
{{persona}}
</character>
writing style: {{writing_style}}
{% else %}
Create a virtual character sheet:
   - Occupation or field of expertise
   - Personality traits (e.g., sarcastic, optimistic, analytical)
   - Particular interests or biases related to the event
   - Writing style (e.g., formal, casual, humorous, sarcastic)
{% endif %}
## Original post:
<original_post>
{{tweet_content}}
</original_post>
{% if event is defined and event %}
## Event Context
There is also a context about this tweet:
{{event}}
{% endif %}
{{ target_means }}
Follow the steps provided:
{% if custom == 'native' %}
{% include 'user/native_tag.j2' %}
{% else %}
{% include 'user/custom.j2' %}
{% endif %}
## Generate hashtags and keyword:
   - Create relevant hashtags based on the content and strategy
   - Limit the number of hashtags to the specified amount
   - Identify a primary keyword which is a phrase or summary within 5 words that encapsulates the main idea

## Output Requirements:
- Write in {{ language }} language
- Ignore the hashtags and mentions in the original post
- Do not append hashtags to the content itself
- Remember to maintain the word limit and style constraints
{% if custom != 'native' %}
- User has important instruction:"{{ custom }}".
Supersede other requirements when there is conflict.
If the instruction is in another language, output should remain {{language}}.
If the instruction itself contains command to change the language, change the language from {{language}} to the command.
{% endif %}
- Format your output as JSON with the following structure:
{
  "content": "Your newly crafted tweet without hashtags",
  "tags": ["tag1", "tag2"],
  "keyword": "summary phrase"
}
