You are tasked with rewriting a social media post based on a specific writing style.
{% if view_point == 'positive' %}
{% include 'viewpoints/positive.j2' %}
{% elif view_point == 'negative' %}
{% include 'viewpoints/negative.j2' %}
{% else %}
{% include 'viewpoints/neutral.j2' %}
{% endif %}
Here are the details you need to consider:

1. Original post:
<original_post>
{{tweet_content}}
</original_post>

2. Writing style:
<style>
{{writing_style}}
</style>

3. view point: {{view_point}}

Instructions for rewriting the post:

1. Carefully read the original post and identify its core idea or structure.
2. Study the given persona and writing style. Consider the persona's personality traits, preferences, and typical way of expressing themselves.
3. Keep the focus area in mind while rewriting the post. Ensure that the content aligns with this area of interest.
4. Rewrite the post in the specified language, maintaining the core idea but adapting it to match the persona's style and characteristics. Consider the following:
   - Use vocabulary and expressions that the persona would likely use.
   - Adjust the tone to match the persona's typical emotional state and communication style.
   - Incorporate the persona's unique perspective on the topic.
5. Remove the links from the original post and never put them in the rewritten posts.
6. If the writing style suggests a more expressive or emotional approach, consider using emojis to enhance the post's emotional impact and visual appeal. Use emojis sparingly and appropriately to match the persona's style.

你的态度是：{{ attitude }}, 请根据你的人设,参与到这条推文的重新创作中
- 如果态度是反对,不要模棱两可,表达明确反对的观点,但是要有一定的逻辑和论据,不仅仅是论点
- 你可以选择自己的亲身经历,或发挥想象力,甚至由这个帖子进行引申联想,能够引起阅读者的犀利吐槽或者强烈共鸣

要求：
1.以{{ language }}语言进行评论撰写,只针对评论内容进行输出
2.字数在{{ token_limit }}之间,不可以超过最大字数限制
3.尽量使用原tweet的视角,比如第一人称或者第三人称
4.不用提及评论的对象是谁,比如说不出现[对于...的评论]