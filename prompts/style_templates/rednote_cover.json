[{"id": 2, "name": "软萌知识卡片风", "nameEn": "Soft and Cute Knowledge Card Style", "desc": "", "prompt": "# 专业要求 ## 软萌知识卡片风 ## 设计风格 - **柔和色彩基调**：以粉色、米黄、淡紫等温和色调为主，营造轻松友好氛围 - **圆角卡片结构**：所有内容采用大圆角矩形或椭圆形容器，没有尖锐边角 - **简约留白处理**：适当留白增强可读性，避免视觉拥挤感 - **渐变色背景**：部分卡片使用柔和渐变背景，增加层次感和温暖感 - **情感化设计**：整体风格偏向亲和、轻松，不过分严肃正式 - **统一视觉语言**：各卡片尽管内容不同，但维持一致的设计语言 - **轻量化边框**：红色封面使用笔记本线条等轻量级边框元素增加趣味性 ## 文字排版风格 - **大字号标题**：标题文字加粗加大，吸引第一眼注意 - **紧凑段落布局**：正文内容分段清晰，段落间距适中 - **感叹号点缀**：频繁使用感叹号增强情感表达和亲近感 - **表情符号融入**：在文字中加入'」'等特殊符号增加表现力 - **重点句加粗**：关键信息或总结性内容加粗处理 - **自然语言表达**：采用口语化、对话式的表达方式，降低阅读门槛 - **多层级排版**：标题、副标题、正文、强调语等形成清晰的阅读层级 ## 视觉元素风格 - **Q版表情角色**：底部配置可爱的emoji表情或形象，增加亲和力 - **表情丰富多样**：使用惊讶、思考、无奈等多种表情，与文本内容情感呼应 - **场景化呈现**：如电脑前工作的人物、阅读书本的角色等场景化表达 - **实物图融合**：如猫咪真实照片与卡通风格的结合 - **点缀型装饰**：适量使用小装饰元素，如笔记本边缘的圆点标记 - **形象位置统一**：视觉元素多位于卡片底部，形成稳定的视觉期待 - **拟人化处理**：将抽象概念通过卡通形象拟人化，增强理解和记忆 ## 文本适应性规则 - **卡片大小自适应**：卡片尺寸根据文本长度动态调整 - **字体大小平衡**：根据内容自动调整字体大小,保持可爱风格 - **留白比例保持**：即使文本增多也保持足够留白 - **文本完整呈现**：确保所有文本内容完整显示，不截断 - **卡通元素位置适应**：装饰元素位置随文本长度智能调整 - **分段优化显示**：长文本自动进行视觉友好的分段", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/95a1c2c14d8c8fdd8b1e07fb828532bb"}]}, {"id": 3, "name": "商务简约信息卡片风", "nameEn": "Business Simple Information Card Style", "desc": "", "prompt": "# 专业要求 ## 商务简约信息卡片风 ## 设计风格 - **极简背景设计**：采用浅灰色或白色背景，减少视觉干扰 - **高对比度呈现**：黑色文字与浅色背景形成强烈对比，提升可读性 - **方正几何布局**：整体结构方正规整，遵循严格的网格系统 - **功能性优先**：设计服务于内容传达，摒弃多余装饰元素 - **色块分区设计**：通过彩色方块标识不同信息点，便于快速识别 - **圆角矩形容器**：软化边缘，增加亲和力但保持商务感 - **留白合理利用**：为重要内容预留足够呼吸空间，避免信息拥挤 ## 文字排版风格 - **问答式标题结构**：以问题开头('在家办公效率低?'、'运动量变小?')引发共鸣 - **解决方案副标题**：紧随问题后给出简洁有力的解决方案 - **字体层级鲜明**：通过明确的字号变化区分标题、副标题和正文 - **短句精炼表达**：多用简短有力的句子，以句号结尾，节奏感强 - **加粗重点处理**：核心词汇或短语加粗处理，引导视线焦点 - **中英文混排**：品牌名称保留英文，增加国际化专业感 - **要点式内容组织**：将功能特点和优势以简短条目形式呈现 ## 视觉元素风格 - **产品实物展示**：在卡片下方放置产品包装实物照片，真实直观 - **功能性图标**：如'居家模式'的房屋图标，增强视觉识别度 - **开关按钮元素**：采用可交互感的UI组件表现，如模式开关按钮 - **数字编号标识**：使用彩色背景数字标记不同要点，提升可读性 - **品牌标识垂直排列**：'CHOCODAY'字样垂直排列于右侧，形成识别特征 - **色彩编码系统**：使用绿色、黄色等不同色彩区分不同信息模块 - **简约线条边框**：适当使用线条框架划分内容区域，结构清晰 ## 文本适应性规则 - **信息区块动态调整**：根据文本量智能调整信息区块大小 - **商务排版保持**：自动调整布局同时保持专业商务感 - **要点列表自适应**：要点数量增加时自动调整间距和字号 - **关键词突显持续**：无论文本如何调整都确保关键词突显 - **最小商务字体限制**：缩小字体时保持商务场景必要的最小可读性 - **内容分区平衡**：确保即使文本增加,各分区信息比例平衡", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/0e9efd7cd526bb5081c51f9672255c56"}]}, {"id": 6, "name": "柔和科技卡片风", "nameEn": "Soft Tech Card Style", "desc": "", "prompt": "# 专业要求\n##  柔和科技卡片风\n## 设计风格\n- **圆角卡片布局**：使用大圆角白色或彩色卡片作为信息容器，创造友好亲和感\n- **轻柔色彩系统**：主要采用淡紫、浅黄、粉色、米色等柔和色调，避免强烈视觉刺激\n- **极简留白设计**：大量留白空间增强可读性，减少视觉疲劳\n- **阴影微立体**：subtle阴影效果赋予界面轻微的立体感，不过分强调\n- **功能美学主义**：设计服务于功能，没有多余装饰元素\n- **网格化布局**：基于明确的网格系统排列卡片，保持整体秩序感\n- **渐变色点缀**：部分界面使用柔和渐变作为背景，如米色到蓝色的过渡，增加现代感\n\n## 文字排版风格\n\n- **数据突显处理**：关键数字信息使用超大字号和加粗处理，如\"12,002\"、\"20x\"\n- **层级分明排版**：标题、说明文字、数据、注释等使用明确的字号层级区分\n- **简约无衬线字体**：全部采用现代简洁的无衬线字体，提升可读性\n- **文字对齐规整**：在卡片内保持统一的左对齐或居中对齐方式\n- **重点色彩标识**：使用蓝色等高对比度颜色标记重要术语，如\"tweets\"和\"threads\"\n- **空间呼吸感**：文字块之间保持充足间距，创造\"呼吸\"空间\n- **品牌名称特殊处理**：产品名称如\"alohi\"、\"deel.\"采用特殊字体或风格，强化品牌识别\n\n## 视觉元素风格\n\n- **微妙图标系统**：使用简约线性或填充图标，大小适中不喧宾夺主\n- **进度可视化**：使用环形或条状图表直观展示进度，如年度完成百分比\n- **色彩编码信息**：不同卡片使用不同色彩，便于快速区分功能模块\n- **品牌标识整合**：将产品logo自然融入界面，如\"alohi\"的圆形标识\n- **人物头像元素**：适当使用圆形头像增加人性化特质，如客户推荐卡片\n- **几何形状装饰**：使用简单几何形状作为背景装饰，如半透明圆形\n- **组件一致性**：按钮、标签、选项卡等元素保持统一风格，提升系统感", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/e553f89599f6530cc0acd8eed1c3c50a"}]}, {"id": 7, "name": "现代商务资讯卡片风", "nameEn": "Modern Business Information Card Style", "desc": "", "prompt": "# 专业要求 ## 现代商务资讯卡片风 ## 设计风格\n\n- **色彩情绪编码**：使用深绿与深红色调分别象征金融稳健与企业活力\n- **主题色块构成**：整体采用大面积单一色调作为背景，营造专业稳重氛围\n- **卡片式设计**：内容以圆角卡片形式呈现，现代简约且边界感明确\n- **商务应用美学**：符合金融科技类应用的视觉设计规范与审美\n- **微妙渐变处理**：背景色采用细腻渐变效果，增强层次感\n- **网格底纹肌理**：融入轻微网格线与点阵纹理，提升科技感与专业度\n- **功能导向设计**：布局与元素安排以提升信息获取效率为首要目标\n\n## 文字排版风格\n\n- **三级信息层级**：通过明确的字号和粗细区分头条标签、主标题和辅助信息\n- **大标题强调**：主要新闻标题占据视觉中心，字号最大且加粗\n- **左对齐规整排版**：所有文字元素保持左对齐，结构严谨有序\n- **无衬线字体选用**：采用现代商务风格的无衬线字体，提高可读性和专业感\n- **标题分行处理**：长标题采用多行排版，每行字数适中，便于快速阅读\n- **日期位置固定**：日期信息位置统一，作为时效性标识\n- **留白节奏控制**：文字块之间保持适当留白，创造舒适阅读节奏\n\n## 视觉元素风格\n\n- **指向性图标**：右上角箭头图标暗示可点击进入详情的交互性质\n- **点阵背景纹理**：背景中的微妙点阵增加设计深度，避免平面单调\n- **进度指示条**：底部的分段线条作为浏览进度或内容分区指示\n- **主题色彩区隔**：不同新闻主题采用不同色调区分（金融绿色/科技红色）\n- **高对比度文字**：浅色文字在深色背景上形成强烈对比，确保可读性\n- **内容统一格式**：\"Today's News\"标签在相同位置出现，建立品牌一致性\n- **简洁无干扰界面**：排除多余装饰元素，聚焦于核心信息传递", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/7b0a59c3b9ae8fbd52f3ed4c60d47e14"}]}, {"id": 8, "name": "流动科技蓝风格", "nameEn": "Flowing Tech Blue Style", "desc": "", "prompt": "# 专业要求 ## 流动科技蓝风格\n\n## 设计风格\n\n- 现代简约科技风，以蓝色系为主体色调\n- 大量使用蓝白渐变，营造轻盈通透感\n- 背景多采用极简白底或浅色调\n- 运用流线型曲线创造动态视觉效果\n- 圆角矩形作为基础框架，增加友好感\n- 整体布局干净有序，空间感强\n- 光影效果柔和，营造科技感与未来感\n\n## 文字排版风格\n\n- 标题简洁有力，通常使用黑体或无衬线字体\n- 显著的标题层级对比，主副标题大小分明\n- 中英文混排，增加国际化视觉效果\n- 关键信息放大处理，辅助文字精简\n- 日期、标签等信息排版整齐规范\n- 文字与背景形成适当对比，确保清晰可读\n- 数字与文本搭配得当，注重整体平衡\n\n## 视觉元素风格\n\n- 流动曲线是主要装饰元素，表现科技流动感\n- 半透明蓝色波纹或螺旋形状贯穿多个设计\n- 几何抽象形状作为点缀（圆环、三角形等）\n- 轻量级图标和按钮设计，简洁明了\n- 折纸元素（如纸飞机）象征传递与连接\n- 光效处理柔和，形成层次感\n- 整体视觉元素与科技、数据、信息等主题高度契合", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/386b37ea46fb83824d289de430261b79"}]}, {"id": 10, "name": "奢华自然意境风", "nameEn": "Luxury Natural Artistic Conception Style", "desc": "", "prompt": "# 专业要求 ## 奢华自然意境风\n\n## 设计风格\n\n- **高级沉稳色调**：暗调景观背景配以细腻光影，营造高端内敛氛围\n- **意境式呈现**：不仅展示实景，更表达一种与自然共融的精神状态\n- **奢华隐喻元素**：通过构图和文字暗示高端生活方式与品质追求\n- **空间层次丰富**：通过前景、中景、远景的搭配创造空间深度感\n- **东西方美学融合**：中式意境与西方现代摄影美学的和谐结合\n- **沉浸式体验设计**：画面设计引导观者产生身临其境的感受\n\n## 文字排版风格\n\n- **悬浮式标题定位**：文字悬浮于景观之上，形成虚实对比\n- **中西文混合排版**：英文与中文标题组合使用，增强国际化气质\n- **层级分明的字阶**：主标题、副标题和说明文字尺寸差异明显\n- **优雅字体选择**：英文多用细腻的衬线体，中文选用简约现代字体\n- **巧妙的文字拆分**：文字的艺术性拆解处理\n- **留白与文字平衡**：大面积留白中点缀核心文字，强化重点信息\n- **边缘式辅助信息**：次要文字信息常放置于画面边缘，不干扰主视觉\n\n## 视觉元素风格\n\n- **摄影级光影处理**：专业摄影级别的光线捕捉，展现自然光影魅力\n- **景深虚化技巧**：背景适度虚化，突出主体，增强画面层次感\n- **半透明叠加处理**：文字与背景间常有微妙的半透明效果\n- **隐性品牌符号**：品牌元素融入自然场景，不刻意张扬\n- **导航指示符号**：左右导航箭头简洁统一，融入整体设计\n- **水墨意境渲染**：部分元素带有东方水墨画的意境处理\n- **大气构图法则**：遵循三分法或黄金分割构图，画面大气平衡", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/6cae0a68d180ade4ebc3d94073fced27"}]}, {"id": 11, "name": "简约醒目风", "nameEn": "Simple and Eye-catching Style", "desc": "", "prompt": "# 专业要求 ## 简约醒目风小红书 ## 设计风格\n\n低饱和度底色：选用淡米色、淡粉色、淡紫色等低饱和度色彩作为背景，营造出舒适、不刺眼的视觉氛围。\n纯色背景：各封面均为纯色背景，简洁干净，无多余图案或复杂纹理。\n无特殊形状容器：文字和元素直接置于背景上，无特定形状的容器框定。\n简洁无留白：整体布局较为紧凑，没有大面积留白，但也不显得拥挤。\n风格统一：整体设计风格简约、现代，不同内容的封面保持一致。\n无明显边框：封面四周没有明显的边框装饰。\n\n## 文字排版风格\n\n超大字号标题：使用超大号字体突出主要内容，瞬间抓住眼球。\n无段落划分：文字整体呈现为一个整体，无明显段落区分。\n无标点符号：较少使用标点符号，以自然流畅的语句呈现内容。\n无表情符号融入：文字中未使用表情符号。\n重点词标注：用不同颜色的底色标注重点词汇，强调关键信息。\n疑问式表达：多以提问或陈述特殊事件的方式表达，引发好奇心。\n单层排版：文字排版层级简单，无明显副标题等多层结构。\n\n## 视觉元素风格\n\n简单表情符号：在部分封面中搭配简单的 emoji 表情，增添趣味性。\n表情与内容呼应：表情符号的情感与文字内容相呼应，增强情感表达。\n无场景化呈现：没有具体的场景化图案或角色。\n无实物图融合：不包含真实照片与卡通元素的融合。\n少量装饰：仅有简单的表情符号作为装饰，无其他多余元素。\n元素位置灵活：表情符号位置不固定，根据文字排版灵活放置。\n无拟人化处理：未将抽象概念通过卡通形象拟人化。", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/879d899e9dc0056ad4819da2af8644e7"}]}, {"id": 12, "name": "干净蓝色背景大字风", "nameEn": "Clean Blue Background Big Character Style", "desc": "", "prompt": "# 专业要求 ## 干净蓝色背景大字风\n## 设计风格：采用简洁清新的设计思路，背景为纯净的浅蓝色，给人以清爽、舒适之感，没有多余的复杂图案干扰，整体视觉上简洁大方，符合当下简约美学潮流，能在视觉上快速吸引对简洁风格感兴趣的用户。\n## 文字排版风格：文字布局清晰明了，主要信息以黑色粗体字呈现，字号较大且居于画面中心偏上位置，突出主题，下方搭配一个俏皮的表情符号，位置自然，起到活跃氛围的作用，使文字与符号形成呼应，排版上注重主次分明，易于阅读。\n## 视觉元素风格：仅使用了一个表情符号作为点缀，表情生动活泼，为整体简洁的画面增添了趣味性和情感温度，没有其他繁杂图形，突出简洁与直观。", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/4c81fcf54609a4d146ebc4e4d5da7bbc"}]}]