[{"id": 1, "name": "新潮工业反叛风", "nameEn": "Trendy Industrial Rebel Wind", "desc": "", "prompt": "# 专业要求 ## 新潮工业反叛风 ## 设计风格 - **黑底强对比美学**：纯黑背景配以白色和荧光黄色元素，形成极强视觉冲击力 - **地下文化气质**：类似独立音乐海报或前卫艺术展的反主流美学 - **工业印刷风格**：模拟工业标识和手册的粗犷实用主义 - **后现代解构主义**：打破传统设计规则，强调不规则性与碎片化 - **都市青年反叛感**：通过设计表达对传统职场文化的质疑和调侃 - **数字朋克气息**：融合数字时代的视觉元素与朋克文化的反抗精神 - **实验性排版探索**：把排版本身作为设计表达的主要手段 ## 文字排版风格 - **巨型中文标题**：超大号汉字形成强烈的视觉重心 - **轮廓线英文**：英文采用线条勾勒的空心字体，增强现代感 - **多向阅读结构**：文字横向、纵向、分散排列，打破常规阅读习惯 - **拆分重组文本**：将词语拆解并重新组合排版，如 '打|工|摸|鱼|指|南' - **重复性文本背景**：将口号反复呈现作为背景填充 - **极端字号对比**：从超大到极小的文字尺寸变化，创造丰富层次 - **悬浮式文字布局**：各文本块看似随意又有序地悬浮在画面中 ## 视觉元素风格 - **线条鱼图形符号**：简笔画风格的鱼作为核心视觉标识和概念象征 - **星号装饰点缀**：使用'*'符号作为点缀元素，增添活力 - **几何框架结构**：L形、方块、椭圆等简单几何形状构建画面架构 - **日期数字化处理**：'07.05-08.20'等数字信息以现代技术感的方式呈现 - **标语口号突显**：'人生是旷野，家里没矿就不敢野'作为文化态度象征 - **荧光高亮区域**：使用荧光黄突出关键内容，如'打工人'标识 - **重复元素韵律**：通过元素重复创造视觉节奏和连续性 ## 文本适应性规则 - **自动换行处理**：长文案自动换行，不会截断内容 - **动态字体大小**：根据文本长度自动调整字体大小 - **响应式布局**：设计能随文本长度调整各元素位置 - **最小可读性保证**：即使缩小文字也确保清晰可读 - **多行文本适配**：过长内容分为多行,保持设计美感 - **视觉层次保护**：调整大小时保持文本的视觉层次关系", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/6725b7bfba1e14e6b08eb31cf6a6f7d7"}]}, {"id": 2, "name": "软萌知识卡片风", "nameEn": "Soft and Cute Knowledge Card Style", "desc": "", "prompt": "# 专业要求 ## 软萌知识卡片风 ## 设计风格 - **柔和色彩基调**：以粉色、米黄、淡紫等温和色调为主，营造轻松友好氛围 - **圆角卡片结构**：所有内容采用大圆角矩形或椭圆形容器，没有尖锐边角 - **简约留白处理**：适当留白增强可读性，避免视觉拥挤感 - **渐变色背景**：部分卡片使用柔和渐变背景，增加层次感和温暖感 - **情感化设计**：整体风格偏向亲和、轻松，不过分严肃正式 - **统一视觉语言**：各卡片尽管内容不同，但维持一致的设计语言 - **轻量化边框**：红色封面使用笔记本线条等轻量级边框元素增加趣味性 ## 文字排版风格 - **大字号标题**：标题文字加粗加大，吸引第一眼注意 - **紧凑段落布局**：正文内容分段清晰，段落间距适中 - **感叹号点缀**：频繁使用感叹号增强情感表达和亲近感 - **表情符号融入**：在文字中加入'」'等特殊符号增加表现力 - **重点句加粗**：关键信息或总结性内容加粗处理 - **自然语言表达**：采用口语化、对话式的表达方式，降低阅读门槛 - **多层级排版**：标题、副标题、正文、强调语等形成清晰的阅读层级 ## 视觉元素风格 - **Q版表情角色**：底部配置可爱的emoji表情或形象，增加亲和力 - **表情丰富多样**：使用惊讶、思考、无奈等多种表情，与文本内容情感呼应 - **场景化呈现**：如电脑前工作的人物、阅读书本的角色等场景化表达 - **实物图融合**：如猫咪真实照片与卡通风格的结合 - **点缀型装饰**：适量使用小装饰元素，如笔记本边缘的圆点标记 - **形象位置统一**：视觉元素多位于卡片底部，形成稳定的视觉期待 - **拟人化处理**：将抽象概念通过卡通形象拟人化，增强理解和记忆 ## 文本适应性规则 - **卡片大小自适应**：卡片尺寸根据文本长度动态调整 - **字体大小平衡**：根据内容自动调整字体大小,保持可爱风格 - **留白比例保持**：即使文本增多也保持足够留白 - **文本完整呈现**：确保所有文本内容完整显示，不截断 - **卡通元素位置适应**：装饰元素位置随文本长度智能调整 - **分段优化显示**：长文本自动进行视觉友好的分段", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/1b9ae45c4309a7c4457d84bcc3f36b0a"}]}, {"id": 3, "name": "商务简约信息卡片风", "nameEn": "Business Simple Information Card Style", "desc": "", "prompt": "# 专业要求 ## 商务简约信息卡片风 ## 设计风格 - **极简背景设计**：采用浅灰色或白色背景，减少视觉干扰 - **高对比度呈现**：黑色文字与浅色背景形成强烈对比，提升可读性 - **方正几何布局**：整体结构方正规整，遵循严格的网格系统 - **功能性优先**：设计服务于内容传达，摒弃多余装饰元素 - **色块分区设计**：通过彩色方块标识不同信息点，便于快速识别 - **圆角矩形容器**：软化边缘，增加亲和力但保持商务感 - **留白合理利用**：为重要内容预留足够呼吸空间，避免信息拥挤 ## 文字排版风格 - **问答式标题结构**：以问题开头('在家办公效率低?'、'运动量变小?')引发共鸣 - **解决方案副标题**：紧随问题后给出简洁有力的解决方案 - **字体层级鲜明**：通过明确的字号变化区分标题、副标题和正文 - **短句精炼表达**：多用简短有力的句子，以句号结尾，节奏感强 - **加粗重点处理**：核心词汇或短语加粗处理，引导视线焦点 - **中英文混排**：品牌名称保留英文，增加国际化专业感 - **要点式内容组织**：将功能特点和优势以简短条目形式呈现 ## 视觉元素风格 - **产品实物展示**：在卡片下方放置产品包装实物照片，真实直观 - **功能性图标**：如'居家模式'的房屋图标，增强视觉识别度 - **开关按钮元素**：采用可交互感的UI组件表现，如模式开关按钮 - **数字编号标识**：使用彩色背景数字标记不同要点，提升可读性 - **品牌标识垂直排列**：'CHOCODAY'字样垂直排列于右侧，形成识别特征 - **色彩编码系统**：使用绿色、黄色等不同色彩区分不同信息模块 - **简约线条边框**：适当使用线条框架划分内容区域，结构清晰 ## 文本适应性规则 - **信息区块动态调整**：根据文本量智能调整信息区块大小 - **商务排版保持**：自动调整布局同时保持专业商务感 - **要点列表自适应**：要点数量增加时自动调整间距和字号 - **关键词突显持续**：无论文本如何调整都确保关键词突显 - **最小商务字体限制**：缩小字体时保持商务场景必要的最小可读性 - **内容分区平衡**：确保即使文本增加,各分区信息比例平衡", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/06df8348dd52db763da21eab6880b764"}]}, {"id": 4, "name": "新构成主义教学风", "nameEn": "New Constructivism Teaching Style", "desc": "", "prompt": "# 专业要求 ## 新构成主义教学风 ## 设计风格 - **黑红白三色系统**：以黑白强对比为基调，点缀醒目红色形成视觉冲击力 - **网格化精准排版**：严谨的网格系统控制整体构图，传达专业设计感 - **学术实验美学**：融合学术研究与实验性设计的双重特质 - **日式现代主义**：汲取日本设计美学，注重留白与紧凑并存的张力 - **教学图解风格**：设计元素同时承载教育功能，如轴线系统的可视化呈现 - **多层次信息构建**：通过色块、线条和排版创造清晰的视觉层次 - **设计史反思性**：将历史元素与当代设计语言结合，形成对话关系 ## 文字排版风格 - **中英双语对照**：专业设计术语同时以中英文呈现，增强学术性 - **极端对比字阶**：超大号标题与小号解释文字形成强烈视觉节奏 - **多向文字排布**：结合横排、竖排和径向排列的文字方向实验 - **标点符号设计化**：将问号、括号等符号放大或突出作为视觉元素 - **注释系统完备**：学术化的引用、说明和注解系统，增强专业可信度 - **数字图形化处理**：'100' 等数字被设计为具有视觉冲击力的图形元素 - **专业术语突显**：关键设计概念通过排版手段强调，如'一根轴'、'构图'等 ## 视觉元素风格 - **红线贯穿引导**：红色线条作为视觉引导和强调，贯穿整体设计 - **几何形符号系统**：三角形、圆点等几何符号作为辅助设计语言 - **教学指示标记**：箭头、下划线等元素具有明确的指向性和教育性 - **区块分明信息区**：内容被清晰划分为不同信息区块，层次分明 - **历史与现代并置**：传统元素与现代设计手法并置，形成时间跨度的视觉对话 - **签名式认证标记**：作者标识、成为设计的权威来源认证 - **微妙纹理变化**：背景中若隐若现的纹理增添设计深度，避免平面化 ## 文本适应性规则 - **网格系统自适应**：根据内容量自动调整网格布局 - **学术文本完整性**：确保所有专业术语和解释完整显示 - **多层级信息收缩**：长文本时学术注释自动减小但保持可读 - **关键术语保护**：任何情况下确保核心学术术语不被截断 - **文本分组优化**：自动将长文本分组显示,保持学术可读性 - **红线引导延展**：视觉引导线根据文本长度智能延展", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/bc7d12fefedb2be3b01a6559ac6ca0c2"}]}, {"id": 5, "name": "数字极简票券风", "nameEn": "Digital Minimalist Ticket Style", "desc": "", "prompt": "# 专业要求 ##  数字极简票券风 ## 设计风格 - **黑白对比主导**：高度对比的黑白配色方案，形成强烈视觉冲击 - **票券化布局**：类似登机牌、门票或电子凭证的结构设计 - **几何分区明确**：画面被精确划分为信息区块，井然有序 - **留白艺术运用**：大量有效留白提升整体通透感和优雅度 - **东西方美学融合**：结合中文传统排版与西方现代设计语言 - **工业设计感**：注册商标符号、条形码等商业元素的精致运用 - **数字界面映射**：模拟电子屏幕或应用界面的信息呈现方式 ## 文字排版风格 - **中英混排对比**：中英文字体混合使用，创造文化融合感 - **尺寸层级分明**：主标题大号处理，副文本精致小巧 - **多向排列组合**：包含横排、竖排、斜排等多方向文字布局 - **间距精确控制**：字符间距和行距经过精心计算，保持呼吸感 - **符号化装饰**：括号、下划线、箭头融入文字设计 - **衬线与非衬线混搭**：不同字体家族交替使用，增强层次感 - **时间信息格式化**：日期标注采用统一格式，搭配方向指示符 ## 视觉元素风格 - **功能性指示符**：各类箭头、星号作为视觉引导和强调 - **UI元素借鉴**：'CHECK IN'、'@'等数字界面元素的平面化应用 - **边框与分割线**：简洁线条用于区隔不同信息区域 - **简约图形符号**：最小化的设计符号传达核心信息 - **手写风点缀**：如'Romantic'的手写体为机械排版增添人文温度 - **方向性视觉流动**：通过元素排布创造从左到右、从上到下的阅读节奏 - **负空间利用**：将空白区域视为积极设计元素的一部分 ## 文本适应性规则 - **极简自适应布局**：保持极简主义的同时适应不同文本长度 - **比例关系保持**：文本变化时保持黑白空间比例关系 - **字体大小智能调整**：根据文本长度自动缩放字体大小 - **留白策略灵活**：文本增加时智能调整留白比例 - **完整信息呈现**：确保所有文本内容完整显示,不截断边缘 - **票券结构延展**：票券设计根据内容自动延长或缩短,保持美感", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/21ac1615bad2c4f447b2764bfa21f842"}]}, {"id": 6, "name": "柔和科技卡片风", "nameEn": "Soft Tech Card Style", "desc": "", "prompt": "# 专业要求\n##  柔和科技卡片风\n## 设计风格\n- **圆角卡片布局**：使用大圆角白色或彩色卡片作为信息容器，创造友好亲和感\n- **轻柔色彩系统**：主要采用淡紫、浅黄、粉色、米色等柔和色调，避免强烈视觉刺激\n- **极简留白设计**：大量留白空间增强可读性，减少视觉疲劳\n- **阴影微立体**：subtle阴影效果赋予界面轻微的立体感，不过分强调\n- **功能美学主义**：设计服务于功能，没有多余装饰元素\n- **网格化布局**：基于明确的网格系统排列卡片，保持整体秩序感\n- **渐变色点缀**：部分界面使用柔和渐变作为背景，如米色到蓝色的过渡，增加现代感\n\n## 文字排版风格\n\n- **数据突显处理**：关键数字信息使用超大字号和加粗处理，如\"12,002\"、\"20x\"\n- **层级分明排版**：标题、说明文字、数据、注释等使用明确的字号层级区分\n- **简约无衬线字体**：全部采用现代简洁的无衬线字体，提升可读性\n- **文字对齐规整**：在卡片内保持统一的左对齐或居中对齐方式\n- **重点色彩标识**：使用蓝色等高对比度颜色标记重要术语，如\"tweets\"和\"threads\"\n- **空间呼吸感**：文字块之间保持充足间距，创造\"呼吸\"空间\n- **品牌名称特殊处理**：产品名称如\"alohi\"、\"deel.\"采用特殊字体或风格，强化品牌识别\n\n## 视觉元素风格\n\n- **微妙图标系统**：使用简约线性或填充图标，大小适中不喧宾夺主\n- **进度可视化**：使用环形或条状图表直观展示进度，如年度完成百分比\n- **色彩编码信息**：不同卡片使用不同色彩，便于快速区分功能模块\n- **品牌标识整合**：将产品logo自然融入界面，如\"alohi\"的圆形标识\n- **人物头像元素**：适当使用圆形头像增加人性化特质，如客户推荐卡片\n- **几何形状装饰**：使用简单几何形状作为背景装饰，如半透明圆形\n- **组件一致性**：按钮、标签、选项卡等元素保持统一风格，提升系统感", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/93e036605887845acbe9a13b2c420132"}]}, {"id": 7, "name": "现代商务资讯卡片风", "nameEn": "Modern Business Information Card Style", "desc": "", "prompt": "# 专业要求 ## 现代商务资讯卡片风 ## 设计风格\n\n- **色彩情绪编码**：使用深绿与深红色调分别象征金融稳健与企业活力\n- **主题色块构成**：整体采用大面积单一色调作为背景，营造专业稳重氛围\n- **卡片式设计**：内容以圆角卡片形式呈现，现代简约且边界感明确\n- **商务应用美学**：符合金融科技类应用的视觉设计规范与审美\n- **微妙渐变处理**：背景色采用细腻渐变效果，增强层次感\n- **网格底纹肌理**：融入轻微网格线与点阵纹理，提升科技感与专业度\n- **功能导向设计**：布局与元素安排以提升信息获取效率为首要目标\n\n## 文字排版风格\n\n- **三级信息层级**：通过明确的字号和粗细区分头条标签、主标题和辅助信息\n- **大标题强调**：主要新闻标题占据视觉中心，字号最大且加粗\n- **左对齐规整排版**：所有文字元素保持左对齐，结构严谨有序\n- **无衬线字体选用**：采用现代商务风格的无衬线字体，提高可读性和专业感\n- **标题分行处理**：长标题采用多行排版，每行字数适中，便于快速阅读\n- **日期位置固定**：日期信息位置统一，作为时效性标识\n- **留白节奏控制**：文字块之间保持适当留白，创造舒适阅读节奏\n\n## 视觉元素风格\n\n- **指向性图标**：右上角箭头图标暗示可点击进入详情的交互性质\n- **点阵背景纹理**：背景中的微妙点阵增加设计深度，避免平面单调\n- **进度指示条**：底部的分段线条作为浏览进度或内容分区指示\n- **主题色彩区隔**：不同新闻主题采用不同色调区分（金融绿色/科技红色）\n- **高对比度文字**：浅色文字在深色背景上形成强烈对比，确保可读性\n- **内容统一格式**：\"Today's News\"标签在相同位置出现，建立品牌一致性\n- **简洁无干扰界面**：排除多余装饰元素，聚焦于核心信息传递", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/1ff91905662115ca314f9d49185831d0"}]}, {"id": 8, "name": "流动科技蓝风格", "nameEn": "Flowing Tech Blue Style", "desc": "", "prompt": "# 专业要求 ## 流动科技蓝风格\n\n## 设计风格\n\n- 现代简约科技风，以蓝色系为主体色调\n- 大量使用蓝白渐变，营造轻盈通透感\n- 背景多采用极简白底或浅色调\n- 运用流线型曲线创造动态视觉效果\n- 圆角矩形作为基础框架，增加友好感\n- 整体布局干净有序，空间感强\n- 光影效果柔和，营造科技感与未来感\n\n## 文字排版风格\n\n- 标题简洁有力，通常使用黑体或无衬线字体\n- 显著的标题层级对比，主副标题大小分明\n- 中英文混排，增加国际化视觉效果\n- 关键信息放大处理，辅助文字精简\n- 日期、标签等信息排版整齐规范\n- 文字与背景形成适当对比，确保清晰可读\n- 数字与文本搭配得当，注重整体平衡\n\n## 视觉元素风格\n\n- 流动曲线是主要装饰元素，表现科技流动感\n- 半透明蓝色波纹或螺旋形状贯穿多个设计\n- 几何抽象形状作为点缀（圆环、三角形等）\n- 轻量级图标和按钮设计，简洁明了\n- 折纸元素（如纸飞机）象征传递与连接\n- 光效处理柔和，形成层次感\n- 整体视觉元素与科技、数据、信息等主题高度契合", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/7bb0ec5aa4ff76f0fd428bcce17a6570"}]}, {"id": 9, "name": "极简格栅主义封面风格", "nameEn": "Minimalist Gridism Cover Style", "desc": "", "prompt": "# 专业要求 ## 极简格栅主义封面风格\n\n## 设计风格\n\n- **黑白极简风格**：以纯黑背景和纯白内容区形成鲜明对比\n- **强烈的几何感**：使用简洁的线条、方框和圆形等基础几何元素\n- **网格系统布局**：遵循严格的网格排版规则，结构清晰有序\n- **留白有度**：大量留白创造呼吸感，同时保持视觉重心\n- **摄影与排版结合**：真实场景照片与极简排版形成互补\n- **工业风格装饰**：细线箭头、指示线条等元素增添设计感\n- **微妙的色彩点缀**：小面积绿色等强调色打破黑白单调\n\n## 文字排版风格\n\n- **大胆字号对比**：核心标题极大化处理，形成主视觉\n- **几何式分割标题**：将主标题分解成独立区块，增强辨识度\n- **纵横组合排版**：文字既有横排也有竖排，创造韵律感\n- **字体粗细对比强烈**：主标题采用超黑体，副文本则较为轻盈\n- **多层级信息排列**：活动名称、日期、宣传语清晰分级\n- **严格的文字对齐**：所有文字元素依循严格的网格对齐原则\n- **中英文混排**：英文作为装饰性元素增添国际设计感\n\n## 视觉元素风格\n\n- **裁切的摄影图像**：图片经过精心裁切，凸显主题\n- **指示性线条**：箭头、曲线和直线作为引导性视觉元素\n- **框架式强调**：使用方框、底色块等元素强调关键信息\n- **简洁图形符号**：最小化的视觉符号传达核心信息\n- **构图对称与不对称并存**：整体结构有序但细节处理不拘一格\n- **空间层次感**：通过元素大小、位置创造前后层次关系\n- **数字图形化处理**：日期数字被赋予视觉设计感", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/b3048fb222d31e1aeecd83628397a1f0"}]}, {"id": 10, "name": "奢华自然意境风", "nameEn": "Luxury Natural Artistic Conception Style", "desc": "", "prompt": "# 专业要求 ## 奢华自然意境风\n\n## 设计风格\n\n- **高级沉稳色调**：暗调景观背景配以细腻光影，营造高端内敛氛围\n- **意境式呈现**：不仅展示实景，更表达一种与自然共融的精神状态\n- **奢华隐喻元素**：通过构图和文字暗示高端生活方式与品质追求\n- **空间层次丰富**：通过前景、中景、远景的搭配创造空间深度感\n- **东西方美学融合**：中式意境与西方现代摄影美学的和谐结合\n- **沉浸式体验设计**：画面设计引导观者产生身临其境的感受\n\n## 文字排版风格\n\n- **悬浮式标题定位**：文字悬浮于景观之上，形成虚实对比\n- **中西文混合排版**：英文与中文标题组合使用，增强国际化气质\n- **层级分明的字阶**：主标题、副标题和说明文字尺寸差异明显\n- **优雅字体选择**：英文多用细腻的衬线体，中文选用简约现代字体\n- **巧妙的文字拆分**：文字的艺术性拆解处理\n- **留白与文字平衡**：大面积留白中点缀核心文字，强化重点信息\n- **边缘式辅助信息**：次要文字信息常放置于画面边缘，不干扰主视觉\n\n## 视觉元素风格\n\n- **摄影级光影处理**：专业摄影级别的光线捕捉，展现自然光影魅力\n- **景深虚化技巧**：背景适度虚化，突出主体，增强画面层次感\n- **半透明叠加处理**：文字与背景间常有微妙的半透明效果\n- **隐性品牌符号**：品牌元素融入自然场景，不刻意张扬\n- **导航指示符号**：左右导航箭头简洁统一，融入整体设计\n- **水墨意境渲染**：部分元素带有东方水墨画的意境处理\n- **大气构图法则**：遵循三分法或黄金分割构图，画面大气平衡", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/1bb95e44fbc11e817217e9bb0e781594"}]}, {"id": 11, "name": "简约醒目风", "nameEn": "Simple and Eye-catching Style", "desc": "", "prompt": "# 专业要求 ## 简约醒目风小红书 ## 设计风格\n\n低饱和度底色：选用淡米色、淡粉色、淡紫色等低饱和度色彩作为背景，营造出舒适、不刺眼的视觉氛围。\n纯色背景：各封面均为纯色背景，简洁干净，无多余图案或复杂纹理。\n无特殊形状容器：文字和元素直接置于背景上，无特定形状的容器框定。\n简洁无留白：整体布局较为紧凑，没有大面积留白，但也不显得拥挤。\n风格统一：整体设计风格简约、现代，不同内容的封面保持一致。\n无明显边框：封面四周没有明显的边框装饰。\n\n## 文字排版风格\n\n超大字号标题：使用超大号字体突出主要内容，瞬间抓住眼球。\n无段落划分：文字整体呈现为一个整体，无明显段落区分。\n无标点符号：较少使用标点符号，以自然流畅的语句呈现内容。\n无表情符号融入：文字中未使用表情符号。\n重点词标注：用不同颜色的底色标注重点词汇，强调关键信息。\n疑问式表达：多以提问或陈述特殊事件的方式表达，引发好奇心。\n单层排版：文字排版层级简单，无明显副标题等多层结构。\n\n## 视觉元素风格\n\n简单表情符号：在部分封面中搭配简单的 emoji 表情，增添趣味性。\n表情与内容呼应：表情符号的情感与文字内容相呼应，增强情感表达。\n无场景化呈现：没有具体的场景化图案或角色。\n无实物图融合：不包含真实照片与卡通元素的融合。\n少量装饰：仅有简单的表情符号作为装饰，无其他多余元素。\n元素位置灵活：表情符号位置不固定，根据文字排版灵活放置。\n无拟人化处理：未将抽象概念通过卡通形象拟人化。", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/9424d7b50ff7bf5a093f752081774db7"}]}, {"id": 12, "name": "干净蓝色背景大字风", "nameEn": "Clean Blue Background Big Character Style", "desc": "", "prompt": "# 专业要求 ## 干净蓝色背景大字风\n## 设计风格：采用简洁清新的设计思路，背景为纯净的浅蓝色，给人以清爽、舒适之感，没有多余的复杂图案干扰，整体视觉上简洁大方，符合当下简约美学潮流，能在视觉上快速吸引对简洁风格感兴趣的用户。\n## 文字排版风格：文字布局清晰明了，主要信息以黑色粗体字呈现，字号较大且居于画面中心偏上位置，突出主题，下方搭配一个俏皮的表情符号，位置自然，起到活跃氛围的作用，使文字与符号形成呼应，排版上注重主次分明，易于阅读。\n## 视觉元素风格：仅使用了一个表情符号作为点缀，表情生动活泼，为整体简洁的画面增添了趣味性和情感温度，没有其他繁杂图形，突出简洁与直观。", "preview": [{"key": "7535a8cd108a6de7ca16a97ba836465e/7db722d8b4aa9aff48c9d8d337833658"}]}]