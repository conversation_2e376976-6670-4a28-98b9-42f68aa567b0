#-*- coding: utf-8 -*-
import gevent.monkey

gevent.monkey.patch_all()
import os
import sys
from flask import Flask
from flask_cors import CORS
from elasticapm.contrib.flask import ElasticAPM

from config import *
from utils.watt_service.call_watt_gpt import CallWattGPT
from utils.mongo_conn import MongodbConn
from utils.init_log import InitLog
from utils.util_flask import printInParams, responsesData, getPostParam, fmtSaveOutput
from service.lib.prompt_fusion import PromptsFusion

app = Flask(APP_NAME,
            template_folder='templates',
            static_folder='static')

app.config.from_pyfile('config.py', silent=True)

app.json.ensure_ascii = False

# util_flask
app.printInParams = printInParams
app.responsesData = responsesData
app.getPostParam = getPostParam
app.fmtSaveOutput = fmtSaveOutput

if app.config['LOG_LEVEL'] == "DEBUG":
    print("APP_NAME:" + app.config['APP_NAME'])
    print("LOG_LEVEL: {}".format(app.config['LOG_LEVEL']))
    print("FLASK_LOG_PATH: {}".format(app.config['FLASK_LOG_PATH']))
    print("ELASTIC_APM: {}".format(app.config['ELASTIC_APM']))

###############################################
# apm
###############################################
APM = ElasticAPM(app)
app.APM = APM

###############################################
# log
###############################################
print("======== 初始化日志 ========")
if not os.path.exists(app.config['LOG_DIR']):
    os.makedirs(app.config['LOG_DIR'])

FlaskLog = InitLog(
    'FlaskLog',
    app.config['FLASK_LOG_PATH'],
    level=app.config['LOG_LEVEL'],
    apm=False,
    apm_client=APM.client
).init()
app.FlaskLog = FlaskLog
print("FlaskLog 初始化完成: {}\n".format(FlaskLog))

CollectLog = InitLog(
    'CollectLog',
    app.config['FLASK_LOG_PATH'],
    level="DATA_COLLECT",
    apm=False,
    apm_client=APM.client
).init()
app.CollectLog = CollectLog
print("CollectLog 初始化完成: {}\n".format(CollectLog))

###############################################
# mongodb
###############################################
print("======== 初始化 Mongodb for Service ========")
try:
    _mongo_conn_creator = MongodbConn(MONGODB_CONFIG)
    mongo_client = _mongo_conn_creator.conn()
    mongoDatabase = MONGODB_DATABASE
    mongoConn = _mongo_conn_creator
    print("初始化 Read Mongodb Client 完成\n")

except Exception as e:
    print("初始化 Mongodb Client 失败 - {}: {}\n".format(e.__class__, e))
    FlaskLog.error("初始化 Mongodb Client连接失败 - {}: {}".format(e.__class__, e))
    sys.exit(201)

###############################################
# Call Watt GPT
###############################################
print("======== 初始化 Watt GPT for Service ========")
try:
    callWattGPT = CallWattGPT(app.config['WATT_AI_GPT_HOST'], app.config['WATT_AI_GPT_TOKEN'])
    print("初始化CallGPT完成\n")
except Exception as e:
    print("初始化CallGPT失败 - {}: {}\n".format(e.__class__, e))
    FlaskLog.error("初始化CallGPT失败 - {}: {}".format(e.__class__, e))
    sys.exit(201)

prompts_fusion = PromptsFusion()

LUPAN_HOST = app.config['LUPAN_HOST']
LUPAN_KEY = app.config['LUPAN_KEY']
GOOGLE_BACK_KEY = app.config['GOOGLE_BACK_KEY']
qwen_domestic_key = app.config['QWEN_DOMESTIC']
qwen_intl_key = app.config['QWEN_INTL']
qwen_intl_url = app.config['QWEN_INTL_URL']

CORS(app)
