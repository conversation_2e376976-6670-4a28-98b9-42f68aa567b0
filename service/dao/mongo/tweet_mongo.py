from service.dao import SuperMongoDao, bson2json
from tqdm import tqdm
import pandas as pd


class TweetMongoDB(SuperMongoDao):
    """目标数据库 Mongodb
    """

    def __init__(self, collection="tweet", database=None):
        super().__init__()
        self.collection_name = collection
        db_to_use = self.client[database] if database else self.db
        if db_to_use is None:
             raise ValueError("MongoDB database object not initialized correctly.")
        self.collection_conn = db_to_use[self.collection_name]

    def countHisETLData(self):
        """
        """
        return self.collection_conn.count_documents({})

    def get_first_n_data(self, limit=0):
        return self.collection_conn.find(limit=limit)

    def get_last_n_data(self, limit=0, sort_by=''):
        if not sort_by:
            sort_by = '_id'
        if limit <= 0:
            return self.collection_conn.find().sort(sort_by, -1)
        else:
            return self.collection_conn.find().sort(sort_by, -1).limit(limit)

    def find_data(self, query={}, id=None, limit=0):
        if id is not None:
            query = {"_id": id}
        return self.collection_conn.find(query).sort('_id', -1).limit(limit) if limit else self.collection_conn.find(query).sort('_id', -1)

    def singleSearch(self, key, value):
        return self.collection_conn.find_one({key: value})

    def purge_collection(self):
        try:
            data_cursor = self.get_first_n_data()
            count = 0
            print(f'Deleting all data from collection: {self.collection_name}')
            for d in tqdm(data_cursor):
                self.collection_conn.delete_one({"_id": d['_id']})
                count += 1
            print(f'Deleted {count} documents.')
            return True
        except Exception as e:
            print('Error', e)
            return False

    def get_formated_data(self, limit=0):
        res_cursor = self.get_last_n_data(limit=limit)
        if res_cursor:
            df = pd.DataFrame(list(res_cursor)).rename(columns={'_id': 'candidate_id'})
            cols = [col for col in ['candidate_id', 'tags', 'summary'] if col in df.columns]
            return df[cols]
        return pd.DataFrame()

    def insert_one(self, data, overwrite=False):
        try:
            if overwrite:
                return self.collection_conn.replace_one({"_id": data['_id']}, data, upsert=True).acknowledged
            return self.collection_conn.insert_one(data).acknowledged
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def insert(self, data):
        try:
            if not isinstance(data, list):
                data = [data]
            return self.collection_conn.insert_many(data).acknowledged
        except Exception as e:
            print('Error inserting data: ', e)
            return False

    def update_one(self, data):
        try:
            if '_id' not in data:
                print("Error updating data: '_id' field missing.")
                return False
            update_operation = {'$set': data}
            return self.collection_conn.update_one({"_id": data['_id']}, update_operation, upsert=True).acknowledged
        except Exception as e:
            print('Error updating data: ', e)
            return False

    def delete_data(self, id):
        try:
            return self.collection_conn.delete_one({"_id": id}).acknowledged
        except Exception as e:
            print('Error deleting data: ', e)
            return False


if __name__ == '__main__':
    pass
