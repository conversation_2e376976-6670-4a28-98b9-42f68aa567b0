from .. import SuperMongoDao, bson2json, mongoDatabase


class BaseMongoDB(SuperMongoDao):
    """
    Base MongoDB Operation
    """

    def __init__(self, collection: str, database: str = None):
        super().__init__()

        db_to_use = self.client[database] if database else self.db
        if db_to_use is None:
            raise ValueError("MongoDB database object not initialized correctly.")

        self.collection_name = collection
        self.collection_conn = db_to_use[self.collection_name]

    def singleSearch(self, key, value):
        # search one query by key
        result = self.collection_conn.find_one({key: value})
        try:
            return True, bson2json(result)
        except Exception as e:
            return False, e

    def dict_single_Search(self, criteria):
        # search one query by dict
        result = self.collection_conn.find_one(criteria)
        try:
            return True, bson2json(result)
        except Exception as e:
            return False, e

    def batchSearch(self, key, value):

        query = {key: {"$in": value}}
        result = self.collection_conn.find(query)
        try:
            cursor = self.collection_conn.find(query)
            result_list = list(cursor)  # 将Cursor对象转换为列表
            return True, bson2json(result_list)
        except Exception as e:
            return False, e

    def duoBatchSearch(self, key1, value1, key2, value2):
        query = {"$and": [{key1: {"$in": value1}}, {key2: {"$in": value2}}]}
        result = self.collection_conn.find_one(query)
        try:
            return True, bson2json(result)
        except Exception as e:
            return False, e

    def countData(self):
        """
        count total number
        """
        try:
            return True, self.collection_conn.count_documents({})
        except Exception as e:
            return False, e

    def insertSingleData(self, data):
        try:
            return True, self.collection_conn.insert_one(data)
        except Exception as e:
            return False, e

    def insertBatchData(self, data):
        try:
            return True, self.collection_conn.insert_many(data)
        except Exception as e:
            return False, e

    def replaceSingleData(self, data):
        try:
            return True, self.collection_conn.replace_one(data[0], data[1], True)
        except Exception as e:
            return False, e

    def updateSingleData(self, data):
        try:
            return True, self.collection_conn.update_one(data[0], data[1], upsert=True)
        except Exception as e:
            return False, e

    def getSortedLastNData(self, limit, sort_key):
        try:
            if limit <= 0:
                return True, self.collection_conn.find().sort(sort_key, -1)
            else:
                return True, self.collection_conn.find().sort(sort_key, -1)[:limit]
        except Exception as e:
            return False, e

    def deleteData(self, key, value):
        try:
            return True, self.collection_conn.delete_one({key: value})
        except Exception as e:
            return False, str(e)

    def batchDeleteData(self, key, value, condition="$in"):

        """
        Args:
            condition: str, default = "$in", batch delete in; "$eq" -> equal; "exists" -> exists, value=true/false
        """

        try:
            return True, self.collection_conn.delete_many({key: {condition: value}})
        except Exception as e:
            return False, e

    def findData(self):
        try:
            return True, self.collection_conn.find()
        except Exception as e:
            return False, e

    def findMemoryLatestList(self, query: dict, time_field, limit=0):
        # 查询多条数据
        cursor = self.collection_conn.find(query).sort(time_field, -1).limit(limit)
        try:
            result_list = list(cursor)
            return True, self.client.bson2json(result_list)
        except Exception as e:
            return False, e
