from service import mongo_client, mongoDatabase
import json
import datetime
import decimal
from bson.objectid import ObjectId


class MongoJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (datetime.datetime,)):
            return str(obj)
        if isinstance(obj, (datetime.date,)):
            return str(obj)
        elif isinstance(obj, (decimal.Decimal,)):
            return float(obj)
        elif isinstance(obj, (ObjectId,)):
            return str(obj)
        else:
            return obj


def bson2json(data):
    return json.loads(json.dumps(
        data, cls=MongoJSONEncoder, ensure_ascii=False))


class SuperMongoDao:
    def __init__(self):      
        self.client = mongo_client
        self.db = self.client[mongoDatabase]
