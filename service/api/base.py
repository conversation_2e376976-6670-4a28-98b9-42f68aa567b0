from flask.views import MethodView
from flask import current_app, request, jsonify
from utils.util import getUUID


class PublicMethodView(MethodView):
    def __init__(self):
        """[类视图初始化, 打印入参]

        """
        # 生成请求ID
        self.request_id = getUUID()

        # 日志 打印入参
        current_app.FlaskLog.debug(f'''{self.request_id} printInParams: {current_app.printInParams(request)}''')

        # Post赋值
        self.result, self.arg = current_app.getPostParam(request.form.keys())

        # header
        self.header = request.headers
        if self.header.get('watttraceid') is None:
            self.request_header_watttraceid = self.request_id
        else:
            self.request_header_watttraceid = self.header['watttraceid']

        super(MethodView, self).__init__()

    def checkAuth(self):
        img_token = self.header.get('img-token', '')
        if not img_token:
            self.result['data'] = 'Error: img-token is empty'
            output = current_app.responsesData(90004, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)
        elif img_token != current_app.config['IMG_TOKEN']:
            self.result['data'] = 'Error: img-token is invalid'
            output = current_app.responsesData(90004, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)
        return True, img_token
