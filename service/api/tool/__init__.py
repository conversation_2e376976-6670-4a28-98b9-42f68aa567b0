# -*- coding: utf-8 -*-
"""
    Gateway of Internal Tools
"""
from flask import Blueprint
tool_blueprint = Blueprint("tool", __name__)

# Filter RedNote
from .rednote_filter import RedNoteFilterPost
tool_blueprint.add_url_rule('/filter_xhs_user', view_func=RedNoteFilterPost.as_view('_RedNoteFilterPost'))

# Extract File Content
from .file_extract import FileExtractPost
tool_blueprint.add_url_rule('/file_extract', view_func=FileExtractPost.as_view('_FileExtractPost'))
