# -*- coding: utf-8 -*-
"""
    RedNote Filter API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis.rednote_analysis import analyze_rednote
import traceback


class RedNoteFilterPost(PublicMethodView):
    """ RedNote Filter Post
    """

    decorators = [requires('noteList')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('noteList', None) is None:
            error_msg = f'noteList is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        note_list = self.arg.get('noteList', [])
        keywords = self.arg.get('keywords', [])
        model = self.arg.get('model', "gpt")
        welfare = self.arg.get('welfare')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, rednote_result = analyze_rednote(note_list=note_list, keywords=keywords, welfare=welfare, 
                                                     model=model, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'RedNote Filter Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'RedNote Filter Failed. {rednote_result}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = rednote_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)