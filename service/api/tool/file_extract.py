# -*- coding: utf-8 -*-
import logging
import traceback
from flask import jsonify, current_app
from ..base import PublicMethodView
from utils.util_flask import requires
import config
from service.server.analysis.file_extract import parse_multiple_files_grequests

# Configure logging
logger = logging.getLogger(__name__)


class FileExtractPost(PublicMethodView):
    """ Extract content from files """
    decorators = [requires('fileList')]

    def post(self):
        file_list = self.arg.get('fileList', [])
        if not file_list:
            self.result['data'] = {'error': 'fileList is empty or not provided.'}
            output = current_app.responsesData(20212, self.result, self.arg, self.request_id)
            current_app.FlaskLog.warning(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)
        direct = self.arg.get('direct', True)
        try:
            # 使用新的并发处理函数，传递 direct 参数
            results = parse_multiple_files_grequests(file_list, direct=direct)

            successful_extractions = []
            failed_extractions = []

            # 从结果中整理数据，只收集成功的内容
            for file_info in file_list:
                url = file_info.get("url")
                if not url:
                    continue
                
                status, content = results.get(url, (False, "Processing not initiated."))
                
                if status:
                    successful_extractions.append({
                        'url': url,
                        'content': content
                    })
                    logger.info(f"Successfully extracted content from {url}, length: {len(content)}")
                else:
                    error_msg = f"[Error processing file: {url}] {content}"
                    failed_extractions.append(error_msg)
                    logger.error(f"Failed to extract content from {url}: {content}")

            total_files = len([f for f in file_list if f.get("url")])
            successful_count = len(successful_extractions)
            
            logger.info(f"Total files: {total_files}, Successful: {successful_count}, Failed: {len(failed_extractions)}")

            if successful_count > 0:
                # 使用换行符分隔不同PDF的内容，并添加文件标识
                content_parts = []
                for i, extraction in enumerate(successful_extractions):
                    if len(successful_extractions) > 1:
                        # 多个文件时添加分隔标识
                        content_parts.append(f"\n=== PDF File {i+1} ===\n")
                    content_parts.append(extraction['content'])
                    if i < len(successful_extractions) - 1:
                        content_parts.append("\n\n")  # 文件间添加空行分隔
                
                final_content = "".join(content_parts)
                logger.info(f"Final merged content length: {len(final_content)}")
                
                self.result['data'] = {
                    "content": final_content,
                    "summary": {
                        "total_files": total_files,
                        "successful_files": successful_count,
                        "failed_files": len(failed_extractions)
                    }
                }
                output = current_app.responsesData(0, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)
            else:
                error_msg = f"All {total_files} files extraction failed. Errors: {'; '.join(failed_extractions)}"
                self.result['data'] = {'error': error_msg}
                output = current_app.responsesData(20212, self.result, self.arg, self.request_id)
                current_app.FlaskLog.error(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)

        except Exception as e:
            error_msg = f'File Extraction Error: {str(e)} - {traceback.format_exc()}'
            self.result['data'] = {'error': error_msg}
            output = current_app.responsesData(20212, self.result, self.arg, self.request_id)
            current_app.FlaskLog.error(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)
