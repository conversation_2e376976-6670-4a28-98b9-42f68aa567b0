# -*- coding: utf-8 -*-
"""
    Rewrite API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis.news_and_event import analyze_news_or_event_wrapper
from service.server.analysis.at_and_reply import analyze_at_or_reply
from service.server.analysis.ttVideo import analyze_tt_video
import json
import traceback


class NewsAndEventAnalysis(PublicMethodView):
    """ news and event analysis
    """

    decorators = [requires('todoTaskSource','userId')]

    # Http method
    def post(self):
        # 获取入参

        if self.arg.get('todoTaskSource', 0) not in [1, 2, 5]:
            error_msg = f'todoTaskSource must be 1 or 2 or 5.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        profile = self.arg.get('profile', {})
        plan_info = self.arg.get('planInfo', {})
        to_do_task_source = self.arg.get('todoTaskSource')
        user_id = self.arg.get('userId')

        # 业务逻辑
        try:
            status, analyze_result = analyze_news_or_event_wrapper(profile, plan_info, to_do_task_source, user_id)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'News and event Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'News and event Analysis Failed. {analyze_result}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = analyze_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class AtAndReplyAnalysis(PublicMethodView):
    """
    analysis for at and reply
    """
    decorators = [requires('profile', 'planInfo', 'mentionPostList', 'todoTaskSource')]

    def post(self):
        # 获取入参
        if self.arg.get('profile', None) is None:
            error_msg = f'profile is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('planInfo', None) is None:
            error_msg = f'planInfo is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('mentionPostList', None) is None:
            error_msg = f'mentionPostList is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('todoTaskSource', 0) not in [3, 4]:
            error_msg = f'todoTaskSource must be 3 or 4.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        profile = self.arg.get('profile', {})
        plan_info = self.arg.get('planInfo', {})
        mention_post_list = self.arg.get('mentionPostList', [])
        to_do_task_source = self.arg.get('todoTaskSource')


        # 业务逻辑
        try:
            status, analyze_result = analyze_at_or_reply(profile, plan_info, mention_post_list, to_do_task_source)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'At and reply Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'At and reply Analysis Failed. {analyze_result}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = analyze_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class ttVideoAnalysis(PublicMethodView):
    """
    analysis for tt video
    """
    decorators = [requires('profile', 'ttList', 'planList', 'todoTaskSource')]

    def post(self):
        # 获取入参
        if self.arg.get('profile', None) is None:
            error_msg = f'profile is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('planList', None) is None:
            error_msg = f'planList is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('ttList', None) is None:
            error_msg = f'ttList is missing.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        profile = self.arg.get('profile', {})
        plan_list = self.arg.get('planList', {})
        tt_list = self.arg.get('ttList', [])
        to_do_task_source = self.arg.get('todoTaskSource')

        # 业务逻辑
        try:
            status, analyze_result = analyze_tt_video(profile, plan_list, tt_list, to_do_task_source)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'tt video Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'tt video Analysis Failed. {analyze_result}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = analyze_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)