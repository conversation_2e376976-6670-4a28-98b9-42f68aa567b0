# -*- coding: utf-8 -*-
"""
    Gateway of Crawling
"""
from flask import Blueprint
analyze_blueprint = Blueprint("analyze", __name__)

# Get Hot Spot Rank
from .tweet_analysis import TweetAnalysisPost, TweetAnalysisPostV1
analyze_blueprint.add_url_rule('/tweetAnalysis', view_func=TweetAnalysisPost.as_view('_TweetAnalysisPost'))
analyze_blueprint.add_url_rule('/tweetAnalysisV1', view_func=TweetAnalysisPostV1.as_view('_TweetAnalysisPostV1'))

from .video_analysis import VideoAnalysisPost
analyze_blueprint.add_url_rule('/videoAnalysis', view_func=VideoAnalysisPost.as_view('_VideoAnalysisPost'))

from .schedule_suggestion import NewsAndEventAnalysis, AtAndReplyAnalysis, ttVideoAnalysis
analyze_blueprint.add_url_rule('/newsAndEvent', view_func=NewsAndEventAnalysis.as_view('_NewsAndEventAnalysis'))
analyze_blueprint.add_url_rule('/atAndReply', view_func=AtAndReplyAnalysis.as_view('_AtAndReplyAnalysis'))
analyze_blueprint.add_url_rule('/ttVideo', view_func=ttVideoAnalysis.as_view('_ttVideoAnalysis'))
