# -*- coding: utf-8 -*-
"""
    Rewrite API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis.get_video_analysis import analyze
import json
import traceback


class VideoAnalysisPost(PublicMethodView):
    """ Video Analysis Post
    """

    decorators = [requires('frames', 'audio')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('frames', None) is None:
            error_msg = f'frames is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('audio', None) is None:
            error_msg = f'audio is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        frames = self.arg.get('frames', [])
        audio = self.arg.get('audio', {})
        description = self.arg.get('description', "")
        videoInfo = self.arg.get('videoInfo', {})
        comment = self.arg.get('comment', [])
        authorInfo = self.arg.get('authorInfo', {})
        bgmName = self.arg.get('bgmName', '')

        # 业务逻辑
        try:
            status, video_result = analyze(frames, audio, description, videoInfo, comment, authorInfo, bgmName)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Video Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Video Analysis Failed. {video_result}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = video_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)