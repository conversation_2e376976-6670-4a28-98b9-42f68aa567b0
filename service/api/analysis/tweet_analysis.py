# -*- coding: utf-8 -*-
"""
    Rewrite API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis import tweet_analysis
import json
import traceback


class TweetAnalysisPost(PublicMethodView):
    """ Tweet Content and Hotspot Analysis Post
    """
    gpt_model = "gpt-4o-2024-11-20"
    tweet_analysis = tweet_analysis.TweetAnalysis(gpt_model=gpt_model)
    decorators = [requires('tweetContent', 'tweetStats')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('tweetContent', None) is None:
            error_msg = f'Input twitter content is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        content = self.arg.get('tweetContent')
        tweet_stats = self.arg.get('tweetStats')
        if isinstance(tweet_stats, str):
            try:
                tweet_stats = json.loads(tweet_stats)
            except Exception:
                error_msg = f'tweetStats must be json format.'
                res = {'error': error_msg}
                self.result['data'] = res
                return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        reply_list = self.arg.get('replyList', [])
        author_info = self.arg.get('authorInfo', {})
        media_list = self.arg.get('mediaList', [])
        event_summary = self.arg.get('event_summary', "")
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, regen_content = self.tweet_analysis.pipeline_v2(content, tweet_stats, reply_list, author_info,
                                                                 media_list, event_summary, watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Tweet Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Tweet Analysis Failed. {regen_content}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = regen_content

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class TweetAnalysisPostV1(PublicMethodView):
    """ Tweet Content and Hotspot Analysis Post
    """
    gpt_model = "gpt-4o-2024-11-20"
    tweet_analysis = tweet_analysis.TweetAnalysis(gpt_model=gpt_model)
    decorators = [requires('tweetContent', 'tweetStats')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('tweetContent', None) is None:
            error_msg = f'Input twitter content is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        content = self.arg.get('tweetContent')
        tweet_stats = self.arg.get('tweetStats')
        if isinstance(tweet_stats, str):
            try:
                tweet_stats = json.loads(tweet_stats)
            except Exception:
                error_msg = f'tweetStats must be json format.'
                res = {'error': error_msg}
                self.result['data'] = res
                return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        reply_list = self.arg.get('replyList', [])
        author_info = self.arg.get('authorInfo', {})
        media_list = self.arg.get('mediaList', [])
        event_summary = self.arg.get('event_summary', "")
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, regen_content = self.tweet_analysis.pipeline(content, tweet_stats, reply_list, author_info,
                                                                 media_list, event_summary, watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Tweet Analysis Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Tweet Analysis Failed. {regen_content}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = regen_content

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
