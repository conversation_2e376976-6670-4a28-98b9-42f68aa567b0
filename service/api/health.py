# -*- coding: utf-8 -*-
"""
    health
"""
from flask import Blueprint, jsonify, current_app
import json

from .base import PublicMethodView

health_blueprint = Blueprint("health", __name__)


# health
class _Health(PublicMethodView):
    """Health

    """

    def get(self):
        self.result['data'] = "ok!"
        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        # 写flask日志
        # current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        # 写收集日志
        # current_app.CollectLog.info(f'Output: {json.dumps(output)}')

        return jsonify(output)


health_blueprint.add_url_rule('/health', view_func=_Health.as_view('_Health'))
