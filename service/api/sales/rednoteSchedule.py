# -*- coding: utf-8 -*-
"""
    RednotePlan API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.sales.schedule import schedule
import json
import traceback


class RednoteSchedulePost(PublicMethodView):
    """ Rednote Schedule Post
    """

    decorators = [requires('auditResult', 'rednoteNum', 'planTopic')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('auditResult', None) is None:
            error_msg = f'auditResult is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('rednoteNum', None) is None:
            error_msg = f'rednoteNum is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        
        if self.arg.get('planTopic', None) is None:
            error_msg = f'planTopic is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        audit_result = self.arg.get('auditResult')
        rednote_num = self.arg.get('rednoteNum')
        plan_topic = self.arg.get('planTopic')
        plan_description = self.arg.get('planDescription', None)
        plan_cycle = self.arg.get('planCycle', 30)
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, schedule_result = schedule(audit_result, rednote_num, plan_topic, plan_description, plan_cycle, watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Rednote Schedule Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20220, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Rednote Schedule Failed. {schedule_result}'}
            output = current_app.responsesData(20220, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = schedule_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)