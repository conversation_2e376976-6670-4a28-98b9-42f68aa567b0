# -*- coding: utf-8 -*-
"""
    Cover Styles API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.recreation.style_list import get_style_list, generate_cover_html
import traceback

class CoverStylesPost(PublicMethodView):
    """
    Cover Styles POST for generation
    """

    # Http method
    def post(self):
        style_list = get_style_list()

        # 正常返回
        self.result['data'] = {"styleList": style_list}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class CoverStylesView(PublicMethodView):
    """
    Cover Styles GET for list and POST for generation
    """

    # Http method
    @requires('style_id', 'content')
    def post(self):
        """
        Generate a cover page based on style and content.
        """
        try:
            # 1. Get params
            style_id = int(self.arg.get('style_id'))
            content = self.arg.get('content')
            media_list = self.arg.get('media_list', []) # optional
            watttraceid = self.request_id

            # 2. Call server logic
            status, result_data, token_usage = generate_cover_html(style_id, content, media_list, watttraceid)

            # 3. Handle response
            if not status:
                # Generation failed
                self.result['data'] = {'error': f'Cover Generation Failed. {result_data}'}
                output = current_app.responsesData(20220, self.result, self.arg, self.request_id)
            else:
                # Generation succeeded
                self.result['data'] = {
                    'html': result_data,
                    'usage': token_usage
                }
                output = current_app.responsesData(0, self.result, self.arg, self.request_id)

        except Exception as e:
            # Handle unexpected errors
            error_msg = f'Cover Generation Error: {str(e)} - {traceback.format_exc()}'
            self.result['data'] = error_msg
            output = current_app.responsesData(50000, self.result, self.arg, self.request_id)
            current_app.FlaskLog.error(current_app.fmtSaveOutput(self.request_id, {"error": error_msg}))

        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)