# -*- coding: utf-8 -*-
"""
    Gateway of Sales
"""
from flask import Blueprint
sales_blueprint = Blueprint("sales", __name__)

# Get sales report
from .rednoteSchedule import RednoteSchedulePost
sales_blueprint.add_url_rule('/createRednotePlan', view_func=RednoteSchedulePost.as_view('CreateRednotePlanPost'))
from .cover_styles import CoverStylesPost
sales_blueprint.add_url_rule('/styleList', view_func=CoverStylesPost.as_view('GetCoverStylesPost'))
