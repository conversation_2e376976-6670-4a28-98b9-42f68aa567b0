# -*- coding: utf-8 -*-
"""
    Create task name API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.recreation.generate_task_name import get_task_name
import traceback


class TaskNameGet(PublicMethodView):
    """ Create task name Post
    """
    decorators = [requires('xInfo')]

    def post(self):
        # 获取入参
        if self.arg.get('xInfo', None) is None:
            error_msg = f'xInfo is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        product = self.arg.get('product', {})
        taskDescription = self.arg.get('taskDescription', '')
        xInfo = self.arg.get('xInfo')
        task_type = self.arg.get('taskType', 1)
        # 业务逻辑
        try:
            status, task_result = get_task_name(product, taskDescription, xInfo, task_type)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"code": False, 'script': f"Get task name except Error: {str(e)} - {traceback.format_exc()}" }
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'code': status, 'script': f'Get task name Failed. {task_result}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'taskName': task_result}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)