# -*- coding: utf-8 -*-
"""
    Extract Fields of Interest API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.reply.extract_fields import ExtractInterestedFields
import traceback


class NewProfilePost(PublicMethodView):
    """ Reply Comment Post
    """
    # 必要入参
    decorators = [requires('xId', 'query')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('xId', None) is None:
            error_msg = f'Input twitter user xID is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
        elif self.arg.get('query', None) is None:
            error_msg = f'Input query is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        x_id = self.arg.get('xId')
        if isinstance(x_id, str):
            x_id = int(x_id)
        query = self.arg.get('query')
        existing_fields = self.arg.get('existingFields', None)
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, code, field_object = ExtractInterestedFields(x_id=x_id, query=query, existing_fields=existing_fields,
                                                                watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Fields of Interest Error: {str(e)} - {traceback.format_exc()}',
                                   'resCode': -100}
            output = current_app.responsesData(20210, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'New Profile Failed. {field_object}', 'resCode': code}
            output = current_app.responsesData(20210, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = field_object

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
