# -*- coding: utf-8 -*-
"""
    Character Profile API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.profile.new_profile import NewProfile
import traceback


class NewProfilePost(PublicMethodView):
    """ Reply Comment Post
    """
    # Http method
    def post(self):
        # 获取入参
        if not self.arg.get('tweetList', []):
            error_msg = f'Input twitter list is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        tweet_list = self.arg.get('tweetList')
        for tweet in tweet_list:
            if not tweet.get('tweetId', ""):
                error_msg = f'Input tweet ID is empty.'
                res = {'error': error_msg}
                self.result['data'] = res
                return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
            elif not tweet.get('tweetText', ""):
                error_msg = f'Input tweet content is empty.'
                res = {'error': error_msg}
                self.result['data'] = res
                return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
            elif tweet.get('tweetType') not in [1, 2, 3, 4]:
                error_msg = f'Input tweet type is invalid.'
                res = {'error': error_msg}
                self.result['data'] = res
                return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, code, profile_msg = NewProfile(tweet_list=tweet_list, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'New Profile Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20209, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'New Profile Failed. {profile_msg}'}
            output = current_app.responsesData(20209, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'profile_summary': profile_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
