# -*- coding: utf-8 -*-
"""
    Rewrite API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.rewrite_tweet import *
import traceback


class RewritePost(PublicMethodView):
    """ Rewrite Content Post
    """
    # Http method
    def post(self):
        # 获取入参
        profile_id = self.arg.get('profileId', None)
        x_id = self.arg.get('xId', None)
        user_id = self.arg.get('userId', None)
        content = self.arg.get('content', None)
        require_media = self.arg.get('requireMedia', False)
        content_purpose = self.arg.get('contentPurpose', None)
        reason = self.arg.get('reason', '')
        # write_style = self.arg.get('writeStyle', [])
        language = self.arg.get('language', 3)
        platform = self.arg.get('platform', 'x')
        word_limit = self.arg.get('wordLimit', 15)
        prompt_content = self.arg.get('promptContent', None)
        event = self.arg.get('eventSummary', None)
        action = self.arg.get('action', 'post')
        product = self.arg.get('product', {})
        url = self.arg.get('url', None)
        strategies = self.arg.get('strategies', [])
        model = self.arg.get('model', 'gpt-4o')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, regen_content = rewrite_tweet(company_id=profile_id, x_id=x_id, user_id=user_id,
                                                  content_purpose=content_purpose,
                                                  original_content=content, word_limit=word_limit,
                                                  with_media=require_media, reason=reason, prompt_content=prompt_content,
                                                  language=language, event=event, action=action, platform=platform,
                                                  product=product, url=url, strategies=strategies, model=model,
                                                  watttraceid=watttraceid, request_id=self.request_id)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Rewrite content Error: {str(e)} - {regen_content}'
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Rewrite content Failed. {regen_content}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = regen_content

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class SmartGenPost(PublicMethodView):
    """ Smart Generation Post
    """
    decorators = [requires('content')]
    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('content', None) is None:
            error_msg = f'Input content empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        content = self.arg.get('content', None)
        promote_target = self.arg.get('promoteTarget', None)
        url = self.arg.get('url', None)
        tags = self.arg.get('tags', [])
        event = self.arg.get('eventSummary', None)
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, output = smart_gen(content=content, promote_target=promote_target, tags=tags, url=url,
                                       event=event, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'SmartGen content Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20212, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'SmartGen content Failed. {output}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = output

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class SimpleGenPost(PublicMethodView):
    """ Simple Generation Post
    """
    decorators = [requires('content')]
    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('content', None) is None:
            error_msg = f'Input content empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        content = self.arg.get('content', None)
        promote_target = self.arg.get('promoteTarget', None)
        url = self.arg.get('url', None)
        event = self.arg.get('eventSummary', None)
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, output = smart_gen(content=content, promote_target=promote_target, url=url, event=event,
                                       watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'SimpleGen content Error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20212, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'SimpleGen content Failed. {output}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = output

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class GetRewriteListPost(PublicMethodView):
    """ Retrieve Rewrite List Post"""
    # 必要入参
    decorators = [requires('xId', 'tweetId')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('xId', None) is None:
            error_msg = f'input x Id is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        elif self.arg.get('tweetId', None) is None:
            error_msg = f'input tweet ID is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        x_id = self.arg.get('xId')
        if isinstance(x_id, str):
            x_id = int(x_id)
        tweet_id = self.arg.get('tweetId')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, search_result = retrieve_rewritten_tweet(x_id, tweet_id, watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = f'Get rewritten results error: {str(e)} - {traceback.format_exc()}'
            output = current_app.responsesData(20207, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'error': f'Get rewritten results Failed - {search_result}'}
            output = current_app.responsesData(20207, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not search_result:
            self.result['data'] = {'error': f'Get rewritten results 0 - {search_result}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {
            "hotspotContent": search_result.get("hotspotContent", ""),
            "textList": search_result.get("textList", []),
            "imgKeyList": search_result.get("imgKeyList", []),
            "videoKeyList": search_result.get("videoKeyList", [])
        }

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
