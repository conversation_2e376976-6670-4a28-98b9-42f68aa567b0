# -*- coding: utf-8 -*-
"""
    Reply API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.reply.intent_classification import classify_intent
import traceback


class IntentClassificationPost(PublicMethodView):
    """ Intent Classification Post
    """
    # 必要入参
    decorators = [requires('input')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('input', None) is None:
            error_msg = f'Input words are empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        input_words = self.arg.get('input', '')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, reply_msg = classify_intent(user_input=input_words, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {f'Intent Classification Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20208, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {f'Intent Classification Failed. {reply_msg}'}
            output = current_app.responsesData(20208, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = reply_msg

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
