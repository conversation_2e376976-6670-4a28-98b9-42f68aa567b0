# -*- coding: utf-8 -*-
"""
    Gateway of Crawling
"""
from flask import Blueprint
rewrite_blueprint = Blueprint("create", __name__)

# Get Hot Spot Rank
from .rewrite import RewritePost, GetRewriteListPost, SmartGenPost, SimpleGenPost
rewrite_blueprint.add_url_rule('/rewriteContent', view_func=RewritePost.as_view('_RewriteContentPost'))
rewrite_blueprint.add_url_rule('/getRewriteList', view_func=GetRewriteListPost.as_view('_GetRewriteListPost'))
rewrite_blueprint.add_url_rule('/smartGen', view_func=SmartGenPost.as_view('_SmartGenPost'))

from .profile import NewProfilePost
rewrite_blueprint.add_url_rule('/newProfile', view_func=NewProfilePost.as_view('_NewProfilePost'))

from .reply import IntentClassificationPost
rewrite_blueprint.add_url_rule('/intentClassification', view_func=IntentClassificationPost.as_view('_IntentClassificationPost'))

from .fields_of_interest import NewProfilePost
rewrite_blueprint.add_url_rule('/generateFields', view_func=NewProfilePost.as_view('_GenerateFieldsPost'))

from .recreation_for_video import ScriptGet, MediaUnderstand, MediaMatch, MediaToScripts, GetVideoCaption, mimicVideoScript
rewrite_blueprint.add_url_rule('/getVideoScript', view_func=ScriptGet.as_view('_GenerateScriptPost'))
rewrite_blueprint.add_url_rule('/understandMedia', view_func=MediaUnderstand.as_view('_MediaUnderstandPost'))
rewrite_blueprint.add_url_rule('/matchMedia', view_func=MediaMatch.as_view('_MediaMatchPost'))
rewrite_blueprint.add_url_rule('/mediaToScript', view_func=MediaToScripts.as_view('_MediaToScriptPost'))
rewrite_blueprint.add_url_rule('/getVideoCaption', view_func=GetVideoCaption.as_view('_GetVideoCaption'))
rewrite_blueprint.add_url_rule('/genVideoScript', view_func=mimicVideoScript.as_view('_GenVideoScriptPost'))
from .task_name_generate import TaskNameGet
rewrite_blueprint.add_url_rule('/createTaskName', view_func=TaskNameGet.as_view('_GetTaskNamePost'))

from .xhs_write import NoteGenPost
rewrite_blueprint.add_url_rule('/rednoteGen', view_func=NoteGenPost.as_view('_RednoteGenPost'))