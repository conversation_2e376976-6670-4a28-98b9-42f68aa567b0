# -*- coding: utf-8 -*-
"""
    Rewrite API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.recreation.generate_video_script import generate_script
from service.server.recreation.mimic_video_script import generate_video_script_mimic_tiktok
from service.server.recreation.media_understand import media_content
from service.server.recreation.media_match import get_match
from service.server.recreation.media_to_scripts import get_script_list_by_media
from service.server.recreation.generate_caption_for_video import get_video_caption
import traceback


class mimicVideoScript(PublicMethodView):
    """ Get Video Script Post
    """
    decorators = [requires('userId', 'profileId', 'requirement', 'videoDescription')]

    def post(self):
        if self.arg.get('userId', None) is None:
            error_msg = f'userId cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        elif self.arg.get('profileId', None) is None:
            error_msg = f'profileId cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        elif self.arg.get('requirement', None) is None:
            error_msg = f'requirement cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        elif self.arg.get('videoDescription', None) is None:
            error_msg = f'videoDescription cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))
        
        user_id = self.arg.get('userId')
        profile_id = self.arg.get('profileId')
        video_description = self.arg.get('videoDescription')
        reason = self.arg.get('reason')
        requirement = self.arg.get('requirement')
        platform = self.arg.get('platform', '')
        platform_id = self.arg.get('platformId', '')
        language = self.arg.get('language', 'English')
        watttraceid = self.request_header_watttraceid
        
        try:
            status, result_ = generate_video_script_mimic_tiktok(user_id, profile_id, video_description, reason, 
                                                                 requirement, platform, platform_id, language, watttraceid)
            if not status:
                self.result['data'] = f"Generate video script Error: {result_}"
                output = current_app.responsesData(0, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"error": f'Generate video script Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)
        
        self.result['data'] = result_
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class ScriptGet(PublicMethodView):
    """ Get Video Script Post
    """
    decorators = [requires('productInfo', 'source')]

    def post(self):
        # 获取入参
        if self.arg.get('productInfo', None) is None:
            error_msg = f'Product Info is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('source', None) is None:
            error_msg = f'Source is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        productInfo = self.arg.get('productInfo')
        source = self.arg.get('source')
        strategy = self.arg.get('strategy', [])

        # 业务逻辑
        try:
            status, script_result = generate_script(productInfo, source, strategy)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"code": False, 'script': f"Get video script Error: {str(e)} - {traceback.format_exc()}" }
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'code': status, 'script': f'Get video script Failed. {script_result}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'code': status, 'script': script_result}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class MediaUnderstand(PublicMethodView):
    """ Understand media for recreation

    """
    # 必要入参
    decorators = [requires('media')]

    def post(self):
        # 获取入参
        if self.arg.get('media', None) is None:
            error_msg = f'Media cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        media = self.arg.get('media')

        # 业务逻辑
        try:
            status, media_result = media_content(media)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'code': False, "content": f'Media understand Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'code': status, "content": f'Media understand Failed. {media_result}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'code': status, "content": media_result}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class MediaMatch(PublicMethodView):
    """ Match media with script
    """
    # 必要入参
    decorators = [requires('userSelectList', 'videoScript')]

    def post(self):
        # 获取入参
        if self.arg.get('userSelectList', None) is None:
            error_msg = f'userSelectList cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('videoScript', None) is None:
            error_msg = f'VideoScript cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        media_list = self.arg.get('userSelectList')
        script = self.arg.get('videoScript')

        # 业务逻辑
        try:
            status, match_result = get_match(media_list, script)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"code": False, "script_list": f'Media match Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {"code": status, 'script_list': f'Match media Failed. {match_result}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"code": status, "script_list": match_result}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class MediaToScripts(PublicMethodView):
    """ Match media with script
    """
    # 必要入参
    decorators = [requires( 'productInfo', 'source', 'userSelectList')]

    def post(self):
        # 获取入参
        if self.arg.get('productInfo', None) is None:
            error_msg = f'productInfo cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('source', None) is None:
            error_msg = f'source cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        if self.arg.get('userSelectList', None) is None:
            error_msg = f'userSelectList cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        productInfo = self.arg.get('productInfo')
        source = self.arg.get('source')
        userSelectList = self.arg.get('userSelectList')
        strategy = self.arg.get('strategy', [])
        language = self.arg.get('language', 'English')

        # 业务逻辑
        try:
            status, result_ = get_script_list_by_media(productInfo, source, userSelectList, strategy, language)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"code": False, "script_list": f'Media to scripts Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {"code": status, 'script_list': f'Media to scripts Failed. {result_}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"code": status, "script_list": result_}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class GetVideoCaption(PublicMethodView):
    """ Get video caption
    """
    # 必要入参
    decorators = [requires("script_list")]

    def post(self):
        # 获取入参
        if self.arg.get("script_list", None) is None:
            error_msg = f'script_list cannot be empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(20003, self.result, self.arg, self.request_id))

        script_list = self.arg.get('script_list', [])

        # 业务逻辑
        try:
            status, result_ = get_video_caption(script_list)
        except Exception as e:
            # 异常返回
            self.result['data'] = {"code": False, "caption": f'get video caption Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {"code": status, 'caption': f'get video caption Failed. {result_}'}
            output = current_app.responsesData(0, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"code": status, "caption": result_}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)