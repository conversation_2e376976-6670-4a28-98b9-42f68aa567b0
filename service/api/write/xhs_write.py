"""
    Xiaohongshu Write API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.recreation.gen_note import generate_note
import traceback


class NoteGenPost(PublicMethodView):
    """ Note Generation Post
    """ 
    # Http method
    def post(self):
        # 获取入参
        auditResult = self.arg.get('auditResult', None)
        plan_topic = self.arg.get('planTopic', None)
        plan_description = self.arg.get('planDescription', None)
        todo_task_title = self.arg.get('todoTaskTitle', None)
        prompt_content = self.arg.get('promptContent', None)
        red_note_content = self.arg.get('rednoteContent', None)
        source_content = self.arg.get('sourceContent', None)
        rewrite = self.arg.get('rewrite', False)
        write_style = self.arg.get('writeStyle', [])
        language = self.arg.get('language', 3)
        watttraceid = self.request_header_watttraceid
 
        # 业务逻辑
        try:
            status, note_result = generate_note(
                auditResult=auditResult, 
                plan_topic=plan_topic, 
                plan_description=plan_description, 
                todo_task_title=todo_task_title, 
                prompt_content=prompt_content, 
                red_note_content=red_note_content, 
                source_content=source_content, 
                rewrite=rewrite, 
                write_style=write_style, 
                language=language, 
                watttraceid=watttraceid
            )
        except Exception as e:
            # 异常返回
            error_msg = f'Note Generation Error: {str(e)} - {traceback.format_exc()}'
            current_app.FlaskLog.error(f"[{self.request_id}] {error_msg}")
            self.result['data'] = {'error': error_msg}
            output = current_app.responsesData(20206, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 业务逻辑失败返回
            error_msg = f'Note Generation Failed: {note_result}'
            current_app.FlaskLog.error(f"[{self.request_id}] {error_msg}")
            self.result['data'] = {'error': error_msg}
            output = current_app.responsesData(20207, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = note_result

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
