# -*- coding: utf-8 -*-
"""
   related search
"""
from flask import Blueprint
search_blueprint = Blueprint("search", __name__)

# related_search
from .search import RelatedSearch, QueryExpand, QueryDecompose
search_blueprint.add_url_rule('/related_search', view_func=RelatedSearch.as_view('RelatedSearch'))
search_blueprint.add_url_rule('/expand_query', view_func=QueryExpand.as_view('ExpandQuery'))
search_blueprint.add_url_rule('/decompose_query', view_func=QueryDecompose.as_view('DecomposeQuery'))

# event_find
from .get_event import GetEvent
search_blueprint.add_url_rule('/get_event', view_func=GetEvent.as_view('GetEvent'))

# image understand
from .understand import ImageUnderstand
search_blueprint.add_url_rule('/image_understand', view_func=ImageUnderstand.as_view('ImageUnderstand'))

from .extract_keywords import ExtractPersonPost, GetKeywordsPost
search_blueprint.add_url_rule('/extract_person', view_func=ExtractPersonPost.as_view('ExtractPersonPost'))
search_blueprint.add_url_rule('/getKeywordByTweetInfo', view_func=GetKeywordsPost.as_view('GetKeywordsPost'))

# extract voices
from .extract_voices import ExtractVoicesPost
search_blueprint.add_url_rule('/get_event_voices', view_func=ExtractVoicesPost.as_view('ExtractVoicesPost'))

# extract website details
from .extract_product import ExtractProductPost, ExtractSellPointPost, ExtractCompanyInfoPost
search_blueprint.add_url_rule('/get_product', view_func=ExtractProductPost.as_view('ExtractProductPost'))
search_blueprint.add_url_rule('/get_sell_points', view_func=ExtractSellPointPost.as_view('ExtractSellPointPost'))
search_blueprint.add_url_rule('/get_company_info', view_func=ExtractCompanyInfoPost.as_view('ExtractCompanyInfoPost'))

from .extract_tags import ExtractTagsPost, UnderstandUrlPost
search_blueprint.add_url_rule('/url_understand', view_func=UnderstandUrlPost.as_view('UnderstandUrlPost'))
search_blueprint.add_url_rule('/getKeywordByURL', view_func=ExtractTagsPost.as_view('ExtractTagsPost'))

from .video_summary import VideoSummaryPost
search_blueprint.add_url_rule('/video_summary', view_func=VideoSummaryPost.as_view('VideoSummaryPost'))

from .video_generate import VideoGenPost, VideoCaptionPost
search_blueprint.add_url_rule('/video_generate', view_func=VideoGenPost.as_view('VideoGenPost'))
search_blueprint.add_url_rule('/video_caption', view_func=VideoCaptionPost.as_view('VideoCaptionPost'))

from .image_generate import ProfilePhotoGenPost
search_blueprint.add_url_rule('/gen_profile_photo', view_func=ProfilePhotoGenPost.as_view('ProfilePhotoGenPost'))

from .video_understand_new import VideoUnderstandPost
search_blueprint.add_url_rule('/videoUnderstand', view_func=VideoUnderstandPost.as_view('VideoUnderstandPost'))

from .company_update import CompanyUpdate
search_blueprint.add_url_rule('/update_company_info', view_func=CompanyUpdate.as_view('CompanyUpdatePost'))
