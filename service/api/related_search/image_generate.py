# -*- coding: utf-8 -*-
"""
    Generate images
"""
import json
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.profile.new_avatar import *
from service.lib.kling_api import *

config_ = KlingConfig(
    access_key=config.KLING_ACCESS_KEY,
    secret_key=config.KLING_SECRET_KEY
)

class ProfilePhotoGenPost(PublicMethodView):
    """ Generate Profile Photo Post
    """
    # Http method
    def post(self):
        x_id = self.arg.get('XId', -1)
        num = self.arg.get('num', 1)
        gender = self.arg.get('gender', None)

        watttraceid = self.request_header_watttraceid

        # 业务逻辑1 - 生成prompt
        status, generated_prompt = GenAvatarPrompt(x_id=x_id, gender=gender, watttraceid=watttraceid)

        if not status:
            # 异常返回
            self.result['data'] = {'error': generated_prompt}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 业务逻辑2 - 生成图片
        kling = KlingRequest(config_)
        success, image_results = kling.generate_portraits(generated_prompt, num=num)
        if success:
            print(f"Generated images: {image_results}")
            # print(f"Time taken: {elapsed_time:.2f} seconds")
        else:
            error_msg = f"Failed to generate images: {str(image_results)}"
            res = {'error': error_msg}
            self.result['data'] = res
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = image_results

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class ProfilePhotoBulkGenPost(PublicMethodView):
    """ Generate Profile Photo Post with Bulk
    """
    # Http method
    def post(self):
        num = self.arg.get('num', 1)
        watttraceid = self.request_header_watttraceid

        # 业务逻辑1 - 生成prompt
        status, generated_prompt = GenAvatarPrompt(x_id=-1, watttraceid=watttraceid)

        if not status:
            # 异常返回
            self.result['data'] = {'error': generated_prompt}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 业务逻辑2 - 生成图片
        kling = KlingRequest(config_)
        success, image_results = kling.generate_portraits(generated_prompt, num=num)
        if success:
            print(f"Generated images: {image_results}")
            # print(f"Time taken: {elapsed_time:.2f} seconds")
        else:
            error_msg = f"Failed to generate images: {str(image_results)}"
            res = {'error': error_msg}
            self.result['data'] = res
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = image_results

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)