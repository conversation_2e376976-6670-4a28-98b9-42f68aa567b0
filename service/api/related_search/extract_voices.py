# -*- coding: utf-8 -*-
"""
    MOCK: Extract media article views/voices API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.topic.article_views import extract_views
import traceback


class ExtractVoicesPost(PublicMethodView):
    """ Extract article voices Post
    """
    # 必要入参
    decorators = [requires('hotspotId')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('hotspotId', None) is None:
            error_msg = f'Hotspot ID is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        hotspot_id = self.arg.get('hotspotId', [])
        max_limit = self.arg.get('max_limit', 10)

        # 业务逻辑
        try:
            status, reply_list = extract_views(hotspot_id=hotspot_id, max_limit=max_limit)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Voices Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20203, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Voices Failed. {reply_list}'}
            output = current_app.responsesData(20203, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'voices': reply_list}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
