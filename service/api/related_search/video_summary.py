# -*- coding: utf-8 -*-
"""
   video understanding API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.mm_understand.process_video import get_video_summary
import traceback


class VideoSummaryPost(PublicMethodView):
    """ Extract tags Post
    """
    # 必要入参
    decorators = [requires('video', 'tweet')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('video', None) is None:
            error_msg = f'Input video information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
        video = self.arg.get('video', [])

        if self.arg.get('tweet', None) is None:
            error_msg = f'Input tweet information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
        tweet = self.arg.get('tweet', [])

        summary = self.arg.get('summary', '')
        model = self.arg.get('model', 'gpt-4o')

        # 业务逻辑
        try:
            status, reply_msg = get_video_summary(video, tweet, summary, model=model)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Video Summary Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Video Summary Failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = reply_msg  # 返回一个字典

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)