# -*- coding: utf-8 -*-
"""
    media understand
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.image_understand_openai import *


class ImageUnderstand(PublicMethodView):
    """ image understand"""
    # 必要入参
    decorators = [requires('img_url')]

    def checkParams(self):
        if len(self.arg.get('img_url')) == 0:
            error_msg = f"img_url must not be empty!"
            res = [{'img_url': self.arg.get('img_url'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90003, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)
        return True, ""

    def post(self):
        status, watt_gpt_key = self.checkAuth()
        if not status:
            return watt_gpt_key
        status, check_result = self.checkParams()
        if not status:
            return check_result

        watttraceid = self.request_header_watttraceid

        img_url = self.arg.get('img_url')
        if isinstance(img_url, str):
            stat, answer = image_extract_keywords(self.arg.get('img_url'), watttraceid)
            self.result['data'] = {'output': [answer]}
        elif isinstance(img_url, list):
            stat, answer = image_list_extract_keywords(self.arg.get('img_url'), watttraceid)
            self.result['data'] = {'output': answer}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
