#-*- coding: utf-8 -*-
"""
    find event by keyword
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.find_event import event_pipeline, send_data_to_api


class GetEvent(PublicMethodView):
    """ Find event by keyword

    """
    # 必要入参
    decorators = [requires('requestId', 'keyword', 'tweetIdList')]

    def checkParams(self):
        if type(self.arg.get('requestId')) != str:
            error_msg = f"requestId must be str!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if len(self.arg.get('requestId')) == 0:
            error_msg = f"requestId must not be empty!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90003, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if type(self.arg.get('keyword')) != str:
            error_msg = f"keyword must be str!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if len(self.arg.get('keyword')) == 0:
            error_msg = f"keyword must not be empty!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90003, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if type(self.arg.get('tweetIdList')) != list:
            error_msg = f"tweetIdList must be list!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if type(self.arg.get('category', '')) != str:
            error_msg = f"category must be str!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        return True, ""

    def post(self):
        status, check_result = self.checkParams()
        if not status:
            return check_result
        code, event, confidence, decision, detail = event_pipeline(self.arg['keyword'], self.arg['tweetIdList'],
                                                                   self.arg.get('requestId'), self.arg['category'])
        report_status, _ = send_data_to_api(self.arg.get('requestId'), code,
                                            {"input": {"keyword": self.arg['keyword'],
                                                       "category": self.arg['category']},
                                             "output": {"event": event, "confidence": confidence, "decision": decision,
                                                        "detail": detail}})

        self.result['data'] = {'requestId': self.arg.get('requestId'), 'resCode': code, 'event': event,
                               'confidence': confidence, "decision": decision, "detail": detail,
                               "report_status": report_status}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
