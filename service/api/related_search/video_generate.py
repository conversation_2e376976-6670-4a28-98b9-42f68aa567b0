# -*- coding: utf-8 -*-
"""
    Generate product video from product URLs
"""
import json
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis.extract_product import GenerateDetails
from service.server.profile.story_generation import *
from service.lib.kling_api import *

config_ = KlingConfig(
    access_key=config.KLING_ACCESS_KEY,
    secret_key=config.KLING_SECRET_KEY
)

class VideoGenPost(PublicMethodView):
    """ Product Advertisement Video Generate Post
    """
    # 必要入参
    decorators = [requires('context')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('context', None) is None:
            error_msg = f'context is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        context = self.arg.get('context')
        num = self.arg.get('num', 5)
        language = self.arg.get('language', 3)
        if isinstance(context, str):
            try:
                context = json.loads(context)
            except Exception as e:
                error_msg = f"context must be json format - {str(e)}"
                res = {'error': error_msg}
                self.result['data'] = res
                output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return False, jsonify(output)

        gpt_model = "gpt-4o-mini"
        watttraceid = self.request_header_watttraceid
        generate_details = GenerateDetails(gpt_model=gpt_model, watttraceid=watttraceid)

        # 业务逻辑1 - 提取卖点
        status, product_details = generate_details.pipeline(context=context, scenario=1, language=language)

        if not status:
            # 异常返回
            self.result['data'] = {'error': product_details}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 业务逻辑2 - 生成视频
        status, story_details = storyTellwithProduct(sellpoints=product_details, num=num, watttraceid=watttraceid,
                                                     langugae=language)

        if not status:
            # 异常返回
            self.result['data'] = {'error': story_details}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        story_title = story_details.get('storyTitle', '')
        video_list = story_details.get('videoList', [])
        caption_list = [video.get("caption") for video in video_list]
        prompt_list = [video.get("prompt") for video in video_list]
        index_list = [video.get("index") for video in video_list]
        if all(item is None for item in caption_list):
            caption_list = [prompt_.get("caption", "") for prompt_ in prompt_list]
            for prompt in prompt_list:
                prompt.pop("caption", None)

        kling = KlingRequest(config_)
        success, image_results = kling.generate_images_concurrently(prompt_list, index_list, caption_list)
        if success:
            print(f"Generated images: {image_results}")
            # print(f"Time taken: {elapsed_time:.2f} seconds")
        else:
            error_msg = f"Failed to generate images: {str(image_results)}"
            res = {'error': error_msg}
            self.result['data'] = res
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"storyTitle": story_title, "videoList": image_results}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class VideoCaptionPost(PublicMethodView):
    """ Product Caption Generate Post
    """

    # Http method
    def post(self):
        context = self.arg.get('context', None)
        idea = self.arg.get('idea', None)
        num = self.arg.get('num', 8)
        language = self.arg.get('language', 'english')
        watttraceid = self.request_header_watttraceid

        if context is not None:
            gpt_model = "gpt-4o-mini"
            generate_details = GenerateDetails(gpt_model=gpt_model, watttraceid=watttraceid)
            # 提取卖点
            status, product_details = generate_details.pipeline(context=context, scenario=1, language=language)

            if not status:
                # 异常返回
                self.result['data'] = {'error': product_details}
                output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)

            # 生成caption
            status, captions_ = captionGenwithProduct(sellpoints=product_details, num=num, watttraceid=watttraceid)
        else:
            # 生成 script
            status, story_details = storyScriptGen(basic_idea=idea, num=num, watttraceid=watttraceid)

            if not status:
                # 异常返回
                self.result['data'] = {'error': story_details}
                output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)
            # print(Fore.GREEN + f"Story details: {story_details}"+Fore.RESET)
            story_title = story_details.get('storyTitle', '')
            story_list = story_details.get('storyList', [])
            caption_list = [video.get("caption") for video in story_list]
            prompt_list = [video.get("prompt") for video in story_list]
            appearance_desc = prompt_list[0].get("Main Subject")
            index_list = [video.get("index") for video in story_list]
            for prompt in prompt_list:
                prompt['Main Subject'] = appearance_desc
            if all(item is None for item in caption_list):
                caption_list = [prompt_.get("caption", "") for prompt_ in prompt_list]
                for prompt in prompt_list:
                    prompt.pop("caption", None)

            kling = KlingRequest(config_)
            success, image_list = kling.generate_images_concurrently(prompt_list, index_list, caption_list,
                                                                        output_video=False)
            captions_ = {}
            if success:
                print(f"Generated images: {image_list}")
                for image, prompt in zip(image_list, prompt_list):
                    image['prompt'] = prompt
                captions_ = {"storyTitle": story_title, "output": image_list}
            else:
                error_msg = f"Failed to generate images: {str(captions_)}"
                res = {'error': error_msg}
                self.result['data'] = res
                output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return jsonify(output)

        # 正常返回
        self.result['data'] = captions_

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
