# -*- coding: utf-8 -*-
"""
    Multi-modal media understanding and extracting tags API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.mm_understand.extract_tags import get_tags
from service.server.analysis.url_extract import digest_content
import traceback


class UnderstandUrlPost(PublicMethodView):
    """ Understand Url content with images/videos Post
    """
    # 必要入参
    decorators = [requires('platform')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('platform', None) is None:
            error_msg = f'Input platform information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        platform = self.arg.get('platform', "")
        title = self.arg.get('title', "")
        body = self.arg.get('body', "")
        image_list = self.arg.get('imageList', [])
        video_list = self.arg.get('videoList', [])
        watttraceid = self.request_header_watttraceid
        

        # 业务逻辑
        try:
            status, output_json = digest_content(platform, title, body, image_list, video_list, watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Understand URL Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Understand URL Failed. {output_json}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = output_json

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class ExtractTagsPost(PublicMethodView):
    """ Extract tags Post
    """
    # 必要入参
    decorators = [requires('url_list')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('url_list', None) is None:
            error_msg = f'Input url_list information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        url_list = self.arg.get('url_list', [])

        # 业务逻辑
        try:
            status, reply_msg = get_tags(url_list)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Tags Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Tags Failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'keywords': reply_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
