# -*- coding: utf-8 -*-
"""
    Multi-modal media understanding and extracting person/keywords API
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.mm_understand.extract_person import extract_person, single_tweet
import traceback


class ExtractPersonPost(PublicMethodView):
    """ Extract person keywords Post
    """
    # 必要入参
    decorators = [requires('tweetList')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('tweetList', None) is None:
            error_msg = f'Input tweet information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        tweet_list = self.arg.get('tweetList', [])
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            # status, code, reply_msg = ExtractPerson(tweet_list=tweet_list, watttraceid=watttraceid)
            status, reply_msg = extract_person(tweet_list)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Person Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Person Failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'resMsg': reply_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class GetKeywordsPost(PublicMethodView):
    """ Get keywords by tweet information Post
    """
    # 必要入参
    decorators = [requires('mediaList')]
    # Http method
    def post(self):
        if self.arg.get('mediaList', None) is None:
        # 获取入参
            error_msg = f'Input tweet media information is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        tweet_content = self.arg.get('content', "")
        media_list = self.arg.get('mediaList', [])
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            # status, reply_msg = ExtractKeywords(content=tweet_content, media_list=media_list,
            #                                     watttraceid=watttraceid)
            status, reply_msg = single_tweet(tweet_=tweet_content, media_list=media_list)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Keywords Error: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20204, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Person Failed. {reply_msg}'}
            output = current_app.responsesData(20204, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"keyword": reply_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
