# -*- coding: utf-8 -*-
"""
    Extract product information from url API
"""
import json
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.analysis.extract_product import GenerateDetails


class ExtractCompanyInfoPost(PublicMethodView):
    """ Extract company information Post
    """
    gpt_model = "gpt-4o-mini"
    generate_details = GenerateDetails(gpt_model=gpt_model)
    # 必要入参
    decorators = [requires('context')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('context', None) is None:
            error_msg = f'context is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        context = self.arg.get('context')
        language = self.arg.get('language', 1)
        if isinstance(context, str):
            try:
                context = json.loads(context)
            except Exception as e:
                error_msg = f"context must be json format - {str(e)}"
                res = {'error': error_msg}
                self.result['data'] = res
                output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return False, jsonify(output)

        # 业务逻辑
        status, company_info = self.generate_details.pipeline_with_full_context(context=context, language=language)

        if not status:
            # 异常返回
            self.result['data'] = {'error': company_info}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = company_info

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)

class ExtractProductPost(PublicMethodView):
    """ Extract product information Post
    """
    gpt_model = "gpt-4o-mini"
    generate_details = GenerateDetails(gpt_model=gpt_model)
    # 必要入参
    decorators = [requires('context')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('context', None) is None:
            error_msg = f'context is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        context = self.arg.get('context')
        if isinstance(context, str):
            try:
                context = json.loads(context)
            except Exception as e:
                error_msg = f"context must be json format - {str(e)}"
                res = {'error': error_msg}
                self.result['data'] = res
                output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return False, jsonify(output)

        # 业务逻辑
        status, product_details = self.generate_details.pipeline(context=context)

        if not status:
            # 异常返回
            self.result['data'] = {'error': product_details}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = product_details

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class ExtractSellPointPost(PublicMethodView):
    """ Extract product sell points Post
    """
    gpt_model = "gpt-4o-mini"
    generate_details = GenerateDetails(gpt_model=gpt_model)
    # 必要入参
    decorators = [requires('context')]

    # Http method
    def post(self):
        # 获取入参
        if self.arg.get('context', None) is None:
            error_msg = f'context is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        context = self.arg.get('context')
        language = self.arg.get('language', 'english')
        if isinstance(context, str):
            try:
                context = json.loads(context)
            except Exception as e:
                error_msg = f"context must be json format - {str(e)}"
                res = {'error': error_msg}
                self.result['data'] = res
                output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
                current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
                return False, jsonify(output)

        # 业务逻辑
        status, product_details = self.generate_details.pipeline(context=context, scenario=2, language=language)

        if not status:
            # 异常返回
            self.result['data'] = {'error': product_details}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = product_details

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)
