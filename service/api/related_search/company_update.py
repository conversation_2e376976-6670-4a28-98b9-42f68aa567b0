#-*- coding: utf-8 -*-
"""
    Update company information
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.reply.company_update_intent import company_update_intent


class CompanyUpdate(PublicMethodView):
    """ Update company information
    """
    # 必要入参
    decorators = [requires('userInput')]

    def checkParams(self):
        if type(self.arg.get('userInput')) != str:
            error_msg = f"userInput must be str!"
            self.result['data'] = {'error': error_msg}
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        return True, "success"

    def post(self):
        status, check_result = self.checkParams()
        user_query = self.arg.get('userInput')
        file_list = self.arg.get('fileList', [])
        company_profile = self.arg.get('companyProfile', {})
        watttraceid = self.request_header_watttraceid
        if not status:
            return check_result
        status, response_json = company_update_intent(user_query, file_list, company_profile, watttraceid)

        if not status:
            # 异常返回
            self.result['data'] = {'error': response_json}
            output = current_app.responsesData(20211, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = response_json

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)

