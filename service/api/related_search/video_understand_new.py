# -*- coding: utf-8 -*-
"""
    Multi-modal video understanding
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.mm_understand.understand_video import generation_report
import traceback


class VideoUnderstandPost(PublicMethodView):
    """
    Understand video for multi-dimension report
    """
    # 必要入参
    decorators = [requires('mediaList')]

    def post(self):
        # 获取入参
        if self.arg.get('mediaList', None) is None or len(self.arg.get('mediaList', [])) == 0:
            error_msg = f'mediaList is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))

        platform = self.arg.get('platform', "")
        detailed_id = self.arg.get('detailedId', "")
        media_list = self.arg.get('mediaList')

        # 业务逻辑
        try:
            status, reply_msg = generation_report(media_list, platform, detailed_id)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f': {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Extract Person Failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {'resMsg': reply_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)