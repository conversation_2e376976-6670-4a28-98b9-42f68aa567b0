#-*- coding: utf-8 -*-
"""
    related search
"""
from flask import jsonify, current_app
from utils.util_flask import requires
from ..base import PublicMethodView
from service.server.related_search import search_related_words
from service.server.analysis.query_expand import query_expand
from service.server.analysis.query_decompose import query_decompose
import traceback


class RelatedSearch(PublicMethodView):
    """ related search

    """
    # 必要入参
    decorators = [requires('requestId', 'keyword')]

    def checkParams(self):
        if type(self.arg.get('requestId')) != str:
            error_msg = f"requestId must be str!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if len(self.arg.get('requestId')) == 0:
            error_msg = f"requestId must not be empty!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90003, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if type(self.arg.get('keyword')) != str:
            error_msg = f"keyword must be str!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90002, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        if len(self.arg.get('keyword')) == 0:
            error_msg = f"keyword must not be empty!"
            res = [{'requestId': self.arg.get('requestId'), 'error': error_msg}]
            self.result['data'] = res[0]
            output = current_app.responsesData(90003, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return False, jsonify(output)

        return True, ""

    def post(self):
        status, check_result = self.checkParams()
        if not status:
            return check_result

        return_nums = self.arg.get('number', 5)
        code, answer = search_related_words(self.arg.get('requestId'), self.arg['keyword'], return_nums)
        self.result['data'] = {'resCode': code, 'requestId': self.arg.get('requestId'), 'relatedWords': answer}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class QueryExpand(PublicMethodView):
    """ Expand original query

    """
    # 必要入参
    decorators = [requires('queryInput')]
    
     # Http method
    def post(self):
        # 获取入参
        if self.arg.get('queryInput', None) is None:
            error_msg = f'Input user query is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
        video = self.arg.get('video', [])


        query_input = self.arg.get('queryInput')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, reply_msg = query_expand(query_input=query_input, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Query Expand Exception: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Query Expand failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = {"outputText": reply_msg}

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)


class QueryDecompose(PublicMethodView):
    """ Decompose original query

    """
    # 必要入参
    decorators = [requires('queryInput')]
    
     # Http method
    def post(self):
        # 获取入参
        if self.arg.get('queryInput', None) is None:
            error_msg = f'Input user query is empty.'
            res = {'error': error_msg}
            self.result['data'] = res
            return jsonify(current_app.responsesData(90001, self.result, self.arg, self.request_id))
        video = self.arg.get('video', [])


        query_input = self.arg.get('queryInput')
        watttraceid = self.request_header_watttraceid

        # 业务逻辑
        try:
            status, reply_msg = query_decompose(query_input=query_input, watttraceid=watttraceid)
        except Exception as e:
            # 异常返回
            self.result['data'] = {'resMsg': f'Query Decompose Exception: {str(e)} - {traceback.format_exc()}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        if not status:
            # 异常返回
            self.result['data'] = {'resMsg': f'Query Decompose failed. {reply_msg}'}
            output = current_app.responsesData(20205, self.result, self.arg, self.request_id)
            current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
            return jsonify(output)

        # 正常返回
        self.result['data'] = reply_msg

        # 格式化返回
        output = current_app.responsesData(0, self.result, self.arg, self.request_id)
        current_app.FlaskLog.info(current_app.fmtSaveOutput(self.request_id, output))
        return jsonify(output)