import config
import requests
from colorama import Fore
import re
import json

crawler_host = config.CRAWLER_HOST
spark_url = "/api/v1/spark/spark_html"
firecrawl_scrape_url = "https://api.firecrawl.dev/v1/scrape"
firecrawl_api_key = config.FIRECRAWL_API_KEY


def is_url_in_base_list(input_str: str):
    original_input = input_str.strip()
    input_str = re.sub(r'\s+', '', original_input)  # 移除所有空格

    # 改进www的检测逻辑 - 匹配任何大小写组合的www
    has_www = bool(re.search(r'(?i)^(?:https?:\/\/)?(www\.)', input_str))

    # 用于匹配的小写版本
    lower_input = input_str.lower()
    # 移除所有形式的www（包括大小写混合）和http(s)://
    lower_input = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', lower_input)
    lower_input = re.sub(r'\/+$', '/', lower_input.rstrip('/')) + ('/' if lower_input.endswith('/') else '')
    lower_input = re.sub(r'^mobile\.(x|twitter)\.', r'\1.', lower_input)

    url_pattern = re.compile(
        r'^(?:'  # 开始
        r'(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+'  # 域名部分
        r'[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?'  # 顶级域名
        r'(?:\/[^\s]*)?' # 路径部分（可选）
        r')$'
    )

    def is_likely_url(s: str) -> bool:
        # 检查是否包含至少一个点号，且不在开头或结尾
        if not re.search(r'^[^.]+\.[^.]+', s):
            return False
        # 检查是否包含常见的句子结束标点
        if re.search(r'\s*[.!?]\s+[A-Z]', original_input):
            return False
        # 检查域名部分的合理性
        domain_part = s.split('/')[0]
        if len(domain_part) < 3:  # 域名至少3个字符
            return False
        # 检查域名中的点号数量（通常不会超过4个）
        if domain_part.count('.') > 4:
            return False

        return True

    match = url_pattern.match(lower_input)
    print(Fore.YELLOW + f"Match: {match}"+Fore.RESET)
    if not match or not is_likely_url(lower_input):
        return False, ''

    # 提取原始URL的路径部分（保持大小写）
    original_parts = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', original_input)
    domain, *path_parts = original_parts.split('/', 1)
    original_path = f"/{path_parts[0]}" if path_parts else ''

    # 构建标准化的URL，但保持路径的原始大小写
    standardized_url = f'https://{"www." if has_www else ""}{domain.lower()}{original_path}'

    # 其他普通URL
    return True, standardized_url


def extract_raw_content(url_str: str = ""):
    body = {"url": url_str}
    try:
        response_ = requests.post(f"{crawler_host}{spark_url}", json=body, timeout=300)
    except requests.RequestException as e:
        return False, f"Error during request: {e}"

    if response_ and response_.status_code == 200:
        if response_.json().get("status", 0) == 0:
            raw_data = response_.json().get("result", {}).get("data", {})
            output_json = {
                "title": raw_data["title"],
                "content": raw_data["content"],
                "additional_content": raw_data["other"][0],
            }
            return True, output_json
        else:
            error = response_.json().get("error", "")
            return False, f"Failed request error: {error}"
    else:
        return False, f"Failed request with status code {response_.status_code}"


def extract_url_firecrawl(url_str: str = "", timeout_seconds: int = 40):
    """
    Extract raw content from URL using Firecrawl API
    
    This function uses the Firecrawl v1 API to scrape web content, which provides:
    - Better handling of dynamic content and JavaScript-rendered pages
    - Cleaner markdown output optimized for LLM processing
    - Built-in rate limiting and anti-bot detection bypass
    - More reliable extraction from complex websites
    
    :param url_str: URL to extract content from
    :param timeout_seconds: Maximum time to wait for extraction (default 40 seconds)
    :return: tuple (success: bool, result: dict or error message)
             - On success: (True, {"title": str, "content": str, "additional_content": str})
             - On failure: (False, error_message: str)
    
    Example usage:
        success, result = extract_url_firecrawl("https://example.com")
        if success:
            print(f"Title: {result['title']}")
            print(f"Content: {result['content']}")
    """
    if not url_str:
        return False, "URL cannot be empty"
    
    if not firecrawl_api_key:
        return False, "Firecrawl API key is not configured"
    
    # Prepare request headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {firecrawl_api_key}"
    }
    
    # Prepare request body according to Firecrawl v1 API
    body = {
        "url": url_str,
        "formats": ["markdown"],  # Get both markdown and html
        "onlyMainContent": True,  # Extract only main content
        "timeout": timeout_seconds * 1000  # Convert to milliseconds
    }
    
    try:
        response = requests.post(
            firecrawl_scrape_url, 
            headers=headers, 
            json=body, 
            timeout=timeout_seconds
        )
        
        if response.status_code == 200:
            response_data = response.json()
            
            if response_data.get("success", False):
                data = response_data.get("data", {})
                metadata = data.get("metadata", {})
                
                # Format output similar to extract_raw_content
                output_json = {
                    "title": metadata.get("title", ""),
                    "content": data.get("markdown", ""),  # Use markdown as main content
                    "additional_content": data.get("html", "")  # Use html as additional content
                }
                
                return True, output_json
            else:
                error_msg = response_data.get("error", "Unknown error from Firecrawl API")
                return False, f"Firecrawl API error: {error_msg}"
        else:
            try:
                error_data = response.json()
                error_msg = error_data.get("error", f"HTTP {response.status_code}")
            except:
                error_msg = f"HTTP {response.status_code}"
            return False, f"Firecrawl API request failed: {error_msg}"
            
    except requests.exceptions.Timeout:
        return False, f"Firecrawl API request timed out after {timeout_seconds} seconds"
    except requests.exceptions.RequestException as e:
        return False, f"Firecrawl API request error: {str(e)}"
    except json.JSONDecodeError:
        return False, "Failed to parse Firecrawl API response"
    except Exception as e:
        return False, f"Unexpected error in Firecrawl extraction: {str(e)}"

