import requests
import time
import asyncio


def log_input_output(func):
    def wrapper(*args, **kwargs):
        # 打印函数的输入参数
        print(f"Calling {func.__name__} with:")
        print(f"{args}")
        # 调用原函数
        result = func(*args, **kwargs)
        # 打印函数的输出结果
        print(f"{func.__name__} returned: {result}")
        return result
    return wrapper


class AudioTranscription:
    def __init__(self, service_host: str = 'http://dev-watt-ai-gpt.watt.chat',
                 watt_gpt_token: str = ''):
        self.service_host = service_host
        self.upload_url = self.service_host + "/v1/audio/transcriptions/upload"
        self.accept_url = self.service_host + "/v1/audio/transcriptions/accept"
        self.watt_gpt_token = watt_gpt_token
        self.headers = {"Content-Type": "application/json", 'watt-gpt-token': self.watt_gpt_token}

    def upload(self, audio_key, audio_type):
        body = {"fileKey": audio_key,
                "fileType": audio_type}
        try:
            response = requests.post(self.upload_url, json=body, headers=self.headers, timeout=5)
            if response.status_code == 200:
                answer = response.json().get("result", {}).get("data", {}).get("taskId", "")
                if not answer:
                    return False, f"empty task id"
                return True, answer
            else:
                return False, f"request fail in getting task id"
        except Exception as e:
            return False, f"error in upload: {e} "

    def accept(self, task_id):
        body = {"taskId": task_id}
        try:
            response = requests.post(self.accept_url, json=body, headers=self.headers, timeout=2)
            if response.status_code == 200:
                answer = response.json().get("result", {}).get("data", {})
                if not answer:
                    return False, f"empty audio result"
                if answer.get('taskStatus', '') == 4:
                    return False, None
                if answer.get("objectKey", ''):
                    return True, answer
                else:
                    return False, f"not yet to get result"
            else:
                return False, f"request fail in getting audio result"
        except Exception as e:
            return False, f"error in accept: {e} "

    def pipeline(self, audio_dict):
        audio_key = audio_dict['key']
        audio_type = audio_dict['suffix']
        status, task_id = self.upload(audio_key, audio_type)
        if not status:
            return None
        time.sleep(1)
        status = False
        for i in range(30):
            status, accept_data = self.accept(task_id)
            if status:
                break
            else:
                time.sleep(0.5)
        if not status:
            return None
        transcript_key = accept_data["objectKey"]
        status, url_list = get_urls([transcript_key])
        if not status:
            return None
        url = url_list[0]
        try:
            response = requests.get(url, timeout=5)
        except Exception as e:
            return None
        if response.status_code == 200:
            text = response.text
            if len(text)> 50:
                return response.text
            else:
                return None
            
    async def pipeline_async(self, audio_dict):
        import aiohttp
    
        audio_key = audio_dict['key']
        audio_type = audio_dict['suffix']
        
        # Upload audio asynchronously
        async with aiohttp.ClientSession() as session:
            # Step 1: Upload and get task_id
            body = {"fileKey": audio_key, "fileType": audio_type}
            try:
                async with session.post(self.upload_url, json=body, headers=self.headers, timeout=5) as response:
                    if response.status == 200:
                        response_json = await response.json()
                        task_id = response_json.get("result", {}).get("data", {}).get("taskId", "")
                        if not task_id:
                            return None
                    else:
                        return None
            except Exception:
                return None
            
            # Wait a bit before checking for results
            await asyncio.sleep(1)
            
            # Step 2: Poll for results
            status = False
            accept_data = None
            
            for i in range(30):
                body = {"taskId": task_id}
                try:
                    async with session.post(self.accept_url, json=body, headers=self.headers, timeout=2) as response:
                        if response.status == 200:
                            response_json = await response.json()
                            accept_data = response_json.get("result", {}).get("data", {})
                            
                            if not accept_data:
                                await asyncio.sleep(0.5)
                                continue
                                
                            if accept_data.get('taskStatus', '') == 4:
                                return None
                                
                            if accept_data.get("objectKey", ''):
                                status = True
                                break
                        
                        await asyncio.sleep(0.5)
                except Exception:
                    await asyncio.sleep(0.5)
                    continue
            
            if not status or not accept_data:
                return None
            
            # Step 3: Get transcript content
            transcript_key = accept_data["objectKey"]
            status, url_list = get_urls([transcript_key])
            
            if not status or not url_list:
                return None
                
            url = url_list[0]
            
            try:
                async with session.get(url, timeout=5) as response:
                    if response.status == 200:
                        text = await response.text()
                        if len(text) > 50:
                            return text
            except Exception:
                pass
                
            return None


def get_urls(key_list):
    rdp_url = 'https://dev-watt-ai-rdp.watt.chat/v1/object/object_find'
    test = '111'
    headers = {"Content-Type": "application/json", "rdp-token": test}
    body = { "object_key_list": key_list}
    try:
        response_ = requests.post(rdp_url, json=body, headers=headers, timeout=5)
        if response_.status_code == 200:
            response = response_.json().get("result", {}).get("data", {})
            urls = []
            for i in response:
                urls.append(i["object_url"])
            if not urls:
                return False, f"No urls returns after get rdp response"
            return True, urls
        else:
            return False, f"Error in call rdp in getting urls"
    except Exception as e:
        return False, f"Error in get image urls by rdp: {e}"


if __name__ == "__main__":
    audio= {
        "key": "7535a8cd108a6de7ca16a97ba836465e/fae74fb7dfefe57f19db35b28f085354",
        "suffix": "mp3",
        "url": "https://d1g5rjveuc2ya5.cloudfront.net/7535a8cd108a6de7ca16a97ba836465e/04045ec44f497bbe8aac49a5c3e7a007?Expires=1735225770&Signature=VZ7isjp5OtFPshd6HtAIaJzRVJQDuIDgllVG~smICeSpCYoX~yfzTXWaINMcCxCU~~hmqoFoE9rGc8t2r8caqlJ4ecfTODJejzoyzd1rQ2PJbvn7ZN-XxtlpmPB45S0asS8kOb-rYXulvHlaNbbXWg1SUjs8ceTE5FvHHGAKaUUPPIn5XcA6wKO7bxG-SR9J-Jjl8by0N1UIROhRMFuXnKDNtVqUmYg-alwM00doBtbqA~c1SiyYI5~NZA2OI1VQwuztC~cxdoFXsqHKgellUqIUmgV6Wpfc~iRU9mkeh8OoDbW80XKdO6CajmVycoiIAkbAZy~cS1~QeE5GL3qxvg__&Key-Pair-Id=K1WJZL3MXMCMUT"
    }
    WATT_AI_GPT_TOKEN = ''
    audio_pipe = AudioTranscription(watt_gpt_token=WATT_AI_GPT_TOKEN)
    status, transcript = audio_pipe.pipeline(audio)
    print(status, transcript)