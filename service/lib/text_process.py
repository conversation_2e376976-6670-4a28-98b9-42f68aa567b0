import re
import json
import string
import random


def count_english_words(text):
    # Regex pattern to match words (a-z or A-Z) and remove everything else
    cleaned_text = re.sub(r'[^a-zA-Z\s]', '', text)
    # Split the text by whitespace to get the words
    words = cleaned_text.split()
    return len(words)


def count_characters(text):
    """
    Count the number of characters in a string with specific rules:
    - Each Chinese character counts as 1
    - Each English letter counts as 1 (individual letters, not words)
    - Each number counts as 1
    - Each emoji and symbol counts as 1
    - Whitespace is not counted
    
    Args:
        text: str, the input text
    
    Returns:
        int, the number of characters
    """
    if not text:
        return 0
    
    count = 0
    
    for char in text:
        # Skip whitespace
        if char.isspace():
            continue
            
        # Count all non-whitespace characters
        count += 1
    
    return count


def url_clean(url: str):
    """
    Clean the input URL by removing the protocol and www. prefix
    Args:
        url: str, the input URL

    Returns:
        str, the cleaned URL
    """
    # Remove the protocol (http:// or https://) and www. prefix from the URL
    cleaned_url = re.sub(r'^(?:https?:\/\/)?(?:www\.)?', '', url)
    return cleaned_url

def url_complete(url: str):
    """
    Complete the input URL by adding the protocol and www. prefix
    Args:
        url: str, the input URL

    Returns:
        str, the completed URL
    """
    # Add the protocol (https://) and www. prefix to the URL
    completed_url = 'https://' + url
    return completed_url


# Extract data using nested dictionary get with defaults
def safe_get(data, *keys, default=None):
    """Safely navigate nested dictionaries"""
    if data is None:
        return default
    
    current = data
    for key in keys:
        if not isinstance(current, dict) or key not in current:
            return default
        current = current[key]
    return current


def clean_dict_recursive(d):
    def is_empty(v):
        if v in (None, "", [], {}, ()):
            return True
        return False

    if isinstance(d, dict):
        return {
            k: clean_dict_recursive(v)
            for k, v in d.items()
            if not is_empty(v) and not is_empty(clean_dict_recursive(v))
        }
    elif isinstance(d, list):
        return [clean_dict_recursive(v) for v in d if not is_empty(v)]
    else:
        return d


def delete_random_items(data, target_key):
    # target_key = random.choice(list(data.keys()))
    target_dict = data[target_key]
    # Get all second-depth keys that are not dictionaries
    eligible_keys = [k for k, v in target_dict.items() if not isinstance(v, dict)]
    # Determine number of items to delete (between 2 and 4, or less if not enough items)
    num_to_delete = min(random.randint(2, 4), len(eligible_keys))
    # Randomly select keys to delete
    keys_to_delete = random.sample(eligible_keys, num_to_delete)
    # Delete the selected keys
    for key in keys_to_delete:
        del target_dict[key]
    return data, target_key, keys_to_delete


def str_count(input_str):
    """Count the number of English, Chinese, space, digit, and punctuation characters in a string"""
    # Define a set of Chinese double-width punctuation
    chinese_punctuation = {'。', '，', '、', '；', '：', '？', '！', '（', '）', '【', '】', '《', '》', '"', '"', ''', '''}
    count_en = sum(c in string.ascii_letters for c in input_str)
    count_dg = sum(c.isdigit() for c in input_str)
    count_sp = sum(c.isspace() for c in input_str)
    count_pu = sum((c in string.punctuation or c in chinese_punctuation) and c not in chinese_punctuation for c in
                   input_str)  # Punctuation excluding Chinese punctuation
    count_zh = sum('\u4e00' <= c <= '\u9fff' for c in input_str) + sum(c in chinese_punctuation for c in input_str)
    count_other = len(input_str) - count_en - count_dg - count_sp - count_pu - count_zh
    return 2 * count_zh + count_en + count_sp + count_dg + count_pu + count_other


def split_string(input_string):
    parts = input_string.split("|")
    content = parts[0].strip()
    keywords = parts[1].split(",")
    if len(parts) < 3:
        return content, [keyword.strip() for keyword in keywords if keyword.strip()], []
    original_keys = parts[2].split(",")
    keyword_list = [keyword.strip() for keyword in keywords if keyword.strip()]
    original_key_list = [keyword.strip() for keyword in original_keys if keyword.strip()]
    return content, keyword_list, original_key_list


def flatten_dictionary(d):
    flattened_list = []
    for value in d.values():
        if isinstance(value, list) and value:
            flattened_list.extend(value)  # 如果值是列表，则将其元素添加到结果列表中
        elif isinstance(value, str) and value:
            flattened_list.append(value)  # 如果值是字符串，则直接添加到结果列表中
    return flattened_list


def load_char_types(input_file):
    """Load the character types of the input file"""
    with open(input_file, "r", encoding="utf-8") as f:
        data = json.load(f)
    return data


def clean_json_string(json_string):
    # 去除前面的 ```json
    if json_string.startswith("```json"):
        json_string = json_string[len("```json"):]

    # 去除后面的 ```
    if json_string.endswith("```"):
        json_string = json_string[:len(json_string) - len("```")]

    # 去除前后多余的空白字符
    json_string = json_string.strip()

    return json_string


def replace_patterns(dict_data: dict, key: str):
    # Define the pattern to replace: ' and ', colons, and quotation marks
    pattern = r' and |:|"'
    replacement = ', '  # You can adjust this replacement as needed

    # Check if the key exists in the dictionary and that it's associated with a list or string
    if key in dict_data:
        if isinstance(dict_data[key], list):
            # Replace patterns in each string in the list
            dict_data[key] = [re.sub(pattern, replacement, item) for item in dict_data[key]]
        elif isinstance(dict_data[key], str):
            # Replace patterns in the string
            dict_data[key] = re.sub(pattern, replacement, dict_data[key])
    return dict_data

def remove_hashtags(text: str):
    return re.sub(r'#\w+', '', text)


def split_text_and_tags(text):
    hashtags = re.findall(r"#(\w+)", text)

    clean_text = re.sub(r"#\w+", "", text).strip()

    return {
        "summary": clean_text,
        "tags": hashtags
    }


def truncate_paragraph(text: str, front_len: int = 1000, back_len: int = 2000):
    """
    Truncate the text by keeping the first front_len and last back_len characters
    Args:
        text: str, the text to truncate
        front_len: int, the number of characters to keep from the beginning
        back_len: int, the number of characters to keep from the end

    Returns:
        The truncated text
    """
    if len(text) <= front_len + back_len:
        return False, text, ""

    return True, text[:front_len], text[-back_len:]


def truncate(text: str, direct: str = 'fd', token_len: int = 1000):
    """
    对超长文本进行截断,截取最大长度为token_len对应的文本
    Args:
        text: str, 需要截断的文本内容
        direct: str, 截断方向，"fd": 前向截取，"bd": 后向截取
        token_len: int, 截取最大tokens长度

    Returns:
        1.truncate_text: str,返回截取后的文本
        2.truncate_text的tokens长度
        3.输入输出字符是否改变
    """
    if not text or text.isspace():
        return "", 0, "unchanged"

    if len(text) <= token_len:
        return text, len(text), "unchanged"

    if direct == "fd":
        truncate_text = text[:token_len]
    else:
        truncate_text = text[-token_len:]

    punctuation_marks = ".?!。？！\n"
    if direct == "fd":
        last_punctuation = max(truncate_text.rfind(mark) for mark in punctuation_marks)
        truncate_text = truncate_text[:last_punctuation + 1] if last_punctuation != -1 else truncate_text
    else:
        first_punctuation = min((idx for idx, char in enumerate(truncate_text) if char in punctuation_marks),
                                default=-1)
        truncate_text = truncate_text[first_punctuation + 1:]

    return truncate_text, len(truncate_text), "changed"


def truncate_link_str(token_num: int, link_str: str, token_cap=30):
    token = token_num - int(len(link_str)*0.3)

    # 如果 token <= token_cap，需要截断 link_str
    if token <= token_cap:
        # 计算需要截断的长度
        truncate_length = len(link_str) - (token_cap - token)
        # 截断 link_str，使得 token 的值至少为 30
        link_str = link_str[:truncate_length]
        # 更新 token 值
        token = token_num - int(len(link_str)*0.3)

    return token, link_str


def compress_text_with_shrink(text, target_length, prompt_gpt, watttraceid, max_retries=3):
    """
    使用shrink模板压缩文本到指定长度
    Args:
        text: str, 需要压缩的文本
        target_length: int, 目标字符数
        prompt_gpt: GPT调用实例
        watttraceid: 追踪ID
        max_retries: int, 最大重试次数
    
    Returns:
        tuple: (success, compressed_text)
    """
    from service import prompts_fusion
    from service.lib.prompt_fusion import call_llm_model
    
    if not text or target_length <= 0:
        return False, text
    
    current_length = count_characters(text)
    if current_length <= target_length:
        return True, text
    
    compressed_text = text
    
    for attempt in range(max_retries):
        try:
            # 使用shrink模板压缩文本
            system_prompt = prompts_fusion.get_shrink({})
            user_prompt = prompts_fusion.get_shrink_user({
                "text": compressed_text,
                "target_length": target_length
            })
            
            status, response = call_llm_model(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                model="gpt-4o-mini",
                watttraceid=watttraceid,
                prompt_gpt=prompt_gpt,
                temperature=0.3,
                json_object=False
            )
            
            if not status:
                return False, compressed_text
            
            compressed_text = response.strip()
            current_length = count_characters(compressed_text)
            
            if current_length <= target_length:
                return True, compressed_text
                
        except Exception as e:
            print(f"压缩文本时出错 (尝试 {attempt + 1}): {str(e)}")
            continue
    
    # 如果重试后仍然超长，强制截断
    if count_characters(compressed_text) > target_length:
        # 按照字符计数规则截断（不计算空格）
        truncated = ""
        char_count = 0
        
        for char in compressed_text:
            # 如果是空格，直接添加（不计入计数）
            if char.isspace():
                truncated += char
            # 如果还没达到限制，添加字符并计数
            elif char_count < target_length:
                truncated += char
                char_count += 1
            # 达到限制则停止
            else:
                break
        
        compressed_text = truncated
    
    return True, compressed_text
