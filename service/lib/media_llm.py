import cv2
import requests
import base64
import tempfile
import json
import re
import os
from service import callWattGPT


def encode_image(image_path):
    if not os.path.exists(image_path):
        return None
    with open(image_path, "rb") as image_file:
        try:
            base64_str = base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            print(f"Failed to encode image: {image_path} {e}")
            return None
    return base64_str

def gpt_with_media(media_list: list, sys_prompt: str, user_prompt: str, audio=None, json_schema=None, model="gpt-4o", max_tokens=2000):
    """
    Call GPT with media
    :param media_list: list of media, each element is an url or a base64 string
    :param sys_prompt: system prompt
    :param user_prompt: user prompt
    :param audio: audio text
    :param json_schema: json schema for the output
    :param model: llm model name
    :param max_tokens: maximum tokens for the output
    """
    # Getting the base64 string
    media_content = [{"type": "text", "text": user_prompt}]
    if media_list:
        first = media_list[0]
        if is_url(first):
            media_content.extend([{"type": "image_url", "image_url": {"url": i}} for i in media_list])
        else:
            media_content.extend([{"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{i}"}} for i in media_list])
    if audio:
        media_content.extend([{"type": "text", "text": "The pictures are extracted from a video at a frame rate of one second. The following is its voice-over text:\n"+audio}])
    body = {
        "model": model,
        "messages": [
            {"role": "system", "content": sys_prompt},
            {"role": "user",
             "content": media_content}],
        "max_tokens": max_tokens
    }
    if json_schema:
        body["response_format"] = {"type": "json_schema", "json_schema": json_schema}
    status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=100)
    if not status:
        return status, response
    try:
        output_string = response['result']['data']['choices'][0]['message']['content']
        if json_schema:
            output_string = json.loads(output_string)
    except Exception as error:
        return False, f"Decode Error of output from GPT: {error}"
    return True, output_string


def chat_gpt(sys_prompt, user_prompt, json_schema='', model='gpt-4o', temperature=0.6):
    body = {
        "model": model,
        'temperature': temperature,
        "messages": [
            {
                "role": "system",
                "content": sys_prompt
            },
            {
                "role": "user",
                "content": user_prompt
            }
        ]
    }
    if json_schema:
        body["response_format"] = {"type": "json_schema", "json_schema": json_schema}
    status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=100)
    if not status:
        return status, response
    try:
        output_string = response['result']['data']['choices'][0]['message']['content']
        if json_schema:
            output_string = json.loads(output_string)
    except Exception as error:
        return False, f"Decode Error of output from GPT: {error}"
    return True, output_string


def remove_urls(text):
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    return re.sub(url_pattern, '', text)


def is_url(string):
    # 定义URL的正则表达式
    regex = re.compile(
        r'^(https?://)?'  # 可选的协议（http或https）
        r'(([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})'  # 域名部分
        r'(:\d+)?'  # 可选的端口号
        r'(/.*)?$',  # 可选的路径
        re.IGNORECASE
    )
    return re.match(regex, string) is not None


def process_video(url):
    # Step 1: Download the video
    if is_url(url):
        download_status, video_data = download_video(url)
        if not download_status:
            return False, video_data
        # Step 2: Save the video to a temporary file
        with tempfile.NamedTemporaryFile(suffix=".mp4") as temp_video:
            temp_video.write(video_data)
            temp_video.flush()  # Ensure data is written to disk
            video_status, frames = extract_frames_from_video(temp_video.name)
            if not video_status:
                return False, frames
            return True, frames
    else:
        video_status, frames = extract_frames_from_video(url)
        if not video_status:
            return False, frames
        return True, frames


def download_video(url):
    response = requests.get(url, timeout=20)
    if response.status_code == 200:
        return True, response.content
    else:
        return False, "Failed to download video"


def extract_frames_from_video(video_path, max_num_frames=20):
    # Load video from the saved video path
    cap = cv2.VideoCapture(video_path)
    # Check if video opened successfully
    if not cap.isOpened():
        return False, "Couldn't read video file"
    # 获取视频帧率和总时长
    fps = cap.get(cv2.CAP_PROP_FPS)
    frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    duration = int(frame_count / fps)  # 视频总时长（秒）

    base64_frames = []
    if duration < 10:  # 如果视频时长小于10秒，每0.5秒提取一帧
        coefficient = 0.5
        duration *= 2
        for t in range(duration):  # 每0.5秒提取一帧
            frame_time = int(coefficient * t * fps)  # 转换为帧索引
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_time)  # 跳到指定帧
            ret, frame = cap.read()
            if not ret:
                break
            # Convert the frame to base64
            _, buffer = cv2.imencode('.jpg', frame)
            base64_str = base64.b64encode(buffer).decode('utf-8')
            base64_frames.append(base64_str)
    elif 10 < duration < 30:  # 如果视频时长大于10秒小于30s, 固定提取20帧
        step = max(frame_count // max_num_frames, 1)
        for i in range(max_num_frames):
            frame_number = i * step
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            ret, frame = cap.read()
            if not ret:
                break
            # Convert the frame to base64
            _, buffer = cv2.imencode('.jpg', frame)
            base64_str = base64.b64encode(buffer).decode('utf-8')
            base64_frames.append(base64_str)

    else:  # 大于30s，提取前30s的20帧
        for t in range(max_num_frames):  # 每1.5秒提取一帧
            frame_time = int(1.5 * t * fps)  # 转换为帧索引
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_time)  # 跳到指定帧
            ret, frame = cap.read()
            if not ret:
                break
            # Convert the frame to base64
            _, buffer = cv2.imencode('.jpg', frame)
            base64_str = base64.b64encode(buffer).decode('utf-8')
            base64_frames.append(base64_str)

    cap.release()
    return True, base64_frames


def play_base64_images(base64_list, delay=100):
    import numpy as np
    for idx, base64_img in enumerate(base64_list):
        img_data = base64.b64decode(base64_img)
        img_array = np.frombuffer(img_data, dtype=np.uint8)
        img = cv2.imdecode(img_array, cv2.IMREAD_COLOR)
        if img is None:
            continue
        cv2.imshow("Base64 Image Viewer", img)
        key = cv2.waitKey(delay)  # `delay` 毫秒后自动切换到下一帧
        if key == 27:  # 按 `ESC` 键退出
            break
    cv2.destroyAllWindows()


def get_audio_binary(audio_url):
    if not audio_url:
        return None
    if is_url(audio_url):
        status, binary_audio = download_video(audio_url)
        if not status:
            return None
        return base64.b64encode(binary_audio).decode('utf-8')
    else:
        try:
            audio = open(audio_url, "rb").read()
            return base64.b64encode(audio).decode('utf-8')
        except:
            return None


if __name__ == "__main__":
    # Test the process_video function
    # for i in range(6):
    # url = f'/Users/<USER>/Documents/vison_test/test_video/{str(2)}.mp4'
    # url = "https://video.twimg.com/ext_tw_video/1848950399296016385/pu/vid/avc1/320x590/qKpqY5xiOPJAOySW.mp4?tag=12"
    # a, b = process_video(url)
    # print(len(b))
    # play_base64_images(b)

    # image = ["https://pbs.twimg.com/media/GaoK_OyXcAAGt2C.jpg"]
    # system = ("You are a helpful AI assistant in recognizing the content of image and video. User provide you with media, "
    #           "you should return the content of the media in text format.")
    # user = 'Here are some frames from a video clip, tell me what the clip is talking about, and abstract its high-points in one sentence.'
    # # s, r = gpt_with_image(b, system, user)
    # frames = json.load(open("/Users/<USER>/Documents/watt-ai-hoc/logs/segment_0.json"))['media']['base64String']
    # play_base64_images(frames)
    # s, r = gpt_with_media(frames, system, user)
    # print(r)
    "The video appears to focus on a vibrant and colorful performance, featuring a woman in striking outfits amidst an enthusiastic crowd, reflecting themes of glamour and spectacle."
    "The video appears to showcase a vibrant and lively party atmosphere, with colorful outfits, energetic dancing, and a fun, carefree vibe."
    "The video seems to focus on themes of self-expression and vibrant individuality, with dynamic and colorful visuals, playful fashion, and confident energy."
    test = """Here is the product to be promoted named <Lilium - Wikipedia>, with a description:
[description starts]
The Lilium Wikipedia page provides comprehensive information about the genus Lilium, commonly known as lilies. It covers a variety of species, their classifications, cultivation methods, and uses in cuisine and medicine. The page details various hybrids and their characteristics, emphasizing the importance of lilies in different cultures. It highlights the beauty and diversity of lily flowers, which are known for their fragrant, trumpet-shaped blooms in a range of colors. Additionally, the page addresses ecological concerns affecting lily populations and their symbolic significance in Christianity. Whether you're a horticulturist, a culinary enthusiast, or simply a flower lover, this page is a valuable resource for understanding lilies and their various applications.
[description ends]
It has some representative product tags as: lilium, flower, garden.
As the product promotion needs to fit the market, here are some social media content which could be helpful to understand the market:
[social_media_post starts]
The text of the social media post is:
  Found some bigger leds I'm going to finish these lady's with. Then I'll be stepping my game up for the indoor season! 
The visual content are:
    1. A image: this is a view of a mountain, with a river in the middle, and a forest on the side
    2. A video: The video appears to focus on a vibrant and colorful performance, featuring a woman in striking outfits amidst an enthusiastic crowd, reflecting themes of glamour and spectacle.
    3. A video: The video appears to showcase a vibrant and lively party atmosphere, with colorful outfits, energetic dancing, and a fun, carefree vibe.
[social_media_post ends]
There are some promoting attitude or strategy keywords as: engagement, awareness, conversion. When generating voice-over script, you should consider this attitude or strategy into it.
Please generate a voice-over script for product promotion based on the above information. Requirements:
    1. The script should be attractive and persuasive, and highlight the features of the product
    2. Script should be in English and less than 250 words
    3. Output should only be a text paragraph"""
    a, b = chat_gpt(sys_prompt="""You are a professional marketing expert specializing in product promotion. Your are good at promoting product through video, especially writing voice-over script that fits the product and market. User give you a product information and some supplementary conent, you should use these resources to generate a voice-over script.""", user_prompt = test, model='gpt-4o')
    print(b)