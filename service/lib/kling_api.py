import grequests
import time
import jwt
import requests
from typing import List, <PERSON><PERSON>, Optional, Dict, Any
from contextlib import contextmanager
from dataclasses import dataclass
import config
from colorama import Fore


# 配置类
@dataclass
class KlingConfig:
    access_key: str
    secret_key: str
    host: str = "https://api.klingai.com"
    token_lifetime: int = 1800
    max_retries: int = 3
    timeout: int = 30
    polling_interval: int = 5
    batch_size: int = 1


@dataclass
class TaskResult:
    index: int
    image_url: str
    caption: str
    task_id: str
    error_msg: str

    def to_dict(self) -> Dict[str, Any]:
        return {
            "index": self.index,
            "image_url": self.image_url,
            "caption": self.caption,
            "task_id": self.task_id,
            "error_msg": self.error_msg
        }

@dataclass
class PortraitResult:
    index: int
    image_url: str
    error_msg: str
    def to_dict(self) -> Dict[str, Any]:
        return {
            "index": self.index,
            "image_url": self.image_url,
            "error_msg": self.error_msg
        }

# 自定义异常
class KlingError(Exception):
    """Base exception for Kling API"""
    pass


class TokenError(KlingError):
    """Token related errors"""
    pass


class RequestError(KlingError):
    """Request related errors"""
    pass


class KlingRequest:
    def __init__(self, config: KlingConfig):
        self.config = config
        self.token = None
        self.token_expiration = 0

    @contextmanager
    def session_scope(self):
        """Session context manager"""
        session = requests.Session()
        try:
            yield session
        finally:
            session.close()

    def get_token(self) -> str:
        """
        Get JWT token with retry mechanism

        Returns:
            str: JWT token
        Raises:
            TokenError: If token generation fails
        """
        current_time = int(time.time())

        if self.token and current_time < self.token_expiration:
            return self.token

        try:
            headers = {
                "alg": "HS256",
                "typ": "JWT"
            }

            payload = {
                "iss": self.config.access_key,
                "exp": current_time + self.config.token_lifetime,
                "nbf": current_time - 5
            }

            self.token = jwt.encode(payload, self.config.secret_key, headers=headers)
            self.token_expiration = current_time + (self.config.token_lifetime - 200)  # Buffer time
            return self.token

        except Exception as e:
            print(f"Token generation failed: {str(e)}")
            raise TokenError(f"Failed to generate token: {str(e)}")

    def generate_image_task(self, prompt: str, num: int=1, ratio: str="9:16") -> grequests.AsyncRequest:
        """
        Create image generation task

        Args:
            prompt: Image generation prompt
            num: Number of images to generate
            ratio: Aspect ratio, default is 9:16
        Returns:
            grequests.AsyncRequest: Async request object
        """
        url = f"{self.config.host}/v1/images/generations"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}"
        }
        data = {
            "prompt": str(prompt),
            "aspect_ratio": ratio,
            "n": num
        }

        return grequests.post(
            url,
            headers=headers,
            json=data,
            timeout=self.config.timeout
        )
        
    def generate_video_task_without_image(self, prompt: str, aspect_ratio: str="9:16") -> requests.Request:
        """
        Create video generation task without image

        Args:
            prompt: Video generation prompt
        Returns:
            requests.Request: request object
        """
        url = f"{self.config.host}/v1/videos/text2video"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}"
        }
        data = {
            "prompt": str(prompt),
            "mode": "pro",
            "aspect_ratio": aspect_ratio
        }

        return requests.post(url, headers=headers, json=data, timeout=self.config.timeout)
    

    def generate_video_task(self, prompt, img_url: str) -> grequests.AsyncRequest:
        """
        Create video generation task

        Args:
            prompt: Video generation prompt
            img_url: Image URL as base for video
        Returns:
            grequests.AsyncRequest: Async request object
        """
        url = f"{self.config.host}/v1/videos/image2video"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.get_token()}"
        }
        data = {
            "model_name": "kling-v1-5",
            "image": img_url,
            "prompt": str(prompt),
            "cfg_scale": 0.7,
            "mode": "pro"
        }

        return grequests.post(url, headers=headers, json=data, timeout=self.config.timeout)

    def query_img_task_status(self, task_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Query image task status with retry mechanism

        Args:
            task_id: Task ID to query
        Returns:
            Tuple[bool, Dict]: Status and response data
        """
        url = f"{self.config.host}/v1/images/generations/{task_id}"

        for attempt in range(self.config.max_retries):
            try:
                with self.session_scope() as session:
                    headers = {"Authorization": f"Bearer {self.get_token()}"}
                    response = session.get(url, headers=headers, timeout=self.config.timeout)

                    if response.status_code == 200:
                        output = response.json()
                        if output.get("code") == 0:
                            return True, output.get("data", {})

                    print(f"Attempt {attempt + 1} failed for task {task_id}")
                    time.sleep(1)  # Short delay between retries

            except requests.RequestException as e:
                print(f"Request failed for task {task_id}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise RequestError(f"Failed to query task {task_id} after {self.config.max_retries} attempts")

        return False, {"error": f"Failed to query task {task_id} after {self.config.max_retries} attempts"}
    
    def query_video_task_without_image_status(self, task_id: None) -> Tuple[bool, Dict[str, Any]]:
        """
        Query video generation task status with retry mechanism

        Args:
            task_id: Task ID to query
        Returns:
            Tuple[bool, Dict]: Status and response data
        """
        if task_id is None:
            url = f"{self.config.host}/v1/videos/text2video"
        else:
            url = f"{self.config.host}/v1/videos/text2video/{task_id}"

        for attempt in range(self.config.max_retries):
            try:
                with self.session_scope() as session:
                    headers = {"Authorization": f"Bearer {self.get_token()}"}
                    response = session.get(url, headers=headers, timeout=self.config.timeout)

                    if response.status_code == 200:
                        output = response.json()
                        if output.get("code") == 0:
                            return True, output.get("data", {})

                    print(f"Attempt {attempt + 1} failed for task {task_id}")
                    time.sleep(1)  # Short delay between retries

            except requests.RequestException as e:
                print(f"Request failed for task {task_id}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise RequestError(f"Failed to query task {task_id} after {self.config.max_retries} attempts")

        return False, {"error": f"Failed to query task {task_id} after {self.config.max_retries} attempts"}


    def query_video_task_status(self, task_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Query video generation task status with retry mechanism

        Args:
            task_id: Task ID to query
        Returns:
            Tuple[bool, Dict]: Status and response data
        """
        url = f"{self.config.host}/v1/videos/image2video/{task_id}"

        for attempt in range(self.config.max_retries):
            try:
                with self.session_scope() as session:
                    headers = {"Authorization": f"Bearer {self.get_token()}"}
                    response = session.get(url, headers=headers, timeout=self.config.timeout)

                    if response.status_code == 200:
                        output = response.json()
                        if output.get("code") == 0:
                            return True, output.get("data", {})

                    print(f"Attempt {attempt + 1} failed for task {task_id}")
                    time.sleep(1)  # Short delay between retries

            except requests.RequestException as e:
                print(f"Request failed for task {task_id}: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise RequestError(f"Failed to query task {task_id} after {self.config.max_retries} attempts")

        return False, {"error": f"Failed to query task {task_id} after {self.config.max_retries} attempts"}

    def poll_task_status(self, task_id: str, timeout: int = 300, interval: float = 5.0) -> Tuple[bool, List[str]]:
        """
        轮询任务状态直到完成或超时

        Args:
            task_id: 任务ID
            timeout: 超时时间(秒)
            interval: 轮询间隔(秒)

        Returns:
            (success, result) 元组
        """
        start_time = time.time()

        while True:
            # 检查是否超时
            if time.time() - start_time > timeout:
                return False, ["Timeout while polling task status"]

            try:
                success, result = self.query_img_task_status(task_id)

                if not success:
                    return False, [result.get("error", "Failed to query task status")]

                status = result.get("task_status")

                if status == "succeed":
                    images = result.get("task_result", {}).get("images", [])
                    if not images:
                        return False, ["No images found in task result"]
                    return True, [image.get("url") for image in images]

                elif status == "failed":
                    return False, [f"Task failed: {result.get('task_status_msg', '')}"]

                elif status in ["submitted", "processing"]:
                    # 任务还在进行中,等待一段时间后继续查询
                    time.sleep(interval)
                    continue

                else:
                    return False, [f"Unknown task status: {status}"]

            except Exception as e:
                return False, [f"Exception: Failed to poll task status: {str(e)}"]


    def generate_portraits(
            self,
            prompt: str,
            num: int = 1,
            ratio: str = "1:1"
    ):
        """
        Generate portraits

        Args:
            prompt: Image prompt
            num: Number of images to generate
            ratio: Aspect ratio, default is 1:1
        Returns:
            Tuple containing:
                - success status (bool)
                - results (url list)
                - elapsed time (float)
        """
        # print(f"Starting generation for {len(prompts)} images")
        # start_time = time.time()
        try:
            task = self.generate_image_task(prompt, num, ratio)
            response = grequests.map([task])[0]

            if not response or response.status_code != 200:
                return False, f"Failed to submit task - {response.status_code if response else 'No response'}"
            output = response.json()
            task_id = output.get("data", {}).get("task_id")
            if not task_id:
                return False, f"Failed to get task_id - {output.get('task_status_msg', '')}"
            return self.poll_task_status(task_id)

        except Exception as e:
            print(f"Image generation failed: {str(e)}")
            return False, f"Exception: Failed to generate images: {str(e)}"


    def generate_images_concurrently(
            self,
            prompts: List[str],
            indexes: List[int],
            captions: List[str],
            output_video: bool = True
    ):
        """
        Generate multiple images and their corresponding video task id concurrently

        Args:
            prompts: List of image prompts
            indexes: List of corresponding indexes
            captions: List of corresponding captions
            output_video: Whether to output video task id
        Returns:
            Tuple containing:
                - success status (bool)
                - results (List of dicts with index, url, and caption)
                - elapsed time (float)
        Raises:
            ValueError: If inputs are invalid
            RequestError: If requests fail
        """
        # print(f"Starting concurrent generation for {len(prompts)} images")
        # start_time = time.time()
        try:
            # Submit tasks in batches
            task_map: Dict[str, Tuple[int, str]] = {}
            for i in range(0, len(prompts), self.config.batch_size):
                batch_prompts = prompts[i:i + self.config.batch_size]
                batch_indexes = indexes[i:i + self.config.batch_size]
                batch_captions = captions[i:i + self.config.batch_size]

                tasks = [self.generate_image_task(prompt) for prompt in batch_prompts]
                responses = grequests.map(tasks)

                for idx, response in enumerate(responses):
                    if response and response.status_code == 200:
                        output = response.json()
                        task_id = output.get("data", {}).get("task_id")
                        if task_id:
                            task_map[task_id] = (batch_indexes[idx], batch_captions[idx], batch_prompts[idx])
                        else:
                            print(f"Failed to get task_id for prompt at index {batch_indexes[idx]}")
                    else:
                        print(Fore.RED + f"{response}" + Fore.RESET)
                        print(f"Failed to submit task for prompt at index {batch_indexes[idx]}")

            if not task_map:
                return False, f"Failed to submit any tasks - {output}.get('task_status_msg', '')"

            # Poll for results
            completed_results: Dict[int, TaskResult] = {}
            pending_video_tasks: Dict[int, grequests.AsyncRequest] = {}
            while len(completed_results) < len(task_map):
                for task_id, (index, caption, prompt) in list(task_map.items()):
                    if index not in completed_results and index not in pending_video_tasks:
                        success, result = self.query_img_task_status(task_id)
                        if success:
                            task_status = result.get("task_status")
                            if task_status == "succeed":
                                images = result.get("task_result", {}).get("images", [])
                                if images:
                                    image_url = images[0].get("url")
                                    print(Fore.GREEN+f"{index} prompt: {prompt}"+Fore.RESET)
                                    if output_video:
                                        # 只在需要生成视频时创建视频任务
                                        pending_video_tasks[index] = {
                                            'request': self.generate_video_task(prompt, image_url),
                                            'image_url': image_url,
                                            'caption': caption,
                                        }
                                    else:
                                        completed_results[index] = TaskResult(
                                            index=index,
                                            image_url=image_url,
                                            caption=caption,
                                            task_id="",
                                            error_msg=""
                                        )
                                del task_map[task_id]
                            elif task_status == "failed":
                                completed_results[index] = TaskResult(
                                    index=index,
                                    image_url="",
                                    caption=caption,
                                    task_id="",
                                    error_msg="image gen error: " + result.get("task_status_msg", "")
                                )
                                del task_map[task_id]

                # 批量处理待处理的视频任务
                if output_video and pending_video_tasks:
                    video_requests = [data['request'] for data in pending_video_tasks.values()]
                    video_responses = grequests.map(video_requests)

                    for index, response in zip(pending_video_tasks.keys(), video_responses):
                        task_data = pending_video_tasks[index]
                        video_task_id = ""
                        error_msg = ""
                        if response.status_code == 200:
                            video_output = response.json()
                            video_task_id = video_output.get("data", {}).get("task_id")
                            if video_task_id:
                                print(f"Created video task {video_task_id} for image at index {index}")
                            else:
                                message_ = video_output.get("message", "")
                                error_msg = f"Video task create error: code - {video_output.get('error_code')}, message - {message_}"
                                print(f"Failed to get video task_id for image at index {index}")
                        else:
                            video_output = response.json()
                            work_code = video_output.get("code", "")
                            work_msg = video_output.get("message", "")
                            error_msg = f"Video task create error: status code - {response.status_code} - work code - {work_code} - {work_msg}"
                            print(f"Failed to create video task for image at index {index}")

                        completed_results[index] = TaskResult(
                            index=index,
                            image_url=task_data['image_url'],
                            caption=task_data['caption'],
                            task_id=video_task_id,  # 如果失败则为空字符串
                            error_msg=error_msg
                        )
                    # 清空已处理的视频任务
                    pending_video_tasks.clear()

                if task_map or pending_video_tasks:
                    time.sleep(self.config.polling_interval)

            # elapsed_time = time.time() - start_time
            # 按索引排序并转换为字典列表
            ordered_results = [
                completed_results[i].to_dict()
                for i in sorted(completed_results.keys())
            ]

            # print(f"Successfully generated {len(completed_results)} images in {elapsed_time:.2f} seconds")
            return True, ordered_results

        except Exception as e:
            print(f"Image generation failed: {str(e)}")
            raise RequestError(f"Failed to generate images: {str(e)}")


def main():
    try:
        # 创建配置
        config_ = KlingConfig(
            access_key=config.KLING_ACCESS_KEY,
            secret_key=config.KLING_SECRET_KEY
        )

        # 初始化客户端
        kling = KlingRequest(config_)

        # prompt = "A minimalistic and playful logo design, silhouette style. The central figure is a sleek , agile cat climbing rapidly upward a straight palm tree, with a dynamic sense of speed captured through motion blur and upright faint afterimages . The palm tree is upright and simple , with long, arching fronds, and the overall style is clean and modern . The background is plain to keep the focus on the action. The design should convey energy, agility , and excitement in a visually striking yet simple way."
        prompt = "A new year celebration scene in Singapore. There is a huge banner writing 'Happy New Year 2025' with ribbon. The city is decorated with colorful lights and festive decorations. People are gathered in the streets, celebrating and enjoying the festive atmosphere. The camera captures a wide shot of the city skyline, showcasing the vibrant lights and decorations. The scene is filled with energy and excitement, with people laughing and dancing in the streets. The camera moves to a close-up of a group of friends, smiling and toasting to the new year. The warm glow of the lights and the festive decorations create a joyful and celebratory atmosphere."
        response = kling.generate_portraits(prompt, 4, "16:9")
        print(response)

        
        # prompt = "A serene morning scene unfolds in a modern bedroom. A beautiful young Chinese girl leans against a large floor-to-ceiling window. She is wearing brown yoga outfit. Her gaze fixed outside, smiling. Golden sunlight streams softly through the glass, bathing the room in a warm glow. The gentle morning breeze stirs the semi-transparent white curtains, causing them to sway gracefully. The camera captures a medium shot, showcasing the entire setting with the window, curtains, and the girl in frame, creating a tranquil and inviting atmosphere. The camera moves to a close-up of the girl's face. the soft texture of the sunlight gently bathes her skin, illuminating her delicate features. her expression is blissful, eyes partially closed, capturing a moment of peaceful contentment. the warmth of the sunlight enhances the details, emphasizing the serene, golden hues that envelop the scene, with her peaceful smile embodying the quiet joy of the morning."
        #
        # response = kling.generate_video_task_without_image(prompt, "16:9")
        # print(response.json().get("message"))
        # if response.status_code == 200:
        #     output = response.json()
        #     task_id = output.get("data", {}).get("task_id")
        #     if task_id:
        #         print(f"Created video task {task_id}")
        #     else:
        #         print(f"Failed to get video task_id - {output.get('task_status_msg', '')}")
        # else:
        #     print(f"Failed to submit video task - {response.status_code if response else 'No response'}")
        
        # while(True):
        #     status, result = kling.query_video_task_without_image_status("")
        #     if result.get("task_status") == "failed":
        #         print(f"Failed to generate video: {result.get('task_status_msg')}")
        #         break
        #     elif result.get("task_status") == "succeed":
        #         print(f"Video task succeeded")
        #         print(f"Video URL: {result.get('task_result').get('videos')[0].get('url')}")
        #         break
        #     else:
        #         time.sleep(20)
        

        # # 测试生成
        # prompts = [
        #     "A beautiful sunset",
        #     "A futuristic cityscape",
        #     "A serene mountain lake"
        # ]
        # indexes = [1, 2, 3]
        # captions = [
        #     "Beautiful sunset view",
        #     "Future city at night",
        #     "Peaceful mountain lake"
        # ]
        #
        # success, results, elapsed_time = kling.generate_images_concurrently(prompts, indexes, captions)
        #
        # if success:
        #     print(f"Generated images: {results}")
        #     print(f"Time taken: {elapsed_time:.2f} seconds")

        # status, result = kling.query_video_task_status("CjkGpGdAL1UAAAAAALd_TA")
        # if status:
        #     print(f"Video task status: {result}")
        # else:
        #     print(f"Failed to query video task status: {result}")

    except Exception as e:
        print(f"Program failed: {str(e)}")


if __name__ == "__main__":
    main()