import requests
import json


def search_media(keyword, media_num=4):
    url = 'https://dev-watt-ai-hoc-rec.watt.chat/api/v1/search/media'
    headers = {'Content-Type': 'application/json'}
    payload = {
        'keyword': keyword,
        'sort': 'DEFAULT',
        'pageNo': 1,
        'pageSize': 10}

    response = requests.post(url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        media_list = response.json()['result']['data']['mediaList']  # 返回响应的JSON数据
        return True, media_list[: media_num]
    else:
        return False, {'error': 'Request failed', 'status_code': response.status_code}


if __name__ == "__main__":
    print(search_media('Nintendo Switch'))