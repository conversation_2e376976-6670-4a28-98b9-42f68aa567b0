from jinja2 import Environment, FileSystemLoader
from config import GEMINI_PRO_MODEL, GEMINI_FLASH_MODEL

# Set up Jinja2 environment
env = Environment(loader=FileSystemLoader('prompts'))


def get_prompt(prompt_dir, data):
    # Set up Jinja2 environment
    env = Environment(loader=FileSystemLoader('prompts'), lstrip_blocks=True, trim_blocks=True)
    prompt_template = env.get_template(prompt_dir)
    return prompt_template.render(data)


def generate_prompt(prompt_type, context):
    if prompt_type == 'system':
        template = env.get_template('system/general.j2')
        if context.get('specific_task'):
            template = env.get_template('system/specific_task.j2')
    elif prompt_type == 'user':
        template = env.get_template('user/query.j2')
        if context.get('is_follow_up'):
            template = env.get_template('user/follow_up.j2')
    else:
        raise ValueError("Invalid prompt type")

    return template.render(context=context)


class PromptsFusion:
    def __init__(self):
        self.template_fields_system = env.get_template('system/gen_fields.j2')
        self.template_fields_user = env.get_template('user/gen_fields_user.j2')
        self.template_gen_profile_system = env.get_template('system/profile/gen_profile.j2')
        self.template_gen_profile_user = env.get_template('user/profile/gen_profile_user.j2')
        self.template_gen_random_profile_system = env.get_template('system/profile/gen_profile_random.j2')
        self.template_gen_random_profile_user = env.get_template('user/profile/gen_profile_random_user.j2')
        self.template_gen_avatar_system = env.get_template('system/profile/gen_avatar.j2')
        self.template_gen_avatar_user = env.get_template('user/profile/gen_avatar.j2')
        self.template_original_system = env.get_template('system/rewrite_original.j2')
        self.template_modify_system = env.get_template('system/rewrite_modify.j2')
        self.template_modify_tag_user = env.get_template('user/rewrite_tweet_tag.j2')
        self.template_reply_system = env.get_template('system/reply/reply.j2')
        self.template_reply_user = env.get_template('user/reply/reply_user.j2')
        self.template_product_system = env.get_template('system/product/post_product.j2')
        self.template_product_user = env.get_template('user/product/post_product.j2')
        self.template_original_user = env.get_template('user/rewrite_original_tag.j2')
        self.template_batch_system = env.get_template('system/rewrite_batch.j2')
        self.template_batch_user = env.get_template('user/batch_tweet_tag.j2')
        self.template_number_system = env.get_template('system/rewrite_number.j2')
        self.template_number_user = env.get_template('user/rewrite_number_user.j2')
        self.website_extract_system = env.get_template('system/product/website_extract.j2')
        self.website_extract_user = env.get_template('user/product/website_extract.j2')
        self.product_extract_system = env.get_template('system/product/product_extract.j2')
        self.extract_sellpoints_user = env.get_template('user/product/extract_sellpoints.j2')
        self.generate_product_scenario_user = env.get_template('user/product/generate_product_scenario.j2')
        self.generate_company_info_user = env.get_template('user/product/generate_company_info.j2')
        self.generate_video_system = env.get_template('system/story/video_generate.j2')
        self.generate_video_user = env.get_template('user/story/video_generate.j2')
        self.generate_caption_system = env.get_template('system/story/caption_generate.j2')
        self.generate_caption_user = env.get_template('user/story/caption_generate.j2')
        self.generate_story_script_system = env.get_template('system/story/story_generate.j2')
        self.generate_story_s_user = env.get_template('user/story/story_generate.j2')
        self.tweet_analysis_system = env.get_template('system/analysis/tweet_analysis.j2')
        self.tweet_analysis_user = env.get_template('user/analysis/tweet_analysis_user_v2.j2')
        self.tweet_analysis_media = env.get_template('user/analysis/tweet_analysis_media_v2.j2')
        self.smart_gen_system = env.get_template('system/analysis/smart_gen.j2')
        self.smart_gen_user = env.get_template('user/analysis/smart_gen.j2')
        self.intent_classify = env.get_template('user/reply/intent_classify.j2')
        self.write_with_profile_system = env.get_template('system/profile/write_with_profile.j2')
        self.write_with_profile = env.get_template('user/profile/write_with_profile.j2')
        self.company_post_system = env.get_template('system/company/post.j2')
        self.company_post_user = env.get_template('user/company/post.j2')
        self.company_reply_system = env.get_template('system/company/reply.j2')
        self.company_reply_user = env.get_template('user/company/reply.j2')
        self.extract_url_tag_user = env.get_template('user/product/extract_url_tags.j2')
        self.query_expand = env.get_template('user/analysis/expand_query.j2')
        self.query_expand_system = env.get_template('system/analysis/expand_query.j2')
        self.query_decompose = env.get_template('user/analysis/decompose_query.j2')
        self.query_decompose_system = env.get_template('system/analysis/decompose_query.j2')
        self.shrink = env.get_template('system/shrink.j2')
        self.shrink_user = env.get_template('user/shrink.j2')
        self.mimic_tiktok = env.get_template('system/story/mimic_tiktok.j2')
        self.mimic_tiktok_user = env.get_template('user/story/mimic_tiktok.j2')
        self.transcript_summary = env.get_template('system/story/transcript_summary.j2')
        self.transcript_summary_user = env.get_template('user/story/transcript_summary.j2')
        self.xiaohongshu_plan_system = env.get_template('system/xiaohongshu/gen_plan.j2')
        self.xiaohongshu_plan_user = env.get_template('user/xiaohongshu/gen_plan.j2')
        self.xiaohongshu_note_system = env.get_template('system/xiaohongshu/gen_note.j2')
        self.xiaohongshu_note_user = env.get_template('user/xiaohongshu/gen_note.j2')
        self.company_update_intent_system = env.get_template('system/company/intent_update.j2')
        self.company_update_intent_user = env.get_template('user/company/intent_update.j2')


    def get_fields_system(self, data):
        return self.template_fields_system.render(data)

    def get_fields_user(self, data):
        return self.template_fields_user.render(data)

    def get_gen_profile_system(self, data):
        return self.template_gen_profile_system.render(data)

    def get_gen_profile_user(self, data):
        return self.template_gen_profile_user.render(data)

    def get_gen_random_profile_system(self, data):
        return self.template_gen_random_profile_system.render(data)

    def get_gen_random_profile_user(self, data):
        return self.template_gen_random_profile_user.render(data)

    def get_gen_avatar_system(self, data):
        return self.template_gen_avatar_system.render(data)

    def get_gen_avatar_user(self, data):
        return self.template_gen_avatar_user.render(data)

    def get_original_system(self, data):
        return self.template_original_system.render(data)

    def get_original_user(self, data):
        return self.template_original_user.render(data)

    def get_modify_system(self, data):
        return self.template_modify_system.render(data)

    def get_modify_tag_user(self, data):
        return self.template_modify_tag_user.render(data)

    def get_reply_system(self, data):
        return self.template_reply_system.render(data)

    def get_reply_user(self, data):
        return self.template_reply_user.render(data)

    def post_product_system(self, data):
        return self.template_product_system.render(data)

    def post_product_user(self, data):
        return self.template_product_user.render(data)

    def get_batch_system(self, data):
        return self.template_batch_system.render(data)

    def get_batch_user(self, data):
        return self.template_batch_user.render(data)

    def get_number_system(self, data):
        return self.template_number_system.render(data)

    def get_number_user(self, data):
        return self.template_number_user.render(data)

    def website_info_extract_system(self, data):
        return self.website_extract_system.render(data)

    def website_info_extract_user(self, data):
        return self.website_extract_user.render(data)

    def product_info_extract_system(self, data):
        return self.product_extract_system.render(data)

    def generate_company_info(self, data):
        return self.generate_company_info_user.render(data)

    def extract_sellpoints(self, data):
        return self.extract_sellpoints_user.render(data)

    def generate_product_scenario(self, data):
        return self.generate_product_scenario_user.render(data)

    def generate_story_system(self, data):
        return self.generate_video_system.render(data)

    def generate_story_user(self, data):
        return self.generate_video_user.render(data)

    def gen_caption_system(self, data):
        return self.generate_caption_system.render(data)

    def gen_caption_user(self, data):
        return self.generate_caption_user.render(data)

    def generate_story_script_system(self, data):
        return self.generate_story_script_system.render(data)

    def generate_story_script_user(self, data):
        return self.generate_story_s_user.render(data)

    def get_tweet_analysis_system(self, data):
        return self.tweet_analysis_system.render(data)

    def get_tweet_analysis_user(self, data):
        return self.tweet_analysis_user.render(data)

    def get_tweet_analysis_media(self, data):
        return self.tweet_analysis_media.render(data)

    def get_smart_gen_system(self, data):
        return self.smart_gen_system.render(data)

    def get_smart_gen_user(self, data):
        return self.smart_gen_user.render(data)

    def get_intent_classify(self, data):
        return self.intent_classify.render(data)

    def get_write_with_profile_system(self, data):
        return self.write_with_profile_system.render(data)

    def get_write_with_profile_user(self, data):
        return self.write_with_profile.render(data)

    def get_company_post_system(self, data):
        return self.company_post_system.render(data)

    def get_company_post_user(self, data):
        return self.company_post_user.render(data)

    def get_company_reply_system(self, data):
        return self.company_reply_system.render(data)

    def get_company_reply_user(self, data):
        return self.company_reply_user.render(data)

    def get_extract_url_tag_user(self, data):
        return self.extract_url_tag_user.render(data)

    def get_query_expand_user(self, data):
        return self.query_expand.render(data)

    def get_query_expand_system(self, data):
        return self.query_expand_system.render(data)
    
    def get_query_decompose_user(self, data):
        return self.query_decompose.render(data)

    def get_query_decompose_system(self, data):
        return self.query_decompose_system.render(data)
    
    def get_shrink(self, data):
        return self.shrink.render(data)

    def get_shrink_user(self, data):
        return self.shrink_user.render(data)
    
    def get_mimic_tiktok(self, data):
        return self.mimic_tiktok.render(data)

    def get_mimic_tiktok_user(self, data):
        return self.mimic_tiktok_user.render(data)
    
    def get_transcript_summary(self, data):
        return self.transcript_summary.render(data)

    def get_transcript_summary_user(self, data):
        return self.transcript_summary_user.render(data)

    def get_xiaohongshu_gen_plan_system(self, data):
        return self.xiaohongshu_plan_system.render(data)

    def get_xiaohongshu_gen_plan_user(self, data):
        return self.xiaohongshu_plan_user.render(data)

    def get_xiaohongshu_gen_note_system(self, data):
        return self.xiaohongshu_note_system.render(data)

    def get_xiaohongshu_gen_note_user(self, data):
        return self.xiaohongshu_note_user.render(data)
    
    def get_company_update_intent_system(self, data):
        return self.company_update_intent_system.render(data)
    
    def get_company_update_intent_user(self, data):
        return self.company_update_intent_user.render(data)


def call_llm_model(system_prompt, user_prompt, model="gemini-2.5-pro", watttraceid=None, prompt_gpt=None,
                   temperature=1.0, json_object=False, json_schema=None):
    """
    Calls the appropriate LLM model based on the model parameter
    Static function for calling various LLM models with unified interface
    
    Args:
        system_prompt (str): System prompt for the model
        user_prompt (str): User prompt for the model
        model (str): Model name to use (default: "gpt-4o")
        watttraceid (str): Trace ID for logging
        temperature (float): Temperature for generation (default: 1.0)
        json_object (bool): Whether to return structured JSON output
        json_schema (dict): JSON schema for Gemini models when json_object=True
        
    Returns:
        tuple: (success_status, response_content)
    """
    if model == "gpt-4o":
        model_ = "gpt-4o-2024-11-20"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gpt-4.1" or model == "gpt":
        model_ = "gpt-4.1-2025-04-14"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gpt-4o-mini":
        model_ = "gpt-4o-mini"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gemini-2.5-pro" or model == "gemini-pro":
        model_ = GEMINI_PRO_MODEL
        schema = json_schema if json_object and json_schema else None
        return prompt_gpt.callGCPGemini(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_schema=schema)
    elif model == "gemini-2.5-flash" or model == "gemini-flash" or model == "gemini":
        model_ = GEMINI_FLASH_MODEL
        schema = json_schema if json_object and json_schema else None
        return prompt_gpt.callGCPGemini(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_schema=schema)
    else:
        return False, f"Error: Invalid model: {model}"


if __name__ == '__main__':
    system_context = {
        'specific_task': True,
        'task_name': 'sentiment_analysis'
    }
    system_prompt = generate_prompt('system', system_context)

    user_context = {
        'query': "What's the sentiment of this tweet?",
        'tweet': "I love sunny days!"
    }
    user_prompt = generate_prompt('user', user_context)

    print(system_prompt)
    print(user_prompt)


