import json
import random
import re
from ..call_gpt import CallGpt
from ... import prompts_fusion
from service.lib.text_process import delete_random_items
from service.server import load_user

prompt_gpt = CallGpt(model='gpt-4o-2024-11-20', temperature=0.9)
user_details = load_user.XUserDetails()

image_style = ["Realistic lifestyle photo", "Cartoon-style avatar", "Professional work headshot",
               "Artistic representation"]
weights = [5, 2, 2, 1]


def GenAvatarPrompt(x_id: int, gender: str = None, watttraceid: str = None):
    persona = {}

    if x_id and x_id > 0:
        original_persona = user_details.get_user_persona(x_id)
        if original_persona:
            if "personalInfo" in original_persona and "toneOfVoice" in original_persona["personalInfo"]:
                del original_persona["personalInfo"]["toneOfVoice"]
            persona, _, _ = delete_random_items(original_persona, "personalInfo")

    chosen_style = random.choices(image_style, weights=weights, k=1)[0]
    input_data = {"persona": persona, "image_style": chosen_style}
    if x_id == -1:
        if not gender:
            gender = random.choice(["female", "male"])
        input_data["gender"] = gender

    system_prompt = prompts_fusion.get_gen_avatar_system(input_data)
    user_prompt = prompts_fusion.get_gen_avatar_user(input_data)

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, temperature=0.6)
    if status_:
        try:
            content = re.search(r"<image_description>\s*(.*?)\s*</image_description>", output_, re.DOTALL)
            if content:
                prompt_ = content.group(1).strip()
                print(f"Structured details: {prompt_}")
                return True, prompt_
            else:
                print(f"Details: {output_}")
                return True, output_
        except json.JSONDecodeError:
            return False, f"Failed to decode JSON output: {output_}"
    return False, f'调用GPT接口生成avatar失败-{output_}'


def GenAvatarPromptList(watttraceid: str = None):
    """
    Create a list of different prompts of generating an avatar randomly
    :param watttraceid: watttraceid
    :return: generated avatar
    """
    chosen_style = random.choices(image_style, weights=weights, k=1)[0]
    input_data = {"image_style": chosen_style, "gender": random.choice(["female", "male"])}

    system_prompt = prompts_fusion.get_gen_avatar_system(input_data)
    user_prompt = prompts_fusion.get_gen_avatar_user(input_data)

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, temperature=1.0,
                                                json_object=True)
    if status_:
        try:
            content = re.search(r"<image_description>\s*(.*?)\s*</image_description>", output_, re.DOTALL)
            if content:
                prompt_ = content.group(1).strip()
                print(f"Structured details: {prompt_}")
                return True, prompt_
            else:
                print(f"Details: {output_}")
                return True, output_
        except json.JSONDecodeError:
            return False, f"Failed to decode JSON output: {output_}"
    return False, f'调用GPT接口生成avatar失败-{output_}'