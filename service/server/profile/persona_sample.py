import random
from typing import Dict, Any, List
from service.lib.profile_tasks import task_list, task_details

memory_schema = {
    "staticProfile": {
        "demographics": {
            "birthday": "1993-05-15",
            "gender": "male",
            "location": {
                "country or state": "China",
                "city": "Beijing"
            },
            "education": {
                "degree": "Master",
                "field": "Computer Science",
                "institution": "Tsinghua University"
            },
            "occupation": {
                "title": "Software Engineer",
                "industry": "Internet",
                "company": "Tesla"
            },
            "language": ["Chinese", "English"]
        },
        "culturalBackground": {
            "values": ["individualism", "achievement", "innovation"],
            "beliefs": ["science", "technology", "progress"]
        },
        "professional": {
            "expertise": ["Artificial Intelligence", "Machine Learning", "Deep Learning"],
            "experience": {
                "years": 8,
                "projects": ["Project A", "Project B", "Project C"]
            }
        }
    },
    "persona": {
        "style": ["humorous", "enthusiastic"],
        "linguisticStyle": {
            "humor": {
                "type": "witty",
                "frequency": 0.3
            },
            "emoticonUsage": {
                "frequency": 0.5,
                "types": ["😊", "😂", "👍"]
            },
            "writingStyle": {
                "sentenceLength": "medium",
                "paragraphStructure": "well-structured",
                "vocabularyRichness": "high"
            }
        },
        "multiMedia": {
            "averageImageNumber": 3,
            "visualStyle": ["minimalist", "colorful", "modern"]
        },
        "publicPersona": {
            "selfDescription": "AI enthusiast, lifelong learner, tech blogger",
            "onlineIdentity": "TechExplorer",
            "interactionStyle": "engaging"
        },
        "longTermInterests": {
            "hobbies": ["reading", "hiking", "photography", "coding"],
            "topics": ["technology", "science", "history", "philosophy"],
            "skills": ["programming", "writing", "public speaking"],
            "goals": ["publish a book", "start a company"]
        }
    },
    "memory": {
        "mood": {
            "current": "negative",
            "average": "positive",
            "trend": "stable"
        },
        "Interests": {
            "keywords": [
                {
                    "name": "Generative AI",
                    "search_keyword": "OpenAI announced ChatGPT 4o",
                    "startDate": "2024-09-01",
                    "lastUpdatedDate": "2024-09-15",
                    "intensity": 0.9,
                    "sentiment": "positive",
                    "historyTweetId": ["event1_xxx02", "event2_xxx04224"]
                },
                {
                    "name": "Web3",
                    "search_keyword": "Facebook rebranded as Meta in October 2024",
                    "startDate": "2024-03-01",
                    "lastUpdatedDate": "2024-04-15",
                    "intensity": 0.6,
                    "sentiment": "neutral",
                    "historyTweetId": ["event3_xxx02", "event4_xxx04224"]
                }
            ]
        },
        "opinionsAndBeliefs": {
            "political": {
                "leaning": "liberal",
                "intensity": 0.6,
                "topics": ["climate change", "social justice"]
            },
            "social": {
                "stance": "progressive",
                "intensity": 0.7,
                "topics": ["gender equality", "LGBTQ+ rights"]
            },
            "technological": {
                "stance": "techno-optimist",
                "intensity": 0.9,
                "topics": ["AI ethics", "data privacy"]
            }
        },
        "significantLifeEvents": [
            {
                "event": "Graduated from university",
                "date": "2017-06-25",
                "description": "Completed Master's degree in Computer Science",
                "sentiment": "positive",
                "impact": "high"
            },
            {
                "event": "Started new job",
                "date": "2022-03-15",
                "description": "Joined a leading tech company as a Software Engineer",
                "sentiment": "positive",
                "impact": "medium"
            }
        ]
    }
}
choices = [0, 1, -1, 2]
weights = [0.4, 0.4, 0.15, 0.05]


def weighted_random_choice(items: List[Dict[str, Any]], weight_key: str = "intensity") -> Dict[str, Any] or None:
    """
    从带有 `weight_key` 的列表中进行加权随机抽取，返回抽中的字典 item。
    :param items: 列表，每个元素为包含权重的字典。
    :param weight_key: 权重对应的 key，默认是 "intensity".
    :return: 被抽中的字典 item
    """
    # 如果 items 为空，直接返回 None
    if not items:
        return None

    weights = [item[weight_key] for item in items]
    total = sum(weights)
    # 计算每一个 item 对应在 [0, total] 区间的概率分布
    rnd = random.uniform(0, total)

    cumsum = 0.0
    for item, w in zip(items, weights):
        cumsum += w
        if rnd <= cumsum:
            return item

    return items[-1]  # 兜底返回最后一个


def sample_persona(persona: Dict[str, Any], sample_count: int = 1) -> Dict[str, Any]:
    """
    Randomly sample a specified number of notable features from the persona.
    :param persona: Persona information dictionary
    :param sample_count: Number of feature categories to sample (clamped between 1 and max available)
    :return: Dictionary of sampled features
    """
    # Extract potential features
    humor_info = persona.get("linguisticStyle", {}).get("humor", {})
    writing_style = persona.get("linguisticStyle", {}).get("writingStyle", {})
    multi_media_number = persona.get("multiMedia", {}).get("averageImageNumber", 0)
    emoticon_info = persona.get("linguisticStyle", {}).get("emoticonUsage", {})
    public_persona = persona.get("publicPersona", {})
    long_term_interests = persona.get("longTermInterests", {})

    # Handle long-term interests sampling
    hobbies = long_term_interests.get("hobbies", [])
    topics = long_term_interests.get("topics", [])
    skills = long_term_interests.get("skills", [])
    goals = long_term_interests.get("goals", [])

    hobbies_choice = random.choice(hobbies) if hobbies else ""
    topics_choice = random.choice(topics) if topics else ""
    skills_choice = random.choice(skills) if skills else ""
    goals_choice = random.choice(goals) if goals else ""

    combined_interests = [item for item in [hobbies_choice, topics_choice, skills_choice, goals_choice] if item]
    chosen_long_term_interests = (
        random.sample(combined_interests, min(random.randint(1, 2), len(combined_interests)))
        if combined_interests else []
    )

    # Define available features with their values
    available_features = {
        "writing_style": writing_style,
        "multi_media_number": multi_media_number,
        "public_persona": public_persona,
        "long_term_interests": chosen_long_term_interests,
        "emoticon": emoticon_info,
        "humor_type": humor_info
    }

    # Clamp sample_count to valid range: 1 to number of available features
    sample_count = max(1, min(sample_count, len(available_features)))

    # Randomly select the specified number of feature keys
    sampled_keys = random.sample(list(available_features.keys()), sample_count)

    # Build output dictionary with only the sampled features
    out_json = {key: available_features[key] for key in sampled_keys}

    # For multi_media_number, no adjustment unless > 0 (fixing the broken random.choices)
    if "multi_media_number" in out_json and multi_media_number > 0:
        # Simple adjustment example: increase by a random small number, capped at 4
        out_json["multi_media_number"] = min(4, multi_media_number + random.randint(0, 2))

    return out_json


def sample_memory(memory: Dict[str, Any], interest_count: int = 1) -> Dict[str, Any]:
    """
    根据 memory 中的 Interests, opinionsAndBeliefs, significantLifeEvents 等信息进行一定量的随机采样。
    :param memory: 记忆信息
    :param interest_count: 需要采样的兴趣数量
    :return: 包含采样结果的字典
    """
    # 1) mood 直接用当前值，也可以根据自己的需求做不同抽取
    # 1) Mood handling
    mood = "happy"  # 默认值
    if isinstance(memory, dict):
        mood = memory.get("mood", {}).get("current", "happy")

    # 2) Interests handling
    interests = []
    if isinstance(memory, dict):
        interests = memory.get("Interests", {}).get("keywords", [])

    chosen_interests = []
    if interests and isinstance(interests, list):
        for _ in range(min(interest_count, len(interests))):  # 避免超出列表范围
            try:
                item = weighted_random_choice(interests, weight_key="intensity")
                if item and isinstance(item, dict):
                    keyword_ = {
                        "name": item.get("search_keyword", ""),
                        "sentiment": item.get("sentiment", "neutral")  # 添加默认值
                    }
                    if keyword_["name"]:  # 只添加有效的关键词
                        chosen_interests.append(keyword_)
            except Exception as e:
                print(f"Error in processing interests: {e}")
                continue

    # 3) Opinions handling
    domain = None
    chosen_opinion = {}
    if isinstance(memory, dict):
        opinions = memory.get("opinionsAndBeliefs", {})
        if opinions and isinstance(opinions, dict):
            try:
                opinion_keys = list(opinions.keys())
                if opinion_keys:
                    domain = random.choice(opinion_keys)
                    chosen_opinion = opinions.get(domain, {})
            except Exception as e:
                print(f"Error in processing opinions: {e}")

    # 4) Life events handling
    chosen_life_event = None
    if isinstance(memory, dict):
        events = memory.get("significantLifeEvents", [])
        if events and isinstance(events, list):
            try:
                chosen_life_event = random.choice(events)
            except Exception as e:
                print(f"Error in processing life events: {e}")
    return {
        "mood": mood,
        "chosen_interests": chosen_interests,
        "chosen_opinion": {
            "domain": domain,
            "detail": chosen_opinion
        },
        "significant_life_event": chosen_life_event
    }


def sample_task(task_list_: List[str], task_details_: Dict[str, Dict[str, List[str]]]) -> Dict[str, Any]:
    """
    从 task_list 中随机选一个任务（大类），再从 task_details 的该大类下选一个子类型和描述。
    :param task_list_: 任务大类列表
    :param task_details_: 任务详情
    :return: 返回 {'task_category': ..., 'sub_category': ..., 'description': ...}
    """
    if not task_list_:
        return {}

    # 随机选择一个大类
    chosen_category = random.choice(task_list_)

    # 进入 task_details 查找该大类下的子类别
    sub_tasks = task_details_.get(chosen_category, {})
    if not sub_tasks:
        return {
            "task_category": chosen_category,
            "sub_category": None,
            "description": None
        }

    # 随机选一个子类别
    sub_category = random.choice(list(sub_tasks.keys()))
    description = random.choice(sub_tasks[sub_category])

    return {
        "task_category": chosen_category,
        "sub_category": sub_category,
        "description": description
    }


def generate_incontext_example(memory_schema_: Dict[str, Any],
                               interest_count: int = 1) -> Dict[str, Any]:
    """
    整合采样逻辑：先从 memory 中采样若干兴趣和关键信息，再从 persona 中采样特征，
    最后从 task_list 和 task_details 中采样一个场景主题，组合得到一次发帖所需的上下文提示。
    """
    basic_info = memory_schema_.get("staticProfile", {})
    if basic_info:
        if basic_info.get("professional").get("userKeywords"):
            del basic_info["professional"]["userKeywords"]
        if basic_info.get("professional").get("experience"):
            del basic_info["professional"]["experience"]
    persona_data = memory_schema_.get("persona", {})
    memory_data = memory_schema_.get("memory", {})

    # 1) 采样 persona
    persona_sample = sample_persona(persona_data, sample_count=interest_count)
    
    # 2) 采样 memory
    memory_sample = sample_memory(memory_data, interest_count=interest_count)

    # 3) 采样 task
    task_sample = sample_task(task_list, task_details)

    # 4) 合并结果
    incontext_example = {
        "basic_info": basic_info,
        "persona": persona_sample,
        "memory": memory_sample,
        "task": task_sample
    }

    return incontext_example


if __name__ == "__main__":
    # 生成一次 In-context Example
    example = generate_incontext_example(memory_schema, interest_count=2)

    print("===== Sampled In-Context Data =====")
    print("Persona Sample:")
    print(example["persona_sample"])
    print("Memory Sample:")
    print(example["memory_sample"])
    print("Task Sample:")
    print(example["task_sample"])
