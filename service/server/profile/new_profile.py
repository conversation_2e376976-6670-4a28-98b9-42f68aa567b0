import json

from ..call_gpt import CallGpt
from ... import prompts_fusion
from service.dao.mongo.tweet_mongo import TweetMongoDB

prompt_gpt = CallGpt(model='gpt-4o-2024-11-20', temperature=0.9)
schema_db = TweetMongoDB(collection="templates")
character_schema = schema_db.singleSearch("name", "character_schema_empty")
type_mapping = {
    1: "post",
    2: "reply",
    3: "quote",
    4: "retweet"
}


def extract_tweets(file_path: str, tweet_num: int = 0):
    """
    Extract tweets from a JSON file
    :param file_path: path to the JSON file
    :param tweet_num: number of tweets to extract
    :return: list of tweets
    """

    with open(file_path, "r", encoding='utf-8') as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError:
            print("Error: Invalid JSON file")
            return []
        if tweet_num == 0:
            return data
        return data[:tweet_num]


def NewProfile(tweet_list: list[dict], watttraceid: str = None):
    """
    Create a new profile for a user based on their tweets history
    :param tweet_list: tweet object list with content and comment list
    :param watttraceid: watttraceid
    :return: generated new profile list
    """
    if character_schema:
        personal_info = character_schema.get("personalInfo", {})
        extra_info = character_schema.get("extraInfo", {})
        # biography = character_schema.get("biography", "")
        person_dict = {"personalInfo": personal_info, "extraInfo": extra_info}
    else:
        return False, 500, "Failed to load character schema"

    input_data = {"character_schema": person_dict}

    tweets = [{"tweetText": tweet["tweetText"], "tweetType": type_mapping[tweet["tweetType"]]} for tweet in tweet_list]
    input_data["tweet_history"] = tweets
    system_prompt = prompts_fusion.get_gen_profile_system(input_data)
    user_prompt = prompts_fusion.get_gen_profile_user(input_data)

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, temperature=1.0,
                                            json_object=True)
    if status_:
        try:
            output_json = json.loads(output_)
            return True, 200, output_json
        except json.JSONDecodeError:
            return False, 500, f"Failed to decode JSON output: {output_}"
    return False, 500, f'调用GPT接口生成推文失败-{output_}'
