import json
import random
from service.server.call_gpt import CallGpt
from service import prompts_fusion


prompt_gpt = CallGpt(model="gpt-4o-2024-11-20", temperature=1.0)

art_style_list = ["宫崎骏 Chihiro", "宫崎骏 Totoro", "宫崎骏 How<PERSON> & Sophie", "<PERSON><PERSON><PERSON>", "One Piece Luffy",
                  "Disney Elsa & Anna", "Pixar Woody & Buzz Lightyear", "<PERSON><PERSON> & <PERSON>", "Minions",
                  "Nemo & Dory", "SpongeBob", "Simpsons", "South Park", "Rick & Morty", "BoJack Horseman",
                  "Adventure Time", "Steven Universe", "Wall-E", "飞屋环游记", "疯狂动物城", "冰雪奇缘", "狮子王",
                  "Pokemon Ash & Pikachu", "马达加斯加Alex & Penguins", "动物森友会Tom Nook & Villagers", "Minecraft Steve & Creeper",
                  "Sonic the Hedgehog", "Link & Zelda", "Lego Figures","Shrek & Donkey", "Ice Age Sid, Manny or Scrat",
                  "Inside Out Emotions", "Dragon Ball Goku & Vegeta","Demon Slayer Tanjiro & Nezuko","Powerpuff Girls",
                  "Shaun the Sheep", "Family Guy" ]


def storyTellwithProduct(sellpoints: dict, num: int = 5, watttraceid: str = "", langugae: str = "english"):
    character_list = random.sample(art_style_list, 3)
    input_data = {"product_sellpoints": sellpoints, "character_list": character_list, "num": num, "language": langugae}
    system_prompt = prompts_fusion.generate_story_system(input_data)
    user_prompt = prompts_fusion.generate_story_user(input_data)
    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                json_object=True)
    if status_:
        try:
            details = json.loads(output_)
            return True, details
        except json.JSONDecodeError as e:
            return False, f"Error loading json: {e}"
    return False, output_


def captionGenwithProduct(sellpoints, num: int = 5, watttraceid: str = ""):
    input_data = {"product_sellpoints": sellpoints, "num": num}
    system_prompt = prompts_fusion.gen_caption_system(input_data)
    user_prompt = prompts_fusion.gen_caption_user(input_data)
    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                json_object=True)
    if status_:
        try:
            details = json.loads(output_)
            return True, details
        except json.JSONDecodeError as e:
            return False, f"Error loading json: {e}"
    return False, output_


def storyScriptGen(basic_idea: str, num: int = 8, watttraceid: str = ""):
    input_data = {"num": num}
    if basic_idea:
        input_data["basic_idea"] = basic_idea
    system_prompt = prompts_fusion.generate_story_script_system(input_data)
    user_prompt = prompts_fusion.generate_story_script_user(input_data)
    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                json_object=True)
    if status_:
        try:
            details = json.loads(output_)
            return True, details
        except json.JSONDecodeError as e:
            return False, f"Error loading json: {e}"
    return False, output_
