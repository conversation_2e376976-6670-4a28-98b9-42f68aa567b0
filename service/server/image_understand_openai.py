import json
from .call_gpt import CallGpt


prompt_gpt = CallGpt(temperature=0.6)

system_prompt_ = """You are an advanced AI assistant specialized in analyzing images and extracting meaningful information. Your task is to examine images carefully, understand their overall scenario and meaning, and understand their overall scenario and meaning, especially the high-level idea."""
user_prompt_ = """
    Analyze the provided image and complete the following tasks:
- Overall Scenario: Describe the main scene or situation depicted in the image in 1-2 sentences. 
- Context: Interpret the potential meaning or context of the image. What message or story might it be conveying? 
- Keywords: List 4-6 key words that best represent the main elements or themes in the image.
- Tags: Provide 3-5 descriptive tags that could be used to categorize this image for social media posting. These should
 be broader than the keywords and could include themes, emotions, or artistic styles. 
- Notable: Mention any particularly striking or unusual elements or identifying some celebrity or influencer in the 
image that contribute to its overall impact.

The output should be in JSON format and include the following fields:
{
    "overall": "The image depicts...",
    "context": "This image might be conveying...",
    "keywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5", "keyword6"],
    "tags": ["tag1", "tag2", "tag3", "tag4", "tag5"],
    "notable": "The most striking element in this image is..."
}
"""

user_prompt_final = [
    {
        "type": "text",
        "text": user_prompt_
    },
    {
        "type": "image_url",
        "image_url": {
            "url": "place_holder_img_url"
        }
    }
]


def image_extract_keywords(image_url: str, watttraceid: str = ''):
    """
    Extract keywords from image
    :param image_url: image url
    :param watttraceid: watt trace id
    :return: status, keywords
    """
    user_prompt_final[1]['image_url']['url'] = image_url
    status_, response = prompt_gpt.callOpenaiGpt(system_prompt_, user_prompt_final, watttraceid=watttraceid,
                                                 vision=True, json_object=True)
    if not status_:
        return status_, response
    try:
        output_string = (response['result']['data']['choices'][0]['message']['content'])
        output_json = json.loads(output_string)
    except json.JSONDecodeError:
        return False, "JSON Decode Error of output from GPT"
    return True, output_json


def image_list_extract_keywords(image_list: list, watttraceid: str = ''):
    """
    Extract keywords from image list
    :param image_list:  image url list
    :param watttraceid: watt trace id
    :return: status, keywords
    """
    sys_list = []
    user_list = []
    for img_url in image_list:
        if isinstance(img_url, str):
            sys_list.append(system_prompt_)
            temp = user_prompt_final.copy()
            temp[1]['image_url']['url'] = img_url
            user_list.append(temp)

    status_, responses = prompt_gpt.gcallOpenaiGpt(sys_list, user_list, watttraceid=watttraceid, vision=True,
                                                   json_object=True)
    if not status_:
        return status_, responses
    output_jsons = []
    for response_ in responses:
        try:
            json_data = json.loads(response_)
            output_jsons.append(json_data)
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON: {e}")
    return True, output_jsons
