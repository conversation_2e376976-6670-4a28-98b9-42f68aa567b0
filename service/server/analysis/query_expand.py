from service.server.call_gpt import CallGpt
from service import prompts_fusion


prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=0.9)


def query_expand(query_input: str, watttraceid: str):
    input_data = {
        "input": query_input
    }
    user_prompt = prompts_fusion.get_query_expand_user(input_data)
    system_prompt = prompts_fusion.get_query_expand_system(input_data)
    status_, reply_msg = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, json_object=False)
    if not status_:
        return False, reply_msg
    print(reply_msg)
    return True, reply_msg
   
