from service.server.analysis.news_and_event import get_prompt, topic_list, chat_gpt, adjust_overall_score
from service.server.mm_understand.extract_person import remove_urls


def analyze_at_or_reply(profile, plan_info, mention_post_list, source):
    """
    :param profile: dict, 用户profile信息,可能为空
    :param plan_info: dict, 用户设定的plan计划信息，可能为空
    :param mention_post_list: list, 用户被@或者回复的post列表，不能为空
    :param source: number, 3 来自at 4 来自reply
    """
    company_info = profile.get('companyInfo', {})
    profile_info = profile.get('profileInfo', {})
    if not (profile_info or plan_info):
        return False, 'profile and plan should not be empty in the same time'
    plan_topic_index = plan_info.get('planTopic', 0)
    if not plan_topic_index:
        plan_topic = {}
    else:
        plan_topic = topic_list[int(plan_topic_index) - 1]
    plan_description = plan_info.get('planDescription', '')
    comment_string = ''
    reply_index = []
    for index, body in enumerate(mention_post_list):
        post_content = remove_urls(body.get('postContent', ''))
        if len(post_content) > 5:
            reply_index.append(index + 1)
            if source == 3:
                comment_string += f"    {index + 1}.  {post_content}\n"
            if source == 4:
                comment_string += f"    {index + 1}.  Reply: {post_content}\n"
                if len(body.get('originalPostContent', '')) > 5:
                    comment_string += f"         with its original post: {body['originalPostContent']}\n"
    if len(reply_index) < 1:
        return True, {'todoReplyTaskList': []}
    data = {
        'name': company_info.get('company', ''),
        'introduction': profile_info.get('introduction', ''),
        'keywords': profile_info.get('subscribeKeywords', []),
        'businessArea': profile_info.get('businessArea', ''),
        'productFeatures': profile_info.get('productFeatures', []),
        'brandValues': profile_info.get('brandValues', ''),
        'targetAudience': profile_info.get('targetAudience', []),
        'marketPosition': profile_info.get('marketPosition', ''),
        'competitor': profile_info.get('competitor', []),
        'recentNews': profile_info.get('recentNews', []),
        'marketCases': profile_info.get('marketCases', []),
        'plan_topic': plan_topic.get('topic', ''),
        'topic_definition': plan_topic.get('definition', ''),
        'plan_description': plan_description,
        'source': source,
        'comment_list': comment_string
    }
    sys_prompt = """You are a senior marketing expert who is good at dealing with customers relationships. You have been asked to analyze the comments to the user's marketing post."""
    user_prompt = get_prompt('user/analysis/at_and_reply.j2', data)
    json_schema = get_json_schema(reply_index)
    status, result = chat_gpt(sys_prompt, user_prompt, json_schema=json_schema)
    if not status:
        return False, result
    output_list = result.get('schedule_and_suggestion_list', [])
    return_dict = {'todoReplyTaskList': []}
    if not output_list:
        return True, return_dict
    if not profile_info:
        # 如果用户没有profile信息，没有suggestion, 只处理schedule
        for i in output_list:
            if i.get('function') == 1:
                i['mentionPost'] = mention_post_list[i['reply_index']-1]
                return_dict['todoReplyTaskList'].append(i)
    elif not plan_topic_index:
        # 如果用户没有plan信息，没有schedule, 只处理suggestion
        for i in output_list:
            if i.get('function') == 2:
                i['mentionPost'] = mention_post_list[i['reply_index'] - 1]
                return_dict['todoReplyTaskList'].append(i)
    else:
        for i in output_list:
            i['mentionPost'] = mention_post_list[i['reply_index'] - 1]
            return_dict['todoReplyTaskList'].append(i)
    for i in return_dict['todoReplyTaskList']:
        i['reason'] = adjust_overall_score(i['reason'])
    return True, return_dict


def get_json_schema(reply_index):
    json_schema = {
            "name": "reply_analysis_for_company_and_marketing",
            "description": "analyze the reply for the company and marketing",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "schedule_and_suggestion_list": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "reply_index": {
                                    "type": "number",
                                    "description": "The index of the selected reply",
                                    "enum": reply_index
                                },
                                "replyContent": {
                                    "type": "string",
                                    "description": "The content of the selected reply"
                                },
                                "reason": {
                                        "type": "object",
                                        "description": "The reason why the reply is selected",
                                        "properties": {
                                            "overallScore": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the overall score"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the overall score"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            },
                                            "brandFit": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the brand fit"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the brand fit"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            },
                                            "industryRelevance": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the industry relevance"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the industry relevance"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            },
                                            "audienceMatch": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the audience match"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the audience match"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            },
                                            "contentTimeliness": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the content timeliness"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the content timeliness"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            },
                                            "riskAssessment": {
                                                "type": "object",
                                                "properties": {
                                                    "score": {
                                                        "type": "number",
                                                        "description": "The score of the risk assessment"
                                                    },
                                                    "description": {
                                                        "type": "string",
                                                        "description": "The description of the risk assessment"
                                                    }
                                                },
                                                "required": ["score", "description"],
                                                "additionalProperties": False
                                            }
                                        },
                                        "required": ["overallScore", "brandFit", "industryRelevance",
                                                     "audienceMatch", "contentTimeliness", "riskAssessment"
                                                     ],
                                        "additionalProperties": False
                                    },
                                "function": {
                                    "type": "number",
                                    "description": "The type of which kind the reply is selected into, 1 for 'Schedule', 2 for 'suggestion' ",
                                    "enum": [1, 2]
                                },
                                "point": {
                                    "type": "number",
                                    "description": "The score of the selected reply"
                                },
                            },
                            "required": ["reply_index", "replyContent", "reason", "function", "point"],
                            "additionalProperties": False
                        }
                    }
                },
                "required": ["schedule_and_suggestion_list"],
                "additionalProperties": False
            }
        }
    return json_schema


if __name__ == "__main__":
    # from jinja2 import Environment, FileSystemLoader
    # def get_prompt(prompt_dir, data):
    #     # Set up Jinja2 environment
    #     env = Environment(loader=FileSystemLoader('/Users/<USER>/Documents/watt-ai-hoc/prompts/user/analysis'),
    #                       lstrip_blocks=True,
    #                       trim_blocks=True, )
    #     prompt_template = env.get_template(prompt_dir)
    #     return prompt_template.render(data)
    data_ = {'name': 'tanka',
        'introduction': 'I am a software engineer',
        'keywords': ['keywords1', 'keywords2', 'keywords3'],
        'businessArea': 'ai',
        'productFeatures': 'nb',
        'brandValues': 'seek for new im',
        'targetAudience':['target1', 'target2', 'target3'],
        'competitor': ['comp1', 'comp2', 'comp3'],
        'recentNews': ['news1', 'news2', 'news3'],
        'marketCases': ['case1', 'case2', 'case3'],
        'plan_topic': "Culture publicity",
        'topic_definition': "Promoting a company or brand's core values, mission, and cultural identity to build a stronger brand image and emotional connection with the target audience.",
        'plan_description': 'want to plan xxxxxxxxxxxxx',
        'source': 2,
        'information': """1.xxxxxxxx,\n2.aaaaaaa\n3.cccccccc""",
        'comment_list': '1. comment1\n2. comment2\n3. comment3\n'
    }
    user_prompt = get_prompt('at_and_reply.j2', data_)
    print(user_prompt)

