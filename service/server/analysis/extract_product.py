import json
import re
from service.server.call_gpt import CallGpt
from service import prompts_fusion
from service import FlaskLog
from flask import current_app
from colorama import Fore
import tiktoken

MAX_TOKENS = 40960
social_media_list = ['tiktok_video', 'tiktok_user', 'youtube_video', 'youtube_user', 'youtube_shorts',
                     'ins_reels', 'ins_user', 'ins_p']
goods_list = ['goods_aliexpress', 'goods_ebay', 'goods_shein', 'goods_tiktok', 'goods_shopapp', 'goods_walmart',
                  'goods_shopify']
store_list = ['store_aliexpress', 'store_ebay', 'store_shein', 'store_shopapp', 'store_walmart', 'store_shopify']
group_list = ["facebook_groups"]
empty_group_phrase = "description: privacy:privacy group"
language_mapping = {
    3: "english",
    1: "简体中文",
    2: "繁體中文",
    4: "japanese",
    5: "korean",
    6: "french",
    7: "german",
}

# 英文到中文的行业映射
industry_mapping_cn = {
    "Retail": "零售业",
    "Healthcare": "医疗保健",
    "E-commerce": "电子商务",
    "Manufacturing": "制造业",
    "Technology": "科技",
    "Software": "软件",
    "IT services": "IT服务",
    "Hardware": "硬件",
    "Education": "教育",
    "Finance": "金融",
    "Real Estate": "房地产",
    "Hospitality": "酒店服务业",
    "Transportation": "交通运输",
    "Media": "媒体",
    "Construction": "建筑业",
    "Food & Beverage": "食品饮料",
    "Entertainment": "娱乐",
    "Consulting": "咨询",
    "Marketing Agency": "营销代理",
    "Energy": "能源",
    "Agriculture": "农业",
    "Legal": "法律",
    "Non-profit": "非营利组织",
    "Telecommunications": "电信",
    "Fashion": "时尚",
    "Automotive": "汽车",
    "Pharmaceutical": "制药",
    "Sports": "体育",
    "Insurance": "保险",
    "Beauty": "美容",
    "Other": "其他",
}

def translate_industry_to_chinese(details, language):
    """
    如果language=1（简体中文），将companyIndustry字段从英文转换为中文
    """
    if language == 1 and isinstance(details, dict) and "companyIndustry" in details:
        english_industry = details["companyIndustry"]
        if english_industry in industry_mapping_cn:
            details["companyIndustry"] = industry_mapping_cn[english_industry]
    return details

def extract_content(details):
    # 定义两种可能的起始模式
    pattern1 = r"隐私帮助中心博客商家专区应用登录下载"
    pattern2 = r"隐私帮助中心博客商家专区应用"

    # 结束模式
    end_pattern = r"WhatsApp 群组邀请加入对话还没有"

    # 构建完整的正则表达式，使用非捕获组和或操作符
    pattern = f"(?:{re.escape(pattern1)}|{re.escape(pattern2)})(.*?){re.escape(end_pattern)}"

    # 尝试匹配
    match = re.search(pattern, details, re.DOTALL)
    return match.group(1) if match else None


class GenerateDetails:
    def __init__(self, gpt_model: str = "gpt-4.1", watttraceid: str = ""):
        self.prompt_gpt = CallGpt(model=gpt_model, temperature=0.9)
        self.watttraceid = watttraceid
        self.encoder = tiktoken.encoding_for_model("gpt-4o")
        self.empty = False

    def pipeline_with_full_context(self, context: dict, language: int = 1):
        """
        New pipeline method specifically for company with full context
        Context structure: {
            "websiteContext": {...},  # may be empty
            "fileContext": {...},     # may be empty  
            "accountContext": {...}   # may be empty
        }
        """
        try:
            website_context = context.get("websiteContext", {})
            file_context = context.get("fileContext", {})
            account_context = context.get("accountContext", {})
            
            # Process and structure the three information sources separately
            
            # 1. Process websiteContext (公司官网信息)
            website_info = None
            if website_context:
                website_info = {}
                if website_context.get("title"):
                    website_info["title"] = website_context["title"]
                if website_context.get("content"):
                    website_info["content"] = website_context["content"]
                if website_context.get("url"):
                    website_info["url"] = website_context["url"]
                if website_context.get("description"):
                    website_info["description"] = website_context["description"]
            
            # 2. Process fileContext (公司宣传手册信息)
            brochure_info = None
            if file_context and file_context.get("content"):
                brochure_info = file_context["content"]
                # Limit brochure content to prevent token overflow
                brochure_tokens = len(self.encoder.encode(brochure_info))
                if brochure_tokens > MAX_TOKENS // 3:  # Reserve 1/3 of tokens for brochure
                    encoded_tokens = self.encoder.encode(brochure_info)[:MAX_TOKENS // 3]
                    brochure_info = self.encoder.decode(encoded_tokens)
            
            # 3. Process accountContext (小红书账号信息)
            account_info = None
            if account_context:
                account_info = {}
                if account_context.get("nickname"):
                    account_info["nickname"] = account_context["nickname"]
                if account_context.get("profileDesc"):
                    account_info["profileDesc"] = account_context["profileDesc"]
                if account_context.get("profilePageLink"):
                    account_info["profilePageLink"] = account_context["profilePageLink"]
                if account_context.get("ipLocation"):
                    account_info["ipLocation"] = account_context["ipLocation"]
                if account_context.get("followsCnt"):
                    account_info["followsCnt"] = account_context["followsCnt"]
                if account_context.get("fansCnt"):
                    account_info["fansCnt"] = account_context["fansCnt"]
                if account_context.get("likedCnt"):
                    account_info["likedCnt"] = account_context["likedCnt"]
                if account_context.get("collectedCnt"):
                    account_info["collectedCnt"] = account_context["collectedCnt"]
                if account_context.get("notesCnt"):
                    account_info["notesCnt"] = account_context["notesCnt"]
                if account_context.get("isVerified"):
                    account_info["isVerified"] = account_context["isVerified"]
            
            # Check if we have any content at all
            if not website_info and not brochure_info and not account_info:
                self.empty = True
                details = {
                    "companyName": "",
                    "companyIndustry": "",
                    "companyBrandPositioning": "",
                    "companyCoreCompetency": "",
                    "companyTargetAudience": "",
                    "companyOtherInfo": {
                        "subscribeKeywords": [],
                        "description": "",
                        "features": [],
                        "scenarios": ""
                    }
                }
                return True, details
            
            # Prepare input data for prompt with structured information
            input_data = {
                "language": language_mapping[language]
            }
            
            # Add structured information to input_data
            if website_info:
                input_data["website_info"] = website_info
            if brochure_info:
                input_data["brochure_info"] = brochure_info
            if account_info:
                input_data["account_info"] = account_info
            
            # Generate prompts
            system_prompt = prompts_fusion.website_info_extract_system(input_data)
            user_prompt = prompts_fusion.generate_company_info(input_data)
            
            # Call GPT
            status_, output_ = self.prompt_gpt.callOpenaiGpt(
                system_prompt, 
                user_prompt, 
                watttraceid=self.watttraceid,
                json_object=True
            )
            
            if status_:
                try:
                    details = json.loads(output_)
                    # Add URL to companyOtherInfo if available from website_info
                    if website_info and website_info.get("url"):
                        if "companyOtherInfo" not in details:
                            details["companyOtherInfo"] = {}
                        details["companyOtherInfo"]["url"] = website_info["url"]
                    # 如果是中文，转换行业字段
                    details = translate_industry_to_chinese(details, language)
                    # 如果是中文，转换行业字段
                    details = translate_industry_to_chinese(details, language)
                    return True, details
                except json.JSONDecodeError as e:
                    return False, f"Error loading json: {e}"
            
            return False, output_
            
        except Exception as e:
            error = f"pipeline_with_structured_context Error: failed to process structured context - {str(e)}"
            FlaskLog.error(current_app.fmtSaveOutput(self.watttraceid, error))
            return False, error

    def pipeline(self, context: dict, scenario: int=0, language: int=1):
        title = context.get("title", "")
        content = context.get("content", "")
        other = context.get("other", [])
        if empty_group_phrase in content.lower() and other[0] == "cleaned_text":
            self.empty = True
        elif "WhatsApp 群组邀请" in content or title == "WhatsApp 群组邀请":
            match = extract_content(other[0])
            if match:
                group_name = match
                # print(Fore.CYAN + f"WhatsApp group: {group_name}" + Fore.RESET)
            else:
                group_name = "WhatsApp group name not found"
                print(Fore.RED + f"未找到匹配的group name" + Fore.RESET)
            return True, {"name": group_name, "type": "group", "description": "", "keywords": [],
                          "OtherInfo": {"community": "", "targetAudience": "", "topics": "", "platform": "whatsapp"}}
        child = context.get("child", [])
        product = context.get("product")
        description = context.get("description", "")
        url_type = context.get("url_type", "")
        type_ = ""
        if url_type == "spotify_artist":
            url_type = "The website is the artist page of Spotify"
            type_ = "SaaS"
        elif url_type == "spotify_song":
            url_type = "The website is the song page of Spotify with song lyrics in the content"
            type_ = "SaaS"
        elif url_type in social_media_list:
            url_type = f"This is a {url_type} page as a website. The type is 'SaaS'."
            type_ = "SaaS"
        elif url_type in goods_list or url_type in store_list:
            url_type = f"This is a {url_type} page as a website. The type is 'ecommerce'."
            type_ = "ecommerce"
        elif url_type in group_list:
            url_type = f"This is a {url_type} page as a group. The type is 'group'."
            type_ = "group"
        description = url_type + " - " + description if url_type and description else description

        web_url = context.get("web_url", "")
        if re.match(r'^(?:https?:\/\/)?(?:www\.)?t\.me\/.*', web_url):
            print(Fore.CYAN + f"Telegram group channel: {web_url}" + Fore.RESET)
            description += "This is a Telegram group channel, which is a private domain group. The type is 'group'."

        website_context = ""
        try:
            if product:
                product_title = product.get("title", "")
                product_contexts = product.get("contexts")

            if description and not content:
                content = description

            if not content and not child and not product and not description:
                if other:
                    website_context = f"Title: {title}\nURL: {web_url}\nOther Information: {other} - Type should be website"
                else:
                    self.empty = True
                    details = {}
                    if scenario == 3:
                        details["companyName"] = ""
                        details["companyIndustry"] = ""
                        details["companyBrandPositioning"] = ""
                        details["companyCoreCompetency"] = ""
                        details["companyTargetAudience"] = ""
                    else:
                        details["description"] = ""
                        details["keywords"] = []
                        details["OtherInfo"] = {"community": "", "targetAudience": "", "topics": "", "platform": ""}
                    return True, details
            else:
                if other and len(other) > 0:
                    if product:
                        website_context = f"{title}\nURL: {web_url} - Content: {content}\n\nProduct Information: {product_title}-{product_contexts}\n\nOther Information: "
                    else:
                        website_context = f"{title}\nURL: {web_url} - Content: {content}\n\nOther Information: "
                    start_tokens = len(self.encoder.encode(website_context))
                    start_idx = 0
                    while start_tokens < MAX_TOKENS and start_idx < len(other):
                        website_context += other[start_idx] + " "
                        start_tokens = len(self.encoder.encode(website_context))
                        start_idx += 1
                else:
                    if product:
                        website_context = f"{title}\nContent: {content}\n\nProduct Information: {product_title}-{product_contexts}"
                    else:
                        website_context = f"{title}\nContent: {content}"

                if child and len(child) > 0 and start_tokens < MAX_TOKENS:
                    website_context += "\n\n Child Page Information: "
                    start_tokens = len(self.encoder.encode(website_context))
                    child_start_idx = 0
                    while start_tokens < MAX_TOKENS and child_start_idx < len(child):
                        child_page_info = f'Child Page {child_start_idx}:\ntitle: {child[child_start_idx]["title"]}\ncontent: {child[child_start_idx]["content"]}'
                        website_context += child_page_info + "\n"
                        start_tokens = len(self.encoder.encode(website_context))
                        child_start_idx += 1

        except Exception as e:
            error = f"generateDetails.pipeline Error: failed to preprocess context - {str(e)}"
            FlaskLog.error(current_app.fmtSaveOutput(self.watttraceid, error))
            return False, error
        input_data = {"context": website_context, "language": language_mapping[language]}
        if type_:
            input_data["type"] = type_
        if description and content:
            # print(Fore.CYAN + f"Description: {description}" + Fore.RESET)
            input_data["description"] = description
        system_prompt = prompts_fusion.website_info_extract_system(input_data)
        if scenario == 0:
            user_prompt = prompts_fusion.website_info_extract_user(input_data)
        elif scenario == 1:
            user_prompt = prompts_fusion.generate_product_scenario(input_data)
        elif scenario == 2:
            user_prompt = prompts_fusion.extract_sellpoints(input_data)
        elif scenario == 3:
            user_prompt = prompts_fusion.generate_company_info(input_data)
        status_, output_ = self.prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=self.watttraceid,
                                                         json_object=True)
        # print(Fore.GREEN + user_prompt + Fore.RESET)
        if status_:
            try:
                details = json.loads(output_)
                if scenario == 0 and details.get("type") == 'group':
                    if type_.lower() == "saas":
                        print(Fore.CYAN + f"Group Name: {details['name']}" + Fore.RESET)
                        details["type"] = "website"
                        details["OtherInfo"]["features"] = details["OtherInfo"].pop("topics")
                        details["OtherInfo"]["scenarios"] = details["OtherInfo"].pop("community")
                        del details["OtherInfo"]["platform"]
                if scenario == 3 and web_url:
                    details["companyOtherInfo"]["url"] = web_url
                return True, details
            except json.JSONDecodeError as e:
                return False, f"Error loading json: {e}"
        return False, output_
