import json
import config
import requests
import tiktoken
import re
from colorama import Fore
from service.server.call_gpt import CallGpt
from service.server.mm_understand.extract_keywords import process_media, rdp_url, rdp_token, extract_url_tags
from service.server.mm_understand.process_video import gemini_with_video, gemini_with_images, gemini_with_frames
from service import prompts_fusion

MAX_TOKENS = 40960
twitter_post_pattern = r"^(?:twitter|x)\.com\/[^\/\s]+\/status\/\d+"
mobile_twitter_post_pattern = r"^(?:mobile\.)?(?:twitter|x)\.com\/[^\/\s]+\/status\/\d+"
tiktok_video_pattern = r"^tiktok\.com\/@[^\/\s]+\/video\/\d+"
crawler_host = config.CRAWLER_HOST
spark_url = "/api/v1/spark/spark_html"
encoder = tiktoken.encoding_for_model("gpt-4o-mini")
prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=0.9)


def is_url_in_base_list(input_str: str):
    original_input = input_str.strip()
    input_str = re.sub(r'\s+', '', original_input)  # 移除所有空格

    # 改进www的检测逻辑 - 匹配任何大小写组合的www
    has_www = bool(re.search(r'(?i)^(?:https?:\/\/)?(www\.)', input_str))

    # 用于匹配的小写版本
    lower_input = input_str.lower()
    # 移除所有形式的www（包括大小写混合）和http(s)://
    lower_input = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', lower_input)
    lower_input = re.sub(r'\/+$', '/', lower_input.rstrip('/')) + ('/' if lower_input.endswith('/') else '')
    lower_input = re.sub(r'^mobile\.(x|twitter)\.', r'\1.', lower_input)

    url_pattern = re.compile(
        r'^(?:'  # 开始
        r'(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+'  # 域名部分
        r'[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?'  # 顶级域名
        r'(?:\/[^\s]*)?' # 路径部分（可选）
        r')$'
    )

    def is_likely_url(s: str) -> bool:
        # 检查是否包含至少一个点号，且不在开头或结尾
        if not re.search(r'^[^.]+\.[^.]+', s):
            return False
        # 检查是否包含常见的句子结束标点
        if re.search(r'\s*[.!?]\s+[A-Z]', original_input):
            return False
        # 检查域名部分的合理性
        domain_part = s.split('/')[0]
        if len(domain_part) < 3:  # 域名至少3个字符
            return False
        # 检查域名中的点号数量（通常不会超过4个）
        if domain_part.count('.') > 4:
            return False

        return True

    match = url_pattern.match(lower_input)
    print(Fore.YELLOW + f"Match: {match}"+Fore.RESET)
    if not match or not is_likely_url(lower_input):
        return False, 'Invalid URL', ''

    # 提取原始URL的路径部分（保持大小写）
    original_parts = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', original_input)
    domain, *path_parts = original_parts.split('/', 1)
    original_path = f"/{path_parts[0]}" if path_parts else ''

    # 构建标准化的URL，但保持路径的原始大小写
    standardized_url = f'https://{"www." if has_www else ""}{domain.lower()}{original_path}'

    # 检查Twitter
    if re.match(twitter_post_pattern, lower_input):
        return True, 'twitter', standardized_url

    # 检查TikTok
    if re.match(tiktok_video_pattern, lower_input):
        return True, 'tiktok', standardized_url

    # 其他普通URL
    return True, 'other', standardized_url


def extract_raw_content(url_str: str = ""):
    body = {"url": url_str}
    status, category, url = is_url_in_base_list(url_str)
    if not status:
        return False, category
    print(Fore.CYAN + f"Category: {category}" + Fore.RESET)
    try:
        response_ = requests.post(f"{crawler_host}{spark_url}", json=body, timeout=300)
    except requests.RequestException as e:
        return False, f"Error during request: {e}"

    if response_ and response_.status_code == 200:
        if response_.json().get("status", 0) == 0:
            raw_data = response_.json().get("result", {}).get("data", {})
            output_json = {}
            video_desc_ = None
            image_desc_ = None
            if category == "twitter":
                content_ = raw_data["product"]["contexts"][0]
                output_json["original_text"] = content_
                if raw_data["product"].get("images"):
                    image_list = raw_data["product"]["images"]
                    print(Fore.GREEN + f"Images: {image_list}" + Fore.RESET)
                    status, image_desc_ = process_media("image", image_list)
                    if status:
                        output_json["media_img_desc"] = image_desc_
                    else:
                        print(Fore.RED + f"Error processing images: {image_desc_}" + Fore.RESET)
                if raw_data["product"].get("videos"):
                    video_list = raw_data["product"]["videos"]
                    status, video_desc_ = process_media("video", video_list)
                    if status:
                        output_json["media_video_desc"] = video_desc_
                    else:
                        print(Fore.RED + f"Error processing videos: {video_desc_}" + Fore.RESET)
            elif category == "tiktok":  # tiktok
                content_ = raw_data["title"]
                output_json["original_text"] = content_
                video_ = raw_data["video_list"][0]
                if video_:
                    status, video_desc_ = process_media("video", [video_])
                    if status:
                        output_json["media_video_desc"] = video_desc_
                    else:
                        print(Fore.RED + f"Error processing videos: {video_desc_}" + Fore.RESET)
            else:
                content_ = raw_data["title"] + "\n" + raw_data["content"]
                print(Fore.RED + "Unknown category" + Fore.RESET)

            status_, output_sum = extract_url_tags(content_, video_desc_, image_desc_)
            if status_:
                output_json["related_tags"] = output_sum["tags"]
                if output_json.get("summary"):
                    output_json["original_text_summary"] = output_sum["summary"]
            return True, output_json
        else:
            error = response_.json().get("error", "")
            return False, f"Failed request error: {error}"
    else:
        return False, f"Failed request with status code {response_.status_code}"


def extract_structure(context: dict, language: str="english", watttraceid: str = ""):
        title = context.get("title", "")
        content = context.get("content", "")
        other = context.get("other", [])
        if other[0] == "cleaned_text":
            other = None
        child = context.get("child", [])
        product = context.get("product")
        description = context.get("description", "")
        url_type = context.get("url_type", "")
        description = url_type + " - " + description if url_type and description else description

        website_context = ""
        try:
            if product:
                product_title = product.get("title", "")
                product_contexts = product.get("contexts")

            if description and not content:
                content = description

            if not content and not child and not product and not description:
                if other:
                    website_context = f"Title: {title}\nOther Information: {other}"
                else:
                    return True, {}
            else:
                if other and len(other) > 0:
                    if product:
                        website_context = f"{title}\n - Content: {content}\n\nProduct Information: {product_title}-{product_contexts}\n\nOther Information: "
                    else:
                        website_context = f"{title}\n - Content: {content}\n\nOther Information: "
                    start_tokens = len(encoder.encode(website_context))
                    start_idx = 0
                    while start_tokens < MAX_TOKENS and start_idx < len(other):
                        website_context += other[start_idx] + " "
                        start_tokens = len(encoder.encode(website_context))
                        start_idx += 1
                else:
                    if product:
                        website_context = f"{title}\nContent: {content}\n\nProduct Information: {product_title}-{product_contexts}"
                    else:
                        website_context = f"{title}\nContent: {content}"

                if child and len(child) > 0 and start_tokens < MAX_TOKENS:
                    website_context += "\n\nChild Page Information: "
                    start_tokens = len(encoder.encode(website_context))
                    child_start_idx = 0
                    while start_tokens < MAX_TOKENS and child_start_idx < len(child):
                        child_page_info = f'Child Page {child_start_idx}:\ntitle: {child[child_start_idx]["title"]}\ncontent: {child[child_start_idx]["content"]}'
                        website_context += child_page_info + "\n"
                        start_tokens = len(encoder.encode(website_context))
                        child_start_idx += 1

        except Exception as e:
            error = f"generateDetails.pipeline Error: failed to preprocess context - {str(e)}"
            # FlaskLog.error(current_app.fmtSaveOutput(watttraceid, error))
            return False, error
        input_data = {"context": website_context, "language": language}
        if url_type:
            input_data["type"] = url_type
        if description and content:
            # print(Fore.CYAN + f"Description: {description}" + Fore.RESET)
            input_data["description"] = description
        system_prompt = prompts_fusion.website_info_extract_system(input_data)
        user_prompt = prompts_fusion.website_info_extract_user(input_data)
        status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                    json_object=True)
        print(Fore.GREEN + user_prompt + Fore.RESET)
        if status_:
            try:
                details = json.loads(output_)
                return True, details
            except json.JSONDecodeError as e:
                return False, f"Error loading json: {e}"
        return False, output_


def digest_content(platform: str, title: str, body: str, image_list: list, video_list: list, watttraceid: str = None):
    image_key_list = []
    image_output_list = []
    video_key_list = []
    video_type_list = []
    video_output_list = []
    if image_list:
        for image_info in image_list:
            if image_info.get("key"):
                image_key_list.append(image_info.get("key"))
                print(Fore.GREEN + f"Image Key: {image_info.get('key')}" + Fore.RESET)
    if image_key_list:
        for image_key in image_key_list:
            status, result = gemini_with_images(image_key, 'image/jpeg', watttraceid)
            print(Fore.YELLOW + f"Image Result: {result}" + Fore.RESET)
            if status:
                image_output_list.append(result)
    if video_list:
        for video_info in video_list:
            # Check if video has videoInfo for direct gemini analysis
            video_s3_info = video_info.get("videoInfo")
            if video_s3_info and video_s3_info.get("key"):
                video_key_list.append(video_s3_info.get("key"))
                video_type_list.append('video/' + video_s3_info.get("suffix"))
                continue  # Skip to next video if we have videoInfo
            
            # Check if video has transcript that needs summarization
            transcript_summary = None
            if video_info.get("transcript"):
                video_transcript = video_info.get("transcript")
                system_prompt = prompts_fusion.get_transcript_summary({"transcript": video_transcript})
                user_prompt = prompts_fusion.get_transcript_summary_user({"transcript": video_transcript})
                status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, json_object=True)
                if status_:
                    transcript_summary = json.loads(output_)
                else:
                    return False, f"Failed to get transcript summary: {output_}"
            
            # Check if video has frames
            if video_info.get("frames") and isinstance(video_info.get("frames"), list):
                frame_list = []
                for frame in video_info.get("frames"):
                    frame_key = frame.get('key')
                    if transcript_summary:
                        # Create dict with url and transcript_summary
                        frame_list.append({
                            "key": frame_key,
                            "transcript_summary": transcript_summary
                        })
                    else:
                        # If no transcript summary, just add the URL
                        frame_list.append({"key": frame_key})
                
                status, result = gemini_with_frames(frame_list, watttraceid)
                if status:
                    video_output_list.append(result)
            elif transcript_summary:
                # Only transcript summary, no frames
                video_output_list.append(transcript_summary)

    # Process videos with direct gemini analysis
    if video_key_list:
        for video_key, video_type in zip(video_key_list, video_type_list):
            status, result = gemini_with_video(video_key, video_type, watttraceid)
            print(Fore.YELLOW + f"Video Result: {result}" + Fore.RESET)
            if status:
                video_output_list.append(result)
    
    input_data = {"platform": platform}
    if title:
        input_data["title"] = title
    if body:
        input_data["content"] = body
    if image_output_list:
        input_data["image"] = image_output_list
    if video_output_list:
        input_data["video"] = video_output_list

    user_prompt = prompts_fusion.get_extract_url_tag_user(input_data)
    status_, output_ = prompt_gpt.callOpenaiGpt("You are a helpful assistant to summarize content",
                                                user_prompt, watttraceid=watttraceid,
                                                temperature=0.6, json_object=True)
    if status_:
        result = json.loads(output_)
        result["originalText"] = body if body else title
        if image_output_list:
            result["mediaImg"] = image_output_list
        if video_output_list:
            result["mediaVideo"] = video_output_list
        return True, result
    else:
        return False, output_
    

def get_image_list(image_list: list) -> list:
    """
    Return image_url list with s3-key list
    :param image_list: image
    :return: list of image_url
    """
    url_list = []
    for image in image_list:
        image_key = image.get("key")
        headers = {"Content-Type": "application/json", "rdp-token": rdp_token}
        body = {
            "object_key_list": [image_key]
        }
        try:
            response_ = requests.post(rdp_url, json=body, headers=headers, timeout=10)
            if response_.status_code == 200:
                response = response_.json().get("result", {}).get("data", {})[0]
                if response.get("object_url"):
                    url_list.append(response.get("object_url"))
        except:
            continue
    return url_list


if __name__ == '__main__':
    url = "https://www.bbc.com/news/uk-60788133"
    status, category_, url = is_url_in_base_list(url)
    if status:
        print(category_)
        print(url)
    else:
        print(category_)

    status, context = extract_raw_content(url)
    if status:
        print(context)
        status, details = extract_structure(context)
        if status:
            print(details)
        else:
            print(details)
    else:
        print(context)
