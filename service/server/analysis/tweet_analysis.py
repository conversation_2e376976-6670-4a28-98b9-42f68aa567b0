import json
import requests
import config
from service.server.call_gpt import CallGpt
from service import prompts_fusion
from service.lib.text_process import split_text_and_tags
from service.server.mm_understand.process_video import get_video_summary, gpt_with_images
from colorama import Fore

rec_host = config.WATT_REC_HOST
rec_topic_relevence = "/api/v1/rec/topic"


def get_heat_score(views):
    # 定义评分规则，区间最小值和对应分值
    score_ranges = [
        (0, 0), (350, 0), (500, 1), (800, 2), (1100, 3), (1700, 4), (2600, 5), (4000, 6), (6000, 7), (9000, 8),
        (13000, 9), (19000, 10), (29000, 11), (44000, 12), (66000, 13), (99000, 14), (150000, 15), (220000, 16),
        (330000, 17), (450000, 18), (750000, 19), (1100000, 20)
    ]

    # 遍历区间规则，找到匹配的分值
    for i in range(len(score_ranges) - 1):
        if score_ranges[i][0] <= views < score_ranges[i + 1][0]:
            return score_ranges[i][1]

    # 如果大于最大区间，返回最高分值
    return score_ranges[-1][1]


def get_high_relevance_topic(content: str):
    content_info = split_text_and_tags(content)
    content_info["needFallback"] = False
    body = {
        "xId": 1,
        "focusAreaStr": "1",
        "contentInfo": content_info,
        "pageNo": 1,
        "pageSize": 1
    }
    try:
        response_ = requests.post(f"{rec_host}{rec_topic_relevence}", json=body, timeout=25)
    except Exception as e:
        return False, f"Error retrieve relevant hotspot: {e}"
    if response_.status_code != 200:
        return False, str(response_)
    try:
        response = response_.json().get("result", {}).get("data", {}).get("hotspotList", [])
        if not response:
            return False, "No hotspot found"
        hotspot_ = response[0]
        if hotspot_:
            relevance_level = hotspot_.get("relevanceLevel", "")
            if relevance_level == "high":
                return True, hotspot_
            else:
                return False, "Relevance level is not high"
        else:
            return False, "No hotspot found"
    except Exception as e:
        return False, f"Error loading json: {e}"


class TweetAnalysis:
    def __init__(self, gpt_model: str = "gpt-4o"):
        self.prompt_gpt = CallGpt(model=gpt_model, temperature=0.7)

    def pipeline(self, content: str, tweet_stats: dict, reply_list: list, author_info: dict, media_list: list,
                 event_summary: str, watttraceid: str):
        if not event_summary and content:
            status, hotspot = get_high_relevance_topic(content)
            print(Fore.YELLOW + "Hotspot: " + Fore.RESET + json.dumps(hotspot))
            if status:
                event_summary = hotspot.get("summary", "")
            else:
                event_summary = ""

        if not content:
            content = "Empty Content"

        input_data = {"tweet_content": content, "context": event_summary, "author_info": author_info,
                      "stats": tweet_stats}
        if author_info:
            follow_count = author_info.get("follower", 0)
            author_desc = author_info.get("authorDesc", "")
            input_data["follow_count"] = follow_count
            input_data["author_desc"] = author_desc
        if reply_list:
            input_data["reply_list"] = reply_list
        image_url_list = []
        video_url_list = []
        video_info_list = []
        ## Step 1: Process videos and images
        if media_list:
            for item in media_list:
                if item.get("mediaType") == "image":
                    image_url = item.get("imgUrl", "")
                    image_id = item.get("uuid", "")
                    image_url_list.append({"url": image_url, "uuid": image_id})
                if item.get("mediaType") == "video":
                    video_url = item.get("videoUrl", "")
                    video_id = item.get("uuid", "")
                    video_url_list.append({"url": video_url, "uuid": video_id})
            for video in video_url_list:
                status, video_summary = get_video_summary(video["url"], content, event_summary)
                if not status:
                    print(Fore.RED + "Video Summary Error: " + Fore.RESET + video_summary)
                    return False, video_summary
                video_summary_str = json.dumps(video_summary)
                print(Fore.GREEN + "Video Summary: " + Fore.RESET + video_summary_str)
                video_info_list.append({"uuid": video["uuid"], "summary": video_summary_str})
            input_data["video_list"] = video_info_list

            status, image_output = gpt_with_images(image_url_list, content, event_summary)
            if status:
                input_data["image_list"] = image_output
                print(Fore.GREEN + "Image Output: " + Fore.RESET + json.dumps(image_output))

        ## Step 2: Process text and images

        system_prompt = prompts_fusion.get_tweet_analysis_system(input_data)
        if video_info_list or image_url_list:
            user_prompt = prompts_fusion.get_tweet_analysis_media(input_data)
        else:
            user_prompt = prompts_fusion.get_tweet_analysis_user(input_data)
        status_, output_ = self.prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                         json_object=True)

        if not status_:
            print(Fore.RED + "Tweet Analysis Error: " + Fore.RESET + output_)
            return False, output_
        # print(Fore.YELLOW + "Tweet Analysis Output: " + Fore.RESET + output_)
        try:
            output_json = json.loads(output_)
            if event_summary:
                output_json["summary"] = event_summary
            visualScore = output_json.get("visualScore", 0)
            if video_info_list or image_url_list:
                if visualScore < 50:
                    output_json["importanceAnalysis"] = f"Textual Content Driven, visual content influencing {visualScore}% of impact."
                else:
                    output_json["importanceAnalysis"] = f"Visual Content Driven, influencing {visualScore}% of impact."
                del output_json["visualScore"]
            else:
                output_json["importanceAnalysis"] = ""
            output_json.setdefault("opinionStance", "")
            output_json.setdefault("contentAnalysis", [])
            output_json.setdefault("mediaContribution", [])
            return True, output_json
        except json.JSONDecodeError:
            return False, "JSON Decode Error of output from GPT"


    def pipeline_v2(self, content: str, tweet_stats: dict, reply_list: list, author_info: dict, media_list: list,
                    event_summary: str, watttraceid: str):

        if not event_summary and content:
            status, hotspot = get_high_relevance_topic(content)
            print(Fore.YELLOW + "Hotspot: " + Fore.RESET + json.dumps(hotspot))
            if status:
                event_summary = hotspot.get("summary", "")
            else:
                event_summary = ""

        if not content:
            content = "Empty Content"

        views = tweet_stats.get("views", 0)
        heat_score = get_heat_score(views)

        input_data = {"tweet_content": content, "context": event_summary, "author_info": author_info,
                      "stats": tweet_stats, "heat_score": heat_score}
        if author_info:
            follow_count = author_info.get("follower", 0)
            author_desc = author_info.get("authorDesc", "")
            input_data["follow_count"] = follow_count
            input_data["author_desc"] = author_desc
        if reply_list:
            input_data["reply_list"] = reply_list
        image_url_list = []
        video_url_list = []
        video_info_list = []
        ## Step 1: Process videos and images
        if media_list:
            for index, item in enumerate(media_list):
                item["index"] = index
                if item.get("mediaType") == "image":
                    image_url = item.get("imgUrl", "")
                    image_url_list.append({"url": image_url, "uuid": index})
                if item.get("mediaType") == "video":
                    video_url = item.get("videoUrl", "")
                    video_url_list.append(video_url)
            for video in video_url_list:
                status, video_summary = get_video_summary(video, content, event_summary)
                if not status:
                    print(Fore.RED + "Video Summary Error: " + Fore.RESET + video_summary)
                    return False, video_summary
                video_summary_str = json.dumps(video_summary)
                print(Fore.GREEN + "Video Summary: " + Fore.RESET + video_summary_str)
                video_info_list.append({"summary": video_summary_str})
            input_data["video_list"] = video_info_list

            status, image_output = gpt_with_images(image_url_list, content, event_summary)
            if status:
                input_data["image_list"] = image_output
                print(Fore.GREEN + "Image Output: " + Fore.RESET + json.dumps(image_output))

                image_type_mapping = {int(item["index"]): item["type"] for item in image_output}

                for item in media_list:
                    if item["mediaType"] == "image":
                        # 如果是 image，查找对应的类别
                        print(f"index: {item['index']} has the type: {image_type_mapping.get(item['index'])}")
                        item["type"] = image_type_mapping.get(int(item["index"]))
                    elif item["mediaType"] == "video":
                        # 如果是 video，设置为 "Key Visual"
                        item["type"] = "Key Visual"

        ## Step 2: Process text and images
        system_prompt = prompts_fusion.get_tweet_analysis_system(input_data)
        if video_info_list or image_url_list:
            user_prompt = prompts_fusion.get_tweet_analysis_media(input_data)
        else:
            user_prompt = prompts_fusion.get_tweet_analysis_user(input_data)
        status_, output_ = self.prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                         json_object=True)

        if not status_:
            print(Fore.RED + "Tweet Analysis Error: " + Fore.RESET + output_)
            return False, output_
        print(Fore.YELLOW + "Tweet Analysis Output: " + Fore.RESET + output_)
        try:
            output_json = json.loads(output_)
            tag_list = output_json.get("trendingFactors", [])
            special_tag_dict = {}
            celebrity_list = []
            audience_list = []
            creator_type = ""
            for tag_object in tag_list:
                tag_type = tag_object.get("tag_type", "")
                if tag_type.lower() == "celebrity":
                    celebrity_list.append(tag_object.get("tag_name", ""))
                elif tag_type.lower() == "niche account":
                    creator_type = tag_object.get("tag_name", "")
                elif tag_type.lower() == "audience profile":
                    audience_list.append(tag_object.get("tag_name", ""))

            special_tag_dict["celebrityTags"] = celebrity_list
            special_tag_dict["creatorType"] = creator_type
            special_tag_dict["audience"] = audience_list

            visualScore = output_json.get("visualScore", 0)
            if video_info_list or image_url_list:
                if visualScore < 50:
                    special_tag_dict["visualImportance"] = \
                        f"Textual Content Driven, visual content influencing {visualScore}% of impact."
                else:
                    special_tag_dict["visualImportance"] = \
                        f"Visual Content Driven, influencing {visualScore}% of impact."
                del output_json["visualScore"]
            if media_list:
                for media in media_list:
                    del media["index"]
                special_tag_dict["mediaList"] = media_list

            output_json["specialTags"] = special_tag_dict
            output_json["trendingScore"] = heat_score
            return True, output_json
        except json.JSONDecodeError:
            return False, "JSON Decode Error of output from GPT"
