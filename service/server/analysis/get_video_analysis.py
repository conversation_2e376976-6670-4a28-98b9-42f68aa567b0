import re
import time
from service.lib.media_llm import gpt_with_media
from service.lib.audio_transcription import AudioTranscription, get_urls
from service import WATT_AI_GPT_TOKEN
from service.lib.prompt_fusion import get_prompt


def analyze(frame_keys, audio_dict, description, video_info, comment, author_info, bgmName):
    frame_status, frame_urls = get_urls(frame_keys)
    if not frame_status:
        return False, frame_urls
    heat_score = get_heat_score(int(video_info.get('view', 0)))
    audio_pipe = AudioTranscription(watt_gpt_token=WATT_AI_GPT_TOKEN)
    transcription = audio_pipe.pipeline(audio_dict)
    data = {"description": description,
            "reply_list": comment,
            "bgmName": bgmName,
            "fansNums": author_info.get("fansNums", 0),
            "author_intro": author_info.get("intro", None),
            "heat_score": heat_score
            }
    sys_prompt = "You are a professional social media analyst specializing in viral content analysis and attribution. Your task is to analyze a provided video comprehensively and generate a detailed attribution report."
    user_prompt = get_prompt('user/analysis/video_analysis_user.j2', data)
    status, gpt_return = gpt_with_media(frame_urls, sys_prompt, user_prompt, transcription, json_schema)
    if not status:
        time.sleep(5)
        status_2, gpt_return = gpt_with_media(frame_urls, sys_prompt, user_prompt, transcription, json_schema)
        if not status_2:
            return False, gpt_return
    video_detail = {"summary": {"narration": gpt_return["summary"],
                                "typeTag": empty_check(gpt_return["category"])},
                    "consumption": {"relationship": gpt_return["contentConsumption"],
                                    "bloggerType": empty_check(gpt_return["nicheAccount"])},  # "nicheAccount" 加一个判断逻辑
                    "audience": gpt_return["audience"],
                    "background": empty_check(gpt_return["eventContext"]),
                    "publicOpinion": empty_check(gpt_return["opinionStance"]),
                    "videoStructure": gpt_return["voiceOverTextAnalysis"]["textStructure"],
                    "hook": gpt_return["hook"]
                    }
    strategies = []
    attitude = empty_check(gpt_return["voiceOverTextAnalysis"]["textAttitude"])
    emotion = empty_check(gpt_return["voiceOverTextAnalysis"]["textEmotion"])
    style = empty_check(gpt_return["voiceOverTextAnalysis"]["writingStyle"])
    if attitude:
        strategies.append(f"Enhance {attitude} Attitude")
    if emotion:
        strategies.append(f"Evoke {emotion} Resonance")
    if style:
        strategies.append(style)

    if not transcription:  # 台本为空的时候
        video_detail["hook"]["textHook"] = ""
        video_detail["videoStructure"] = []
        strategies = []
    text_structure = video_detail["videoStructure"]
    if text_structure:
        video_detail["videoStructure"] = merge_repeated_items(text_structure)
    final_score = 0
    for i in gpt_return["heatDimension"]:
        final_score += i["score"]
    int_final_score = int(final_score)
    if final_score != int_final_score:
        for i in gpt_return["heatDimension"]:
            if i["score"] >= 0.5:
                i["score"] -= 0.5
                break
    answer = {"heatScore": int_final_score, 'heatDimension': gpt_return["heatDimension"],
              "videoDetail": video_detail, "wordCloud": gpt_return["tags"], "strategies": strategies}
    return True, answer


def get_heat_score(views):
    # 定义评分规则，区间最小值和对应分值
    score_ranges = [
        (0, 0), (350, 0), (500, 1), (800, 2), (1100, 3), (1700, 4), (2600, 5), (4000, 6), (6000, 7), (9000, 8),
        (13000, 9), (19000, 10), (29000, 11), (44000, 12), (66000, 13), (99000, 14), (150000, 15), (220000, 16),
        (330000, 17), (450000, 18), (750000, 19), (1100000, 20)
    ]
    # 遍历区间规则，找到匹配的分值
    for i in range(len(score_ranges) - 1):
        if score_ranges[i][0] <= views < score_ranges[i + 1][0]:
            return score_ranges[i][1]
    # 如果大于最大区间，返回最高分值
    return score_ranges[-1][1]


def empty_check(data: str):
    if not data:
        return data
    if re.search(r'(omit|empty|null|none)', data, re.IGNORECASE):
        return ''
    else:
        return data


def merge_repeated_items(original_list):
    new_list = []
    previous_item = None
    for item in original_list:
        match = re.match(r"(.+?) \d+$", item)  # 匹配格式为 "xxx <数字>" 的项
        if match:
            common_part = match.group(1)
            if previous_item != common_part:
                new_list.append(common_part)  # 只添加一次共同部分
                previous_item = common_part
        else:
            new_list.append(item)
    return new_list


json_schema = {
        "name": "analyze_video",
        "description": "analyze video in required ways",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "heatDimension": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "dimension": {
                                "type": "string",
                            },
                            "score": {
                                "type": "number",
                                "description": "Round to one decimal place"
                            },
                            "reason": {
                                "type": "string",
                                "description": "limit to 15 words"
                            },
                        },
                        "required": ["dimension", "score", "reason"],
                        "additionalProperties": False
                    }
                },
                "summary": {
                    "type": "string",
                    "description": "the narration of the whole video, 2 to 3 sentences"
                },
                "category": {
                    "type": "string",
                    "description": "video category"
                },
                "contentConsumption": {
                    "type": "string",
                    "description": "the description between producer and audience (limit to 20 words)"
                },
                "eventContext": {
                    "type": "string",
                    "description": "the context (limit to 20 words), empty if no specific context"
                },
                "opinionStance": {
                    "type": "string",
                    "description": "the author's viewpoint on the event (limit to 20 words) Please use concise but informative description instead of simple sentiment labels (e.g., positive, negative). Also can indicate that the author represents a certain group, resonating with them deeply. Empty if no clear sentiment or perspective is present"
                },
                "voiceOverTextAnalysis": {
                    "type": "object",
                    "properties": {
                        "textStructure": {
                            "type": "array",
                            "items": {
                                "type": "string",
                                "description": "structure phrase in order, max to 6 phrases, can be empty"
                            }
                        },
                        "textAttitude": {
                            "type": "string",
                            "description": "the attitude of the voice-over text. A word, can be empty"
                        },
                        "textEmotion": {
                            "type": "string",
                            "description": "the emotion of the voice-over text. A word, can be empty"
                        },
                        "writingStyle": {
                            "type": "string",
                            "description": "the writing style of the voice-over text, in format 'Use xxxx writing style'. A sentence, can be empty"
                        },
                    },
                    "required": ["textStructure", "textAttitude", "textEmotion", "writingStyle"],
                    "additionalProperties": False
                },
                "hook": {
                    "type": "object",
                    "properties": {
                        "textHook": {
                            "type": "string",
                            "description": "the hook in the voice-over text"
                        },
                        "visionHook": {
                            "type": "string",
                            "description": "the hook in the vision"
                        }
                    },
                    "required": ["textHook", "visionHook"],
                    "additionalProperties": False
                },
                "audience": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "description": "audience profile"
                    }
                },
                "nicheAccount": {
                    "type": "string",
                    "description": "Niche Account tag"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "word": {
                                "type": "string",
                                "description": "tag name"
                            },
                            "brief": {
                                "type": "string",
                                "description": "tag description"
                            },
                            "type": {
                                "type": "string",
                                "description": "tag type"
                            }
                        },
                        "required": ["word", "brief", "type"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["heatDimension",  "summary", "category", "contentConsumption", "eventContext", "opinionStance", "voiceOverTextAnalysis", "hook", "audience",  "nicheAccount", "tags"],
            "additionalProperties": False
        }
    }


if __name__ == "__main__":
    # test2 = "/Users/<USER>/Documents/gemini/test2.mp4"
    # test3 = "/Users/<USER>/Documents/gemini/test3.mp4"
    # file = "/Users/<USER>/Downloads/test3.mp3"
    # file1 = "/Users/<USER>/Documents/vison_test/test_video/11.mp4"
    # audio_file = open(file1, "rb")
    import requests

    # status, answer = audio_transcription(audio_file)
    audio_text = "It's Big Dick Don here. I'm hearing rumors that you're locking people up for posting memes. What the fuck are you playing at, you dirty commie cocksucker? Yes, but... but because they are far-right thugs saying hurty words and upsetting... Listen, I'm going to stick my foot far right up your ass. You can't do that to me. My dad was a toolmaker. Well, he certainly made a tool for the World Economic Forum. Don't you bring Davos into this. I'll tell my mummy. Listen, you slimy bastard, I'll own everything and love it. She worked in the NHS. Yes, well, my mom, who was probably the greatest mom there ever has been, came from Scotland. She always told me, Donny, don't ever let those commie bastards take over the home country. So when I do get re-elected, I'm going to invade your fucking country and make Britain great again."
    fake_data = {
                 "description": "",
                 "videoInfo": {},
                 "comment": [],
                 "authorInfo": {},
                 "bgmName": "",
    }
    # print(status)
    # print(type(answer)) # <class 'str'>
    # print(answer)