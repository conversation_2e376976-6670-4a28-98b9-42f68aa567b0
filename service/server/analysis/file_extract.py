import tempfile
from urllib.parse import urlparse
import requests
import os
import mimetypes
import time
import random
import grequests
import gevent
import config


# RDP配置
rdp_host = config.RDP_HOST
rdp_url = rdp_host + config.RDP_CDN_URL
rdp_token = config.RDP_TOKEN


def get_url_from_rdp_key(rdp_key):
    """
    根据RDP key获取永久有效的URL
    
    Args:
        rdp_key (str): RDP服务的永久有效key
        
    Returns:
        tuple: (success: bool, url: str)
            - 如果成功: (True, object_url)
            - 如果失败: (False, error_message)
    """
    if not rdp_key:
        return False, "RDP key is empty"
        
    headers = {"Content-Type": "application/json", "rdp-token": rdp_token}
    body = {
        "object_key_list": [rdp_key]
    }
    
    try:
        response = requests.post(rdp_url, json=body, headers=headers, timeout=10)
        if response.status_code != 200:
            return False, f"RDP API returned status {response.status_code}: {response.text}"
        
        result = response.json().get("result", {}).get("data", [])
        if not result:
            return False, "No data returned from RDP API"
            
        object_url = result[0].get("object_url")
        if not object_url:
            return False, "No object_url in RDP response"
            
        return True, object_url
    except Exception as e:
        return False, f"RDP API request failed: {str(e)}"


def download_file_from_url(url, file_extension=".pdf"):
    """
    从URL下载文件到临时文件（保持向后兼容）
    
    Args:
        url (str): 文件URL
        file_extension (str): 文件扩展名，默认为.pdf
        
    Returns:
        str: 临时文件路径
    """
    print(f"正在从URL下载文件: {url}")
    # 添加浏览器头部来避免被拒绝
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    response = requests.get(url, stream=True, headers=headers)
    response.raise_for_status()
    
    # 创建临时文件
    with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
        for chunk in response.iter_content(chunk_size=8192):
            temp_file.write(chunk)
        temp_file_path = temp_file.name
    
    print(f"文件已下载到临时位置: {temp_file_path}")
    return temp_file_path


def download_file_from_url_with_fallback(url, rdp_key=None, file_extension=".pdf"):
    """
    从URL下载文件到临时文件，支持RDP key作为fallback
    
    Args:
        url (str): 主要文件URL
        rdp_key (str): RDP服务的永久有效key，用作fallback
        file_extension (str): 文件扩展名，默认为.pdf
        
    Returns:
        tuple: (success: bool, result: str)
            - 如果成功: (True, temp_file_path)
            - 如果失败: (False, error_message)
    """
    print(f"正在从URL下载文件: {url}")
    
    # 首先尝试使用原始URL，添加浏览器头部来避免被拒绝
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
    }
    try:
        response = requests.get(url, stream=True, timeout=30, headers=headers)
        response.raise_for_status()
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            for chunk in response.iter_content(chunk_size=8192):
                temp_file.write(chunk)
            temp_file_path = temp_file.name
        
        print(f"文件已下载到临时位置: {temp_file_path}")
        return True, temp_file_path
        
    except Exception as e:
        print(f"使用原始URL下载失败: {e}")
        
        # 如果有RDP key，尝试fallback
        if rdp_key:
            print(f"尝试使用RDP key fallback: {rdp_key}")
            success, new_url_or_error = get_url_from_rdp_key(rdp_key)
            
            if success:
                new_url = new_url_or_error
                print(f"从RDP获取到新URL: {new_url}")
                
                try:
                    response = requests.get(new_url, stream=True, timeout=30, headers=headers)
                    response.raise_for_status()
                    
                    # 创建临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
                        for chunk in response.iter_content(chunk_size=8192):
                            temp_file.write(chunk)
                        temp_file_path = temp_file.name
                    
                    print(f"使用RDP fallback成功，文件已下载到: {temp_file_path}")
                    return True, temp_file_path
                    
                except Exception as fallback_e:
                    error_msg = f"RDP fallback也失败了: {fallback_e}"
                    print(error_msg)
                    return False, error_msg
            else:
                error_msg = f"获取RDP URL失败: {new_url_or_error}"
                print(error_msg)
                return False, error_msg
        else:
            error_msg = f"原始URL下载失败且没有RDP key可用: {e}"
            print(error_msg)
            return False, error_msg


def parse_multiple_files_grequests_direct(files_to_process, max_pages=8):
    """
    使用grequests并发处理多个文件，直接传递URL而不下载
    """
    # 1. 为每个文件分配一个固定的API key，直接使用URL
    files_info = []
    for file_info in files_to_process:
        url = file_info.get("url")
        if not url:
            continue
        
        api_key = get_random_api_key()
        files_info.append({
            "url": url,
            "original_url": url,  # 保存原始URL
            "type": "pdf",  # 默认类型
            "status": "ready_for_upload",
            "api_key": api_key,
            "rdp_key": file_info.get("key")
        })

    # 2. 并发上传URL（直接模式）
    upload_requests = []
    base_url = "https://api.cloud.llamaindex.ai/api/v1/parsing"
    upload_url = f"{base_url}/upload"

    for file_data in files_info:
        # 使用该文件固定的API key
        headers = {"Authorization": f"Bearer {file_data['api_key']}"}
        
        # 直接使用URL参数而不是文件上传
        data = {
            "fast_mode": "true", 
            "max_pages": str(max_pages),
            "input_url": file_data['url']  # 使用 input_url 参数直接传递URL
        }
        
        req = grequests.post(
            upload_url,
            headers=headers,
            data=data
        )
        upload_requests.append(req)

    # 添加异常处理
    try:
        upload_responses = grequests.map(upload_requests, exception_handler=lambda req, exc: None)
    except Exception as e:
        print(f"Upload grequests.map failed: {e}")
        upload_responses = [None] * len(upload_requests)

    # 处理上传结果
    failed_files_for_retry = []
    for i, res in enumerate(upload_responses):
        file_data = files_info[i]
        if res is None:
            file_data['status'] = 'upload_failed'
            file_data['error'] = "Upload request returned None - likely timeout or connection error"
            print(f"Failed to upload {file_data['url']}: {file_data['error']}")
            # 如果有 RDP key，标记为需要重试
            if file_data.get('rdp_key'):
                failed_files_for_retry.append(file_data)
        elif res.status_code == 200:
            # 检查响应内容是否包含AccessDenied错误（支持XML和文本格式）
            response_text = res.text
            if ("AccessDenied" in response_text or 
                "Access denied" in response_text or 
                "<Code>AccessDenied</Code>" in response_text or
                "<?xml" in response_text and "Error" in response_text):
                file_data['status'] = 'upload_failed'
                file_data['error'] = f"URL expired or access denied: {response_text[:200]}..."
                print(f"Failed to upload {file_data['url']}: URL expired or access denied")
                # 如果有 RDP key，标记为需要重试
                if file_data.get('rdp_key'):
                    failed_files_for_retry.append(file_data)
            else:
                try:
                    file_data['status'] = 'uploaded'
                    file_data['job_id'] = res.json()['id']
                    print(f"Successfully uploaded {file_data['url']}, job_id: {file_data['job_id']}")
                except (ValueError, KeyError) as e:
                    # JSON解析失败，可能是错误响应
                    file_data['status'] = 'upload_failed'
                    file_data['error'] = f"Invalid response format: {response_text[:200]}..."
                    print(f"Failed to parse response for {file_data['url']}: {file_data['error']}")
                    # 如果有 RDP key，标记为需要重试
                    if file_data.get('rdp_key'):
                        failed_files_for_retry.append(file_data)
        else:
            file_data['status'] = 'upload_failed'
            file_data['error'] = f"HTTP {res.status_code}: {res.text}"
            print(f"Failed to upload {file_data['url']}: {file_data['error']}")
            # 如果有 RDP key 且是访问被拒绝的错误，标记为需要重试
            if file_data.get('rdp_key') and (res.status_code in [403, 404, 401]):
                failed_files_for_retry.append(file_data)
    
    # 对失败的文件使用 RDP key 进行重试
    if failed_files_for_retry:
        print(f"尝试使用 RDP key 重试 {len(failed_files_for_retry)} 个失败的文件...")
        retry_requests = []
        
        for file_data in failed_files_for_retry:
            rdp_key = file_data['rdp_key']
            print(f"尝试使用 RDP key 获取新 URL: {rdp_key}")
            
            # 使用 RDP key 获取新的 URL
            success, new_url_or_error = get_url_from_rdp_key(rdp_key)
            
            if success:
                new_url = new_url_or_error
                print(f"从 RDP 获取到新 URL: {new_url}")
                
                # 使用新 URL 重新创建请求
                headers = {"Authorization": f"Bearer {file_data['api_key']}"}
                data = {
                    "fast_mode": "true", 
                    "max_pages": str(max_pages),
                    "input_url": new_url  # 使用新的 URL
                }
                
                req = grequests.post(
                    upload_url,
                    headers=headers,
                    data=data
                )
                retry_requests.append((req, file_data, new_url))
            else:
                error_msg = f"获取 RDP URL 失败: {new_url_or_error}"
                print(error_msg)
                file_data['error'] = f"{file_data.get('error', '')} | {error_msg}"
        
        # 执行重试请求
        if retry_requests:
            retry_responses = grequests.map([req for req, _, _ in retry_requests], exception_handler=lambda req, exc: None)
            
            for j, res in enumerate(retry_responses):
                req, file_data, new_url = retry_requests[j]
                
                if res is None:
                    error_msg = "Retry request returned None - likely timeout or connection error"
                    print(f"重试失败 {file_data['url']}: {error_msg}")
                    file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                elif res.status_code == 200:
                    # 检查重试响应内容是否包含AccessDenied错误（支持XML和文本格式）
                    response_text = res.text
                    if ("AccessDenied" in response_text or 
                        "Access denied" in response_text or 
                        "<Code>AccessDenied</Code>" in response_text or
                        "<?xml" in response_text and "Error" in response_text):
                        error_msg = f"Retry URL also expired or access denied: {response_text[:200]}..."
                        print(f"重试失败 {file_data['url']}: {error_msg}")
                        file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                    else:
                        try:
                            file_data['status'] = 'uploaded'
                            file_data['job_id'] = res.json()['id']
                            file_data['url'] = new_url  # 更新为新的 URL
                            print(f"重试成功! 使用新 URL {new_url}, job_id: {file_data['job_id']}")
                        except (ValueError, KeyError) as e:
                            error_msg = f"Retry response format invalid: {response_text[:200]}..."
                            print(f"重试失败 {file_data['url']}: {error_msg}")
                            file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                else:
                    error_msg = f"HTTP {res.status_code}: {res.text}"
                    print(f"重试失败 {file_data['url']}: {error_msg}")
                    file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
    
    # 3. 并发轮询解析结果
    jobs_to_poll = {
        f['job_id']: f 
        for f in files_info if f.get('status') == 'uploaded'
    }
    
    # 用于收集轮询阶段发现的AccessDenied错误文件，需要RDP重试
    polling_failed_for_retry = []
    
    max_attempts = 120  # 增加到120次，约6分钟
    attempt = 0
    gevent.sleep(10) # 初始等待时间增加到10秒

    while jobs_to_poll and attempt < max_attempts:
        poll_requests = []
        job_ids_in_batch = list(jobs_to_poll.keys())
        
        for job_id in job_ids_in_batch:
            file_data = jobs_to_poll[job_id]
            # 使用该文件固定的API key
            headers = {"Authorization": f"Bearer {file_data['api_key']}"}
            result_url = f"{base_url}/job/{job_id}/result/text"
            poll_requests.append(grequests.get(result_url, headers=headers))

        # 添加异常处理和超时设置
        try:
            poll_responses = grequests.map(poll_requests, size=len(poll_requests), exception_handler=lambda req, exc: None)
        except Exception as e:
            print(f"grequests.map failed: {e}")
            poll_responses = [None] * len(poll_requests)
        
        finished_jobs = []
        for i, res in enumerate(poll_responses):
            job_id = job_ids_in_batch[i]
            file_data = jobs_to_poll[job_id]
            
            if res is None:
                print(f"Request for job {job_id} ({file_data['url']}) returned None - likely timeout or connection error")
                continue  # 不标记为失败，继续重试
            elif res.status_code == 200:
                try:
                    response_data = res.json()
                    content = response_data.get('text', '')
                    
                    # 检查内容是否包含AccessDenied错误（支持XML和文本格式）
                    if ("AccessDenied" in content or 
                        "Access denied" in content or 
                        "<Code>AccessDenied</Code>" in content or
                        "<?xml" in content and "Error" in content):
                        print(f"Job {job_id} for {file_data['url']} returned access denied error.")
                        file_data['status'] = 'polling_failed'
                        file_data['error'] = f"URL expired or access denied in result: {content[:200]}..."
                        finished_jobs.append(job_id)
                        # 如果有 RDP key，标记为需要重试
                        if file_data.get('rdp_key'):
                            polling_failed_for_retry.append(file_data)
                    else:
                        print(f"Job {job_id} for {file_data['url']} finished.")
                        file_data['status'] = 'completed'
                        file_data['content'] = content
                        finished_jobs.append(job_id)
                except (ValueError, KeyError) as e:
                    print(f"Failed to parse response for job {job_id} ({file_data['url']}): {e}")
                    file_data['status'] = 'polling_failed'
                    file_data['error'] = f"Invalid response format: {res.text[:200]}..."
                    finished_jobs.append(job_id)
            elif res.status_code == 202 or res.status_code == 404:
                print(f"Job {job_id} for {file_data['url']} still processing... (status: {res.status_code})")
            else:
                print(f"Polling failed for job {job_id} ({file_data['url']}): {res.status_code} - {res.text}")
                file_data['status'] = 'polling_failed'
                file_data['error'] = f"HTTP {res.status_code}: {res.text}"
                finished_jobs.append(job_id)

        for job_id in finished_jobs:
            del jobs_to_poll[job_id]
        
        if jobs_to_poll:
            gevent.sleep(5)  # 增加轮询间隔到5秒
        attempt += 1

    # 处理超时
    for job_id, file_data in jobs_to_poll.items():
        file_data['status'] = 'timeout'
        file_data['error'] = 'Polling timed out'

    # 对轮询阶段发现的AccessDenied错误文件使用 RDP key 进行重试
    if polling_failed_for_retry:
        print(f"尝试使用 RDP key 重试轮询阶段发现的 {len(polling_failed_for_retry)} 个AccessDenied文件...")
        retry_requests = []
        
        for file_data in polling_failed_for_retry:
            rdp_key = file_data['rdp_key']
            print(f"尝试使用 RDP key 获取新 URL: {rdp_key}")
            
            # 使用 RDP key 获取新的 URL
            success, new_url_or_error = get_url_from_rdp_key(rdp_key)
            
            if success:
                new_url = new_url_or_error
                print(f"从 RDP 获取到新 URL: {new_url}")
                
                # 重置文件状态，使用新 URL 重新创建上传请求
                file_data['status'] = 'retry_upload'
                headers = {"Authorization": f"Bearer {file_data['api_key']}"}
                data = {
                    "fast_mode": "true", 
                    "max_pages": str(max_pages),
                    "input_url": new_url  # 使用新的 URL
                }
                
                req = grequests.post(
                    upload_url,
                    headers=headers,
                    data=data
                )
                retry_requests.append((req, file_data, new_url))
            else:
                error_msg = f"获取 RDP URL 失败: {new_url_or_error}"
                print(error_msg)
                file_data['error'] = f"{file_data.get('error', '')} | {error_msg}"
        
        # 执行重试上传请求
        if retry_requests:
            retry_responses = grequests.map([req for req, _, _ in retry_requests], exception_handler=lambda req, exc: None)
            
            for j, res in enumerate(retry_responses):
                req, file_data, new_url = retry_requests[j]
                
                if res is None:
                    error_msg = "Retry upload request returned None - likely timeout or connection error"
                    print(f"重试上传失败 {file_data['url']}: {error_msg}")
                    file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                elif res.status_code == 200:
                    # 检查重试上传响应内容是否包含AccessDenied错误
                    response_text = res.text
                    if ("AccessDenied" in response_text or 
                        "Access denied" in response_text or 
                        "<Code>AccessDenied</Code>" in response_text or
                        "<?xml" in response_text and "Error" in response_text):
                        error_msg = f"Retry URL also expired or access denied: {response_text[:200]}..."
                        print(f"重试上传失败 {file_data['url']}: {error_msg}")
                        file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                    else:
                        try:
                            file_data['status'] = 'retry_uploaded'
                            file_data['job_id'] = res.json()['id']
                            file_data['url'] = new_url  # 更新为新的 URL
                            print(f"重试上传成功! 使用新 URL {new_url}, job_id: {file_data['job_id']}")
                        except (ValueError, KeyError) as e:
                            error_msg = f"Retry upload response format invalid: {response_text[:200]}..."
                            print(f"重试上传失败 {file_data['url']}: {error_msg}")
                            file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
                else:
                    error_msg = f"HTTP {res.status_code}: {res.text}"
                    print(f"重试上传失败 {file_data['url']}: {error_msg}")
                    file_data['error'] = f"{file_data.get('error', '')} | Retry: {error_msg}"
            
            # 对重试上传成功的文件进行轮询
            retry_jobs_to_poll = {
                f['job_id']: f 
                for f in polling_failed_for_retry if f.get('status') == 'retry_uploaded'
            }
            
            if retry_jobs_to_poll:
                print(f"开始轮询 {len(retry_jobs_to_poll)} 个重试文件的解析结果...")
                retry_attempt = 0
                retry_max_attempts = 60  # 重试文件的轮询次数稍微少一些
                gevent.sleep(5)  # 等待5秒开始轮询
                
                while retry_jobs_to_poll and retry_attempt < retry_max_attempts:
                    retry_poll_requests = []
                    retry_job_ids_in_batch = list(retry_jobs_to_poll.keys())
                    
                    for job_id in retry_job_ids_in_batch:
                        file_data = retry_jobs_to_poll[job_id]
                        headers = {"Authorization": f"Bearer {file_data['api_key']}"}
                        result_url = f"{base_url}/job/{job_id}/result/text"
                        retry_poll_requests.append(grequests.get(result_url, headers=headers))
                    
                    try:
                        retry_poll_responses = grequests.map(retry_poll_requests, size=len(retry_poll_requests), exception_handler=lambda req, exc: None)
                    except Exception as e:
                        print(f"Retry polling grequests.map failed: {e}")
                        retry_poll_responses = [None] * len(retry_poll_requests)
                    
                    retry_finished_jobs = []
                    for i, res in enumerate(retry_poll_responses):
                        job_id = retry_job_ids_in_batch[i]
                        file_data = retry_jobs_to_poll[job_id]
                        
                        if res is None:
                            print(f"Retry polling request for job {job_id} ({file_data['url']}) returned None")
                            continue
                        elif res.status_code == 200:
                            try:
                                response_data = res.json()
                                content = response_data.get('text', '')
                                
                                # 再次检查内容是否包含AccessDenied错误
                                if ("AccessDenied" in content or 
                                    "Access denied" in content or 
                                    "<Code>AccessDenied</Code>" in content or
                                    "<?xml" in content and "Error" in content):
                                    print(f"Retry job {job_id} for {file_data['url']} still returned access denied error.")
                                    file_data['status'] = 'retry_failed'
                                    file_data['error'] = f"{file_data.get('error', '')} | Retry still failed: {content[:200]}..."
                                    retry_finished_jobs.append(job_id)
                                else:
                                    print(f"Retry job {job_id} for {file_data['url']} finished successfully!")
                                    file_data['status'] = 'completed'
                                    file_data['content'] = content
                                    retry_finished_jobs.append(job_id)
                            except (ValueError, KeyError) as e:
                                print(f"Failed to parse retry response for job {job_id} ({file_data['url']}): {e}")
                                file_data['status'] = 'retry_failed'
                                file_data['error'] = f"{file_data.get('error', '')} | Retry parse error: {res.text[:200]}..."
                                retry_finished_jobs.append(job_id)
                        elif res.status_code == 202 or res.status_code == 404:
                            print(f"Retry job {job_id} for {file_data['url']} still processing... (status: {res.status_code})")
                        else:
                            print(f"Retry polling failed for job {job_id} ({file_data['url']}): {res.status_code} - {res.text}")
                            file_data['status'] = 'retry_failed'
                            file_data['error'] = f"{file_data.get('error', '')} | Retry polling error: HTTP {res.status_code}: {res.text}"
                            retry_finished_jobs.append(job_id)
                    
                    for job_id in retry_finished_jobs:
                        del retry_jobs_to_poll[job_id]
                    
                    if retry_jobs_to_poll:
                        gevent.sleep(3)  # 重试轮询间隔稍短一些
                    retry_attempt += 1
                
                # 处理重试轮询超时
                for job_id, file_data in retry_jobs_to_poll.items():
                    file_data['status'] = 'retry_timeout'
                    file_data['error'] = f"{file_data.get('error', '')} | Retry polling timed out"

    # 4. 返回结果（无需清理临时文件）
    final_results = {}
    for file_data in files_info:
        # 使用原始URL作为key，确保调用方能找到对应的结果
        original_url = file_data.get('original_url', file_data['url'])
        if file_data.get('status') == 'completed':
            final_results[original_url] = (True, file_data.get('content'))
        else:
            final_results[original_url] = (False, file_data.get('error', 'Unknown error'))
    
    return final_results


def parse_multiple_files_grequests(files_to_process, max_pages=8, direct=False):
    """
    使用grequests并发处理多个文件
    
    Args:
        files_to_process: 要处理的文件列表
        max_pages: 最大页面数
        direct: 是否直接使用URL而不下载文件
    """
    if direct:
        print("使用直接URL模式处理文件...")
        return parse_multiple_files_grequests_direct(files_to_process, max_pages)
    else:
        print("使用下载模式处理文件...")
        return parse_multiple_files_grequests_download(files_to_process, max_pages)


def parse_multiple_files_grequests_download(files_to_process, max_pages=8):
    """
    使用grequests并发处理多个文件（原有的下载模式）
    """
    # 1. 同步下载所有文件，并为每个文件分配一个固定的API key
    local_files_info = []
    for file_info in files_to_process:
        url = file_info.get("url")
        file_type = "pdf"
        rdp_key = file_info.get("key")  # 获取RDP key
        if not url:
            continue
        
        # 使用带fallback的下载函数
        success, result = download_file_from_url_with_fallback(url, rdp_key, f".{file_type}")
        api_key = get_random_api_key()
        
        if success:
            temp_path = result
            print(f"Successfully downloaded {url}")
            local_files_info.append({
                "url": url,
                "type": file_type,
                "local_path": temp_path,
                "status": "downloaded",
                "api_key": api_key,  # 固定的API key
                "rdp_key": rdp_key
            })
        else:
            error_msg = result
            print(f"Failed to download {url}: {error_msg}")
            local_files_info.append({
                "url": url,
                "type": file_type,
                "local_path": None,
                "status": "download_failed",
                "error": error_msg,
                "api_key": api_key,  # 即使失败也分配key保持一致性
                "rdp_key": rdp_key
            })

    # 2. 并发上传已成功下载的文件
    upload_requests = []
    opened_files = []
    files_for_upload = [f for f in local_files_info if f['status'] == 'downloaded']

    base_url = "https://api.cloud.llamaindex.ai/api/v1/parsing"
    upload_url = f"{base_url}/upload"

    for file_data in files_for_upload:
        # 使用该文件固定的API key
        headers = {"Authorization": f"Bearer {file_data['api_key']}"}
        
        f = open(file_data['local_path'], "rb")
        opened_files.append(f)
        
        mime_type = mimetypes.guess_type(file_data['local_path'])[0] or 'application/octet-stream'
        file_tuple = (os.path.basename(file_data['local_path']), f, mime_type)
        
        req = grequests.post(
            upload_url,
            headers=headers,
            files={"file": file_tuple},
            data={"fast_mode": "true", "max_pages": str(max_pages)}
        )
        upload_requests.append(req)

    # 添加异常处理
    try:
        upload_responses = grequests.map(upload_requests, exception_handler=lambda req, exc: None)
    except Exception as e:
        print(f"Upload grequests.map failed: {e}")
        upload_responses = [None] * len(upload_requests)
    
    for f in opened_files:
        f.close()

    # 处理上传结果
    for i, res in enumerate(upload_responses):
        file_data = files_for_upload[i]
        if res is None:
            file_data['status'] = 'upload_failed'
            file_data['error'] = "Upload request returned None - likely timeout or connection error"
            print(f"Failed to upload {file_data['url']}: {file_data['error']}")
        elif res.status_code == 200:
            # 检查响应内容是否包含AccessDenied错误（支持XML和文本格式）
            response_text = res.text
            if ("AccessDenied" in response_text or 
                "Access denied" in response_text or 
                "<Code>AccessDenied</Code>" in response_text or
                "<?xml" in response_text and "Error" in response_text):
                file_data['status'] = 'upload_failed'
                file_data['error'] = f"URL expired or access denied: {response_text[:200]}..."
                print(f"Failed to upload {file_data['url']}: URL expired or access denied")
                # 下载模式暂不支持RDP key重试，因为文件已经下载到本地
            else:
                try:
                    file_data['status'] = 'uploaded'
                    file_data['job_id'] = res.json()['id']
                    print(f"Successfully uploaded {file_data['url']}, job_id: {file_data['job_id']}")
                except (ValueError, KeyError) as e:
                    # JSON解析失败，可能是错误响应
                    file_data['status'] = 'upload_failed'
                    file_data['error'] = f"Invalid response format: {response_text[:200]}..."
                    print(f"Failed to parse response for {file_data['url']}: {file_data['error']}")
        else:
            file_data['status'] = 'upload_failed'
            file_data['error'] = f"HTTP {res.status_code}: {res.text}"
            print(f"Failed to upload {file_data['url']}: {file_data['error']}")
    
    # 3. 并发轮询解析结果
    jobs_to_poll = {
        f['job_id']: f 
        for f in files_for_upload if f.get('status') == 'uploaded'
    }
    
    max_attempts = 120  # 增加到120次，约6分钟
    attempt = 0
    gevent.sleep(10) # 初始等待时间增加到10秒

    while jobs_to_poll and attempt < max_attempts:
        poll_requests = []
        job_ids_in_batch = list(jobs_to_poll.keys())
        
        for job_id in job_ids_in_batch:
            file_data = jobs_to_poll[job_id]
            # 使用该文件固定的API key
            headers = {"Authorization": f"Bearer {file_data['api_key']}"}
            result_url = f"{base_url}/job/{job_id}/result/text"
            poll_requests.append(grequests.get(result_url, headers=headers))

        # 添加异常处理和超时设置
        try:
            poll_responses = grequests.map(poll_requests, size=len(poll_requests), exception_handler=lambda req, exc: None)
        except Exception as e:
            print(f"grequests.map failed: {e}")
            poll_responses = [None] * len(poll_requests)
        
        finished_jobs = []
        for i, res in enumerate(poll_responses):
            job_id = job_ids_in_batch[i]
            file_data = jobs_to_poll[job_id]
            
            if res is None:
                print(f"Request for job {job_id} ({file_data['url']}) returned None - likely timeout or connection error")
                continue  # 不标记为失败，继续重试
            elif res.status_code == 200:
                try:
                    response_data = res.json()
                    content = response_data.get('text', '')
                    
                    # 检查内容是否包含AccessDenied错误（支持XML和文本格式）
                    if ("AccessDenied" in content or 
                        "Access denied" in content or 
                        "<Code>AccessDenied</Code>" in content or
                        "<?xml" in content and "Error" in content):
                        print(f"Job {job_id} for {file_data['url']} returned access denied error.")
                        file_data['status'] = 'polling_failed'
                        file_data['error'] = f"URL expired or access denied in result: {content[:200]}..."
                        finished_jobs.append(job_id)
                    else:
                        print(f"Job {job_id} for {file_data['url']} finished.")
                        file_data['status'] = 'completed'
                        file_data['content'] = content
                        finished_jobs.append(job_id)
                except (ValueError, KeyError) as e:
                    print(f"Failed to parse response for job {job_id} ({file_data['url']}): {e}")
                    file_data['status'] = 'polling_failed'
                    file_data['error'] = f"Invalid response format: {res.text[:200]}..."
                    finished_jobs.append(job_id)
            elif res.status_code == 202 or res.status_code == 404:
                print(f"Job {job_id} for {file_data['url']} still processing... (status: {res.status_code})")
            else:
                print(f"Polling failed for job {job_id} ({file_data['url']}): {res.status_code} - {res.text}")
                file_data['status'] = 'polling_failed'
                file_data['error'] = f"HTTP {res.status_code}: {res.text}"
                finished_jobs.append(job_id)

        for job_id in finished_jobs:
            del jobs_to_poll[job_id]
        
        if jobs_to_poll:
            gevent.sleep(5)  # 增加轮询间隔到5秒
        attempt += 1

    # 处理超时
    for job_id, file_data in jobs_to_poll.items():
        file_data['status'] = 'timeout'
        file_data['error'] = 'Polling timed out'

    # 4. 清理并返回结果
    final_results = {}
    for file_data in local_files_info:
        if file_data['local_path'] and os.path.exists(file_data['local_path']):
            os.unlink(file_data['local_path'])
        
        url = file_data['url']
        if file_data.get('status') == 'completed':
            final_results[url] = (True, file_data.get('content'))
        else:
            final_results[url] = (False, file_data.get('error', 'Unknown error'))
    
    return final_results

# 简单的API key随机选择函数
def get_random_api_key():
    """从配置的API key池中随机选择一个key"""
    from config import LLAMA_CLOUD_API_KEY_POOL
    selected_key = random.choice(LLAMA_CLOUD_API_KEY_POOL)
    
    return selected_key

def parse_pdf_with_llamaparse(file_path, target_pages=None, max_pages=8, file_type="pdf", rdp_key=None):
    """
    使用LlamaParse API解析文件
    
    Args:
        file_path (str): 文件URL（远程URL，不包含文件扩展名）
        target_pages (str): 指定要解析的页面，例如 "0,1,2,3"
        max_pages (int): 最大解析页面数，默认为8
        file_type (str): 文件类型，默认为"pdf"
        rdp_key (str): RDP服务的永久有效key，用作fallback
        
    Returns:
        tuple: (success: bool, content: str)
            - 如果成功: (True, 解析后的文本内容)
            - 如果失败: (False, 错误信息)
    """
    api_key = get_random_api_key()
    headers = {"Authorization": f"Bearer {api_key}"}
    base_url = "https://api.cloud.llamaindex.ai/api/v1/parsing"
    temp_file_path = None
    
    try:
        # 1. 根据file_type添加文件扩展名并下载文件
        upload_url = f"{base_url}/upload"
        file_extension = f".{file_type}"
        
        print(f"正在处理URL: {file_path}")
        print(f"根据file_type '{file_type}' 添加扩展名: {file_extension}")
        
        # 使用带fallback的下载文件到临时位置
        success, result = download_file_from_url_with_fallback(file_path, rdp_key, file_extension)
        if not success:
            return False, f"下载文件失败: {result}"
        temp_file_path = result
        
        # 使用下载的临时文件上传
        with open(temp_file_path, "rb") as f:
            mime_type = mimetypes.guess_type(temp_file_path)[0]
            files = {"file": (f"downloaded_file{file_extension}", f, mime_type)}
            data = {
                "fast_mode": "true",
                "max_pages": str(max_pages)
            }
            
            # 如果指定了target_pages，添加到data中
            if target_pages:
                data["target_pages"] = target_pages
            
            print(f"正在上传下载的文件...")
            response = requests.post(upload_url, headers=headers, files=files, data=data)
            response.raise_for_status()
            print("✅ 文件上传成功！")
        
        # 2. 获取job_id
        job_id = response.json()["id"]
        print(f"上传成功，Job ID: {job_id}")
        
        # 3. 轮询结果直到完成
        result_type = "text"  # 可以是 "text" 或 "markdown"
        result_url = f"{base_url}/job/{job_id}/result/{result_type}"
        
        print("正在等待解析完成...")
        max_attempts = 60  # 最大等待时间约2分钟
        attempt = 0
        
        # 先等待一段时间让job endpoint准备好
        print("等待job endpoint准备...")
        time.sleep(5)
        
        while attempt < max_attempts:
            attempt += 1
            
            response = requests.get(result_url, headers=headers)
            
            if response.status_code == 200:
                print("解析完成！")
                result = response.json()
                return True, result[result_type]
            elif response.status_code == 202:
                print(f"解析中... (尝试 {attempt}/{max_attempts})")
                time.sleep(3)  # 解析中时等待3秒
                continue
            elif response.status_code == 404:
                # 404可能表示job endpoint还没准备好，继续等待
                print(f"Job endpoint未准备好，继续等待... (尝试 {attempt}/{max_attempts})")
                time.sleep(5)  # 404时等待更长时间
                continue
            else:
                error_msg = f"检查状态时出错: {response.status_code} - {response.text}"
                print(error_msg)
                return False, error_msg
        
        error_msg = "解析超时，请稍后重试"
        print(error_msg)
        return False, error_msg
        
    except requests.exceptions.RequestException as e:
        error_msg = f"API请求错误: {e}"
        print(error_msg)
        return False, error_msg
    except FileNotFoundError:
        error_msg = f"文件未找到: {file_path}"
        print(error_msg)
        return False, error_msg
    except Exception as e:
        error_msg = f"解析过程中出现错误: {e}"
        print(error_msg)
        return False, error_msg
    finally:
        # 清理临时文件
        if temp_file_path and os.path.exists(temp_file_path):
            try:
                os.unlink(temp_file_path)
                print(f"已清理临时文件: {temp_file_path}")
            except Exception as e:
                print(f"清理临时文件时出错: {e}")