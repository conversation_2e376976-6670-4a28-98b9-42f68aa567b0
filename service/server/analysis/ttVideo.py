from service.server.analysis.news_and_event import get_prompt, topic_list, chat_gpt, adjust_overall_score
from service.server.mm_understand.understand_video import batch_video_understand


def analyze_tt_video(profile, plan_list, tt_list, source):
    """
    :param profile: dict, 用户profile信息,可能为空
    :param plan_list: dict, 用户设定的plan计划信息，可能为空
    :param tt_list: list, tt视频列表
    :param source: number, 2 来自trending, 5 来自rival
    """
    company_info = profile.get('companyInfo', {})
    profile_info = profile.get('profileInfo', {})
    if not (profile_info or plan_list):
        return False, 'profile and plan should not be empty in the same time'
    # get plans from plan_list
    plans, ori_plan_id = organize_plan_text(plan_list)
    # get video information
    information, video_list, ori_video_id = organize_video_text(tt_list)
    if len(ori_video_id)< 1:
        return False, f"Error in getting video analysis result{video_list}"
    data = {
        'name': company_info.get('company', ''),
        'introduction': profile_info.get('introduction', ''),
        'keywords': profile_info.get('subscribeKeywords', []),
        'businessArea': profile_info.get('businessArea', ''),
        'productFeatures': profile_info.get('productFeatures', []),
        'brandValues': profile_info.get('brandValues', ''),
        'targetAudience': profile_info.get('targetAudience', []),
        'marketPosition': profile_info.get('marketPosition', ''),
        'competitor': profile_info.get('competitor', []),
        'recentNews': profile_info.get('recentNews', []),
        'marketCases': profile_info.get('marketCases', []),
        'plans': plans,
        'information': information
    }
    sys_prompt =  """You are a senior marketing strategy expert who is good at converting information into brand marketing opportunities. You have been asked to analyze the social media related to the user's business."""
    user_prompt = get_prompt('user/analysis/ttVideo.j2', data)
    json_schema = obtain_json_schema(ori_plan_id, ori_video_id)
    status, result = chat_gpt(sys_prompt, user_prompt, json_schema=json_schema)
    if not status:
        return False, f"gpt error: {result}"
    output_list = result.get('schedule_and_suggestion_list', [])
    if not output_list:
        return True, {'todoVideoTaskList': []}
    return_dict = output_list[0]
    tt_index = return_dict.get('video_index', 0) - 1
    if tt_index < 0:
        return False, f"wrong tt index: {return_dict}"
    video_detail = video_list[tt_index]
    return_dict['originalContent'] = video_detail.get('contentBody', 'bad')
    return_dict['originalMediaList'] = video_detail.get('mediaList', ['bad'])
    return_dict['analysisResult'] = video_detail.get('analysisResult', {'bad'})
    return_dict['platform'] = video_detail.get('platform', 'bad')
    return_dict['contentId'] = video_detail.get('contentId', 'bad')
    return_dict['PostTime'] = video_detail.get('postTime', 'bad')
    author = video_detail.get('author', {})
    return_dict['authorName'] = author.get('authorName', 'bad')
    return_dict['authorAvatar'] = author.get('authorAvatar', 'bad')
    plan_id = return_dict.get('plan_id', -1)
    if plan_id > -1:
        for plan in plan_list:
            if plan['planId'] == plan_id:
                return_dict['planInfo'] = plan
                break
    return_dict['reason'] = adjust_overall_score(return_dict.get('reason', {}))
    return True, {'todoVideoTaskList': [return_dict]}


def organize_plan_text(plan_list):
    if len(plan_list) < 1:
        return None, [-1]
    text = """"""
    plan_id_list = [-1]
    for plan in plan_list:
        plan_topic_index = plan.get('planTopic', 0)
        if not plan_topic_index:
            continue
        plan_topic = topic_list[int(plan_topic_index) - 1]
        plan_description = plan.get('planDescription', '')
        plan_id = plan.get('planId', '')
        text += f"""Plan_id: {plan_id}\n    Topic: {plan_topic['topic']}({plan_topic['definition']})\n    Description: {plan_description}\n"""
        plan_id_list.append(plan_id)
    return text, plan_id_list


def organize_video_text(tt_list):
    # 将single tt中的mediaList的所有字段复制到single tt中
    for tt in tt_list:
        tt.update(tt.get('mediaList', [])[0])
    # 请求视频理解接口
    result = batch_video_understand(tt_list)
    text = """"""
    video_id = []
    for i, tt in enumerate(result):
        if 'error' in tt:
            continue
        analysis_result = result[i].get('analysisResult', {})
        sound_analysis = analysis_result.get('soundAnalysis', {})
        content_analysis = analysis_result.get('contentAnalysis', {})
        text += f"""Video {i+1}:\n    Main Content: {content_analysis.get('mainContent', '')}\n    Core Themes: {content_analysis.get('coreThemes', '')}\n    Narration Tone: {sound_analysis.get('narrationTone', '')}\n    Script Style: {content_analysis.get('scriptStyle', '')}\n"""
        video_id.append(i+1)
    return text, result, video_id


def obtain_json_schema(ori_plan_id, ori_video_id):
    json_schema = {
        "name": "video_analysis_for_company_and_marketing",
        "description": "analyze the video for the company and marketing",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "schedule_and_suggestion_list": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "title": {
                                "type": "string",
                                "description": "The title of the selected video"
                            },
                            "video_index": {
                                "type": "number",
                                "description": "The index of the selected video",
                                "enum": ori_video_id
                            },
                            "reason": {
                                "type": "object",
                                "description": "The reason why the video is selected",
                                "properties": {
                                    "overallScore": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the overall score"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the overall score"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    },
                                    "brandFit": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the brand fit"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the brand fit"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    },
                                    "industryRelevance": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the industry relevance"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the industry relevance"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    },
                                    "audienceMatch": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the audience match"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the audience match"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    },
                                    "contentTimeliness": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the content timeliness"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the content timeliness"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    },
                                    "riskAssessment": {
                                        "type": "object",
                                        "properties": {
                                            "score": {
                                                "type": "number",
                                                "description": "The score of the risk assessment"
                                            },
                                            "description": {
                                                "type": "string",
                                                "description": "The description of the risk assessment"
                                            }
                                        },
                                        "required": ["score", "description"],
                                        "additionalProperties": False
                                    }
                                },
                                "required": ["overallScore", "brandFit", "industryRelevance",
                                             "audienceMatch", "contentTimeliness", "riskAssessment"
                                             ],
                                "additionalProperties": False
                            },
                            "function": {
                                "type": "number",
                                "description": "The type of which kind the reply is selected into, 1 for 'Schedule', 2 for 'suggestion' ",
                                "enum": [1, 2]
                            },
                            "plan_id": {
                                "type": "number",
                                "description":  "The id of the plan which the video is allocated to. Required if function=1, else should be -1.",
                                "enum": ori_plan_id,
                            }
                        },
                        "required": ["title", "video_index", "reason", "function", "plan_id"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["schedule_and_suggestion_list"],
            "additionalProperties": False
        }
    }
    return json_schema


if __name__ == "__main__":
    pass

