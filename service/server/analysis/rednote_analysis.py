# from service.lib.media_llm import gpt_with_media
from service.server.call_gpt import CallGpt
from service.lib.prompt_fusion import get_prompt
from config import GEMINI_PRO_MODEL, GEMINI_FLASH_MODEL
import json
from colorama import Fore
import math

prompt_gpt = CallGpt(model="gpt-4.1", temperature=0.7)
gemini_json_schema = {
    "type": "array",
    "items": {
        "type": "object",
        "properties": {
            "index": {
                "type": "integer",
                "description": "帖子索引"
            },
            "potentialScore": {
                "type": "integer",
                "description": "潜力得分"
            },
            "userProfile": {
                "type": "string",
                "description": "用户简介，包含定位、内容风格及可能痛点"
            },
            "hookMessage": {
                "type": "string",
                "description": "针对该用户的沟通引导话术"
            }
        },
        "required": ["index", "potentialScore", "userProfile", "hookMessage"]
    }
}


def gpt_json_schema():

    return {
        "name": "rednote_filter",
        "schema": {
            "type": "object",
            "properties": {
                "result": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "index": {
                                "type": "integer",
                                "description": "帖子索引"
                            },
                            "potentialScore": {
                                "type": "integer",
                                "description": "潜力得分，通常基于内容质量、商业意图和互动潜力综合评估"
                            },
                            "userProfile": {
                                "type": "string",
                                "description": "用户简介，概述其定位、内容风格及可能存在的痛点"
                            },
                            "hookMessage": {
                                "type": "string",
                                "description": "为该用户量身定制的沟通引导话术"
                            }
                        },
                        "required": ["index", "potentialScore", "userProfile", "hookMessage"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["result"]
        }
    }


def g_call_llm_model(system_prompts, user_prompts, model="gemini", watttraceid=None, temperature=1.0, json_object=False):
    """
    Calls the appropriate LLM model for batch requests
    """
    if model == "gpt-4o":
        model_ = "gpt-4o-2024-11-20"
        json_schema = None
        if json_object:
            json_schema = gpt_json_schema()
        return prompt_gpt.gcallOpenaiGpt(system_prompts, user_prompts, model=model_,
                                         watttraceid=watttraceid, temperature=temperature, json_object=json_object, json_schema=json_schema)

    elif model == "gpt-4.1" or model == "gpt":
        model_ = "gpt-4.1"
        json_schema = None
        if json_object:
            json_schema = gpt_json_schema()
        return prompt_gpt.gcallOpenaiGpt(system_prompts, user_prompts, model=model_,
                                         watttraceid=watttraceid, temperature=temperature, json_object=json_object, json_schema=json_schema)

    elif model == "gpt-4o-mini":
        model_ = "gpt-4o-mini"
        json_schema = None
        if json_object:
            json_schema = gpt_json_schema()
        return prompt_gpt.gcallOpenaiGpt(system_prompts, user_prompts, model=model_,
                                         watttraceid=watttraceid, temperature=temperature, json_object=json_object, json_schema=json_schema)
    elif model == "gemini-2.5-pro" or model == "gemini-pro":
        model_ = GEMINI_PRO_MODEL
        json_schema = None
        if json_object:
            json_schema = gemini_json_schema
        return prompt_gpt.gCallGCPGemini(system_prompts, user_prompts, model=model_, max_out_tokens=4096,
                                         watttraceid=watttraceid, temperature=temperature, json_schema=json_schema)
    elif model == "gemini-2.5-flash" or model == "gemini-flash" or model == "gemini":
        model_ = GEMINI_FLASH_MODEL
        json_schema = None
        if json_object:
            json_schema = gemini_json_schema
        return prompt_gpt.gCallGCPGemini(system_prompts, user_prompts, model=model_, max_out_tokens=4096,
                                         watttraceid=watttraceid, temperature=temperature, json_schema=json_schema)
    else:
        return False, f"Error: Invalid model: {model}"


def prepare_prompt_for_batch(batch_notes, keywords, welfare):
    """Prepare system and user prompts for a batch of notes"""
    note_content = []
    index_to_id_mapping = {}
    for ind, note_info in enumerate(batch_notes):
        note_item = {
            "index": ind,
            "title": note_info['title'],
            "nickname": note_info['nickname'],
            "description": note_info['desc']
        }
        
        if not keywords and 'keyword' in note_info:
            note_item["keyword"] = note_info['keyword']
            
        note_content.append(note_item)
        index_to_id_mapping[ind] = note_info['noteid']

    # prompt construction
    data = {
        "note_content": note_content,
        "keywords": keywords if keywords else None,
        "welfare": welfare if welfare else None
    }

    sys_prompt = get_prompt('system/profile/rednote_filter.j2', data)
    user_prompt = get_prompt('user/profile/rednote_filter.j2', data)

    return sys_prompt, user_prompt, index_to_id_mapping


def analyze_rednote(note_list: list, keywords: list, welfare: str = None, model: str = "gpt", 
                    max_part_size: int = 500, max_group_size: int = 10, watttraceid: str = ""):
    """
    Process notes in sequential parts, with each part processed in concurrent groups

    Args:
        note_list: List of notes to analyze
        keywords: List of keywords for filtering
        model: LLM model to use
        welfare: Welfare type for filtering
        max_part_size: Maximum size for each sequential part (will be balanced if total size > max_part_size)
        max_group_size: Maximum number of concurrent groups in one part
        watttraceid: Watt trace ID for logging

    Returns:
        Tuple of (success_status, results or error message)
    """
    if not note_list:
        return True, []

    # Calculate optimal part sizes for sequential processing
    total_notes = len(note_list)

    if total_notes <= max_part_size:
        print(Fore.GREEN +
              f"Processing {total_notes} notes in 1 part" + Fore.RESET)
        return process_part(note_list, keywords, welfare, model, max_group_size, watttraceid)

    # Calculate optimal part size for balanced distribution
    num_parts = math.ceil(total_notes / max_part_size)
    if num_parts > 1 and total_notes % max_part_size > 0:
        # If we have remainder, balance the parts for more even distribution
        part_size = math.ceil(total_notes / num_parts)
    else:
        part_size = max_part_size

    print(
        f"Processing {total_notes} notes in {num_parts} sequential parts of ~{part_size} each")

    # Split notes into parts (processed sequentially)
    parts = []
    for i in range(0, total_notes, part_size):
        parts.append(note_list[i:i + part_size])

    # Process each part sequentially, combining results
    all_results = []

    for part_idx, part in enumerate(parts):
        print(
            f"Processing part {part_idx+1}/{len(parts)} with {len(part)} notes")
        status, results = process_part(
            part, keywords, welfare, model, max_group_size, watttraceid)

        if status:
            all_results.extend(results)
        else:
            print(
                Fore.RED + f"Error processing part {part_idx+1}: {results}" + Fore.RESET)
            # Continue processing other parts even if one fails
            continue

    if not all_results:
        return False, "Failed to process any parts successfully"

    return True, all_results


def process_part(note_part, keywords, welfare, model, max_group_size, watttraceid):
    """
    Process a single part of notes with grouped concurrent API calls

    Args:
        note_part: Part of notes to process
        keywords: List of keywords for filtering
        welfare: Welfare type for filtering
        model: LLM model to use
        max_group_size: Maximum number of concurrent groups
        watttraceid: watt trace ID for logging

    Returns:
        Tuple of (success_status, results or error message)
    """
    # Calculate group size for concurrent processing
    total_notes = len(note_part)

    # Determine optimal batch size to split the part into groups
    # Each group will be processed concurrently
    batch_size = math.ceil(total_notes / max_group_size)

    # Split the part into groups/batches
    batches = []
    for i in range(0, total_notes, batch_size):
        batches.append(note_part[i:i + batch_size])

    print(
        f"  Splitting into {len(batches)} concurrent groups of ~{batch_size} notes each")

    # Prepare all prompts for the groups in this part
    system_prompts = []
    user_prompts = []
    index_mappings = []

    for batch in batches:
        sys_prompt, user_prompt, index_mapping = prepare_prompt_for_batch(
            batch, keywords, welfare)
        system_prompts.append(sys_prompt)
        user_prompts.append(user_prompt)
        index_mappings.append(index_mapping)

    # Call the grouped API for concurrent processing
    status, responses = g_call_llm_model(system_prompts, user_prompts, model=model,
                                         watttraceid=watttraceid, json_object=True)

    if not status:
        return False, responses

    # Process responses from all groups
    all_results = []
    for j, response in enumerate(responses):
        try:
            json_response = json.loads(response)
            
            # Extract result array if present
            items_to_process = []
            if isinstance(json_response, dict) and "result" in json_response:
                items_to_process = json_response["result"]
            elif isinstance(json_response, list):
                items_to_process = json_response
            else:
                print(Fore.RED + f"Unexpected JSON structure in response from group {j}" + Fore.RESET)
                continue
                
            # Process each item
            for item in items_to_process:
                if 'hookMessage' in item:
                    item['hookMessage'] = item['hookMessage'].replace('[WELFARE_PLACEHOLDER]', welfare)
                if 'index' in item and item['index'] in index_mappings[j]:
                    item['noteid'] = index_mappings[j][item['index']]
                del item['index']  # 删除index字段
            all_results.extend(items_to_process)
        except Exception as e:
            print(
                Fore.RED + f"Error parsing JSON response from group {j}: {e}" + Fore.RESET)

    if not all_results:
        return False, "Failed to process any groups in this part successfully"

    return True, all_results
