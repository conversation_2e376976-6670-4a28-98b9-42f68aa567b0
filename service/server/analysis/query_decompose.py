from service.server.call_gpt import CallGpt
from service import prompts_fusion
import json
from datetime import datetime, timezone


prompt_gpt = CallGpt(model="gpt-4.1", temperature=0.2)
json_schema = {
  "name": "query_decompose",
  "strict": True,
  "schema": {
    "type": "object",
    "properties": {
        "queryContent": {
            "type": "string",
            "description": "The core query content."
        },
        "mediaType": {
        "type": "array",
        "items": {
            "type": "string",
            "enum": ["video", "image", "text"]
        }
        },
        "platform": {
        "type": "array",
        "items": {
            "type": "string",
            "enum": ["tiktok", "x", "shorts", "youtube", "others"]
        }
        },
        "startDate": {
        "type": "string",
        "description": "The start date of the query UTC format: yyyy-MM-dd'T'HH:mm:ssXXX"
        },
        "endDate": {
        "type": "string",
        "description": "The end date of the query UTC format: yyyy-MM-dd'T'HH:mm:ssXXX"
        },
        "author": {
            "type": "array",
            "items": {
                "type": "string",
                "description": "The author of the query"
            }
        }
    },
    "required": ["queryContent", "mediaType", "platform", "startDate", "endDate", "author"],
    "additionalProperties": False
  }
}


def query_decompose(query_input: str, watttraceid: str):
    # UTC date format: yyyy-MM-dd'T'HH:mm:ssXXX ("2024-07-19T01:28:26Z")
    start_time_str = datetime.now(timezone.utc).strftime("%Y-%m-%dT%H:%M:%S%z")
    input_data = {
        "input": query_input,
        "current_time": start_time_str
    }
    user_prompt = prompts_fusion.get_query_decompose_user(input_data)
    system_prompt = prompts_fusion.get_query_decompose_system(input_data)
    status_, response = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, json_object=True, json_schema=json_schema)
    # status_, response = prompt_gpt.callGCPGemini(system_prompt, user_prompt, model="gemini-1.5-pro", temperature=0.2, watttraceid=watttraceid, json_schema=json_schema)
    if not status_:
        return False, response
    
    try:
        print(response)
        reply_msg = json.loads(response)
        return True, reply_msg
    except json.JSONDecodeError as e:
        return False, f"Error loading json: {e} {response}"

