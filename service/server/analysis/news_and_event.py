from service.server.analysis.get_video_analysis import get_prompt
from service.lib.media_llm import chat_gpt
import requests
from service import WATT_REC_HOST


def analyze_news_or_event_wrapper(profile, plan_info, source, user_id):
    if source != 5:
        return analyze_news_or_event(profile, plan_info, source, user_id)
    profile_info = profile.get('profileInfo', {})
    competitor = profile_info.get('competitor', [])
    if not competitor:
        return False, 'no competitor'
    return_dict = {'todoGenTaskList': []}
    error = []
    for i in competitor:
        keywords = i.get("subscribeKeywords", '')
        if not keywords:
            continue
        status, result = analyze_news_or_event(profile, plan_info, source, user_id, keywords)
        if status:
            return_dict['todoGenTaskList'].extend(result["todoGenTaskList"])
        else:
            error.append(result)
    if len(return_dict['todoGenTaskList']) < 1:
        return False, str(error)
    return_dict['todoGenTaskList'] = remove_duplicate_content(return_dict['todoGenTaskList'])
    return True, return_dict


def analyze_news_or_event(profile, plan_info, source, user_id, keywords=None):
    """"
    :param profile: dict, 用户profile信息,可能为空
    :param plan_info: dict, 用户设定的plan计划信息，可能为空
    :param source: number, 1 来自资讯 2 来自trending, 5 来自竞争对手
    :param user_id: number, 用户id
    :param keywords: 用来搜索咨询的
    """
    company_info = profile.get('companyInfo', {})
    profile_info = profile.get('profileInfo', {})
    if not (profile_info or plan_info):
        return False, 'profile and plan should not be empty in the same time'
    plan_topic_index = plan_info.get('planTopic', 0)
    if not plan_topic_index:
        plan_topic = {}
    else:
        plan_topic = topic_list[int(plan_topic_index) - 1]
    plan_description = plan_info.get('planDescription', '')
    if not keywords:
        keywords = profile_info.get('subscribeKeywords', [])
    status, information_list = get_news_or_event(keywords, user_id, source)
    if not status:
        return False, f'error: {information_list}, userId {user_id} in getting news or event.source{source}, {keywords}'
    if source == 5:
        source = 1
    information_string = ''
    infor_index_list = []
    if source == 1:
        for index, body in enumerate(information_list):
            single_title = body.get('title', '')
            single_content = body.get('contentBody', '')
            if len(single_title) > 10:
                if len(single_content) > 10:
                    information_string += f'    {index + 1}. {single_title}:\n      {single_content}\n'
                    infor_index_list.append(index + 1)
                else:
                    information_string += f'    {index + 1}. {single_title}\n'
                    infor_index_list.append(index + 1)
            elif len(single_content) > 10:
                information_string += f'    {index + 1}. {single_content}\n'
                infor_index_list.append(index + 1)
    else:
        for index, body in enumerate(information_list):
            information_string += f"    {index + 1}. Event: {body['hotspotContent']} , with summary: {body['summary']}\n"
            infor_index_list.append(index + 1)

    data = {
        'name': company_info.get('company', ''),
        'introduction': profile_info.get('introduction', ''),
        'keywords': keywords,
        'businessArea': profile_info.get('businessArea', ''),
        'productFeatures': profile_info.get('productFeatures', []),
        'brandValues': profile_info.get('brandValues', ''),
        'targetAudience': profile_info.get('targetAudience', []),
        'marketPosition': profile_info.get('marketPosition', ''),
        'competitor': [{"company": item["company"], "details": item["details"]} for item in profile_info.get('competitor', [])],
        'recentNews': [{"title": item["title"], "content": item["content"]} for item in profile_info.get('recentNews', [])],
        'marketCases': [{"title": item["title"], "content": item["content"]} for item in profile_info.get('marketCases', [])],
        'plan_topic': plan_topic.get('topic', ''),
        'topic_definition': plan_topic.get('definition', ''),
        'plan_description': plan_description,
        'source': source,
        'information': information_string
    }
    sys_prompt = """You are a senior marketing strategy expert who is good at converting information and hot events into brand marketing opportunities and providing customers with accurate and persuasive marketing plans. You have been asked to analyze the information or events related to the user's business."""
    user_prompt = get_prompt('user/analysis/news_and_event.j2', data)
    json_schema = get_json_schema(infor_index_list)
    status, result = chat_gpt(sys_prompt, user_prompt, json_schema=json_schema)
    if not status:
        return False, f"gpt error: {result} "
    return_dict = {'todoGenTaskList': []}
    output_list_ = result.get('schedule_and_suggestion_list', [])
    if len(output_list_) < 1:
        return True, return_dict
    output_list = [item for item in output_list_ if 1 <= item.get('material_index') <= len(information_list)]
    if not profile_info:
        # 如果用户没有profile信息，没有suggestion, 只处理schedule
        for i in output_list:
            origin_item = information_list[int(i.get('material_index')) - 1]
            if i.get('function') == 1:
                i['originalMediaList'] = normalize_media_list(origin_item.get('mediaList', []))
                i['contentId'] = origin_item.get('id', '') if source == 1 else origin_item.get('hotspotId', '')
                if source == 1:
                    i["contentPostTime"] = origin_item.get("postTime", '')
                    i["userName"] = origin_item.get('author', '').get("authorName", '')
                    i["avatarUrl"] = origin_item.get('author', '').get("authorAvatar", '')
                return_dict['todoGenTaskList'].append(i)
    elif not plan_topic_index:
        # 如果用户没有plan信息，没有schedule, 只处理suggestion
        for i in output_list:
            origin_item = information_list[int(i.get('material_index')) - 1]
            if i.get('function') == 2:
                i['originalMediaList'] = normalize_media_list(origin_item.get('mediaList', []))
                i['contentId'] = origin_item.get('id', '') if source == 1 else origin_item.get('hotspotId', '')
                if source == 1:
                    i["contentPostTime"] = origin_item.get("postTime", '')
                    i["userName"] = origin_item.get('author', '').get("authorName", '')
                    i["avatarUrl"] = origin_item.get('author', '').get("authorAvatar", '')
                return_dict['todoGenTaskList'].append(i)
    else:
        for i in output_list:
            origin_item = information_list[int(i.get('material_index')) - 1]
            i['originalMediaList'] = normalize_media_list(origin_item.get('mediaList', []))
            i['contentId'] = origin_item.get('id', '') if source == 1 else origin_item.get('hotspotId', '')
            if source == 1:
                i["contentPostTime"] = origin_item.get("postTime", '')
                i["userName"] = origin_item.get('author', '').get("authorName", '')
                i["avatarUrl"] = origin_item.get('author', '').get("authorAvatar", '')
            return_dict['todoGenTaskList'].append(i)
    return_dict['todoGenTaskList'] = remove_duplicate_content(return_dict['todoGenTaskList'])
    if source == 2:
        for i in return_dict['todoGenTaskList']:
            status, media_list = get_media_for_event(i['contentId'])
            if status:
                i["mediaList"] = media_list
            else:
                i["mediaList"] = []
    for i in return_dict['todoGenTaskList']:
        i['reason'] = adjust_overall_score(i['reason'])
    return True, return_dict


def get_news_or_event(keywords: list, user_id: int, source: int, num=10):
    """
    :param keywords: list, 关键词列表
    :param user_id: str, 用户id
    :param source: int, 1 来自资讯 2 来自trending
    :param num: int, 返回的数量
    :param data_filter: 过滤源
    """
    news_url = WATT_REC_HOST + "/api/v1/rec/information"
    event_url = WATT_REC_HOST + "/api/v1/rec/topicInfo"
    call_url = event_url if source == 2 else news_url
    headers = {"Content-Type": "application/json"}
    body = {"keywords": keywords, 'userId': user_id}
    if source == 5:
        body = {"keywords": keywords, 'userId': user_id, "platformsFilter": ["x"]}
    if source == 1:
        body = {"keywords": keywords, 'userId': user_id, "platformsFilter": ["other"]}
    try:
        response = requests.post(call_url, json=body, headers=headers, timeout=30)
        if response.status_code == 200:
            result = response.json().get("result", {}).get("data", {})
        else:
            return False, response.text
    except Exception as e:
        return False, str(e)
    if source == 2:
        hotspot_list = result.get('hotspotList', [])
        if len(hotspot_list) >= 5:
            return True, hotspot_list[:num]
        else:
            return False, 'Event less than 5'
    else:
        subscription_list = result.get('subscriptionList', [])
        if len(subscription_list) >= 5:
            return True, subscription_list[:num]
        else:
            return False, 'News less than 5'


def normalize_media_list(media_list):
    normalized_list = []
    for media in media_list:
        normalized_media = {
            "mediaType": "",
            "downloadSource": "",
            "imgPath": "",
            "imgUrl": "",
            "keyframePath": "",
            "keyframeUrl": "",
            "videoPath": "",
            "videoUrl": "",
            "durationMillis": 0
        }
        media_type = media.get("mediaType", "")
        normalized_media["mediaType"] = media_type
        if media_type == "image":
            normalized_media["imgUrl"] = media.get("mediaUrl", "")
        elif media_type == "video":
            normalized_media["videoPath"] = media.get("mediaPath", "")
            normalized_media["videoUrl"] = media.get("mediaUrl", "")
            normalized_media["keyframePath"] = media.get("mediaKeyframePath", "")
            normalized_media["keyframeUrl"] = media.get("mediaKeyframeUrl", "")
            normalized_media["durationMillis"] = media.get("durationMillis", 0)
        normalized_list.append(normalized_media)
    return normalized_list


def remove_duplicate_content(data):
    unique_content = {}
    result = []
    for item in data:
        content_id = item["contentId"]
        if content_id not in unique_content:
            unique_content[content_id] = True
            result.append(item)
    return result


def get_media_for_event(content_id):
    get_url = WATT_REC_HOST + "/api/v1/support/topic/relatedMedia"
    headers = {"Content-Type": "application/json"}
    body = {"hotspotId": content_id}
    error = []
    for i in range(1):
        try:
            response = requests.post(get_url, json=body, headers=headers, timeout=20)
            if response.status_code == 200:
                result = response.json().get("result", {}).get("data", {})
                return True, result["mediaList"][:5]
            else:
                error.append(response.text)
        except Exception as e:
            error.append(str(e))
    return False, error


def get_json_schema(ori_material_index):
    json_schema = {
            "name": "material_analysis_for_company_and_marketing",
            "description": "analyze the information or hot event for the company and marketing",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "schedule_and_suggestion_list": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "title": {
                                    "type": "string",
                                    "description": "The generated title"
                                },
                                "material_index": {
                                    "type": "number",
                                    "description": "The index of the selected material",
                                    "enum": ori_material_index,
                                },
                                "originalContent": {
                                    "type": "string",
                                    "description": "The original content of the selected material"
                                },
                                "reason": {
                                    "type": "object",
                                    "description": "The reason why the material is selected",
                                    "properties": {
                                        "overallScore": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the overall score"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the overall score"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        },
                                        "brandFit": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the brand fit"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the brand fit"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        },
                                        "industryRelevance": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the industry relevance"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the industry relevance"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        },
                                        "audienceMatch": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the audience match"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the audience match"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        },
                                        "contentTimeliness": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the content timeliness"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the content timeliness"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        },
                                        "riskAssessment": {
                                            "type": "object",
                                            "properties": {
                                                "score": {
                                                    "type": "number",
                                                    "description": "The score of the risk assessment"
                                                },
                                                "description": {
                                                    "type": "string",
                                                    "description": "The description of the risk assessment"
                                                }
                                            },
                                            "required": ["score", "description"],
                                            "additionalProperties": False
                                        }
                                    },
                                    "required": ["overallScore", "brandFit", "industryRelevance",
                                                 "audienceMatch", "contentTimeliness", "riskAssessment"
                                                 ],
                                    "additionalProperties": False
                                },
                                "function": {
                                    "type": "number",
                                    "description": "The type of which kind the mateiral is selected into, 1 for 'Schedule', 2 for 'Suggestion' ",
                                    "enum": [1, 2]
                                },
                                "point": {
                                    "type": "number",
                                    "description": "The score of the selected material"
                                },
                            },
                            "required": ["title", "material_index", "originalContent", "reason", "function", "point"],
                            "additionalProperties": False
                        }
                    }
                },
                "required": ["schedule_and_suggestion_list"],
                "additionalProperties": False
            }
        }
    return json_schema


def adjust_overall_score(reason):
    if not reason:
        return reason
    scores = [v["score"] for k, v in reason.items() if k != "overallScore"]
    avg_score = round(sum(scores) / len(scores))
    reason["overallScore"]["score"] = avg_score
    return reason


topic_list = [
        {
            "topic": "Culture publicity",
            "definition": "Promoting a company or brand's core values, mission, and cultural identity to build a stronger brand image and emotional connection with the target audience."
        },
        {
            "topic": "Product introduction",
            "definition": "Introducing and showcasing a new or existing product to inform potential customers of its features, benefits, and unique selling points, with the goal of driving interest and sales."
        },
        {
            "topic": "Knowledge sharing",
            "definition": "Providing valuable insights, educational content, and industry expertise to the audience to build credibility, trust, and long-term relationships with potential customers."
        },
        {
            "topic": "Daily interaction",
            "definition": "Engaging with the audience on a daily basis through social media posts, comments, or other forms of communication to foster a sense of community, increase brand visibility, and enhance customer loyalty."
        }
    ]