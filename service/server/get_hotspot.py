import config
import random
from utils.watt_service.call_watt_ai_service import CallAiService


call_rec = CallAiService(config.WATT_REC_HOST)

url_get_hot_keywords = "/api/v1/support/topic/hot"
url_rec_hot_topic_list = "/api/v1/rec/topic"
url_rec_tweet_list = "/api/v1/rec/topic/tweetDetail"
# url_rec_media = "/api/v1/support/tweet/relatedMedia"
url_rec_media = "/api/v1/search/media"


def find_content_by_id(data: list[dict], target_id: str, key: str):
    filtered = list(filter(lambda x: x["hotspotId"] == target_id, data))
    return filtered[0][key] if filtered else None


def find_hotspots(focus_area, tweet_num: int = 3):
    status_, hot_topic_id, hot_topic, keyword_list = get_hot_topics(str(focus_area))
    if not status_:
        return False, f"获取热门话题失败: {hot_topic_id}"
    status_, tweet_list = get_tweets_from_topic(hot_topic_id)
    if not status_:
        return False, f"获取话题推文失败: {tweet_list}"

    if len(tweet_list) < tweet_num:
        status_, hot_topic_id, hot_topic, keyword_list = get_hot_topics(str(focus_area))
        if not status_:
            return False, f"获取热门话题失败: {hot_topic_id}"
        status_, tweet_list = get_tweets_from_topic(hot_topic_id)
        if not status_:
            return False, f"获取话题推文失败: {tweet_list}"

    if len(tweet_list) < tweet_num:
        random_tweet_list = tweet_list
    else:
        random_tweet_list = random.sample(tweet_list, tweet_num)

    return True, {"hot_topic": hot_topic, "hot_topic_id": hot_topic_id, "tweet_list": random_tweet_list,
                  "keyword_list": keyword_list}

def find_hotspots_media(focus_area, media_num: int = 4, max_retries: int = 10):
    real_num_media = 0
    return_dict = {}
    media_list = []
    retries = 0
    while real_num_media < media_num and retries < max_retries:
        status, return_dict = find_hotspots(focus_area, 3)
        if not status:
            return False, return_dict
        hot_topic_id = return_dict.get("hot_topic_id", "")
        status, media_list = get_tweets_media_from_topic(hot_topic_id)
        if not status:
            return False, media_list
        if media_list:
            real_num_media = len(media_list)
        else:
            real_num_media = 0
        retries += 1

    if real_num_media < media_num:
        return False, f"获取media推文失败,media资源数量少于要求: {media_list}"

    filtered_media_list = random.sample(media_list, media_num)
    return_dict["media_list"] = filtered_media_list
    return True, return_dict


def get_hot_keywords(num=2):
    body = {
        "xId": 3,
        "limit": 12
    }
    try:
        status_, code_, response_ = call_rec.call(url_get_hot_keywords, body, timeout=3)
    except Exception as e:
        return False, f"获取热门关键词失败: {str(e)}"
    if not status_:
        return False, f"获取热门关键词失败: {response_}  {code_}"
    keyword_list = response_.get("result", {}).get("data", {}).get("topicList", [])

    single_word_keywords = [keyword for keyword in keyword_list if len(keyword.split()) == 1]
    if len(single_word_keywords) < num:
        body = {
            "xId": 3,
            "limit": 25
        }
        try:
            status_, code_, response_ = call_rec.call(url_get_hot_keywords, body, timeout=3)
        except Exception as e:
            return False, f"获取热门关键词失败: {str(e)}"
        if not status_:
            return False, f"获取热门关键词失败: {response_}  {code_}"
        keyword_list = response_.get("result", {}).get("data", {}).get("topicList", [])

        single_word_keywords = [keyword for keyword in keyword_list if len(keyword.split()) == 1]
    random_keyword_list = random.sample(single_word_keywords, min(num, len(single_word_keywords)))
    return_key_list = []
    for keyword in random_keyword_list:
        if not keyword.startswith("#"):
            return_key_list.append(f"#{keyword}")
        else:
            return_key_list.append(keyword)
    return True, " ".join(return_key_list)


def get_hot_topics(focus_area: str):
    body = {
        "xId": 3,
        "location": 1,
        "focusAreaStr": str(focus_area),
        "pageNo": 1,
        "pageSize": 12
    }

    try:
        status_, code_, response_ = call_rec.call(url_rec_hot_topic_list, body, timeout=3)
    except Exception as e:
        return False, f"获取热门话题失败: {str(e)}"
    if not status_:
        return False, f"获取热门话题失败: {response_}  {code_}"
    hotspots = response_.get("result", {}).get("data", {}).get("hotspotList", [])
    topic_id_list = [hotspot.get("hotspotId") for hotspot in hotspots]
    if topic_id_list:
        keyword_list = []
        tag_list = [hotspot.get("tagList", []) for hotspot in hotspots]
        for tags in tag_list:
            content = [tag.get("tagContent") for tag in tags if tag.get("tagContent", "")]
            if content:
                keyword_list.extend(content)

        single_word_keywords = [keyword for keyword in keyword_list if len(keyword.split()) == 1]
        random_keyword_list = random.sample(single_word_keywords, min(3, len(single_word_keywords)))
        return_key_list = []
        for keyword in random_keyword_list:
            if keyword.startswith("#"):
                keyword.lstrip("#")
                return_key_list.append(keyword)
            else:
                return_key_list.append(keyword)

        random_topic_id = random.choice(topic_id_list)
        topic_content = find_content_by_id(hotspots, random_topic_id, 'hotspotContent')
        return True, random_topic_id, topic_content, return_key_list
    else:
        return False, "", "", []


def get_tweets_from_topic(hotspot_id: str):
    body = {
        "xId": 3,
        "hotspotId": hotspot_id,
        "pageNo": 1,
        "pageSize": 15
    }
    try:
        status_, code_, response_ = call_rec.call(url_rec_tweet_list, body, timeout=3)
    except Exception as e:
        return False, f"获取话题推文失败: {str(e)}"
    if not status_:
        return False, f"获取话题推文失败: {response_}  {code_}"
    tweet_list = response_.get("result", {}).get("data", {}).get("tweetDetailList", [])
    return True, tweet_list


def get_tweets_media_from_topic(hotspot_id: str):
    body = {
        "hotspotId": hotspot_id,
    }
    try:
        status_, code_, response_ = call_rec.call(url_rec_media, body, timeout=3)
    except Exception as e:
        return False, f"获取related media失败: {str(e)}"
    if not status_:
        return False, f"获取related media失败: {response_}  {code_}"
    try:
        media_list = response_.get("result", {}).get("data", {}).get("mediaList", [])
    except Exception as e:
        return False, f"获取related media失败: {str(e)}"

    return True, media_list
