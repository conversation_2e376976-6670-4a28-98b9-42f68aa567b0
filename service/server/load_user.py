import config
import random
from service.dao.mongo.base_mongo import BaseMongoDB
from service import MONGODB_DATABASE
from utils.watt_service.call_watt_ai_service import CallAiService
from service.server.profile.persona_sample import generate_incontext_example
from operator import itemgetter


hoc_host = config.HOC_BACK_HOST
hoc_token = config.HOC_TOKEN
user_info_uri = config.USER_INFO_URI
user_list_uri = config.USER_LIST_URI
user_persona_uri = config.USER_PERSONA_URI
memory_db = BaseMongoDB(database=MONGODB_DATABASE, collection="data_account_profile")
# Database connections for the company profile schema
user_company_profile_db = BaseMongoDB(database=MONGODB_DATABASE, collection="data_user_company_profile")
company_resource_db = BaseMongoDB(database=MONGODB_DATABASE, collection="data_company_resource")
memory_posts = BaseMongoDB(database=MONGODB_DATABASE, collection = "data_posts")

# 创作风格:5种 1 幽默风趣、2 专业深度、3 激情煽动、4 简洁明了、5 亲切随和
creative_style_mapping = {
    1: "humorous",
    2: "professional depth",
    3: "passionate",
    4: "concise",
    5: "gentle"
}

# 聚焦领域:9个 1 新闻时事 2 地区资讯 3 副业主播 4 Web3炒币 5 副业影视剪辑 6 影视资讯 7 音乐资讯 8 综艺节目资讯 9 AI科技资讯
focus_area_mapping = {
    1: "新闻时事",
    2: "地区资讯",
    3: "副业主播",
    4: "Web3炒币",
    5: "副业影视剪辑",
    6: "影视资讯",
    7: "音乐资讯",
    8: "综艺节目资讯",
    9: "AI科技资讯"
}


def extract_tweets(x_ids: list, require_media: bool = False, limit: int = 60):
    query = {"_id": {"$in": x_ids}}
    status, result = memory_posts.findMemoryLatestList(query,  'updatedAt', limit)
    if not status or not result:
        return False, f'Failed to extract tweets from x_ids: {x_ids}'
    sorted_results = sorted(result, key=lambda x: x['updatedAt'])
    if status and sorted_results:
        xposts = [result.get("xPost") for result in sorted_results]
        tweet_list = [xpost.get("tweetText") for xpost in xposts
            if xpost.get("tweetType") == "post" and len(xpost.get("tweetText", "").split()) > 10]
        if not tweet_list:
            return False, f'No tweets found'
        for tweet in tweet_list:
            if require_media:
                tweet["MediaList"] = [
                    {
                        "mediaType": media.get("mediaType"),
                        "imgUrl": media.get("imgUrl") if media.get("mediaType") == "image" else media.get("keyframeUrl"),
                        "videoUrl": media.get("videoUrl") if media.get("mediaType") == "video" else None
                    }
                    for media in tweet["MediaList"]
                ]
        return True, tweet_list
    else:
        return False, f'Failed to extract tweets'


def get_user_memory(profile_id: str):
    status, result = memory_db.dict_single_Search({"_id": profile_id})
    if status and result:
        x_post_ids = result.get("xIds")
        user_profile_info = result.get("profileInfo")
        if not user_profile_info:
            return False, f'No memory found', '', ''
        profile = user_profile_info.get("profile")
        if profile:
            if profile.get("persona"):
                if profile.get("persona").get("style"):
                    style_list = profile.get("persona").get("style")
                    del profile["persona"]["style"]
            
            memory_elements = generate_incontext_example(profile, 3)
            if style_list:
                if isinstance(style_list, list):
                    style = random.choice(style_list)
            return True, memory_elements, style, x_post_ids
        else:
            return False, f'No profile found', '', ''
    return False, f'Failed to extract profile and memory from profile_id: {profile_id}', '', ''


def get_company_memory(company_id: str):
    try:
        status, result = user_company_profile_db.dict_single_Search({"_id": company_id})
        
        if status and result:
            # Get basic profile info from user_company_profile
            profile_info = result.get("profileInfo", {})
            
            if not profile_info:
                return False, f'No company memory found'
            
            # Initialize profile with basic information
            profile = profile_info.copy()
            
            # Get resource information
            resource_id = result.get("resourcesId")
            
            if resource_id:
                resource_status, resource_result = company_resource_db.dict_single_Search({"_id": resource_id})
                
                if resource_status and resource_result:
                    # Process recent news
                    if resource_result.get("recentNews"):
                        recent_news = resource_result.get("recentNews")
                        if recent_news and isinstance(recent_news, list):
                            profile["recentNews"] = [{"title": x["title"], "content": x["content"]}
                                for x in sorted(recent_news, key=itemgetter("intensity"), reverse=True)][:5]
                    
                    # Process market cases
                    if resource_result.get("marketCases"):
                        market_cases = resource_result.get("marketCases")
                        if market_cases and isinstance(market_cases, list):
                            profile["marketCases"] = [{"title": x.get("title"), "content": x.get("content")}
                                for x in sorted(market_cases, key=itemgetter("intensity"), reverse=True)][:5]
                    
                    # Process competitors
                    if resource_result.get("competitors"):
                        competitors = resource_result.get("competitors")
                        if competitors and isinstance(competitors, list):
                            profile["competitor"] = [{"name": x.get("company"), "details": x.get("details")} 
                                for x in competitors]
            
            # Clean up profile data
            if profile.get("subscribeKeywords"):
                del profile["subscribeKeywords"]
            
            return True, profile
        else:
            return False, f'No company profile found for company_id: {company_id}'
    except Exception as e:
        print(f"Error processing company: {str(e)}")
        import traceback
        print(f"Full traceback: {traceback.format_exc()}")
        return False, f'Failed to extract profile and memory from company_id: {company_id}'


class XUserDetails:
    def __init__(self):
        self.persona = {}
        self.style = {}
        self.user_list = None
        self.call_get_user_x_info = CallAiService(hoc_host)
        self.timeout = 10
        self.headers_ = {"token": 'Bearer ' + hoc_token}

    def get_user_persona(self, x_id: int):
        body = {
            "xId": x_id
        }
        status_, code_, response_ = self.call_get_user_x_info.call(user_persona_uri, body, self.timeout, self.headers_)
        if not status_:
            return None
        output_ = response_.get("result", {}).get("data", {}).get("profile_summary", {})
        if not output_:
            return None
        # try:
        #     output_json = json.loads(output_)
        # except json.JSONDecodeError as e:
        #     return False, f"Json decode error: {e}"
        return output_

    def get_user_list(self):
        uri = user_list_uri
        body = {
            "pageNo": 1,
            "pageSize": 10
        }
        status_, code_, response_ = self.call_get_user_x_info.call(uri, body, self.timeout, self.headers_)
        if not status_:
            if not self.user_list:
                print(f"Record not found: {code_}")
                return False, "Record not found"
            else:
                return True, "Use cached data"
        output_list = response_.get("result", {}).get("data", {}).get("userXIdList", [])
        self.user_list = []
        for item in output_list:
            self.user_list.extend(item.get('xIdList', []))
        return True, self.user_list

    def get_user_info(self, x_id: int):

        uri = user_info_uri
        body = {
            "xId": x_id,
            "isUpdate": False
        }
        status_, code_, response_ = self.call_get_user_x_info.call(uri, body, self.timeout, self.headers_)
        if not status_:
            print(f"Record not found: {code_}")
        if isinstance(response_, str):
            print(f"Record not found: {response_}")
            return False, "Record not found"
        details = response_.get("result", {}).get("data", {}).get("userXInfo", {})
        creative_style_int_list = details.get("creativeStyle", [])
        category_list = details.get("categoryList", [])
        if category_list:
            category_name_list = [x.get("category", "") for x in category_list]
        else:
            category_name_list = []
        follower_count = details.get("followerCount", 0)
        following_count = details.get("followingCount", 0)

        self.style["creative_style"] = creative_style_int_list
        self.style["category"] = category_name_list
        self.style["follower_count"] = follower_count
        self.style["following_count"] = following_count

        return True, self.style


if __name__ == '__main__':
    user_details = XUserDetails()
    user_details.get_user_list()
    print(user_details.user_list)
    for x_id in user_details.user_list:
        print(f"User id: {x_id}")
        user_details.get_user_info(x_id)
        print(f"Creative style: {user_details.creative_style_list}")
        print(f"Focus area: {user_details.focus_area_list}")
        print(f"Follower count: {user_details.follower_count}")
        print(f"Following count: {user_details.following_count}")
        print("====================================")
