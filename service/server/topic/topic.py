import json
from pprint import pprint
from sentence_splitter import <PERSON><PERSON><PERSON><PERSON>plitter

from service.lib.chat import ChatService
from service.server.topic.data_helper import get_tweets_df_from_mongo


gpt_service = ChatService()
splitter = SentenceSplitter(language='en')

areas = {      
    8: "Reality Shows",
    7: "Music release & concerts",
    6: "Movies & TV Shows",
    5: "Video Editing",
    4: "Web3 Cryptocurrency",
    3: "Live Stream",
    2: "Regional",
    1: "Trending News",
    0: "Others"
}



def get_gpt_prompts(topics_df, keyword, exclude_outliers=True):
    prompts = []
    #get prompts list
    for i, row in topics_df.iterrows():
        topic = row.Topic
        if exclude_outliers and topic < 0:            
            continue
        docs = '\n• '.join(row['Representative_Docs'])
        reps = [r for r in row['Representation'] if r][:5]               
        # use minimum adjective, yet eye catching, 
        # predicate or verb
        prompt = f"""You are an expert in social media and SEO, please help me extract the main topic/event/theme and topic category covered in the following tweets {f'on #{keyword}' if keyword else ''}.
tweets:<start>
• {docs}
<end>
(most common words appeared in the topic are: {reps})        
Requirement:
- The topic should be SHORT, succinct(such as commonly seen on social media's trending topics) 
- topic is only based on tweets, dont make up content
- prefered formats: SUBJECT verb OBEJECT, ADJ SUBJECT, or SUBJECT ADV
topic:"""    
        prompts.append(prompt)   
    return prompts


def _get_gpt_topics(topics_df, keyword):
    topic_names = []
    default_name = keyword if keyword else 'Unknown'
    if -1 in topics_df.Topic.values:        
        topic_names.append(default_name)

    prompts = get_gpt_prompts(topics_df, keyword)
    #openai gcall
    status, res_list = gpt_service.chat_paralell(prompts, model='gpt-3.5-turbo', temperature=0.1)
    if not status:
        print('Error getting GPT topics')
        return

    for con, msg in res_list:
        if not con:
            name = default_name
        else:
            name = msg.strip()
        topic_names.append(name)
    return topic_names


cluster_function = [{
                "name": "extract_topics",
                "description": "extract and group topics from tweets",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "topics":{
                            "type": "array",
                            "description": "list of topics extracted from the tweets",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "topic_name": {
                                        "type": "string",
                                        "description": "topic name",
                                    },   
                                    "tweet_ids": {
                                        "type": "array",
                                        "description": f"tweet ids of belong to the topic",
                                        "items": {
                                            "type": "integer",                                
                                        }
                                    },
                                                       
                                },
                                "required": ["topic_name", "tweet_ids"],
                            }},                    
                    },
                    "required": ["topics"],
                },
            }]


def gpt_cluster_topics(tweets_df, keyword, min_tweets=0, verbose=False):    
    #shorten tweet text for gpt processing
    n_sents = 2
    tweets_df['full_text'] = tweets_df['full_text'].apply(lambda x: ' '.join(splitter.split(x)[:n_sents]))
    
    min_tweets_req = f'\n- each topic should inclue at least {min_tweets} tweets!' if min_tweets else ''
    tweets = tweets_df.to_markdown(index=False)
    prompt = f"""You are an expert in social media and SEO, please help me extract the top three main topics/news covered in the following tweets {f'on #{keyword}' if keyword else ''}.
tweets:<start>
• {tweets}
<end>    
Requirement:
- topic should be a main event or news subject covered by tweets, dont make up content {min_tweets_req}
- topic name should be SHORT, succinct but descriptive(such as commonly seen on social media's trending topics) 
"""    
    # - topic name prefered formats: SUBJECT verb OBEJECT, ADJ SUBJECT, or SUBJECT ADV
    
    con, msg, func = gpt_service.function_call(prompt, functions=cluster_function, model='gpt-4o', temperature=0.5)
    if not con:
        print('Error getting GPT topics')
        return []
    params = func.get('extract_topics', {})
    if isinstance(params, str):
        params = json.loads(params)
    topics = params.get('topics', [])

    if verbose:
        print(f'\n==prompts: {prompt}\n==topics: {topics}')
    return topics    


topic_function = [{
                "name": "get_top_topic",
                "description": "get top topic from tweets",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "topic_name":{
                            "type": "string",
                            "description": "topic name",
                            },
                        "relevance":{
                            "type": "number",
                            "description": "relevance score of the topic to the tweets, min 0, max 1",
                            },                    
                    },
                    "required": ["topic_name", "relevance"],
                }               
            }]

def gpt_get_topic(tweets_df, keyword, verbose=False):    
    #shorten tweet text for gpt processing
    # n_sents = 2
    # tweets_df['full_text'] = tweets_df['full_text'].apply(lambda x: ' '.join(splitter.split(x)[:n_sents]))

    tweets = '\n* '.join(tweets_df.full_text)
    prompt = f"""You are an expert in social media and SEO, get one top topic from below tweets {f'on #{keyword}' if keyword else ''}.
tweets:<start>
* {tweets}
<end>    
Task:
- try to find one common topic mentioned by all of the tweets.
- if there are more then one topic covered, the topic mentioned in the first tweet is the top topic.
Requirements:
- topic should be a event or news mentioned by tweets, dont make up content.
- topic name should be SHORT and succinct in 2 to 5 words, shorter the better
- topic name prefered formats: SUBJECT OBEJECT, ADJ SUBJECT, or SUBJECT ADV
"""    
    #(max 5 words) and descriptive(such as commonly seen on social media's trending topics)
    # - topic name prefered formats: SUBJECT verb OBEJECT, ADJ SUBJECT, or SUBJECT ADV
    #, in format of: SUBJECT + PREDICT
    
    con, msg, func = gpt_service.function_call(prompt, functions=topic_function, model='gpt-4o-mini', temperature=0.5)
    if not con:
        print('Error getting GPT topics')
        return []    
    params  = func.get('get_top_topic', {})
    if isinstance(params, str):
        params = json.loads(params)
    
    topic = [
        (params.get('topic_name', keyword), params.get('relevance', 0.5))
             ]

    if verbose:
        print(f'\n==prompts: {prompt}\n==params: {params}')
    return topic
  

def get_tweet_topics(keyword='', tweet_ids=[], n_tweets=15, verbose=False):
    if not keyword or not tweet_ids:
        print('Invalid keyword or tweet ids')
        return []
    
    tweet_ids = tweet_ids[:n_tweets]
    df = get_tweets_df_from_mongo(tweet_ids=tweet_ids)
    if df is None or len(df) == 0:
        print('No tweets found')
        return []

    # 计算每行full_text中的单词数
    df['word_count'] = df['full_text'].apply(lambda x: len(x.split()))
    # 过滤掉单词数少于3的行
    filtered_df = df[df['word_count'] >= 3].reset_index(drop=True)
    # 取前三条数据
    result_df = filtered_df.head(3)
    if result_df is None or len(result_df) < 2:  # 少于两条推文时事件置信度为0
        return []

    topic = gpt_get_topic(result_df, keyword, verbose=verbose)
    if not topic:
        print('No topics found')
        return []
    
    return topic


if __name__ == '__main__':
    from colorama import Fore, Style

    test = {
    'Kamala': [
    "1815491927480951208",
    "1815203347059621928",
    "1815752874728792084",
  ]
    ,
    'OlympicOpeningCeremony': [
    "1816946326673715346",
    "1816867703933612059",
    "1816931661839548759"
  ],
    'Athena': ["1808575999157669901",
    "1809850414701195608",
    "1805521906059428284",],

    'Azerbaijan': [
    "1698076231407665434",
    "1730046509461147746",
    "1806812862754488673",
    "1706380704412516539",
    "1803669093590999464",
    "1785718985595502860"
  ],
    
    }
    
    for kw, ids in test.items():
        res = get_tweet_topics(kw, ids, verbose=True)  
        print(f'\n{Fore.YELLOW}==Keyword: {kw}, Topic: {res}{Style.RESET_ALL}')      