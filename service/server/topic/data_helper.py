import re
import pandas as pd
from colorama import Fore, Style

from service.dao.mongo.tweet_mongo import TweetMongoDB


tweet_db = TweetMongoDB(collection="tweet")
en_pattern = re.compile(r'^[a-zA-Z!@#$%^&*()_+=\-{}[\]:;"\'<>,.?/\\|`~ ]+$')

def log(*args, level='info'):
    if level != 'info':
        print(Fore.RED)
    print(*args)
    print(Style.RESET_ALL)


def clean_text(text):
    #remove links
    text = text.replace('\n', ' ').replace('&amp;', '&').split('http')[0].strip()
    # remove Emoji
    emoji_pattern = re.compile(
        "["
        u"\U0001F600-\U0001F64F"  # emoticons
        u"\U0001F300-\U0001F5FF"  # symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # flags (iOS)
        u"\U00002700-\U000027BF"  # Dingbats
        u"\U000024C2-\U0001F251" 
        "]+", flags=re.UNICODE
    )
    return emoji_pattern.sub(r'', text)


def get_tweets_df_from_mongo(tweet_ids=[], rank_by_viewcount=False):
    '''
    Get tweets data from mongo db for keyword, merge keyword and simple rank
    '''

    if not tweet_ids:
        return
        
    tweets = []                     
    #allow processes tweets for other keywords to reuse here
    tweets = tweet_db.find_data({'id_str': {'$in': tweet_ids}}) #  {ai_topic_status': False}
    if not tweets:
        log(f'No tweets found in db', level='warning')
        return
    
    #2. process tweets data        
    processed = [{k:v for k,v in t.items() if v is not None and not isinstance(v, dict)} for t in tweets]
    if not processed:
        return

    #3. RANK tweets      
    cols = ['id_str', 'full_text']   
    df = pd.DataFrame(processed).drop_duplicates(subset='id_str', keep='first')[cols]    
    
    if rank_by_viewcount:
        ct = int((df.view_count>0).sum())
        log(f'{ct}/{len(df)} tweets have view count > 0.')
        df = df.sort_values('view_count', ascending=False)        
    #if original id_list contain twitter order
    else:
        df.set_index('id_str', inplace=True)
        use_rank_ids = [i for i in tweet_ids if i in df.index]
        df = df.loc[use_rank_ids]
        df.reset_index(inplace=True)
    
    #clean text
    df = df.rename(columns = {'id_str': 'tweet_id'})    
    df['full_text'] = df['full_text'].apply(clean_text)    
    # df['is_english']  = df.full_text.apply(lambda x: bool(en_pattern.match(x)))
    # df = df[df.is_english]  
    
    return df


if __name__ == '__main__':
    pass