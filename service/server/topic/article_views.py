from bson import ObjectId
from service.dao.mongo.base_mongo import BaseMongoDB
from service import MONGODB_DATABASE

ai_voices_db = BaseMongoDB(database=MONGODB_DATABASE, collection="topic_voices")

def extract_views(hotspot_id: str, max_limit: int):
    try:
        hotspot_id_obj = ObjectId(hotspot_id)
    except Exception as e:
        return False, f"Error: {str(hotspot_id)} is not a valid ObjectId: {str(e)}"
    status, voices = ai_voices_db.singleSearch("_id", hotspot_id_obj)
    if not status:
        return False, voices
    if not voices:
        return True, []
    voice_list = voices.get("news_list", [])
    truncated_voice_list = voice_list[:max_limit]
    return True, truncated_voice_list
