import json
import datetime
from service.lib.prompt_fusion import call_llm_model
from service import prompts_fusion
from service.server.call_gpt import CallGpt
from colorama import Fore

prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=1.0)

topic_mapping = {
        1: "引流私域",
        2: "带货变现",
        3: "品牌曝光",
        4: "涨粉提升",
    }

gemini_json_schema = {
    "type": "object",
    "properties": {
        "rednoteSchedule": {
            "type": "array",
            "items": {
                "type": "object",
                "properties": {
                    "todoTaskDate": {
                        "type": "string",
                        "description": "The date of the task, format: YYYY-MM-DD"
                    },
                    "todoTaskTitle": {
                        "type": "string",
                        "description": "The title of the task"
                    }
                },
                "required": ["todoTaskDate", "todoTaskTitle"]
            },
            "description": "The schedule of the tasks"
        }
    },
    "required": ["rednoteSchedule"]
}


def schedule(audit_result, rednote_num, plan_topic, plan_description=None, plan_cycle=30, watttraceid=None):
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    
    # Handle audit_result - it might already be a Python object or a JSON string
    if isinstance(audit_result, str):
        audit_infomation = json.loads(audit_result)
    else:
        audit_infomation = audit_result
    
    plan_topic_string = topic_mapping[plan_topic]
    input_data = {
        "audit_input": audit_infomation,
        "rednote_num": rednote_num,
        "plan_topic": plan_topic_string,
        "plan_description": plan_description,
        "plan_cycle": plan_cycle,
        "current_date": today
    }
    system_prompt = prompts_fusion.get_xiaohongshu_gen_plan_system(input_data)
    user_prompt = prompts_fusion.get_xiaohongshu_gen_plan_user(input_data)
    status, result = call_llm_model(system_prompt, user_prompt, model="gpt-4o-mini", 
                                    watttraceid=watttraceid, prompt_gpt=prompt_gpt, 
                                    json_object=True, json_schema=gemini_json_schema)

    if not status:
        return False, result
    result_data = json.loads(result)

    return True, result_data