import requests
import json
from service import L<PERSON><PERSON>_HOST, LUPAN_KEY, MONGODB_DATABASE, GOOGLE_BACK_KEY
from service.server.topic.topic import get_tweet_topics
from service.server.is_related import is_related_news, is_related_search, is_related_events, compare_tweet_and_search
from datetime import datetime
from service.server.get_search_content import get_summary_from_url
from service.dao.mongo.base_mongo import BaseMongoDB
import time


def event_pipeline(keyword, tweet_ids, trace_id, category):
    keyword = keyword.lstrip('#')
    # if category:
    #     keyword = keyword + ' ' + category
    # 1. search event by keyword
    search_event_status, search_event, search_score, search_details = event_by_search(keyword)
    if not search_event_status:
        search_event = ''
        send_data_to_api(trace_id, False, search_event)
        # return False, search_event, search_score, False, search_details
    # 2. get topics from tweets
    tweet_result = get_tweet_topics(keyword, tweet_ids)
    if not tweet_result:
        tweet_event, tweet_score = '', 0
    else:
        tweet_event, tweet_score = tweet_result[0][0], tweet_result[0][1]
    final_status, final_event, final_score = compare_tweet_and_search(keyword, tweet_event, tweet_score,
                                                                      search_event, search_score)
    detail = {"tweet_event": tweet_event, "tweet_score": tweet_score,
              "search_event": search_event, "search_score": search_score}
    finale_score = round(final_score, 2)
    #  make decision
    decision = False
    if finale_score > 0.4:
        decision = True

    # log how the final event is generated
    if final_score == 1:
        search_and_tweet_is_related = True
        final_event_from = "both"
    else:
        search_and_tweet_is_related = False
        if search_score > tweet_score:
            final_event_from = "search"
        else:
            final_event_from = "tweet"

    #  get summary from search results
    result_to_summarize = []
    len_news, len_bing = 0, 0
    if search_details['news_result']:
        result_to_summarize.extend(search_details['news_result'])
        len_news = len(search_details['news_result'])
    if search_details['bing_result']:
        result_to_summarize.extend(search_details['bing_result'])
        len_bing = len(search_details['bing_result'])
    # if len_news + len_bing == 0:
    summary_list = []
    output_summary = ''
    # else:
        #summary_status, summary_list = get_summary_from_url(result_to_summarize)
        # if summary_status:
        #     if search_details['event_from'] == 'bing':
        #         output_summary = summary_list[len_news]['summary']
        #     else:
        #         output_summary = summary_list[0]['summary']
        # else:
        #     output_summary = ''
    mongo_event_details = BaseMongoDB(database=MONGODB_DATABASE, collection='event_details')
    mongo_event_details.insertSingleData({"final_event": final_event, "final_score": final_score,
                                          "search_and_tweet_is_related": search_and_tweet_is_related,
                                          "final_event_from": final_event_from,
                                          "search_event": search_event, "search_score": search_score,
                                          "tweet_event": tweet_event, "tweet_score": tweet_score,
                                          "search_details": search_details, "trace_id": trace_id,
                                          "keyword": keyword, "summary_list": summary_list, "summary": output_summary,
                                          "local_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())})
    return final_status, final_event, finale_score, decision, detail


def event_by_search(keyword):
    search_details = {"news_result": [], "news_event": '', "news_score": -1.0,
                      "bing_result": [], "bing_event": '', "bing_score": -1.0,
                      "is_related": False, "event_from": None}
    news_status, news_list = google_news_search(keyword)
    if not news_status:  # google news search 无内容, 走bing search
        bing_status, bing_event, bing_score, bing_result = event_by_bing(keyword)
        if bing_status:  # bing search有内容，返回事件和分数
            search_details['bing_result'] = bing_result
            search_details['bing_event'] = bing_event
            search_details['bing_score'] = bing_score
            search_details['event_from'] = 'bing'
            return True, bing_event, bing_score, search_details
        # bing search false
        else:
            if bing_event == 'Bing have no search result':  # bing search无内容，返回无事件
                return True, '', 0, search_details
            else:   # bing search报错
                return False, f'error in search_event_by_bing: {bing_event}', 0, search_details

    # google news search有内容，对news_list处理
    search_details['news_result'] = news_list
    news_event_status, news_event_dict = is_related_news(keyword, news_list)  # 判断news前三个结果是否相关
    if not news_event_status:  # 报错
        return False, f'error in is_related_news:{news_event_dict}', 0, search_details
    news_event_code, news_event = news_event_dict['related'], news_event_dict['event']  # 返回事件和分数
    search_details['news_event'] = news_event
    if news_event_code:  # 前三个结果相关，返回事件和分数
        search_details['event_from'] = 'news'
        nums = len(news_list)  # 搜索的新闻事件小于3时，进行置信度衰减
        if nums == 1:
            search_details['news_score'] = 0.6
            return True, news_event, 0.6, search_details
        elif nums == 2:
            search_details['news_score'] = 0.88
            return True, news_event, 0.88, search_details
        else:
            search_details['news_score'] = 1
            return True, news_event, 1, search_details
    else:  # 前三个结果不相关，bing search进行处理
        news_score = 0.33  # 初始分数为0.33
        if len(news_list) == 3:
            two_status, two_event_dict = is_related_news(keyword, news_list[0:2])  # 判断news前两个结果是否相关
            if two_status:
                if two_event_dict['related']:
                    news_score = 0.88
        else:
            news_score = 0.5  # 两个事件取0.5分
        search_details['news_score'] = news_score
        bing_status, bing_event, bing_score, bing_result = event_by_bing(keyword)
        if not bing_status:  # bing search无结果，直接返回news结果和分数
            search_details['event_from'] = 'news'
            return True, news_event, news_score, search_details
        else:  # bing search有结果，将news结果与bing进行对比
            search_details['bing_result'] = bing_result
            search_details['bing_event'] = bing_event
            search_details['bing_score'] = bing_score
            compare_status, compare_event = is_related_events(keyword, [news_event, bing_event])
            if compare_status:
                if compare_event['related']:  # news和bing结果相关，返回news结果和分数
                    search_details['is_related'] = True
                    search_details['event_from'] = 'both'
                    return True, compare_event['event'], 0.5 * (news_score + bing_score) + 0.11, search_details
                else:  # news和bing结果不相关，返回news结果和分数
                    if news_score >= bing_score:
                        search_details['event_from'] = 'news'
                        return True, news_event, news_score * 0.8, search_details
                    else:
                        search_details['event_from'] = 'bing'
                        return True, bing_event, bing_score * 0.8, search_details
            return False, f'error in compare news and bing: {compare_event}', 0, ''


def event_by_bing(keyword):
    search_status, search_list = bing_search(keyword)
    if not search_status:  # bing search内容为空，返回无事件
        return False, 'Bing have no search result', 0, ''
    # 对bing, search_list处理，返回事件和分数
    bing_event_status, bing_event_dict = is_related_search(keyword, search_list)
    if not bing_event_status:
        return False, bing_event_dict, 0, ''
    if bing_event_dict['related']:  # bing search前三个事件都相关
        nums = len(search_list)
        decay = -0.05 * nums * nums + 0.45 * nums + 0.1  # 衰减系数，搜索结果小于3时对置信度衰减
        return True, bing_event_dict['event'], 0.999 * decay, search_list
    if len(search_list) == 3:  # bing search前三个事件不相关
        two_status, two_event_dict = is_related_search(keyword, search_list[0:2])
        if two_status:
            if two_event_dict['related']:  # 前俩相关，0.66
                return True, bing_event_dict['event'], 0.66, search_list
        return True, bing_event_dict['event'], 0.33, search_list  # 否则0.33
    else:
        return True, bing_event_dict['event'], 0.5, search_list  # 两个事件取0.5分


def send_data_to_api(trace_id, status, detail):
    success_code = 1
    message = ""
    app_name = "service-ai-getevent"
    if status:
        return True, "Success"
    time_stamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + "Z"
    headers = {"Content-Type": "application/json"}
    data = [{
        "traceId": trace_id,
        "appName": app_name,
        "appBatchId": trace_id + '_' + app_name + '_' + "0001",
        "logLevel": "CRITICAL",
        "code": success_code,
        "msg": message,
        "timestamp": time_stamp,
        "data": detail,
        }]
    url = "https://dev-watt-ai-hoc-rec-admin.watt.chat/api/v1/dataHub/report"
    try:
        response = requests.post(url, headers=headers, data=json.dumps(data))
        response.raise_for_status()
        return True, response.json()  # 返回JSON格式的响应
    except requests.exceptions.HTTPError as errh:
        return False, f"Http Error: {errh}"
    except requests.exceptions.ConnectionError as errc:
        return False, f"Error Connecting: {errc}"
    except requests.exceptions.Timeout as errt:
        return False, f"Timeout Error: {errt}"
    except requests.exceptions.RequestException as err:
        return False, f"Request Error: {err}"


def search_by_luban(param_dict, engine_code):
    base_search_url = LUPAN_HOST
    headers = {'Call-Source': 'Coze', 'Content-Type': 'application/json'}
    data = {"lupan_key": LUPAN_KEY}
    data.update(param_dict)
    search_url = base_search_url + engine_code
    try:
        search_response = requests.post(search_url, headers=headers, json=data).json()
        result = json.loads(search_response['data']['resp'])
        return True, result
    except Exception as e:
        return False, f'Error {str(e)} in {engine_code} search'


def bing_search(keyword):
    params = {'q': keyword, "cc": "US", 'filters': "freshness=day"}
    status, result = search_by_luban(params, '332')
    if not status:
        return False, result
    # 返回搜索结果前三个中的title和url，给pplx分析
    organic_results = result.get("organic_results", None)
    if not organic_results:
        return False, organic_results
    output = []
    for i in range(min(3, len(organic_results))):
        output.append({"title": organic_results[i]['title'], "link": organic_results[i]['link']})
    return True, output


def google_news_search(keyword):
    query = f"""{keyword} when:1d"""
    params = {'q': query}
    status, result = search_by_luban(params, '903930')
    if not status:
        back_status, back_result = google_news_backup(keyword)
        if not back_status:
            return False, back_result
        return True, back_result
    news = result.get("news_results", None)
    if not news:
        back_status, back_result = google_news_backup(keyword)
        if not back_status:
            return False, news
        return True, back_result
    output = []
    for i in range(min(3, len(news))):
        news_title = news[i].get('title')
        news_link = news[i].get('link')
        if not news_title or not news_link:
            continue
        output.append({'title': news[i]['title'], 'link': news[i]['link']})
    if len(output) < 1:
        return False, None
    return True, output


def google_news_backup(keyword):
    url = 'https://lupan.watt.chat/api/shanda-data-platform-backend/commonApi/single/903967'
    headers = {'Call-Source': 'Coze', 'Content-Type': 'application/json'}
    data = {'lupan_key': GOOGLE_BACK_KEY,
            'query': f"{keyword} when:1d"}
    response = requests.post(url, headers=headers, json=data)
    if response.status_code != 200:
        return False, None
    output = []
    response_data = response.json()
    try:
        news = json.loads(response_data['data']['resp'])["data"]
    except Exception as e:
        return False, f"error: {e}"
    for i in range(min(3, len(news))):
        news_title = news[i].get('title', None)
        news_link = news[i].get('link', None)
        if not news_title or not news_link:
            continue
        output.append({'title': news[i]['title'], 'link': news[i]['link']})
    if len(output) < 1:
        return False, None
    return True, output

if __name__ == "__main__":
    k = 'Typhoon Gaemi'
    print(bing_search(k))
    print('\n')
    print(google_news_search(k))
    # print(event_by_search(k))