import requests
from flask import current_app
from . import writing_mongo
from .call_gpt import CallGpt
from ..lib.text_process import *
from ..lib.lang_detect import lang_detect
from ..lib.media_search import search_media
from .. import prompts_fusion
from service.server import load_user
from service.server.get_hotspot import *
from config import ENV, GEMINI_PRO_MODEL, GEMINI_FLASH_MODEL
from colorama import Fore
import traceback
import re
import json

user_details = load_user.XUserDetails()
prompt_gpt = CallGpt(model="gpt-4o-mini", temperature=1.0)
rec_host = config.WATT_REC_HOST
url_get_tweet_detail = "/api/v1/support/tweet/detailInfo"
url_rec_hot_tweet_list = "/api/v1/rec/topic/tweetDetail"
writing_styles_mapping = load_user.creative_style_mapping
product_default = "x account booster"

language_mapping = {
    1: "Simplified Chinese",
    2: "Traditional Chinese",
    3: "English",
    4: "Others"
}
choices = [True, False]

gemini_json_schema = {
    "type": "object",
    "properties": {
        "content": {
            "type": "string",
            "description": "Generated tweet text"
        },
        "tags": {
            "type": "array",
            "items": {
                "type": "string"
            },
            "description": "List of hashtags"
        }
    },
    "required": ["content", "tags"]
}


def get_tweet_detail(tweet_id: str):
    body = {
        "tweetIdList": [tweet_id],
        "withContentMediaUrl": True,
        "withAuthorMediaUrl": True
    }
    try:
        response_ = requests.post(
            f"{rec_host}{url_get_tweet_detail}", json=body, timeout=10)
    except Exception as e:
        return False, str(response_) + f" Error retrieve tweet detail: {e}"
    if response_.status_code != 200:
        return False, str(response_)
    response = response_.json().get("result", {}).get("data", {})
    return True, response


def get_hotspot_detail(hotspot_id: str):
    body_tweet_list = {
        "hotspotId": hotspot_id,
        "xId": 9,
        "pageNo": 1,
        "pageSize": 10
    }
    try:
        response_ = requests.post(
            f"{rec_host}{url_rec_hot_tweet_list}", json=body_tweet_list, timeout=25)
    except Exception as e:
        return False, 99999, f"Error retrieve hotspot detail: {e}"
    if response_.status_code != 200:
        return False, 99998, str(response_)
    try:
        response = response_.json().get("result", {}).get("data", {})
    except Exception as e:
        return False, 99997, f"Error loading json: {e}"
    return True, response


def get_tweet_content(tweet_id: str):
    stat_, response = get_tweet_detail(tweet_id)
    if stat_:
        tweet_detail_list = response.get("tweetDetailList", [])
    else:
        return False, response
    if not tweet_detail_list:
        return False, "Tweet not found"
    tweet_detail = tweet_detail_list[0]
    if isinstance(tweet_detail, str):
        try:
            tweet_detail = json.loads(tweet_detail)
        except Exception as e:
            print(f"Error loading json: {e}")
            return False, f"Error loading json: {e}"
    try:
        content = tweet_detail.get("content", "")
    except Exception as e:
        print(f"Error getting tweet content: {e}")
        return False, f"Error getting tweet content {e}"
    return True, content


def calculate_token_limits(platform, gen_tag, url):
    """Calculate token limits based on platform, action, and other parameters."""
    if platform == "tiktok":
        token_limit_up = 149
        token_up = token_limit_up - 50
    else:
        token_limit_up = 279
        token_up = token_limit_up - 40
        if gen_tag:
            token_up -= 40
        if url:
            token_limit_up -= 23
            token_up -= 23
    token_bot = token_up - 60 if token_up > 60 else 0
    return token_bot, token_up, token_limit_up


def get_profile_data(x_id, env):
    """Fetch and prepare company or user profile data. TODO: Next version cross-profile data fetching.
    """
    profile_id = f"{env.lower()}_x_{x_id}"
    status, user_profile, style, x_post_ids = load_user.get_user_memory(
        profile_id)
    if status:
        user_profile = clean_dict_recursive(user_profile)
        media_count = user_profile.get(
            "persona", {}).pop("multi_media_number", 0)
        keywords = user_profile.get("memory", {}).get("chosen_interests", [])
        keyword_backup = random.choice(
            [kw.get("name", "") for kw in keywords]) if keywords else ""
        return {
            "writing_style": style,
            "character": {
                "basic_info": user_profile.get("basic_info"),
                "persona": user_profile.get("persona"),
                "memory": user_profile.get("memory")
            },
            "media_count": media_count,
            "keyword_backup": keyword_backup
        }, x_post_ids
    print(Fore.RED + "Error loading user profile" + Fore.RESET)
    return None, None


def get_company_profile_data(company_id, user_id, env):
    """Fetch and prepare company profile data."""
    company_profile_id = f"{env.lower()}_{company_id}_{user_id}"
    status, company_profile = load_user.get_company_memory(company_profile_id)
    if status:
        return {"company_profile": company_profile, "media_count": 0}
    print(
        Fore.RED + f"Error loading company profile: {company_profile}" + Fore.RESET)
    return None


def prepare_input_data(company_id, user_id, x_id, content_purpose, original_content, word_limit, reason,
                       prompt_content, language, event, action, platform, product, url, strategies, env):
    """Prepare input data for prompt generation."""
    gen_tag = False if action == "reply" else True
    company_profile_ = True
    x_post_ids = None
    token_bot, token_up, token_limit_up = calculate_token_limits(
        platform, gen_tag, url)
    profile_data = get_company_profile_data(company_id, user_id, env)
    if profile_data is None:
        company_profile_ = False
        profile_data, x_post_ids = get_profile_data(x_id, env)
        if profile_data is None:
            return None, None, False

    input_data = {
        "word_limit": word_limit,
        "tag_num": random.choice([1, 2]),
        "language": language_mapping.get(language, "English"),
        "custom": prompt_content if prompt_content else "native",
        "token_limit": f"{token_bot}-{token_up}"
    }
    input_data.update(profile_data)

    if "persona" in profile_data and not prompt_content:
        input_data["task"] = profile_data["persona"].get(
            "memory", {}).get("task", "")
    if x_post_ids:
        status, tweet_list = load_user.extract_tweets(x_post_ids, False, 80)
        if status:
            input_data["history_x_list"] = tweet_list[:11]
    if event:
        input_data["event"] = event
    if strategies:
        input_data["strategies"] = strategies
    if original_content:
        input_data["tweet_content"] = original_content

    if reason and company_profile_:
        input_data["marketing_suggestion"] = reason
    elif product and product.get("name", "").lower() != "x account booster":
        input_data["target"] = product
        if slogan := product.get("keyDescription", ""):
            input_data["slogan"] = slogan
        input_data["target_means"] = "create consumer desire" if content_purpose == 1 else "sell products"
    elif original_content:
        input_data["target_means"] = (
            "Your goal is to maintain the core fact and situation of the original post while "
            "adapting it to fit the given characteristics and style. DO NOT mimic the original "
            "post but express your own perspective and style to form a brand-new appealing post."
        )
    elif action == "reply":
        input_data["target_means"] = "replying to a post"

    return input_data, token_limit_up, company_profile_


def select_prompt_functions(company_profile_exist, product, with_media, original_content, action):
    """Select appropriate system and user prompt functions."""
    if company_profile_exist:
        return (prompts_fusion.get_company_reply_system, prompts_fusion.get_company_reply_user) if action == "reply" else \
               (prompts_fusion.get_company_post_system,
                prompts_fusion.get_company_post_user)
    # elif product and product.get("name", "").lower() != "x account booster":
    #     return (prompts_fusion.get_reply_system, prompts_fusion.get_reply_user) if action == "reply" else \
    #            (prompts_fusion.post_product_system, prompts_fusion.post_product_user)
    elif with_media and not original_content:
        return prompts_fusion.get_write_with_profile_system, prompts_fusion.get_write_with_profile_user
    elif original_content:
        return (prompts_fusion.get_reply_system, prompts_fusion.get_reply_user) if action == "reply" else \
               (prompts_fusion.get_modify_system,
                prompts_fusion.get_modify_tag_user)
    return prompts_fusion.get_original_system, prompts_fusion.get_original_user


def call_llm_model(system_prompt, user_prompt, model="gpt-4o", watttraceid=None, temperature=1.0, json_object=False):
    """
    Calls the appropriate LLM model based on the model parameter
    """
    if model == "gpt-4o":
        model_ = "gpt-4o-2024-11-20"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gpt-4.1" or model == "gpt":
        model_ = "gpt-4.1-2025-04-14"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gpt-4o-mini":
        model_ = "gpt-4o-mini"
        return prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_object=json_object)
    elif model == "gemini-2.5-pro" or model == "gemini-pro":
        model_ = GEMINI_PRO_MODEL
        json_schema = None
        if json_object:
            json_schema = gemini_json_schema
        return prompt_gpt.callGCPGemini(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_schema=json_schema)
    elif model == "gemini-2.0-flash" or model == "gemini-flash" or model == "gemini":
        model_ = GEMINI_FLASH_MODEL
        json_schema = None
        if json_object:
            json_schema = gemini_json_schema
        return prompt_gpt.callGCPGemini(system_prompt, user_prompt, model=model_,
                                        watttraceid=watttraceid, temperature=temperature, json_schema=json_schema)
    else:
        return False, f"Error: Invalid model: {model}"


def process_gpt_output(output_, input_data, with_media, gen_tag, url, token_limit_up, watttraceid):
    """Process GPT output, handle media, hashtags, and character limits."""
    # print(Fore.CYAN + f"output_: {output_}" + Fore.RESET)

    # Pre-process the string to limit tags before JSON parsing
    try:
        MAX_TAGS = 3
        # Regex to find "tags": [...] including potential whitespace
        tags_match = re.search(r'("tags"\s*:\s*\[)(.*?)(\])', output_, re.DOTALL)
        if tags_match:
            prefix = tags_match.group(1) # '"tags": ['
            tags_content = tags_match.group(2).strip() # Content within brackets
            suffix = tags_match.group(3) # ']'
            original_tags_array_str = tags_match.group(0) # The full matched string

            # Split tags carefully, handling potential quotes and whitespace
            # This simple split assumes tags are comma-separated and don't contain commas
            tags = [tag.strip() for tag in tags_content.split(',') if tag.strip()]

            if len(tags) > MAX_TAGS:
                truncated_tags = tags[:MAX_TAGS]
                processed_truncated_tags = []
                for tag in truncated_tags:
                    # Basic check: if it doesn't start and end with ", add them.
                    if not (tag.startswith('"') and tag.endswith('"')):
                        # Remove existing quotes just in case, then add them using json.dumps for safety
                        tag = json.dumps(tag.strip('"'))
                    processed_truncated_tags.append(tag)

                truncated_tags_str = ", ".join(processed_truncated_tags)
                new_tags_array_str = f'{prefix}{truncated_tags_str}{suffix}'
                output_ = output_.replace(original_tags_array_str, new_tags_array_str, 1)
                print(Fore.YELLOW + f"Truncated tags string in raw output: {new_tags_array_str}" + Fore.RESET)

    except Exception as e:
        # Log regex processing error, but proceed to try parsing anyway
        print(Fore.RED + f"Error during tag pre-processing regex: {e} traceback: {traceback.format_exc()}" + Fore.RESET)

    # Main JSON parsing attempt
    try:
        rewritten_content = json.loads(output_)
    except json.JSONDecodeError as e:
        print(Fore.RED + f"Error loading json: {e}" + Fore.RESET)
        # Fallback: Attempt to parse after completely removing the tags field
        try:
            # Regex to remove the entire "tags": [...] field, including optional comma
            output_no_tags = re.sub(r'"tags"\s*:\s*\[.*?\],?\s*', '', output_, flags=re.DOTALL)
            # Clean up potential trailing/leading comma if the tags field was last/first
            output_no_tags = re.sub(r'(,\s*})|({\s*,)', '\1\2', output_no_tags)
            output_no_tags = output_no_tags.replace('{,', '{').replace(',}', '}')
            rewritten_content = json.loads(output_no_tags)
            print(Fore.YELLOW + "Successfully parsed JSON after removing tags string." + Fore.RESET)
            # Ensure tags field exists, even if empty, for downstream consistency
            if "tags" not in rewritten_content:
                rewritten_content["tags"] = []
        except Exception as inner_e:
            print(Fore.RED + f"Failed to parse JSON even after removing tags: {inner_e} traceback: {traceback.format_exc()}" + Fore.RESET)
            return False, f"Error loading json: {e} (final attempt failed)"

    # Post-parsing check (redundant if pre-processing worked, but safe)
    if "tags" in rewritten_content and isinstance(rewritten_content["tags"], list):
        if len(rewritten_content["tags"]) > MAX_TAGS:
             print(Fore.YELLOW + f"Tags list still too long after parsing ({len(rewritten_content['tags'])}) - truncating." + Fore.RESET)
             rewritten_content["tags"] = rewritten_content["tags"][:MAX_TAGS]

    tweet_content = rewritten_content.get("content", "")
    keyword = rewritten_content.get("keyword", "")
    tweet_content = remove_hashtags(tweet_content)
    count_char = str_count(tweet_content)

    media_list = []
    media_count = input_data.get("media_count", 0)
    if with_media:
        if media_count == 0:
            with_media = random.choices([True, False], [0.2, 0.8], k=1)[0]
        if with_media:
            keyword = keyword or input_data.get("keyword_backup", "")
            status_, media_list = search_media(keyword, media_count)
            if not status_:
                print(
                    Fore.RED + f"Error getting media: {media_list}" + Fore.RESET)

    tag_content = rewritten_content.get("tags", [])
    tag_list = []
    count_tag = 0
    if tag_content and gen_tag:
        del rewritten_content["tags"]
        if "keyword" in rewritten_content:
            del rewritten_content["keyword"]
        count_tag = sum(str_count(tag) for tag in tag_content)
        count_char += count_tag
        for keyword in tag_content:
            split_tags = keyword.replace("#", " #").split()
            tag_list.extend('#' + tag.strip('#') for tag in split_tags)
        tweet_content += " " + " ".join(tag_list)

    if url:
        tweet_content += " " + url
        count_char += 23

    if count_char <= token_limit_up:
        rewritten_content["content"] = tweet_content
        if media_list:
            rewritten_content["mediaList"] = media_list
        return True, rewritten_content

    # Retry if content exceeds limit
    retry_count = 0
    retry_max_time = 5
    token_bot, token_up = map(int, input_data["token_limit"].split("-"))
    while retry_count < retry_max_time:
        try:
            input_data["tweet_content"] = rewritten_content.get("content", "")
            token_bot = max(token_bot - 20, 0)
            token_up -= 20
            input_data["token_limit"] = f"{token_bot}-{token_up}"
            system_prompt = prompts_fusion.get_number_system(input_data)
            user_prompt = prompts_fusion.get_number_user(input_data)
            status_, gpt_output = call_llm_model(system_prompt, user_prompt, model="gpt-4o-mini",
                                                 watttraceid=watttraceid, temperature=0.5, json_object=False)
            if status_:
                tweet_content = remove_hashtags(gpt_output)
                count_char = str_count(tweet_content)
                if gen_tag:
                    count_char += count_tag
                    tweet_content += " " + " ".join(tag_list)
                if url:
                    count_char += 23
                    tweet_content += " " + url
                if count_char <= token_limit_up:
                    rewritten_content["content"] = tweet_content
                    if media_list:
                        rewritten_content["mediaList"] = media_list
                    return True, rewritten_content
            else:
                print(Fore.RED + f"Error: {gpt_output}" + Fore.RESET)
            retry_count += 1
        except Exception as e:
            return False, f"调用GPT接口生成推文失败 {str(e)} traceback: {traceback.format_exc()}"

    rewritten_content["content"] = tweet_content
    if media_list:
        rewritten_content["mediaList"] = media_list
    return True, rewritten_content


def generate_tweet(original_content: str, language: int, style_list: list, watttraceid: str = None):
    """
    Generate a tweet content based on original content and language.
    :param original_content: original content
    :param language: language
    :param style_list: style list
    :param watttraceid: watt trace id for logging
    :return: generated content
    """
    if language == 3:
        token_limit_up = 230
        token_bot = token_limit_up - 80 if token_limit_up > 80 else 0
    else:
        token_limit_up = 220
        token_bot = token_limit_up - 80 if token_limit_up > 80 else 0

    style_mapping = {key: writing_styles_mapping[key]
                     for key in style_list if key in writing_styles_mapping}

    language_str = language_mapping.get(language, 'English')
    input_data = {"tweet_content": original_content, "token_limit": f"{token_bot}-{token_limit_up}",
                  "language": language_str, "writing_style": style_mapping}

    system_prompt = prompts_fusion.get_batch_system(input_data)
    user_prompt = prompts_fusion.get_batch_user(input_data)

    status_, output_ = call_llm_model(
        system_prompt, user_prompt, watttraceid=watttraceid, json_object=True)
    if status_:
        return True, output_
    return False, '调用GPT接口生成推文失败'


def rewrite_tweet(company_id: int, x_id: int, user_id: int, content_purpose: int, original_content: str,
                  word_limit: int = 25, with_media: bool = False, reason: str = "", prompt_content: str = None,
                  language: int = 3, event: str = "", action: str = "post", platform: str = "x", product: dict = None,
                  url: str = None, strategies: list = None, model: str = "gpt-4o", watttraceid: str = None,
                  request_id: str = None):
    """
    Online rewrite a tweet content based on user's view point
    :param company_id: company's profile id
    :param x_id: user's x (twitter) id
    :param user_id: GM user's id
    :param content_purpose: content purpose 1: 种草|原创 2: 直销|蹭原帖
    :param original_content: original content
    :param word_limit: word limit
    :param with_media: require media
    :param reason: the detailed reason of choosing the strategy
    :param prompt_content: prompt content
    :param language: language int
    :param event: event summary
    :param action: action
    :param platform: platform
    :param product: product
    :param url: url
    :param strategies: strategies
    :param model: model
    :param watttraceid: watt trace id for logging
    :param request_id: request id
    :return: rewritten content
    """
    gen_tag = False if action == "reply" else True
    if word_limit == 0:
        word_limit = 25
    input_data, token_limit_up, company_profile_exist = prepare_input_data(
        company_id, user_id, x_id, content_purpose, original_content, word_limit, reason, prompt_content,
        language, event, action, platform, product, url, strategies, ENV
    )
    if input_data is None:
        return False, "Error loading profile data"

    system_prompt_func, user_prompt_func = select_prompt_functions(
        company_profile_exist, product, with_media, original_content, action
    )
    system_prompt = system_prompt_func(input_data)
    user_prompt = user_prompt_func(input_data)

    current_app.FlaskLog.info(
        current_app.fmtSaveOutput(request_id, input_data))
    # print(Fore.YELLOW + f"prompt: {user_prompt}" + Fore.RESET)

    status_, output_ = call_llm_model(
        system_prompt, user_prompt, model=model, watttraceid=watttraceid, temperature=0.9, json_object=True)
    if not status_:
        return False, f"调用GPT接口生成推文失败 {output_}"

    return process_gpt_output(output_, input_data, with_media, gen_tag, url, token_limit_up, watttraceid)


def retrieve_rewritten_tweet(x_id: int, tweet_id: str, watttraceid: str = None):
    """
    Retrieve rewritten tweet content based on user's id and tweet id
    :param x_id: user's id (int)
    :param tweet_id: tweet id (string)
    :param watttraceid: watt trace id for logging
    :return: status and content
    """
    status, search_result = writing_mongo.duoBatchSearch(
        "xId", [x_id], "tweetId", [tweet_id])
    is_dict = isinstance(search_result, dict)
    if not status or not is_dict:
        # 需要在线生成
        status_, user_info = user_details.get_user_info(x_id)
        if not status_:
            # print(f"User info not found: {user_info}")
            return False, f"User info not found: {user_info}"
        writing_styles = user_info.get("creative_style", [])
        stat_, response = get_tweet_detail(tweet_id)
        if stat_:
            tweet_detail_list = response.get("tweetDetailList", [])
        else:
            return False, response
        if not tweet_detail_list:
            return False, "Tweet not found"
        tweet_detail = tweet_detail_list[0]
        img_key_list = []
        video_key_list = []
        output_json = {}
        if isinstance(tweet_detail, str):
            try:
                tweet_detail = json.loads(tweet_detail)
            except Exception as e:
                print(f"Error loading json: {e}")
                return False, f"Error loading json: {e}"
        try:
            tweet_id = tweet_detail.get("tweetId", "")
            content = tweet_detail.get("content", "")
        except Exception as e:
            print(f"Error getting tweet content: {e}")
            return False, f"Error getting tweet content {e}"
        if content:
            media_list = tweet_detail.get("mediaList", [])
            if media_list:
                for item in media_list:
                    if item.get("mediaType") == "image":
                        img_path = item.get("imgPath", "")
                        img_key_list.append(img_path)
                    if item.get("mediaType") == "video":
                        video_path = item.get("videoPath", "")
                        # keyframe_key = item.get("keyframePath", "")
                        video_key_list.append(video_path)
            # 执行生成推文模块
            lang_ = lang_detect(content)
            language_ = 3
            if lang_ == 'en':
                language_ = 3
            elif lang_ == 'zh-cn':
                language_ = 1
            elif lang_ == 'zh-tw':
                language_ = 2
            status, generated_tweet = generate_tweet(
                content, language_, writing_styles, watttraceid=watttraceid)
            if not status:
                print("Error: ", generated_tweet)
                return False, f"Error: {generated_tweet}"
            try:
                generated_tweet_json = json.loads(generated_tweet)
            except Exception as e:
                print(f"Error loading json: {e}")
                return False, f"Error loading json: {e}"
            text_list = generated_tweet_json.get("textList", [])
            for text in text_list:
                content = text.get("content", "")
                tag_list = []
                tags = text.get("tags", [])
                for keyword in tags:
                    split_tags = keyword.replace("#", " #").split()
                    corrected_tags = [
                        '#' + tag.strip('#') for tag in split_tags]
                    tag_list.extend(corrected_tags)
                content += " " + " ".join(tag_list)
                text["content"] = content
                text["viewpoint"] = 1
                text["language"] = language_
            output_json["textList"] = text_list
            output_json["imgKeyList"] = img_key_list
            output_json["videoKeyList"] = video_key_list
            output_json["xId"] = x_id
            output_json["tweetId"] = tweet_id
            # output_json["hotspotContent"] = hotspot_content
            # 将生成的推文存入数据库
            status, result = writing_mongo.insertSingleData(output_json)
            if not status:
                print(f"Error inserting mongodb: {result}")
            return True, output_json
        else:
            return False, "Tweet content not found"
    # print(Fore.YELLOW + f"Tweet written found: {search_result}" + Fore.RESET)
    text_list = search_result.get("textList", [])
    for text in text_list:
        try:
            style = text.get("writeStyle", [])
        except Exception as e:
            print(f"Error getting write style: {e}")
            return False, f"Error getting write style {e}"
        if isinstance(style, int):
            text["writeStyle"] = [style]
    return True, search_result


def smart_gen(content: str, promote_target: dict, url: str, event: str, tags: list, watttraceid: str = None):
    input_data = {"tweet_content": content}
    if promote_target:
        input_data = {"target": promote_target}
    if event:
        input_data["event"] = event
    strategy_list = []
    if tags:
        for tag in tags:
            tag_type = tag.get("tag_type", "")
            if tag_type.lower() == "attitude":
                tag_name = tag.get("tag_name", "")
                strategy_list.append(f"Enhance '{tag_name}' attitude")
            elif tag_type.lower() == "emotional resonance":
                tag_name = tag.get("tag_name", "")
                strategy_list.append(f"Evoke '{tag_name}' resonance")
            elif tag_type.lower() == "rhetorics & narratives":
                tag_name = tag.get("tag_name", "")
                strategy_list.append(f"Use '{tag_name}'")

        input_data["strategies"] = strategy_list

    system_prompt = prompts_fusion.get_original_system(input_data)
    user_prompt = prompts_fusion.get_smart_gen_user(input_data)
    status_, output_ = call_llm_model(system_prompt, user_prompt, model="gpt-4o-2024-11-20", watttraceid=watttraceid,
                                      temperature=1.0, json_object=True)
    if status_:
        try:
            rewritten_content = json.loads(output_)
        except json.JSONDecodeError as e:
            print(Fore.RED + f"Error loading json: {e}" + Fore.RESET)
            return False, f"Error loading json: {e}"
        try:
            tweet_content = rewritten_content.get("content", "")
            tweet_content = remove_hashtags(tweet_content)
            count_char = str_count(tweet_content)
            count_word = count_english_words(tweet_content)
            if url:
                tweet_content += " " + url
                count_char += 23
            tag_content = rewritten_content.get("tags", [])
            tag_list = []
            if tag_content:
                count_tag = sum([str_count(tag) for tag in tag_content])
                count_char += count_tag
                for keyword in tag_content:
                    split_tags = keyword.replace("#", " #").split()
                    corrected_tags = [
                        '#' + tag.strip('#') for tag in split_tags]
                    tag_list.extend(corrected_tags)
                print(Fore.YELLOW + f"tag_list: {tag_list}" + Fore.RESET)
                tweet_content += " " + " ".join(tag_list)
            print(Fore.YELLOW +
                  f"tweet_content word count: {count_word}" + Fore.RESET)
            if count_char > 278:
                print(
                    Fore.RED + f"tweet_content: {tweet_content}" + Fore.RESET)
                input_data["tweet_content"] = rewritten_content.get(
                    "content", "")
                status_, tweet_content = call_llm_model(system_prompt, user_prompt, watttraceid=watttraceid,
                                                        temperature=1.0, json_object=True)
                if status_:
                    tweet_content = remove_hashtags(tweet_content)
                    if url:
                        tweet_content += " " + url
                    if tag_list:
                        tweet_content += " " + " ".join(tag_list)
                else:
                    return False, f'二次调用GPT接口生成推文失败 {tweet_content}'
            output_json = {"outputTweet": tweet_content,
                           "strategies": strategy_list}
            return True, output_json
        except Exception as e:
            return False, f'调用GPT接口生成推文失败 {str(e)} traceback: {traceback.format_exc()}'
    return False, f'调用GPT接口生成推文失败 {output_}'
