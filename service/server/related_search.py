import requests
import json
from flask import current_app
from service import LUPAN_HOST, LUPAN_KEY


def search_related_words(request_id, keyword, n):
    current_app.FlaskLog.info(f"request_id = {request_id}, keyword = {keyword}")
    if not keyword:
        return False, f'Error: Keywords is empty!'
    if contain_chinese(keyword):
        status, words = baidu_search(keyword, n)
        if status:
            return True, words
        else:
            return True, english_search(keyword, n)
    else:
        return True, english_search(keyword, n)


# 判断keywords是否含有中文
def contain_chinese(keywords):
    for ch in keywords:
        if '\u4e00' <= ch <= '\u9fff':
            return True
    return False


def search_by_luban(keywords, engine='baidu'):
    headers = {'Call-Source': 'Coze', 'Content-Type': 'application/json'}
    data = {"lupan_key": LUPAN_KEY, "q": keywords}
    base_search_url = LUPAN_HOST
    search_url = base_search_url + '330'
    if engine == 'google':
        search_url = base_search_url + '287'
    elif engine == 'trend':
        search_url = base_search_url + '294'
        data["data_type"] = "RELATED_TOPICS"
    try:
        search_response = requests.post(search_url, headers=headers, json=data).json()
        result = json.loads(search_response['data']['resp'])
        return True, result
    except Exception as e:
        return False, f'Error {str(e)} in {engine} search'


def english_search(keywords, num):
    status, words = google_search(keywords, num)
    if status:
        if len(words) < num:
            trend_status, trend_words = google_trend_related_search(keywords, num)
            if trend_status:
                for i in trend_words:
                    if i not in words:
                        words.append(i)
                    if len(words) == num:
                        break
        return words
    else:
        status, words = google_trend_related_search(keywords, num)
        if status:
            return words
        else:
            return []


def baidu_search(keywords,  num):
    status, result = search_by_luban(keywords, 'baidu')
    if not status:
        return False, result
    people_also_search_for = result.get("people_also_search_for", None)
    if not people_also_search_for:
        output = result.get("organic_results", None)
        return False, output
    else:
        output = []
        for i in range(min(num, len(people_also_search_for))):
            output.append(people_also_search_for[i]['text'])
        if len(output) < num:
            result = english_search(keywords, num)
            for i in result:
                if i not in output:
                    output.append(i)
                if len(output) == num:
                    break
        return True, output


def google_search(keywords, num):
    status, result = search_by_luban(keywords, 'google')
    if not status:
        return False, result
    related_searches = result.get("related_searches", None)
    if not related_searches:
        organic = result.get("organic_results", None)
        return False, organic
    else:
        output = []
        for i in range(min(num, len(related_searches))):
            output.append(related_searches[i]['query'])
            return True, output


def google_trend_related_search(keywords, num):
    status, result = search_by_luban(keywords, 'trend')
    if not status:
        return False, result
    related_topics = result.get("related_topics", None)
    if not related_topics:
        return False, 'No related topics found'
    else:
        output = []
        rising = related_topics.get('rising', None)
        if rising:
            for i in range(len(rising)):
                if rising[i]['topic']['title'].isdigit():
                    continue
                output.append(rising[i]['topic']['title'])
                if len(output) == num:
                    break
        top = related_topics.get('top', None)
        if top:
            for i in range(min(num//2, len(top))):
                if top[i]['topic']['title'].isdigit():
                    continue
                if len(output) == num:
                    output.pop()
                output.append(top[i]['topic']['title'])
        return True, output


if __name__ == "__main__":

    keyword_list = ['Donald trump']
    for k in keyword_list:
        print(search_related_words(0, k, 10))
