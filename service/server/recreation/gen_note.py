from flask import current_app
from service.server.call_gpt import CallGpt
from service.lib.text_process import *
from service.lib.lang_detect import lang_detect
from service.lib.media_search import search_media
from service.lib.prompt_fusion import call_llm_model
from service import prompts_fusion
from service.server.rewrite_tweet import language_mapping
from colorama import Fore
import traceback
import json


# 初始化GPT调用实例
prompt_gpt = CallGpt(model="gpt-4.1", temperature=0.8)
writing_styles_mapping = {
    1: "幽默风趣",
    2: "专业深度",
    3: "激情煽动",
    4: "简洁明了",
    5: "亲切随和"
}

# 基础JSON Schema for structured output  
def get_json_schema(is_rewrite=False, red_note_content=None):
    """
    根据是否为重写场景和是否有历史内容返回对应的JSON Schema
    
    Args:
        is_rewrite: 是否为重写场景
        red_note_content: 上一版本的笔记内容
        
    Returns:
        dict: JSON Schema
    """
    base_schema = {
        "type": "object",
        "properties": {
            "rednoteTitle": {
                "type": "string",
                "description": "小红书笔记标题"
            },
            "rednoteContent": {
                "type": "string", 
                "description": "小红书笔记正文内容"
            }
        },
        "required": ["rednoteTitle", "rednoteContent"]
    }
    
    # 只有在非重写场景且没有历史内容时，才添加 structuredReason 要求
    if not is_rewrite and (not red_note_content or red_note_content.strip() == ""):
        base_schema["properties"]["structuredReason"] = {
            "type": "object",
            "properties": {
                "overallScore": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                },
                "brandFit": {
                    "type": "object", 
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                },
                "industryRelevance": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                },
                "audienceMatch": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                },
                "contentTimeliness": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                },
                "riskAssessment": {
                    "type": "object",
                    "properties": {
                        "score": {"type": "integer", "minimum": 0, "maximum": 100},
                        "description": {"type": "string"}
                    },
                    "required": ["score", "description"]
                }
            },
            "required": ["overallScore", "brandFit", "industryRelevance", "audienceMatch", "contentTimeliness", "riskAssessment"]
        }
        base_schema["required"].append("structuredReason")
    
    return base_schema


def determine_scenario(auditResult, plan_topic, todo_task_title, 
                      red_note_content, source_content, rewrite):
    """
    根据参数判断创作场景
    
    Returns:
        tuple: (scenario, error_message)
        scenario可能的值：
        - "schedule_first": 从schedule卡片进入，第一次创作
        - "schedule_rewrite": 从schedule卡片进入，重写
        - "suggestion_first": 从suggestion进入，第一次创作  
        - "suggestion_rewrite": 从suggestion进入，重写
        - "blank_rewrite": 从空白入口进入，重写
        - "blank_first": 从空白入口进入，第一次创作
        - None: 参数不符合任何场景
    """
    
    if not rewrite:
        # 第一次创作场景
        if auditResult and plan_topic and todo_task_title:
            # 从schedule卡片进入，第一次创作
            return "schedule_first", None
        elif todo_task_title and source_content:
            # 从suggestion进入，第一次创作
            return "suggestion_first", None
        else:
            # 其他第一次创作场景（可能只有prompt_content或todo_task_title）
            return "blank_first", None
    else:
        # 重写场景
        if not red_note_content or red_note_content.strip() == "":
            return None, "重写场景必须提供上一版本的笔记内容"
            
        if todo_task_title and (auditResult or plan_topic):
            # 从schedule卡片进入，重写
            return "schedule_rewrite", None
        elif source_content:
            # 从suggestion进入，重写
            return "suggestion_rewrite", None
        else:
            # 从空白入口进入，重写
            return "blank_rewrite", None


def generate_note(auditResult, plan_topic, plan_description, todo_task_title, 
                  prompt_content, red_note_content, source_content, rewrite, 
                  write_style, language, watttraceid):
    """
    生成小红书笔记
    
    Args:
        auditResult: 诊断报告，营销提案
        plan_topic: 营销计划主题
        plan_description: 营销计划描述
        todo_task_title: AI生成的营销主题计划
        prompt_content: 用户自定义要求
        red_note_content: AI上一次生成的小红书笔记
        source_content: 资讯类原贴原文
        rewrite: 是否重写 (bool)
        write_style: 写作风格列表
        language: 语言类型 (1:英文, 2:繁体中文, 3:简体中文)
        watttraceid: 追踪ID
        
    Returns:
        tuple: (success, result)
    """
    
    try:
        # 1. 判断创作场景
        scenario, error_msg = determine_scenario(
            auditResult, plan_topic, todo_task_title,
            red_note_content, source_content, rewrite
        )
        
        if not scenario:
            return False, f"参数错误: {error_msg}"
        
        current_app.FlaskLog.info(f"[{watttraceid}] 小红书笔记生成场景: {scenario}")

        # 2. 准备prompt数据
        # 判断是否需要结构化推理（CoT）
        need_structured_reason = not rewrite and (not red_note_content or red_note_content.strip() == "")
        
        prompt_data = {
            "scenario": scenario,
            "audit_result": auditResult,
            "plan_topic": plan_topic,
            "plan_description": plan_description,
            "todo_task_title": todo_task_title,
            "prompt_content": prompt_content,
            "rednote_content": red_note_content,
            "source_content": source_content,
            "write_style": [writing_styles_mapping[style] for style in write_style] if write_style else [],
            "language": language if language else 3,
            "is_rewrite": rewrite,  # 添加重写标识供prompt使用
            "need_structured_reason": need_structured_reason  # 添加是否需要结构化推理的标识
        }
        
        # 3. 生成prompt
        system_prompt = prompts_fusion.get_xiaohongshu_gen_note_system(prompt_data)
        user_prompt = prompts_fusion.get_xiaohongshu_gen_note_user(prompt_data)
       
        # 4. 调用LLM生成内容
        # 根据是否为重写场景和历史内容获取对应的JSON Schema
        # json_schema = get_json_schema(is_rewrite=rewrite, red_note_content=red_note_content)
        
        status, response = call_llm_model(
            system_prompt=system_prompt,
            user_prompt=user_prompt,
            model="gpt-4.1",
            watttraceid=watttraceid,
            prompt_gpt=prompt_gpt,
            temperature=0.8,
            json_object=True,
            json_schema=None
        )
        
        if not status:
            current_app.FlaskLog.error(f"[{watttraceid}] LLM调用失败: {response}")
            return False, f"LLM调用失败: {response}"
        
        # 5. 解析响应
        try:
            result = json.loads(response)
            
            # 验证必要字段 - 根据新的逻辑调整验证
            required_fields = ["rednoteTitle", "rednoteContent"]
            if need_structured_reason:
                # 只有在非重写且无历史内容时才需要structuredReason
                required_fields.append("structuredReason")
                
            for field in required_fields:
                if field not in result:
                    return False, f"响应缺少必要字段: {field}"
            
            # 6. 验证并压缩内容，确保符合字数要求
            compress_success, result, compress_error = validate_and_compress_content(
                result, prompt_gpt, watttraceid
            )
            
            if not compress_success:
                current_app.FlaskLog.warning(f"[{watttraceid}] 内容压缩过程出现问题: {compress_error}")
                # 即使压缩失败，也继续返回结果，只是记录警告
            
            current_app.FlaskLog.info(f"[{watttraceid}] 小红书笔记生成成功 - 场景: {scenario}")
            return True, result
            
        except json.JSONDecodeError as e:
            current_app.FlaskLog.error(f"[{watttraceid}] JSON解析失败: {e}, 原始响应: {response}")
            return False, f"响应格式错误: {str(e)}"
            
    except Exception as e:
        current_app.FlaskLog.error(f"[{watttraceid}] 小红书笔记生成异常: {str(e)}")
        current_app.FlaskLog.error(f"[{watttraceid}] 异常堆栈: {traceback.format_exc()}")
        return False, f"生成过程异常: {str(e)}"


def validate_and_compress_content(result, prompt_gpt, watttraceid):
    """
    验证并压缩标题和内容，确保符合字数要求
    Args:
        result: dict, 包含rednoteTitle和rednoteContent的结果
        prompt_gpt: GPT调用实例
        watttraceid: 追踪ID
    
    Returns:
        tuple: (success, updated_result, error_message)
    """
    try:
        title = result.get("rednoteTitle", "")
        content = result.get("rednoteContent", "")
        
        # 定义字数限制
        TITLE_LIMIT = 20
        CONTENT_LIMIT = 800
        
        # 检查标题字数
        title_char_count = count_characters(title)
        if title_char_count > TITLE_LIMIT:
            current_app.FlaskLog.info(f"[{watttraceid}] 标题超长({title_char_count}字)，开始压缩")
            success, compressed_title = compress_text_with_shrink(
                title, TITLE_LIMIT, prompt_gpt, watttraceid
            )
            if success:
                result["rednoteTitle"] = compressed_title
                current_app.FlaskLog.info(f"[{watttraceid}] 标题压缩成功: {count_characters(compressed_title)}字")
            else:
                current_app.FlaskLog.warning(f"[{watttraceid}] 标题压缩失败，保持原内容")
        
        # 检查内容字数
        content_char_count = count_characters(content)
        if content_char_count > CONTENT_LIMIT:
            current_app.FlaskLog.info(f"[{watttraceid}] 内容超长({content_char_count}字)，开始压缩")
            success, compressed_content = compress_text_with_shrink(
                content, CONTENT_LIMIT, prompt_gpt, watttraceid
            )
            if success:
                result["rednoteContent"] = compressed_content
                current_app.FlaskLog.info(f"[{watttraceid}] 内容压缩成功: {count_characters(compressed_content)}字")
            else:
                current_app.FlaskLog.warning(f"[{watttraceid}] 内容压缩失败，保持原内容")
        
        # 记录最终字数
        final_title_count = count_characters(result.get("rednoteTitle", ""))
        final_content_count = count_characters(result.get("rednoteContent", ""))
        current_app.FlaskLog.info(f"[{watttraceid}] 最终字数 - 标题: {final_title_count}字, 内容: {final_content_count}字")
        
        return True, result, None
        
    except Exception as e:
        error_msg = f"内容验证和压缩过程异常: {str(e)}"
        current_app.FlaskLog.error(f"[{watttraceid}] {error_msg}")
        return False, result, error_msg