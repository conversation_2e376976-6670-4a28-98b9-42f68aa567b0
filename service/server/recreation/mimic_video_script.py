from typing import Tuple, List
import json
from ..call_gpt import CallGpt
from service.lib.prompt_fusion import PromptsFusion
from service.server import load_user
from service.lib.text_process import safe_get
from colorama import Fore
from config import ENV

prompt_gpt = CallGpt(temperature=0.9)
prompts_fusion = PromptsFusion()


# Define schema using a dictionary format that the Gemini API can handle
json_schema = {
    "type": "OBJECT",
    "properties": {
        "title": {"type": "STRING"},
        "desc": {"type": "STRING"},
        "shots": {
            "type": "ARRAY",
            "items": {
                "type": "OBJECT",
                "properties": {
                    "shotId": {"type": "STRING"},
                    "shotTitle": {"type": "STRING"},
                    "shotContent": {"type": "STRING"},
                    "transcript": {"type": "STRING"},
                    "soundEffect": {"type": "STRING"},
                    "environment": {"type": "STRING"},
                    "shotType": {"type": "STRING"}
                },
                "required": ["shotId", "shotTitle", "shotContent", "transcript", "soundEffect", "environment", "shotType"]
            }
        }
    },
    "required": ["title", "desc", "shots"]
}


def get_company_profile_data(company_id, user_id, env):
    """Fetch and prepare company profile data."""
    company_profile_id = f"{env.lower()}_{company_id}_{user_id}"
    status, company_profile = load_user.get_company_memory(company_profile_id)
    if status:
        return {"company_profile": company_profile, "media_count": 0}
    print(Fore.RED + f"Error loading company profile: {company_profile}" + Fore.RESET)
    return None


def generate_video_script_mimic_tiktok(user_id: str, profile_id: str, video_description: str, reason: str, requirement: str, platform: str, platform_id: str, language: str, watttraceid: str) -> Tuple[bool, dict]:
    profile_data = get_company_profile_data(profile_id, user_id, ENV)
    if profile_data is None:
        return False, {"error": "Profile data not found"}

    # Build input data dictionary with all parameters
    input_data = {
        "language": language,
        # Requirement data
        "duration": safe_get(requirement, 'videoDuration'),
        "video_style": safe_get(requirement, 'videoStyle'),
        "video_content_description": safe_get(requirement, 'videoContentDescription'),
        "brand_positioning": safe_get(requirement, 'brandPositioning'),
        "core_competency": safe_get(requirement, 'coreCompetency'),
        "target_audience": safe_get(requirement, 'targetAudience'),
        "action": safe_get(requirement, 'action'),
        
        # Video description data
        "pace": safe_get(video_description, 'structureAnalysis', 'pace'),
        "bgm_style": safe_get(video_description, 'soundAnalysis', 'bgmStyle'),
        "narration_tone": safe_get(video_description, 'soundAnalysis', 'narrationTone'),
        "narration_pace": safe_get(video_description, 'soundAnalysis', 'narrationPace'),
        "script_style": safe_get(video_description, 'contentAnalysis', 'scriptStyle'),
        "core_themes": safe_get(video_description, 'contentAnalysis', 'coreThemes'),
    }

    input_data.update(profile_data)

    # Add optional call to action if present
    call_to_action = safe_get(video_description, 'contentAnalysis', 'callToAction')
    if call_to_action:
        input_data["call_to_action"] = call_to_action
    
    # Add reason data if available
    if reason is not None:
        reason_fields = {
            "has_reason": True,
            "overall_score": ('overallScore', 'score'),
            "brand_fit_score": ('brandFit', 'score'),
            "industry_relevance_score": ('industryRelevance', 'score'),
            "audience_match_score": ('audienceMatch', 'score'),
            "risk_assessment_score": ('riskAssessment', 'score'),
            "content_timeliness_score": ('contentTimeliness', 'score'),
            "overall_score_description": ('overallScore', 'description'),
            "brand_fit_description": ('brandFit', 'description'),
            "industry_relevance_description": ('industryRelevance', 'description'),
            "audience_match_description": ('audienceMatch', 'description'),
            "content_timeliness_description": ('contentTimeliness', 'description'),
            "risk_assessment_description": ('riskAssessment', 'description'),
        }
        
        # Add all reason fields to input_data
        for field_name, keys in reason_fields.items():
            if isinstance(keys, (list, tuple)):
                input_data[field_name] = safe_get(reason, *keys)
            else:
                # Handle the case where keys is not an iterable
                input_data[field_name] = safe_get(reason, keys)
    
    # Get prompt using PromptsFusion
    system_prompt = prompts_fusion.get_mimic_tiktok(input_data)
    user_prompt = prompts_fusion.get_mimic_tiktok_user(input_data)
 
    # status_, response = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
    #                                             temperature=1.0, json_object=True)
    status_, response = prompt_gpt.callGCPGemini(system_prompt, user_prompt, model='gemini-2.5-flash-preview-04-17', watttraceid=watttraceid,
                                                 temperature=1.0, json_schema=json_schema, max_out_tokens=8192)
    if status_:
        try:
        #   print(Fore.GREEN + f"LLM response: {response}" + Fore.RESET)
          response_data = json.loads(response)
          return True, response_data
        except Exception as e:
          print(Fore.RED + f"Error parsing LLM response: {e}" + Fore.RESET)
          return False, {"error": str(e)}
    else:
        print(Fore.RED + f"Error calling LLM: {response}" + Fore.RESET)
        return False, {"error": response}