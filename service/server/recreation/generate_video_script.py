from service.server.recreation.media_understand import media_content
from service.lib.media_llm import chat_gpt, remove_urls


def generate_script(product_info: dict, source: dict, strategy: list):
    """
    Generate video script
    :param product_info: product information, a dict object,
    e.g,{"name": "Sample Product", "description": "This is a sample product description.","keywords": ["keyword1",
    "keyword2", "keyword3"]}
    :param source: source, a dict object, e.g, {"sourceType": 1,"originalContent": "This is the original content of
    the tweet or video.", "mediaList": [{"mediaType": "image", "downloadSource": "twitter", "imgPath": "c2","imgUrl":
    "https://d1g", "keyframePath": "", "keyframeUrl": "", "videoPath": "", "videoUrl": ""}]}
    :param strategy: use for controle script generation, a list object, e.g, [ "engagement", "awareness", "conversion"]
    :param source_result: source result, a dict object, e.g, {"content": 1, "media_result": "This is the original
    the product_info and source could be empty, but cannot be both empty
    """
    product_name = product_info.get("name", '')
    if product_name:
        product_description = product_info.get("description")
        product_keywords = product_info.get("keywords")

        system_prompt = """You are a professional marketing expert specializing in product promotion. Your are good at promoting product through video, especially writing voice-over script that fits the product and market. User give you a product information and some supplementary content, you should use these resources to generate a voice-over script."""

        user_prompt = f"Here is the product to be promoted named <{product_name}>, with a description:\n[description starts]\n{product_description}\n[description ends]\n"

        if product_keywords:
            user_prompt += f"It has some representative product tags as: {', '.join(product_keywords)}.\n"
        if source:
            original_content = source.get("originalContent", '')
            media_result = understand_source_media(source.get("mediaList", []))
            if original_content or media_result:
                user_prompt += f"As the product promotion needs to fit the market, here are some social media content which could be helpful to understand the market:\n[social_media_post starts]\n"
                if source.get("sourceType", 0) == 1:
                    original_content = remove_urls(original_content)
                if original_content:
                    user_prompt += f"The text of the social media post is:\n  {original_content}\n"
                if media_result:
                    user_prompt += f"The visual content are:\n"
                    for index, single_result in enumerate(media_result):
                        user_prompt += f"    {str(index + 1)}. {single_result['mediaType']}:\n{single_result['content']}"
                user_prompt += '[social_media_post ends]\n'

        if strategy:
            user_prompt += f"There are some promoting attitude or strategy keywords as: {', '.join(strategy)}. When generating voice-over script, you should consider this attitude or strategy into it.\n"
        user_prompt += """Please generate a voice-over script for product promotion based on the above information. Requirements:
        1. The script should be attractive and persuasive, and highlight the features of the product
        2. Script should be in English and less than 100 words
        3. Output should only be a text paragraph."""
    else:
        system_prompt = """You are a professional video expert good at writing voice-over script. User give you some useful content, you should use these resources to generate a voice-over script."""
        if not source:
            return False, 'Product and source cannot be both empty'
        original_content = source.get("originalContent", '')
        media_result = understand_source_media(source.get("mediaList", []))
        if original_content or media_result:
            user_prompt = "There are some social media content from social media post:\n[social_media_post starts]\n"
            if source.get("sourceType", 0) == 1:
                original_content = remove_urls(original_content)
            if original_content:
                user_prompt += f"The text of the social media post is:\n  {original_content}\n"
            if media_result:
                user_prompt += f"The visual content are:\n"
                for index, single_result in enumerate(media_result):
                    user_prompt += f"    {str(index + 1)}. {single_result['mediaType']}:\n{single_result['content']}"
            user_prompt += '[social_media_post ends]\n'
        else:
            return False, 'Without product, the content and media list of the source are all invalid '
        user_prompt += """Please generate a voice-over script based on the above information. Requirements:
                1. The script content are generated based on the content of the social media post, but don't be exactly the same.
                2. You can diverge the script content further to some other fields which the social media post can be related to.
                3. Script should be in English and less than 100 words
                4. Output should only be a text paragraph."""
    return chat_gpt(sys_prompt=system_prompt, user_prompt=user_prompt)


def understand_source_media(media_list: list) -> (bool, list):
    """
    Understand source media
    :param media_list: a list object, e.g, [{"mediaType": "image", "imgUrl": "twitter","videoUrl"}]
    """
    if not media_list:
        return []
    media_result = []
    for media in media_list:
        status, result = media_content(media)
        if status:
            media_result.append({'mediaType': media['mediaType'], 'content': result})
    return media_result


if __name__ == "__main__":
    a = {
        "productInfo": {
            "name": "Lilium - Wikipedia",
            "description": "The Lilium Wikipedia page provides comprehensive information about the genus Lilium, commonly known as lilies. It covers a variety of species, their classifications, cultivation methods, and uses in cuisine and medicine. The page details various hybrids and their characteristics, emphasizing the importance of lilies in different cultures. It highlights the beauty and diversity of lily flowers, which are known for their fragrant, trumpet-shaped blooms in a range of colors. Additionally, the page addresses ecological concerns affecting lily populations and their symbolic significance in Christianity. Whether you're a horticulturist, a culinary enthusiast, or simply a flower lover, this page is a valuable resource for understanding lilies and their various applications.",
            "keywords": [
                "lilium",
                "flower",
                "garden"
            ]
        },
        "source": {
            "sourceType": 1,
            "originalContent": "Found some bigger leds I'm going to finish these lady's with. Then I'll be stepping my game up for the indoor season! https://t.co/QRJEbmiKCu",
            "mediaList": [
                {
                    "mediaType": "video",
                    "downloadSource": "twitter",
                    "imgPath": "",
                    "imgUrl": "",
                    "keyframePath": "c2fb1c53e70e3831ab0662aba3748a0c/e3d6a0a242f3863e2778fe5c739970f1",
                    "keyframeUrl": "https://d1g5rjveuc2ya5.cloudfront.net/c2fb1c53e70e3831ab0662aba3748a0c/e3d6a0a242f3863e2778fe5c739970f1?Expires=1731966170\u0026Signature=DjpCFY~7DrnWCObVFuFEupCYf3x1Nml5PZ2eNQUYThmb060Ghxmbd3RvsHpvgt7Y3L0vPKzdDlnJfkUIerGX~Reg3EO06T~3CS9yRvsCAPKovFtDIv9xHKAizsurarTsc~1eze9l1-Z3TmQrOyEGomBnCzA3J6kIOc23I3zyzkzyjkfgMUfBs-TlkXbC6FE4Faarr1hcx5aaRdzBC1S0CW~tKfaX50jZaNjxSYT~YRCz8ULjZQai0k5n4q08O9HPduYogljztzhnInwpjKjUDVsVQzaX3Z74sUddDSguHUyRzfu11b6NpShIFbSsSrDsQjHB1NJXpQ~8p3RSJ2qNNg__\u0026Key-Pair-Id=K1WJZL3MXMCMUT",
                    "videoPath": "c2fb1c53e70e3831ab0662aba3748a0c/30b46e7e56ab3ba747b4d3949e8a49a5",
                    "videoUrl": "https://d1g5rjveuc2ya5.cloudfront.net/c2fb1c53e70e3831ab0662aba3748a0c/30b46e7e56ab3ba747b4d3949e8a49a5?Expires=1731966170\u0026Signature=l2P-yrtLmYRfDKqvHM~EmElsgXYtORjdlmK362R-VEANmv4aQ1oBAbU~-EDBZaNxMv2rNLi31JWHYE2xgwZbTD6fYDqQXAqavLpCErOgcKmQcOoG4Jjo~pGk8U6kuVp0OfUvhRdQK65DUMu-BE~h53kDJCRsl~ZlTy8P2uKoyaQPSEMLYlHxqzGCbnG5E62zckQCdfSgRI8Fls8n0-afXz~Mtm45PJ5t7he7FBXDfarv6h5TB2-w5eOeG74c4V6Q4l87TvCZPCyYEe1RMAO~2fCF~nO0Z22XeNYsfhlrqXyha84LJv24dzxvMSKkMoAhAb55fSEDo3AhPGAOz2Wryw__\u0026Key-Pair-Id=K1WJZL3MXMCMUT"
                }
            ]
        }
    }
    media_result = [{'mediaType': 'image',
                     'content': 'this is a view of a mountain, with a river in the middle, and a forest on the side'},
                    {'mediaType': 'video',
                     'content': "The video appears to focus on a vibrant and colorful performance, featuring a woman in striking outfits amidst an enthusiastic crowd, reflecting themes of glamour and spectacle."},
                    {'mediaType': 'video',
                     'content': "The video appears to showcase a vibrant and lively party atmosphere, with colorful outfits, energetic dancing, and a fun, carefree vibe."}]
    generate_script(a['productInfo'], a['source'], ["engagement", "awareness", "conversion"],{})