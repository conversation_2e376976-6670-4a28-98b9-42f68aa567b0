from service.lib.media_llm import gpt_with_media, process_video


def media_content(media: dict) -> (bool, str):
    sys_ = "You are a helpful AI assistant in recognizing the content of image and video. User provide you with media, you should return the content of the media in JSON format."

    if media["mediaType"] == 'image':
        # user_ = "Here is a image, please tell me what the image is talking about and its high-points. "
        user_ = """Describe the content of an image systematically, covering the following aspects:
        General Description: An overall summary of what the image depicts, including its theme, main content, and key highlights.
        Key Elements: The primary objects, people, celebrity, or notable features in the image. 
        Background and Setting: The setting of the image, such as location, time of day, or weather, the color scheme or lighting effects.
        Feelings and Associations: The emotions or thoughts the image evokes, and any scenarios or concepts it might remind you of, considering its visual and contextual details.
        Ensure your description is structured and thorough, adapting the focus based on the purpose, whether for observation, storytelling, or analysis.
        Output should be less than 120 words."""
        status, answer = gpt_with_media([media["imgUrl"]], sys_, user_, json_schema=image_json, max_tokens=500)
        if not status:
            return False, answer
        key_name = ["General_Description", "Key_Elements", "Background_and_Setting", "Feelings_and_Associations"],
        output = ''

        try:
            for key in key_name:
                output += f'    [{key}]: {answer[key]}\n'
        except:
            for key, value in answer.items():
                output += f'    [{key}]: {value}\n'
        return True, output

    elif media["mediaType"] == 'video':
        status_, frames = process_video(media["videoUrl"])
        if not status_:
            return False, frames
        user_ = "Here are some frames from a video."
    else:  # clip
        user_ = "Here are some frames from a video clip."
        frames = media["base64String"]

    base_video_prompt = """Describe these frames systematically by focusing on:
    Main Elements: Identify and describe the primary objects, people, celebrity, actions, or features in the video.
    Dynamics: Highlight frames connections, noting visible movements, shifts, or transformations between frames.
    Themes: Identify recurring elements or primary actions that unify the frames into a cohesive sequence.
    Background and Atmosphere: Summarize the consistent or evolving elements of the environment or context shown across frames. Mention the general tone conveyed by the sequence of frames.
    Summary: Conclude with a concise overview of the video, summarizing the main event or idea illustrated by the frames.
    Keep the description structured and clear.
    Output should be less than 120 words"""
    user_ += base_video_prompt
    status, answer = gpt_with_media(frames, sys_, user_, json_schema=video_json, max_tokens=500)
    if not status:
        return False, answer
    key_name = ["Main_Elements", "Dynamics", "Themes", "Background_and_Atmosphere", "Summary"]
    output = ''
    try:
        for key in key_name:
            output += f'    [{key}]: {answer[key]}\n'
    except:
        for key, value in answer.items():
            output += f'    [{key}]: {value}\n'
    return True, output


image_json = {
        "name": "Image_analyze_and_describe",
        "description": "Describe the content of an image systematically in required fields.",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "General_Description": {
                    "type": "string",
                    "description": "An overall summary of what the image depicts, including its main theme and key highlights",
                },
                "Key_Elements": {
                    "type": "string",
                    "description": "primary objects, people, celebrity, or features in the image, ",
                },
                "Background_and_Setting": {
                    "type": "string",
                    "description": "The background and setting of the image",
                },
                "Feelings_and_Associations": {
                    "type": "string",
                    "description": "emotions or thoughts the image evokes, or the scenarios it reminds you of",
                },
            },
            "required": ["General_Description", "Key_Elements", "Background_and_Setting", "Feelings_and_Associations"],
            "additionalProperties": False
        }
    }

video_json = {
        "name": "Video_frames_analyze_and_describe",
        "description": "Describe the content of frames from video systematically in required fields.",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "Main_Elements": {
                    "type": "string",
                    "description": "primary objects, people, celebrity, or features",
                },
                "Dynamics": {
                    "type": "string",
                    "description": "frames connection, visible movements, shifts, or transformations",
                },
                "Themes": {
                    "type": "string",
                    "description": "recurring elements or primary actions unify the frames into a cohesive sequence",
                },
                "Background_and_Atmosphere": {
                    "type": "string",
                    "description": "environment or context shown across frames, general tone conveyed by the sequence of frames",
                },
                "Summary": {
                    "type": "string",
                    "description": "concise overview of the video, the main event or idea",
                },
            },
            "required": ["Main_Elements", "Dynamics", "Themes", "Background_and_Atmosphere", "Summary"],
            "additionalProperties": False
        }
    }


def media_content_mock(media: dict) -> (bool, str):
    mock = "this is a view of a mountain, with a river in the middle, and a forest on the side"
    return True, mock