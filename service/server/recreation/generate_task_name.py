from service.lib.media_llm import chat_gpt
import re


def get_task_name(product, task_description, x_info, task_type):
    system_prompt = "You are a helpful AI assistant. User now needs your help to generate a task name based on the following information."
    product_name = product.get('name', '')
    product_description = product.get('description', '')
    if product_name:
        user_prompt = "A task name is for users to distinguish the difference between created tasks at a glance. So the task name normally present the mainly purpose of the task. The task is for a product or service promotion, so you should focus on the following information."
        user_prompt += f"The product or service to be promoted is: {product_name}."
        if product_description:
            user_prompt += f"With description as: {product_description}."
        if task_description:
            user_prompt += f"Also, there are some preference or slogan which are preferred for this promotion: {task_description}."
        user_prompt += f"""Please generate a task name based on the above information. The task name should prioritize the product, then the user's preference or slogan. Requirment: 1.Do not blankly annotate the promotion, 2. The task name should be less then 8 words, 3. Output only the task name as the result."""
    else:
        if task_description:
            user_prompt = f"""A task name is for users to distinguish the difference between created tasks at a glance. The task is for a post publication, and user provide some preference or slogan as: {task_description}. 
            Please generate a task name based on the above information. The task name should present the user's preference or slogan. Requirment: 1.Do not blankly annotate the promotion and the preference, 2. The task name should be less then 8 words, 3. Output only the task name as the result."""
        else:
            # 1 热点发帖 2 个人随笔
            user_description = x_info[0].get('glimpse', '')
            if task_type == 1:
                user_prompt = "A task name is for users to distinguish the difference between created tasks at a glance. The task is for a post publication, and user now is posting a social media post on certain hot topics."
                if user_description:
                    user_prompt += f"The user is with below description: {user_description}"
                user_prompt += f"As the user is just posting a post for riding th trend, the task name should be in style like: 'xxx rides the trend'. Requirment: 1.The task name should be less then 8 words, 2. Output only the task name as the result."
            else:
                user_prompt = "A task name is for users to distinguish the difference between created tasks at a glance. The task is for a post publication, and user now is posting a personal essay."
                if user_description:
                    user_prompt += f"The user is with below description: {user_description}"
                user_prompt += f"As the user is just posting a personal essay, the task name should be in style like: 'xxx writes personal essay'. Requirment: 1.The task name should be less then 8 words, 2. Output only the task name as the result."

    status, result = chat_gpt(system_prompt, user_prompt, model='gpt-4o-mini', temperature=1.3)
    if status:
        result = re.sub(r"[\"']", "", result)
    return status, result