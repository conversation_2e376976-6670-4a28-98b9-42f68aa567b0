import re
from service.lib.media_llm import chat_gpt


def get_video_caption(script_list):
    text = ''
    for i in script_list:
        text += i['script']
    if not text:
        return False, 'No scripts found!'
    sys_prompt = "You are a professional expert specializing in text editing. User give you a written text, you should follow user's requirement to answer"
    user_prompt = f"""There is a voice-over text of a video: {text}
    you should understand its content and points to generate a brief video introduction with tags. The introduction and tags will be added together to the video, then published in Tiktok. So you should follow the requirement below:
    1. introduction are used when user post a tiktok, so catch a point, then feel free to use you talent,
    2. one tag is a single word or a phrase, use '#' to mark tag. Try to extract 1~3 tags, if there is no tag extracted, omit it.
    3. output you answer text as result,
    4. text length should be less than 130 characters.
"""
    status, output = chat_gpt(sys_prompt, user_prompt, temperature=1.2)
    if not status:
        return False, 'Error in generating caption'
    return True, text_check(output)


def text_check(input_string):
    if len(input_string) <= 150:
        return input_string
    # 分离句子和标签部分
    parts = re.split(r'(\s#[\w#]+)', input_string)
    sentences = parts[0].strip()  # 前面的句子部分
    tags = ''.join(parts[1:]).strip()  # 标签部分
    # 将句子分割为单独句子
    sentence_list = re.split(r'(?<=[.!?])\s+', sentences)
    # 如果有标签，删除第一个句子；否则删除最后一个句子
    if sentence_list:
        sentence_list.pop()  # 删除最后一个句子
    # 重新拼接句子和标签
    modified_sentences = ' '.join(sentence_list).strip()
    return f"{modified_sentences} {tags}".strip()



if __name__ == "__main__":
    test_1 = """Level up your fun with the Nintendo Switchm! Enjoy gaming anywhere with vibrant joy-Con" controllers. Let's play together #NintendoSwitch #JoyCol #gaming"""
    print(text_check(test_1))