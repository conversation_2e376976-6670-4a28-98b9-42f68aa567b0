from service.lib.media_llm import chat_gpt


def get_match(media_list: list[dict], script: str) -> (bool, list[dict]):
    """the script a written voice-over text, and the media_list is a list of media objects,
     the function is to select suitable part from the media_list and match them with the script
     :param media_list: a list of media objects, each object contains mediaType, mediaId,
     clipId, projectId, description
     :param script: a written voice-over text
     return: a list of media objects, each object contains mediaType, mediaId, clipId,
     and part of the original script
     """
    json_schema = {
        "name": "split_given_text",
        "description": "split given text into several parts",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "split_result": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "script": {
                                "type": "string",
                                "description": "The split parts from given text"
                            },
                        },
                        "required": ["script"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["split_result"],
            "additionalProperties": False
        }
    }
    # visual_content = ""
    # for index, media in enumerate(meidia_list):
    #     media_type = 'image'
    #     if media['mediaType'] == 'clip':
    #         media_type = 'video clip'
    #     visual_content += f"{str(index+1)}.This is a {media_type} expressing:\n{media['description']}"

    sys_prompt = "You are a professional expert specializing in text editing. User give you a written text, you should follow user's requirement and return with JSON format"
    user_prompt = f"""There is a written text for video use:\n[text starts]\n{script}\n[text ends]\nYour task is to split the text into several parts in order, and each part contains 1 to 2 sentences. Requirements: 1. Output a list of split text, 2. The order of the split text in the list must follow their order in origin text,3. Do not add or omit any word when splitting, 4. Each split text should be less than 200 characters in total.
    Provide the final output in JSON format as required.
    """
    status, result = chat_gpt(sys_prompt, user_prompt, json_schema=json_schema)
    if not status:
        return False, result
    try:
        split_result = result['split_result']
    except Exception as e:
        return False, f"Error {e} in get match result: {result}"
    media_length = len(media_list)
    text_length = len(split_result)
    if media_length >= text_length:
        for i, item in enumerate(split_result):
            item["mediaType"] = media_list[i]['mediaType']
            item["mediaId"] = media_list[i]['mediaId']
            item["clipId"] = media_list[i]['clipId']
    else:
        factor = text_length / media_length
        start = 0
        for media in media_list:
            end = round(start + factor)
            end = min(end, text_length)
            # 给切片中的每个字典添加短列表的当前元素
            for item in split_result[start: end]:
                item["mediaType"] = media['mediaType']
                item["mediaId"] = media['mediaId']
                item["clipId"] = media['clipId']
            start = end
        if start < len(split_result):
            # 将剩余部分分配给最后一个媒体
            for item in split_result[start:]:
                item["mediaType"] = media_list[-1]['mediaType']
                item["mediaId"] = media_list[-1]['mediaId']
                item["clipId"] = media_list[-1]['clipId']
    return True, split_result


def get_match_mock(meidia_list: list[dict], script: str) -> (bool, list[dict]):
    script_list = script.split(" ")
    media_num = len(meidia_list)
    output = []

    # 确保 n 不超过列表长度
    n = min(media_num, len(script_list))
    # 计算每份的基础大小和多出的元素个数
    chunk_size = len(script_list) // n
    remainder = len(script_list) % n

    start = 0
    for i in range(n):
        # 每一部分包含基础大小的元素，前 remainder 部分多一个元素
        end = start + chunk_size + (1 if i < remainder else 0)
        output.append({"mediaType": meidia_list[i]['mediaType'],
                       "mediaId": meidia_list[i]['mediaId'],
                       "clipId": meidia_list[i]['clipId'],
                       "script": " ".join(script_list[start:end])})
        start = end
    return True, output


if __name__ == "__main__":
    # sys_prompt = """You are a professional marketing expert specializing in product promotion. Your are good at promoting product through video. User give you a written text, and several visual materails, you should follow user's requirement and return with JSON format."""
    """
        Video is a good form of expression for production promotion.The visual content of the video will have a huge impact on the effect of product promotion. Appropriate video vision will make the promotional video reasonable and powerful. Your task is to select the appropriate visual content from the following list to match the written text. The chooseble visual content are given with id:\n[media_list starts]\n{visual_content}[media_list ends]\nBefore providing your final output, break down your thought process. Include the following steps:
        1. The given text will be used as the voice-over script for the video, so you should first use the given text to calculate the length of the accompanying video by reading speed
        2. Then use your professional product promotion knowledge to split the text into several parts and get the voice-over scripts in order
        3. Make sure there is no redundancy or omission when splitting the text into voice-over scripts
        4. For each individual script obtained after splitting, select a visual content from the given media list, based on content relevance or appropriate expression
        5. Select a suitable visual content for each script in order\nRequirements
        - Try to split the single voiceover script into complete sentences, but less than 15 words
        - The id of the visual content should be used to identify the visual content
        - The split scripts should be completely consistent with the original text after being combined in order
        - Output a list corresponding to the script and the visual content, and the order of the elements in the list is the order when the text is split
        Provide the final output in JSON format as required.
        """
    a = {
        "userSelectList": [
            {
                "mediaType": "clip",
                "mediaId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "clipId": "",
                "projectId": "project-12345",
                "description": "    [Main_Elements]: A performer with vibrant red hair, wearing a crown and bold attire, along with dancers.\n    [Dynamics]: The performer and dancers are actively dancing, with expressive hand movements and shifting positions.\n    [Themes]: Energetic dancing, expressive gestures, colorful costumes, and vibrant personalities.\n    [Background_and_Atmosphere]: A vividly colorful room with abstract patterns, creating a lively and fun atmosphere.\n    [Summary]: The video clip features a vivacious performance by a central figure, engaging in dynamic dance sequences with others in a colorful, playful setting, emphasizing energy and excitement.\n"
            },
            {
                "mediaType": "clip",
                "mediaId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "clipId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "projectId": "project-12345",
                "description": "    [Main_Elements]: A woman with vibrant red hair, various poses, and photographers.\n    [Dynamics]: She moves her arms energetically, interacts with flashes and cameras.\n    [Themes]: Performance, fashion, and glamour.\n    [Background_and_Atmosphere]: Colorful, dynamic settings with a lively, upbeat mood.\n    [Summary]: The video clip features a charismatic woman posing and performing in vibrant, energetic scenes while photographers capture the action, highlighting themes of fashion and fame.\n"
            },
            {
                "mediaType": "clip",
                "mediaId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "clipId": "",
                "projectId": "project-12345",
                "description": "    [Main_Elements]: The video features a woman with vibrant red hair, engaging in expressive dance and posing.\n    [Dynamics]: The frames transition as the woman moves energetically, gesturing with her hands and interacting with props like microphones.\n    [Themes]: Recurring elements include colorful clothing, dramatic makeup, and dynamic dance movements, creating a lively sequence.\n    [Background_and_Atmosphere]: The background changes from energetic, zebra-printed walls to plain blue, conveying a playful and vibrant tone.\n    [Summary]: The video showcases a woman in a bold, spirited performance, emphasizing colorful expression and movement against dynamic backdrops.\n"
            },
            {
                "mediaType": "image",
                "mediaId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "clipId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "projectId": "project-12345",
                "description": "    [General_Description]: The image is a creative cover art featuring a man at a grand piano with a reflection of him on the piano's lid, set against an urban street backdrop.\n    [Key_Elements]: The main focus is on the man sitting at the piano. There is a reflection of him, highlighting his presence and the musical theme.\n    [Background_and_Setting]: The setting appears to be a city street, with buildings visible in the background. The colors are vibrant, indicating a bright daytime scene. The piano adds an elegant contrast to the urban environment.\n    [Feelings_and_Associations]: The image evokes a sense of introspection and creativity. It may remind one of urban life interwoven with art and music, suggesting themes of expression and reflection.\n"
            },
            {
                "mediaType": "clip",
                "mediaId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "clipId": "c2fb1c53e70e3831ab0662aba3748a0c/26c4a3e35a6894d4a4eccd1ba5a7ab53",
                "projectId": "project-12345",
                "description": "    [Main_Elements]: A person with light hair in a blue jacket is seated, holding a microphone. Other individuals appear intermittently, engaging with the primary person.\n    [Dynamics]: The seated person moves slightly, gestures, and interacts with others who come into view. Another person approaches and stands close, engaging in conversation or activity.\n    [Themes]: Interaction and casual conversation or practice are evident, with a focus on camaraderie and engagement among the individuals.\n    [Background_and_Atmosphere]: The setting is a dimly lit room, possibly a rehearsal or recording space, suggesting an informal and relaxed atmosphere.\n    [Summary]: The video features individuals interacting in a casual setting, focusing on practice or conversation, creating a light-hearted and engaging atmosphere.\n"
            },
        ],
        "videoScript": "Unlock the world of Lilium on Wikipedia—a treasure trove for both the curious and the connoisseur. Dive into the rich tapestry of lilies, where each petal spells beauty and diversity. From their fragrant trumpet-shaped blooms that captivate gardens, to their revered roles in cuisine, medicine, and culture, Lilium is more than just a flower—it's an experience. Unearth valuable insights on their myriad species, vibrant hybrids, and cultivation secrets, all within a single click. With its deep roots in history and symbolism, discover why lilies stand as timeless icons in Christianity and beyond. Enhance your awareness of their ecological importance and engage with information that brings your garden, culinary, and cultural pursuits to full bloom. Let Lilium on Wikipedia be your guide to converting fascination into knowledge and passion. Visit today, and let the lilies whisper their secrets to you."
    }
    # print(get_match(a['userSelectList'], a['videoScript']))
    get_match(a['userSelectList'], a['videoScript'])