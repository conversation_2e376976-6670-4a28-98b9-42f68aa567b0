from service.server.recreation.generate_video_script import understand_source_media
from service.lib.media_llm import chat_gpt, remove_urls


def get_script_list_by_media(product_info: dict, source: dict, select_list: list[dict], strategy: list[str],
                             language: str) -> (bool, list[dict]):
    product_name = product_info.get("name", '')
    product_description = product_info.get("description", '')
    product_keywords = product_info.get("keywords", '')

    system_prompt = """You are a professional marketing expert specializing in product promotion. Your are good at promoting product through video, especially choosing the right visual material to go with the well-written text. User give you a product information and supplementary content, you should use these resources follow user's requirements."""
    if product_name:
        user_prompt = f"Here is the product to be promoted named <{product_name}>, with a description:\n[description starts]\n{product_description}\n[description ends]\n"
    else:
        user_prompt = "There possible product to be promote can be extract from the below media content. "

    if product_keywords:
        user_prompt += f"It has some representative product tags as: {', '.join(product_keywords)}.\n"

    if source:
        user_prompt += f"As the product promotion needs to fit the market, here are some social media post content which could be helpful to understand the market:\n[social_media_post starts]\n"

        original_content = source.get("originalContent", '')
        if source.get("sourceType", 0) == 1:
            original_content = remove_urls(original_content)
        if original_content:
            user_prompt += f"The text of the social media post is:\n  {original_content}\n"
        media_result = understand_source_media(source.get("mediaList", []))
        if media_result:
            user_prompt += f"The visual content of the post are:\n"
            for index, single_result in enumerate(media_result):
                user_prompt += f"    {str(index + 1)}. {single_result['mediaType']}:\n{single_result['content']}"
        user_prompt += '[social_media_post ends]\n'

    visual_content = ""
    for index, media in enumerate(select_list):
        media_type = 'an image'
        if media['mediaType'] == 'clip':
            media_type = 'a video clip'
        visual_content += f"    {str(index+1)}.This is {media_type} expressing:\n{media['description']}"

    if strategy:
        strategy_str = "\n".join(strategy)
        user_prompt += f"There are some promoting strategies you should follow: {strategy_str}"

    user_prompt += f"""Your task is to generate a video with voice-over script for product promotion. To complete this task, you should follow the steps below:
1. Understand all the information given above, especially those product-related.
2. Choose suitable visual materials for promotion video by considering their content and presentation，single text summary of a video or image is below:\n[visual_material starts]\n{visual_content}[visual_content ends]
3. Organize the visual materials you choose as parts of the promotion video in order.
4. Generate voice-over script for production promotion with all the useful information and visual materials you have.
5. Assign the voice-over script you generated to each visual element that makes up the video in order.

Requirements:
    1. The voice-over script should be in {language}. 
    2. The voice-over script should be attractive and persuasive, and highlight the features of the product.
    3. The total generated voice-over script should be less than 150 words.
    4. The id of the visual material should be used to identify the visual material.
    5. Output a list corresponding to the visual material id and the script, and the order of the elements in the list is their order in the video.
Provide the final output in JSON format as required.
    """
    json_schema = {
        "name": "get_product_promotion_video_with_voice_over_script",
        "description": "generate video with voice-over script for product promotion",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "video_result": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "script": {
                                "type": "string",
                                "description": "The corresponding voice-over script"
                            },
                            "mediaId": {
                                "type": "number",
                                "description": "The id of the visual material"
                            }
                        },
                        "required": ["script", "mediaId"],
                        "additionalProperties": False
                    }
                }
            },
            "required": ["video_result"],
            "additionalProperties": False
        }
    }
    status, result = chat_gpt(system_prompt, user_prompt, json_schema=json_schema)
    if not status:
        return False, result
    try:
        match_result = result['video_result']
    except Exception as e:
        return False, f"Error {e} in get match result: {result}"
    for i in match_result:
        media_id = i['mediaId']
        i['mediaId'] = select_list[media_id - 1]['mediaId']
        i['mediaType'] = select_list[media_id - 1]['mediaType']
        i['clipId'] = select_list[media_id - 1]['clipId']
        i['index'] = media_id-1
    return True, match_result
