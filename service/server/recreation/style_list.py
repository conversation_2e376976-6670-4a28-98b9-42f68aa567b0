# -*- coding: utf-8 -*-
"""
    Style List Server
"""
import traceback
import re
import json
import os
from flask import current_app


def extract_html(ai_response: str) -> str:
    """
    Extracts HTML code from an AI response.
    It tries to find HTML within ```html ... ``` blocks.
    If not found, it tries to find any code block within ``` ... ```.
    If not found, it checks for <!DOCTYPE html> or <html> tags.
    As a last resort, it wraps the entire raw response in a basic HTML boilerplate structure.
    """
    if "```html" in ai_response:
        parts = ai_response.split("```html")
        if len(parts) > 1:
            end_parts = parts[1].split("```")
            if len(end_parts) > 0:
                return end_parts[0].strip()

    if "```" in ai_response:
        parts = ai_response.split("```")
        if len(parts) > 1:
            return parts[1].strip()

    if "<!DOCTYPE html>" in ai_response or "<html" in ai_response:
        return ai_response

    return f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body, html {{
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
        }}
        .cover-container {{
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            background-color: #f8f9fa;
            color: #333;
        }}
        .ai-response {{
            white-space: pre-wrap;
            font-size: 16px;
            line-height: 1.6;
            overflow: auto;
            max-height: 100%;
            padding: 20px;
        }}
    </style>
</head>
<body>
    <div class="cover-container">
        <div class="ai-response">{ai_response}</div>
    </div>
</body>
</html>
"""

def get_style_list():
    """
    Returns a list of available cover styles.
    Reads from rednote_cover.json file and sets all prompt fields to empty string.
    """
    try:
        # Get the project root directory (assuming this file is in service/server/recreation/)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.join(current_dir, '../../..')
        json_file_path = os.path.join(project_root, 'prompts/style_templates/rednote_cover.json')
        
        # Read the JSON file
        with open(json_file_path, 'r', encoding='utf-8') as f:
            styles = json.load(f)
        
        # Set all prompt fields to empty string
        for style in styles:
            style['prompt'] = ""
        
        return styles
        
    except FileNotFoundError:
        current_app.logger.error(f"Style template file not found: {json_file_path}")
        return []
    except json.JSONDecodeError as e:
        current_app.logger.error(f"Error parsing JSON file: {e}")
        return []
    except Exception as e:
        current_app.logger.error(f"Error loading style list: {e}")
        return []

def generate_cover_html(style_id: int, content: str, media_list: list, watttraceid: str = ''):
    """
    Generates cover HTML by calling an AI service.
    """
    try:
        # 1. Get style details
        styles = get_style_list()
        style = next((s for s in styles if s['id'] == style_id), None)
        if not style:
            return False, "Style not found", None

        # 2. Construct the prompt
        prompt = style['base_prompt'] + "\n" + style['prompt'] + "\n\n# 用户输入内容\n\n" + content

        # 3. Handle media list (images)
        # In a real scenario, we might need to resolve media_key to URL.
        # Here we assume media_list contains objects with a 'media_url' key.
        # The Go code uses a proxy, which is good practice. We'll simulate that.
        if media_list and len(media_list) > 0:
            image_urls = [item.get('media_url') for item in media_list if item.get('media_url')]
            if image_urls:
                prompt += "\n\n# 参考图片链接\n"
                # This should come from config, e.g., current_app.config.get('APP_BASE_URL')
                # Using a placeholder for now.
                base_url = "https://www-dev.gm8.ai"
                for img_url in image_urls:
                    # Simulate the proxy URL structure from Go code
                    proxy_url = f"{base_url}/api/v1/image-proxy?url={img_url}"
                    prompt += f"\n图片链接：{proxy_url}"


        # 4. Call the AI service
        # The Go code calls a custom service. We'll use the project's watt_gpt service.
        # The response structure in Go code suggests it's calling Anthropic via a gateway.
        body = {
            "model": current_app.config.get("CLAUDE_HAIKU", "claude-3-haiku-20240307"), # Assuming a model from config
            "messages": [{"role": "user", "content": prompt}],
            "max_tokens": current_app.config.get("COVER_MAX_TOKENS", 4096) # Use a config value
        }

        # Using callPplxChannelChatCompletions as an example, as it's a common channel.
        # The exact method depends on which channel is configured for this task.
        # Let's assume we are calling a Claude model via the OpenAI compatible endpoint.
        status, response = current_app.watt_gpt.callOpenaiChannelChatCompletions(body, watttraceid=watttraceid)

        if not status:
            current_app.FlaskLog.error(f"RequestID: {watttraceid} - AI service call failed: {response}")
            return False, f"AI service call failed: {response}", None

        # 5. Extract content and token usage from response
        # The Go code has a specific response structure. We need to adapt to what callOpenaiChannelChatCompletions returns.
        # Assuming it returns a dict with 'choices' and 'usage' like OpenAI.
        ai_response_text = response.get("choices", [{}])[0].get("message", {}).get("content", "")
        if not ai_response_text:
            current_app.FlaskLog.error(f"RequestID: {watttraceid} - AI response content is empty.")
            return False, "AI response content is empty", None

        # 6. Extract HTML from the response text
        html_content = extract_html(ai_response_text)

        # 7. Calculate token usage and cost (replicating Go logic)
        usage = response.get("usage", {})
        input_tokens = usage.get("input_tokens", 0)
        output_tokens = usage.get("output_tokens", 0)
        
        # Costs are hardcoded in Go code, let's use config values or reasonable defaults.
        # (3.0 / 1000000) for input, (15.0 / 1000000) for output
        prompt_cost = float(input_tokens) * current_app.config.get("CLAUDE_HAIKU_P_COST", 0.00000025) # Use Claude Haiku pricing
        completion_cost = float(output_tokens) * current_app.config.get("CLAUDE_HAIKU_C_COST", 0.00000125)
        
        token_usage = {
            "prompt_tokens": input_tokens,
            "completion_tokens": output_tokens,
            "total_tokens": input_tokens + output_tokens,
            "prompt_cost": prompt_cost,
            "completion_cost": completion_cost,
            "total_cost": prompt_cost + completion_cost,
        }

        return True, html_content, token_usage

    except Exception as e:
        error_msg = f"Generate Cover HTML Error: {str(e)} - {traceback.format_exc()}"
        current_app.FlaskLog.error(f"RequestID: {watttraceid} - {error_msg}")
        return False, error_msg, None
