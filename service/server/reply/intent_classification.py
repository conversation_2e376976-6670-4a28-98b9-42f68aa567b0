import json
import re
from service import prompts_fusion
from service.server.call_gpt import CallGpt
from service.lib.text_process import url_clean
from colorama import Fore
# from urllib.parse import urlparse

prompt_gpt = CallGpt(model="gpt-4o-2024-11-20", temperature=0.2)


system_prompt = "You are an advanced AI system designed to classify user intentions related to social media actions. Your task is to analyze the given input and determine the most appropriate intent based on the following criteria."

TEMPLATE = {
    "type": "",
    "productURL": "",
    "accountPlatform": "",
    "postURL": "",
    "postPlatform": "",
    "keywords": [],
    "keyName": ""
}

twitter_profile_pattern = r"^(?:twitter|x)\.com\/[^\/\s]+$"
twitter_post_pattern = r"^(?:twitter|x)\.com\/[^\/\s]+\/status\/\d+"
mobile_twitter_post_pattern = r"^(?:mobile\.)?(?:twitter|x)\.com\/[^\/\s]+\/status\/\d+"
tiktok_profile_pattern = r"^tiktok\.com\/@[^\/\s]+$"
tiktok_video_pattern = r"^tiktok\.com\/@[^\/\s]+\/video\/\d+"
# youtube_profile_pattern = r"^youtube\.com\/(@|c\/|channel\/)[^\/\s]+$"
# youtube_video_pattern = r"^youtube\.com\/watch\?v=[^&\s]+"

twitter_url_pattern = ["twitter.com/home", "twitter.com/explore", "twitter.com/notifications", "twitter.com/messages",
                       "twitter.com/grok", "twitter.com/jobs", "x.com/home", "x.com/explore", "x.com/notifications",
                       "x.com/messages", "x.com/grok", "x.com/jobs", "mobile.twitter.com/home",
                       "mobile.twitter.com/explore", "mobile.twitter.com/notifications", "mobile.twitter.com/messages",
                       "mobile.twitter.com/grok", "mobile.twitter.com/jobs", "mobile.x.com/home", "mobile.x.com/explore",
                       "mobile.x.com/notifications", "mobile.x.com/messages", "mobile.x.com/grok", "mobile.x.com/jobs"]

def complete_json(input_json):
    # 更新模板中的字段
    completed_json = TEMPLATE.copy()
    completed_json.update(input_json)
    return completed_json


def is_url_in_base_list(input_str: str) -> tuple:
    # 保存原始输入，用于最后返回
    original_input = input_str.strip()

    str_ = original_input.lower()
    if str_ == "tiktok":
        return 'subscribe', ["TikTok","social media","short video","trending","viral"], 'TikTok platform'
    elif str_ == "x" or str_ == "twitter":
        return 'subscribe', ["Twitter","social media","micro-blogging","tweets","hashtags"], 'X platform'
    elif str_ == "youtube":
        return 'subscribe', ["YouTube","social media","video sharing","streaming","vlogging"], 'YouTube platform'
    elif str_ == "facebook":
        return 'subscribe', ["Facebook","social media","social networking","friends","groups"], 'FaceBook platform'
    elif str_ == "instagram":
        return 'subscribe', ["Instagram","social media","photo sharing","video sharing","stories"], 'Instagram platform'

    # 检查是否是完整的句子（包含其他单词）
    sentence_indicators = [
        r'^[A-Za-z]+\s+[A-Za-z]+.*?(?:https?:\/\/|www\.|\S+\.[a-zA-Z]{2,}\/?).*$',  # 句子开头的文字
        r'(?:https?:\/\/|www\.|\S+\.[a-zA-Z]{2,}\/?).+?\s+[A-Za-z]+\s+[A-Za-z]+$',  # 句子结尾的文字
        r'\b(?:in|on|at|the|a|an|and|or|but|for|with|to)\b',  # 常见虚词
        r'[.!?]\s+[A-Z]',  # 句子结束标点后跟大写字母
    ]
    for pattern in sentence_indicators:
        if re.search(pattern, original_input, re.IGNORECASE):
            # 但是要排除URL中本身包含这些模式的情况
            if not re.match(r'^(?:https?:\/\/|www\.)', original_input, re.IGNORECASE):
                # print(Fore.RED + f"False: {original_input}"+Fore.RESET)
                return 'other', '', ''

    input_str = re.sub(r'\s+', '', input_str)  # 移除所有空格

    # 改进www的检测逻辑 - 匹配任何大小写组合的www
    has_www = bool(re.search(r'(?i)^(?:https?:\/\/)?(www\.)', input_str))

    # 用于匹配的小写版本
    lower_input = input_str.lower()
    # 移除所有形式的www（包括大小写混合）和http(s)://
    lower_input = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', lower_input)
    lower_input = re.sub(r'\/+$', '/', lower_input.rstrip('/')) + ('/' if lower_input.endswith('/') else '')
    lower_input = re.sub(r'^mobile\.(x|twitter)\.', r'\1.', lower_input)

    # Check if the host is in twitter_host and path is in twitter_pattern
    if lower_input in twitter_url_pattern:
        return 'promotion', '', 'https://' + lower_input

    url_pattern = re.compile(
        r'^(?:'  # 开始
        r'(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+'  # 域名部分
        r'[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?'  # 顶级域名
        r'(?:\/[^\s]*)?' # 路径部分（可选）
        r')$'
    )

    def is_likely_url(s: str) -> bool:
        # 检查是否包含至少一个点号，且不在开头或结尾
        if not re.search(r'^[^.]+\.[^.]+', s):
            return False
        # 检查是否包含常见的句子结束标点
        if re.search(r'\s*[.!?]\s+[A-Z]', original_input):
            return False
        # 检查域名部分的合理性
        domain_part = s.split('/')[0]
        if len(domain_part) < 3:  # 域名至少3个字符
            return False
        # 检查域名中的点号数量（通常不会超过4个）
        if domain_part.count('.') > 4:
            return False

        return True

    match = url_pattern.match(lower_input)
    print(Fore.YELLOW + f"Match: {match}"+Fore.RESET)
    if not match or not is_likely_url(lower_input):
        return 'other', '', ''

    # 提取原始URL的路径部分（保持大小写）
    original_parts = re.sub(r'(?i)^(?:https?:\/\/)?(?:www\.)?', '', original_input)
    domain, *path_parts = original_parts.split('/', 1)
    original_path = f"/{path_parts[0]}" if path_parts else ''

    # 构建标准化的URL，但保持路径的原始大小写
    standardized_url = f'https://{"www." if has_www else ""}{domain.lower()}{original_path}'

    # 检查Twitter
    if re.match(twitter_profile_pattern, lower_input):
        return 'growth', 'x', standardized_url
    elif re.match(twitter_post_pattern, lower_input):
        return 'reference', 'x', standardized_url

    # 检查TikTok
    if re.match(tiktok_profile_pattern, lower_input):
        return 'growth', 'tiktok', standardized_url
    elif re.match(tiktok_video_pattern, lower_input):
        return 'reference', 'tiktok', standardized_url

    # 其他普通URL
    return 'promotion', '', standardized_url


# 定义意图分类
intent_definitions = [
    {
        "name": "growth",
        "description": "User wants to gain more followers for his account, increase the exposure of the posts, or maintain a certain level of engagement.",
        "parameters": {
            "properties": {
                "accountURL": {
                    "type": "string",
                    "description": "The URL of the account you want to target."
                },
                "accountPlatform": {
                    "type": "string",
                    "description": "The social media platform of the account you want to target (e.g., TikTok, Twitter).",
                    "enum": ["TikTok", "Twitter"],
                    "default": "Twitter"
                }
            },
            "required": ["accountURL"],
            "type": "object"
        }
    },
    {
        "name": "promotion",
        "description": "User wants to promote and advertise their product, APP, service, or brand on social media platforms.",
        "parameters": {
            "properties": {
                "productURL": {
                    "type": "string",
                    "description": "The URL of the product, website, APP or service you want to promote."
                }
            },
            "required": ["productURL"],
            "type": "object"
        }
    },
    {
        "name": "subscribe",
        "description": "User is interested in specific topics and keywords. He wants to hear more or get updates on these topics.",
        "examples": [
            "Show me news about AI and machine learning.",
            "Which singer is trending on TikTok?",
            "Which party, the Democratic Party or the Republican Party, currently holds more advantage?"
            ],
        "parameters": {
            "properties": {
                "keywords": {
                    "type": "list",
                    "description": "The keywords you want to subscribe to or the query target. Expand 5 tightly related keywords/entities.",
                    "items": {
                        "type": "string"
                    }
                },
                "keyname": {
                    "type": "string",
                    "description": "The summary of the query and keywords within 20 characters.",
                }
            },
            "required": ["keywords", "keyName"],
            "type": "object"
        }
    },
    {
        "name": "reference",
        "description": "User wants to analyze, refer to or get inspirations from the content of a post or resource.",
        "parameters": {
            "properties": {
                "postURL": {
                    "type": "string",
                    "description": "The URL of the post or resource you want to target."
                },
                "postPlatform": {
                    "type": "string",
                    "description": "The social media platform of the resource you want to target (e.g., TikTok, Twitter).",
                    "enum": ["TikTok", "Twitter"],
                    "default": "Twitter"
                }
            },
            "required": ["postURL"],
            "type": "object"
        }
    },
    {
        "name": "other",
        "description": "None of the above options apply. Or the target platform is not in the enum list",
        "parameters": {
            "properties": {
                "reason": {
                    "type": "string",
                    "description": "The description of the task you want to perform."
                }
            }
        }
    }
]

# Intent classification function
def classify_intent(user_input: str, watttraceid: str = ''):
    type_, platform, url = is_url_in_base_list(user_input)
    # print(Fore.YELLOW + f"Output:\n {type_} - {url}"+Fore.RESET)
    if type_ == 'promotion':
        return True, {"type": type_, "productURL": url}
    elif type_ == 'growth':
        return True, {"type": type_, "accountURL": url, "accountPlatform": platform}
    elif type_ == 'reference':
        return True, {"type": type_, "postURL": url, "postPlatform": platform}
    elif type_ == 'subscribe':
        return True, {"type": type_, "keywords": platform, "keyName": url, "keyDescription": user_input}
    else:
        input_data = {"input": user_input}
        user_prompt = prompts_fusion.get_intent_classify(input_data)
        status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid)
        if status_:
            if output_.startswith('```json') and output_.endswith('```'):
                output_ = output_[7:-3].strip()
            output_json = json.loads(output_)
            output_json = complete_json(output_json)
            if output_json["type"] == "reference":
                output_platform = output_json.get("postPlatform", "").lower()
                if output_json.get("postPlatform", "") not in ["tiktok", "twitter", "x"]:
                    output_json["type"] = "other"
                    output_json["postURL"] = ""
                    output_json["reason"] = "Platform not supported"
                elif output_platform == "twitter":
                    output_platform = "x"
                    output_json["postPlatform"] = output_platform
                else:
                    output_json["postPlatform"] = output_platform
                output_url = url_clean(output_json.get("postURL", ""))
                # 检查Twitter
                if ((not re.match(twitter_post_pattern, output_url)) and (not re.match(tiktok_video_pattern, output_url))
                        and (not re.match(mobile_twitter_post_pattern, output_url))):
                    output_json["type"] = "other"
                    output_json["postURL"] = ""
                    output_json["postPlatform"] = ""
                    output_json["reason"] = "This is not a valid reference URL"
            elif output_json["type"] == "growth":
                output_platform = output_json.get("accountPlatform", "").lower()
                if output_json.get("accountPlatform", "") not in ["tiktok", "twitter", "x"]:
                    output_json["type"] = "other"
                    output_json["accountURL"] = ""
                    output_json["accountPlatform"] = ""
                    output_json["reason"] = "Platform not supported"
                elif output_platform == "twitter":
                    output_platform = "x"
                    output_json["accountPlatform"] = output_platform
                else:
                    output_json["accountPlatform"] = output_platform
                url_output = url_clean(output_json.get("accountURL", ""))
                if url_output in twitter_url_pattern:
                    output_json["type"] = "promotion"
                    output_json["accountURL"] = ""
                    output_json["accountPlatform"] = ""
                    output_json["productURL"] = "https://" + url_output
            elif output_json["type"] == "subscribe":
                output_json["keyDescription"] = user_input
            return True, output_json
        else:
            return False, output_

# Generate prompt based on intent
def generate_prompt(intent_name, user_input):
    for intent in intent_definitions:
        if intent["name"] == intent_name:
            prompt = {
                "intent": intent_name,
                "parameters": {key: f"<{key}>" for key in intent["parameters"]["properties"].keys()}
            }
            return json.dumps(prompt, indent=4)
    return None


