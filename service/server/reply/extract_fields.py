import json
from ..call_gpt import CallGpt
from ... import prompts_fusion


prompt_gpt = CallGpt(temperature=0.6)


def ExtractInterestedFields(x_id: str, query: str, existing_fields: str = None, watttraceid: str = None):
    """
    Extract fields of interest based on user's query
    :param x_id: user's id
    :param query: user's query
    :param existing_fields: existing fields of interest
    :param watttraceid: watttraceid
    :return: generated field list
    """
    input_data = {"user_query": query, "fields": existing_fields}

    system_prompt = prompts_fusion.get_fields_system(input_data)
    user_prompt = prompts_fusion.get_fields_user(input_data)

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, json_object=True)
    if status_:
        try:
            output_ = json.loads(output_)
        except Exception as e:
            return False, 500, f'JSON解析失败 - {str(e)}'
        return True, 200, output_
    return False, 500, '调用GPT接口生成推文失败'
