import json
import re
from colorama import Fore
from ..call_gpt import CallGpt
from service import prompts_fusion
from ..analysis.file_extract import parse_multiple_files_grequests_direct
from ...lib.text_process import url_clean, url_complete
from ...lib.url_extract import extract_raw_content, extract_url_firecrawl
from ..analysis.url_extract import is_url_in_base_list
import concurrent.futures
import time

prompt_gpt = CallGpt(model="gpt-4.1", temperature=0.6)


def detect_and_validate_urls(text):
    """
    Detect URLs in text and validate/fix them
    :param text: input text
    :return: list of validated URLs
    """
    # URL pattern that matches various formats
    url_patterns = [
        r'https?://[^\s<>"]+',  # Standard HTTP/HTTPS URLs
        r'www\.[^\s<>"]+',      # www.domain.com
        r'[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}(?:/[^\s<>"]*)?'  # domain.com or domain.com/path
    ]
    
    found_urls = []
    normalized_urls = set()  # Track normalized URLs for deduplication
    
    for pattern in url_patterns:
        matches = re.findall(pattern, text, re.IGNORECASE)
        for match in matches:
            # Clean up the URL
            url = match.strip('.,!?;)')
            
            # Add https:// if missing
            if not url.startswith(('http://', 'https://')):
                url = url_complete(url_clean(url))
            
            # Basic validation using is_url_in_base_list
            is_valid, url_type, standardized_url = is_url_in_base_list(url)
            if is_valid and url_type != 'other':
                # Use standardized URL from is_url_in_base_list
                url = standardized_url
            elif is_valid:
                # For other URLs, use the standardized URL from is_url_in_base_list
                # This preserves the original www. prefix if it exists
                url = standardized_url
            else:
                continue
            
            # Use cleaned URL for deduplication
            normalized = url_clean(url).lower()
            if normalized not in normalized_urls:
                normalized_urls.add(normalized)
                found_urls.append(url)
    
    return found_urls


def is_valid_url(url):
    """
    Basic URL validation using is_url_in_base_list
    :param url: URL to validate
    :return: True if valid, False otherwise
    """
    is_valid, _, _ = is_url_in_base_list(url)
    return is_valid


def extract_raw_content_with_timeout(url_str: str, timeout_seconds: int = 40):
    """
    Extract raw content from URL with timeout control
    :param url_str: URL to extract content from
    :param timeout_seconds: Maximum time to wait for extraction (default 40 seconds)
    :return: tuple (success: bool, result: dict or error message)
    """
    try:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(extract_raw_content, url_str)
            try:
                result = future.result(timeout=timeout_seconds)
                return result
            except concurrent.futures.TimeoutError:
                print(f"URL extraction timed out after {timeout_seconds} seconds for: {url_str}")
                return False, f"URL extraction timed out after {timeout_seconds} seconds"
    except Exception as e:
        print(f"Error in URL extraction with timeout: {e}")
        return False, f"Error in URL extraction: {str(e)}"


def parse_files_with_timeout(file_list: list, timeout_seconds: int = 40, max_pages: int = 12):
    """
    Parse multiple files with timeout control
    :param file_list: List of files to process
    :param timeout_seconds: Maximum time to wait for file processing (default 40 seconds)
    :param max_pages: Maximum pages to process per file
    :return: dict with file results
    """
    try:
        with concurrent.futures.ThreadPoolExecutor() as executor:
            future = executor.submit(parse_multiple_files_grequests_direct, file_list, max_pages)
            try:
                result = future.result(timeout=timeout_seconds)
                return result
            except concurrent.futures.TimeoutError:
                print(f"File processing timed out after {timeout_seconds} seconds for {len(file_list)} files")
                # Return empty results for all files
                return {file_info.get("url", ""): (False, f"File processing timed out after {timeout_seconds} seconds") for file_info in file_list}
    except Exception as e:
        print(f"Error in file processing with timeout: {e}")
        return {file_info.get("url", ""): (False, f"Error in file processing: {str(e)}") for file_info in file_list}


def company_update_intent(user_input: str, file_list: list, company_profile: dict, watttraceid=None):
    """
    Extract fields of interest based on user's query and determine intent
    :param user_input: user's input
    :param file_list: file list (optional)
    :param company_profile: company profile
    :param watttraceid: watttraceid
    :return: tuple (success: bool, result: dict)
    """
    try:
        start_time = time.time()
        
        # Detect URLs in user input
        detected_urls = detect_and_validate_urls(user_input)
        print(f"Detected URLs: {detected_urls}")
        
        # Extract URL content if URLs are detected with timeout control
        url_content = None
        if detected_urls:
            elapsed_time = time.time() - start_time
            # 给 Firecrawl API 更充足的时间，最少 15 秒，最多 40 秒
            remaining_time = max(40 - elapsed_time, 15)  # At least 15 seconds, max 40 seconds
            print(f"Starting URL extraction with {remaining_time:.1f} seconds timeout")
            
            url_start_time = time.time()
            status, raw_data = extract_url_firecrawl(detected_urls[0], timeout_seconds=int(remaining_time))
            url_elapsed_time = time.time() - url_start_time
            
            if status:
                url_content = raw_data
                if isinstance(url_content, dict):
                    content_length = len(url_content.get('content', ''))
                else:
                    content_length = len(str(url_content) if url_content else '')
                print(f"Successfully extracted URL content: {content_length} characters after {url_elapsed_time:.1f} seconds")
            else:
                print(f"Error extracting raw content (timeout or error after {url_elapsed_time:.1f}s): {raw_data}")
        
        # Process files if provided with timeout control
        file_content = ""
        if file_list and len(file_list) > 0:
            elapsed_time = time.time() - start_time
            remaining_time = max(40 - elapsed_time, 5)  # At least 5 seconds, max 40 seconds
            print(f"Processing {len(file_list)} files with {remaining_time:.1f} seconds timeout...")
            
            file_results = parse_files_with_timeout(file_list, timeout_seconds=int(remaining_time), max_pages=12)
            
            # Combine successful file contents
            successful_contents = []
            for file_info in file_list:
                file_url = file_info.get("url", "")
                if file_url in file_results:
                    success, content = file_results[file_url]
                    if success and content:
                        successful_contents.append(f"File Content: {content[:2000]}...")
            
            if successful_contents:
                file_content = "\n\n".join(successful_contents)
                print(f"Successfully processed {len(successful_contents)} files")
            else:
                print("No files were successfully processed")

        # Prepare prompt data
        prompt_data = {
            "user_input": user_input,
            "url_content": url_content,
            "file_content": file_content if file_content else None,
            "language": "Simplified Chinese",
            "company_profile": company_profile,
            "detected_urls": detected_urls  # Pass detected URLs to prompt
        }

        # Get system and user prompts
        system_prompt = prompts_fusion.get_company_update_intent_system(prompt_data)
        user_prompt = prompts_fusion.get_company_update_intent_user(prompt_data)

        # print(f"{Fore.YELLOW}system_prompt: {system_prompt}{Fore.RESET}")
        # print(f"{Fore.CYAN}user_prompt: {user_prompt}{Fore.RESET}")

        status_, response = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid, json_object=True)
        if not status_:
            return False, response

        try:
            result_json = json.loads(response)
            if not isinstance(result_json, dict):
                print(f"Invalid response format: expected dict, got {type(result_json)}")
                return False, "Invalid response format from GPT"
            
            # Validate new response structure
            if "typeList" not in result_json:
                print(f"Missing 'typeList' field in response")
                return False, "Missing 'typeList' field in response"
            
            if "reply" not in result_json:
                print(f"Missing 'reply' field in response")
                return False, "Missing 'reply' field in response"
            
            if not isinstance(result_json["typeList"], list):
                print(f"Invalid typeList format: expected list, got {type(result_json['typeList'])}")
                return False, "Invalid typeList format"
            
            if not isinstance(result_json["reply"], str):
                print(f"Invalid reply format: expected string, got {type(result_json['reply'])}")
                return False, "Invalid reply format"
            
            # Validate typeList contents
            type_list = result_json["typeList"]
            for item in type_list:
                if not isinstance(item, dict):
                    print(f"Invalid item format: expected dict, got {type(item)}")
                    return False, "Invalid item format in typeList"
                
                if "type" not in item or "response" not in item:
                    print(f"Missing required fields in item: {item}")
                    return False, "Missing required fields in typeList item"
                
                # Only allow types 1, 2, 3 (no type 0)
                if item["type"] not in [1, 2, 3]:
                    print(f"Invalid type value: {item['type']} (only 1, 2, 3 allowed)")
                    return False, "Invalid type value in response"
                
                if item["type"] in [1, 3]:
                    if "profileUpdates" not in item:
                        print(f"Missing profileUpdates field for type {item['type']}")
                        return False, f"Missing profileUpdates field for type {item['type']}"
                    
                    # Validate profileUpdates structure
                    profile_updates = item["profileUpdates"]
                    if not isinstance(profile_updates, dict):
                        print(f"Invalid profileUpdates format: expected dict, got {type(profile_updates)}")
                        return False, "Invalid profileUpdates format"
                    
                    # Expected profile fields (excluding companyName and industry)
                    expected_fields = ["keywords", "wordCloud", "websiteUrl", "coreCompetency", 
                                     "targetAudience", "brandPositioning", "brandValues", 
                                     "brandVision", "companyDesc"]

                    for field in expected_fields:
                        if field not in profile_updates:
                            if field == "keywords":
                                profile_updates[field] = []
                            elif field == "wordCloud":
                                profile_updates[field] = []
                            else:
                                profile_updates[field] = ""
                            print(f"Auto-filled missing field {field} with empty value")
                
                elif item["type"] == 2:
                    if "profileUpdates" not in item:
                        print(f"Missing profileUpdates field for type {item['type']}")
                        return False, f"Missing profileUpdates field for type {item['type']}"
                    
                    # Validate profileUpdates structure
                    profile_updates = item["profileUpdates"]
                    if not isinstance(profile_updates, dict):
                        print(f"Invalid profileUpdates format: expected dict, got {type(profile_updates)}")
                        return False, "Invalid profileUpdates format"
                    
                    # For type 2, only urlList is needed in profileUpdates
                    if "urlList" not in profile_updates:
                        profile_updates["urlList"] = []
                        print(f"Auto-filled missing urlList field with empty list")
                    
                    # Validate urlList format
                    if not isinstance(profile_updates["urlList"], list):
                        print(f"Invalid urlList format: expected list, got {type(profile_updates['urlList'])}")
                        return False, "Invalid urlList format"

            # Ensure URL handling: if URLs were detected, ensure type 2 exists
            # This is critical - even if URL extraction times out, we still need type 2
            if detected_urls:
                has_type_2 = any(item.get("type") == 2 for item in type_list)
                if not has_type_2:
                    print(f"URLs detected but no type 2 response from GPT. Adding type 2 response.")
                    url_response = {
                        "type": 2,
                        "response": "已成功添加素材到您的创作工具库。新增链接：" + "、".join(detected_urls) + "。这些素材将用于丰富您的内容创作和营销活动，提升创作效率和内容质量。",
                        "profileUpdates": {
                            "urlList": detected_urls
                        }
                    }
                    type_list.append(url_response)
                    print(f"Added type 2 response with URLs: {detected_urls}")
                else:
                    # Validate existing type 2 response has correct URLs
                    for item in type_list:
                        if item.get("type") == 2:
                            profile_updates = item.get("profileUpdates", {})
                            if "urlList" not in profile_updates or not profile_updates["urlList"]:
                                profile_updates["urlList"] = detected_urls
                                print(f"Updated type 2 response with detected URLs: {detected_urls}")
                            else:
                                # Merge detected URLs with existing ones
                                existing_urls = profile_updates["urlList"]
                                # Normalize and deduplicate URLs
                                normalized_urls = set()
                                all_urls = []
                                for url in existing_urls + detected_urls:
                                    normalized = url_clean(url).lower()
                                    if normalized not in normalized_urls:
                                        normalized_urls.add(normalized)
                                        all_urls.append(url)
                                profile_updates["urlList"] = all_urls
                                print(f"Merged and deduplicated URLs: {all_urls}")

            total_time = time.time() - start_time
            print(f"Successfully analyzed intent in {total_time:.2f} seconds, found {len(type_list)} intent(s)")
            return True, result_json

        except json.JSONDecodeError as e:
            print(f"Failed to parse GPT response as JSON: {e}")
            print(f"Raw response: {response}")
            return False, f"Failed to parse GPT response: {e}"

    except Exception as e:
        print(f"Error in company_update_intent: {e}")
        return False, f"Error processing request: {str(e)}"
