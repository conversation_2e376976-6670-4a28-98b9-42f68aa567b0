import json
import re
import requests
from service import callWattGPT
from service.server.mm_understand.qwen_vl import call_with_image_url_foreign


def log_io(func):
    def wrapper(*args, **kwargs):
        print(f"{func.__name__}, 输入: {args}, {kwargs}")
        result = func(*args, **kwargs)
        print(f"输出: {result}")
        return result
    return wrapper


def get_tags(url_list: list) -> (bool, list):
    """
    Extract tags from media information in url list
    :param url_list: list of url, s3 url or http url
    :return: list of tags extracted from media
    """
    if len(url_list) == 1 and (not is_s3_url(url_list[0])):
        if is_url_image(url_list[0]):
            return image_with_gpt(url_list)
        else:
            web_status, web_description = website_understand(url_list[0])
            if not web_status:
                return False, web_description
            gpt_status, gpt_answer = call_gpt(web_description)
            if not gpt_status:
                return False, gpt_answer
            return True, gpt_answer
    else:
        return image_with_gpt(url_list)


def image_with_gpt(image_list):
    sys_prompt = "You are an advanced AI assistant specialized in analyzing images. User provide you with image, you should analyze the image carefully, understand their content, scenario and meanings to follow the user's requirements."
    user_prompt = """Please analyze the provided image and give a detailed interpretation by following steps:
        First understand what the image is containing, including: 
           The content or product of the image, focus on the main element of the image 
           The background or associated field the content may refer to,
        Then, use your ability to associate and relate to reality to give 6 tags including:
           1 tag: Focus on the main content of the image,
           2 tags: Focus on related application scenario, product, field, or people,
           3 tags: Associations which are diverged in a wider and different range with logic.
    """
    json_example = {
        "name": "extract_tags_from_image",
        "description": "Analysis and summary image content to extract tags",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "tags": {
                    "type": "array",
                    "items": {"type": "string",
                              "description": "summary、abstraction or association of the image content"},
                    "description": "Tags extracted from images content from different aspects",
                }
            },
            "required": ["tags"],
            "additionalProperties": False
        }
    }
    image_content = [{"type": "text", "text": user_prompt},]
    for i in image_list:
        image_content.append({"type": "image_url",
                              "image_url": {"url": i}})
    body = {
        "model": "gpt-4o-mini",
        "response_format": {"type": "json_schema",
                            "json_schema": json_example},
        "messages": [
            {"role": "system", "content": sys_prompt},
            {"role": "user",
             "content": image_content}],
        "max_tokens": 500
    }
    error = []
    for i in range(3):
        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=10)
        if not status:
            error.append(response)
            continue
        try:
            output_string = (response['result']['data']['choices'][0]['message']['content'])
            output_json = json.loads(output_string)
            tags = output_json['tags']
        except json.JSONDecodeError:
            error.append("JSON Decode Error of output from GPT")
            continue
        return True, tags
    return False, error


def website_understand(website_url):
    classify_sys_prompt = """You are an advanced AI assistant specialized in analyzing website. User provide you with website url, you should analyze the website carefully, understand their overall scenario and meanings to completely follow the user's requirements, and return the result in JSON format."""
    classify_user_prompt = "Here is a website below: \n" + website_url + """
    Please analyze the provided website by following steps:
        1. Examine the website thoroughly, paying attention to the main element and its features and application scenarios.
        2. Conclude the features and highlights of its main element with summary statement.
        3. Conclude the application scenarios of the product with a general statement.

        Important considerations:
        - Website normally contains one main element or product.
        - Summary or abstraction of the website content should be precise and comprehensive.

    Finally, you should give a description of the website as below:
        description - STRING: paragraph extracted from the website content. It should be focus on the main content in the website, including the content itself, its feature and highlights, it's application scenario.

    Response should strictly be a JSON object, with key "description" and corresponding information as values in English.

    Example response:
        {
         "description": "xxxxxxxxx"
        }

    Please ensure that:
    
    There must be no introductory paragraph
    """
    msg = [{"role": "system", "content": classify_sys_prompt},
           {"role": "user", "content": classify_user_prompt}]
    body = {"messages": msg, "model": "llama-3.1-sonar-small-128k-online", "stream": False}
    status, code, response = callWattGPT.callPplxChannelChatCompletions(body, timeout=10)
    if status:
        res = response["result"]['data']["choices"][0]["message"]["content"]
        res = remove_before_bracket(res)
        try:
            info = json.loads(res)
            tags = info['description']
            return True, tags
        except Exception as e:
            return False, e
    else:
        return False, response


def is_s3_url(url: str) -> bool:
    """
    Classify whether the type of given url is a s3 url
    :param url: string
    :return: bool
    """
    pattern = r"^https://d1g5rjveuc2ya5\.cloudfront\.net/.*"
    return bool(re.match(pattern, url))


def is_url_image(url):
    # 发送 HEAD 请求来获取响应头信息
    try:
        response = requests.head(url, timeout=1)
        # 获取 Content-Type
        content_type = response.headers.get("Content-Type", "")
    except:
        return False
    # 根据 Content-Type 判断
    if "image" in content_type:
        return True
    else:
        return False


def remove_before_bracket(s):
    match = re.search(r'{.*}', s, re.DOTALL)
    if match:
        return match.group(0)
    else:
        return s


def call_gpt(text, user_prompt='', sys_prompt='', model="gpt-4o"):
    if not sys_prompt:
        sys_prompt = "You are an advanced AI assistant specialized in text generation. User provide you with text, you should analyze the text carefully, understand their content to follow the user's requirements."
    if not user_prompt:
        user_prompt = f"""
        Here is a text concerning a certain product or character: {text},
        First understand what the text is containing, including: 
           The content or product of the text,
           The background or associated field the content may refer to,
        Then, summary the main content with 1 tag:
           1 tag: Focus on the main content of the text
        Finally, use your ability to associate and relate to reality to give another 5 tags including:
           2 tags: Focus on related application scenario, field, or people,
           3 tags: Associations which are diverged in a wider and different range.
           
           Noted that each tags should be a phrase or a word which is precise and concise. 
           Please proceed with your analysis and finally give 6 tags in JSON format"""
    json_example = {
        "name": "generate_tags_from_text",
        "description": "Generate tags with given text",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "tags": {
                    "type": "array",
                    "items": {"type": "string",
                              "description": "summary、abstraction or association of the text"},
                    "description": "Tags generated from text in different aspects",
                }
            },
            "required": ["tags"],
            "additionalProperties": False}}
    body = {
        "model": model,
        "response_format": {"type": "json_schema",
                            "json_schema": json_example},
        "messages": [
            {"role": "system", "content": sys_prompt},
            {"role": "user", "content": user_prompt}],
    }
    status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=200)
    if not status:
        return status, response
    try:
        output_string = (response['result']['data']['choices'][0]['message']['content'])
        output_json = json.loads(output_string)
        tags = output_json['tags']
    except json.JSONDecodeError:
        return False, "JSON Decode Error of output from GPT"
    return True, tags


def image_understand(url_list):
    """
    Extract tages based on the images
    :param url_list: image list
    :return: str, text contains name of extracted persons
    """
    sys_prompt = "You are an advanced AI assistant specialized in analyzing images. User provide you with image, you should analyze the image carefully to follow the user's requirements, and return the result in JSON format."
    user_prompt = """
    You should analyze the content of the provided image and extract key information, then give tags as below:
        tags - LIST: Several words or phrases extracted from the image content, which contain the main information of the image. Such as: 1.main content(people or product or event）, 2.functions or highlights, 3.scenarios, 4.related thing or people.

    Example response:
        { "tags": ['xxxx', 'xxx', 'xxx', 'xx'] }
    """
    status, answer = call_with_image_url_foreign(url_list, sys_prompt, user_prompt)
    if not status:
        return False, answer
    res = remove_before_bracket(answer)
    try:
        info = json.loads(res)
        tags = info['tags']
        return True, tags
    except Exception as e:
        return False, e


if __name__ == "__main__":
    urls = ["https://docs.qingque.cn/d/home/<USER>"]
    print(get_tags(urls))
