from locust import HttpUser, task, between


system_prompt = f"You are good at analyzing videos to describe their content concisely. If applicable, highlight unique visual details, interactions, or unusual elements. Keep the description natural and informative while maintaining brevity."
headers = {"Content-Type": "application/json"}
content = [
    {"type": "image_url", "image_url": {
        "url": "https://gitlab.com/handuo/msc_storage/-/raw/master/images/hillary.png"}},
    {
        "type": "text",
        "text": "Analyze the image and return description within 100 words."}
]

content_vid = [
    {"type": "video_url", "video_url": {
        "url": "https://github.com/Remembermyid/vison_test/raw/refs/heads/main/test_video/9.mp4"}},
    {
        "type": "text",
        "text": "Analyze the video and return description within 100 words together with some notable tags or person."}
]

class VLLMUser(HttpUser):
    wait_time = between(1, 5)  # Wait 1-5 seconds between requests

    @task
    def test_vllm(self):
        payload = {
            "model": "Qwen/Qwen2.5-VL-7B-Instruct",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content_vid}
            ]
        }
        self.client.post("/v1/chat/completions", json=payload)
