from service.server.mm_understand.process_video import prompt_gpt
import time
import requests
import json


def generation_report(media_list, platform, detailed_id):
    result = batch_video_understand(media_list)
    duration = media_list[0].get('durationMillis', 0)//1000
    try:
        video_report = result[0]['analysisResult']
        video_report["structureAnalysis"]["duration"] = duration
        output = {"platform": platform, "platformDetailId": detailed_id, "analysisResult": video_report}
    except Exception as e:
        return False, f"error in parsing report json {e}, result: \n{result}"
    return True, output


def batch_video_understand(media_list, model='gemini-2.0-flash'):
    """
        Understand video for multi-dimension report
        :param media_list: list of media
        :param model: model name
        :return: bool, str
    """
    system_prompt = "You are an advanced AI assistant specialized in analyzing social media videos."
    user_prompt = """
            Please analyze the provided video and give a comprehensive report based on the following aspects:
            1. Structure Analysis:
            - Describe the overall pace of the video, use one of: Fast, Medium, Slow.
            - Identify the paragraphs in the video, then summarize theme for each paragraph.
            2. Sound Analysis:
            - Describe the style of the background music.(Default value is None if no background music)
            - Describe the tone of voice-over narration.(Default value is None if no voice-over narration)
            - Describe the pace of the voice-over narration, use one of: Fast, Medium, Slow, None(if no voice-over narration).
            3. Content Analysis:
            - Summarize the main content of the video by comprehensively understand the video elements.
            - Describe the style of the script. E.g., rigorous, formal, humorous, witty, warm & emotional. None if no script.
            - Identify the core themes of the video. E.g., special knowledge presentation, specific product promotion, etc. (If there is a specific product or brand, follow the specific format as: function demonstration/ user evaluation/ usage scenario ...)
            - Identify call-to-action elements in the video. Should be one of: Register, Purchase, View or None.

            Important considerations:
            - All key elements of the video, including vision, audio, and subtitles, need to be taken into consideration when analyzing the video.

            Proceed with your analysis and output a report in JSON format as required."""

    # create report request for each video
    for video in media_list:
        video_key = video['videoPath']
        video_type = video['mediaType']
        video_format = ["video/mp4", "video/mpeg", "video/mov", "video/avi", "video/x-flv",
                        "video/mpg", "video/webm", "video/wmv", "video/3gpp"]
        if video_type not in video_format:
            video_type = "video/mp4"
        status, responses = prompt_gpt.callGCPMediaCreate(media_type=video_type, media_key=video_key,
                                                          system_prompt=system_prompt, user_prompt=user_prompt,
                                                          model=model, json_shcema=report_json_schema)
        if status:
            video['task_id'] = responses
        else:
            video['retry'] = True
            video['create_error'] = f"Error No task_id return {str(responses)}"
    time.sleep(2)
    # get report for each video
    for video in media_list:
        task_id = video.get("task_id", None)
        if not task_id:
            continue
        status, responses = prompt_gpt.callGCPTaskStatus(task_id=task_id)
        if status:
            try:
                response_data = requests.get(responses, timeout=10).text
                result = json.loads(response_data)
                result_text = result['candidates'][0]['content']['parts'][0]['text']
                video['analysisResult'] = json.loads(result_text)
                video['analysisResult']['structureAnalysis']['duration'] = video['durationMillis']//1000
            except Exception as e:
                video['task_error'] = f"Error in getting task: {task_id},with: {str(e)}"
                video['retry'] = True
        else:
            video['retry'] = True
            video['task_error'] = f"Error {task_id}, status ={status}"

    # 增加retry
    for i in range(2):
        retry_count = 0
        for video in media_list:
            if video.get('retry', False):
                retry_count += 1
                video_key = video['videoPath']
                video_type = video['mediaType']
                video_format = ["video/mp4", "video/mpeg", "video/mov", "video/avi", "video/x-flv",
                                "video/mpg", "video/webm", "video/wmv", "video/3gpp"]
                if video_type not in video_format:
                    video_type = "video/mp4"
                status, responses = prompt_gpt.callGCPMediaCreate(media_type=video_type, media_key=video_key,
                                                                  system_prompt=system_prompt, user_prompt=user_prompt,
                                                                  model=model, json_shcema=report_json_schema)
                if status:
                    video['task_id'] = responses
                else:
                    video['create_error'] = f"Error No task_id return {str(responses)}"
        if retry_count < 1:
            break
        time.sleep(2)
        for video in media_list:
            if not video.get('retry', False):
                continue
            task_id = video.get("task_id", None)
            if not task_id:
                continue
            status, responses = prompt_gpt.callGCPTaskStatus(task_id=task_id)
            if status:
                try:
                    response_data = requests.get(responses).text
                    result = json.loads(response_data)
                    result_text = result['candidates'][0]['content']['parts'][0]['text']
                    video['analysisResult'] = json.loads(result_text)
                    video['analysisResult']['structureAnalysis']['duration'] = video['durationMillis'] // 1000
                    video['retry'] = False
                except Exception as e:
                    video['task_error'] = f"Error {task_id}, {str(e)}"
    return media_list


def correct_none(analysis_result):
    sound_analysis = analysis_result.get("soundAnalysis", None)
    content_analysis = analysis_result.get("contentAnalysis", None)
    if sound_analysis:
        narration_tone = sound_analysis.get("narrationTone", None)
        bgm_style = sound_analysis.get("bgmStyle", None)
        narration_pace = sound_analysis.get("narrationPace", None)
        if narration_tone == 'None':
            analysis_result["soundAnalysis"]["narrationTone"] = ''
        if bgm_style == 'None':
            analysis_result["soundAnalysis"]["bgmStyle"] = ''
        if narration_pace == 'None':
            analysis_result["soundAnalysis"]["narration_pace"] = ''
    if content_analysis:
        script_style = content_analysis.get("scriptStyle", None)
        cta = content_analysis.get("callToAction", None)
        if script_style == 'None':
            analysis_result["contentAnalysis"]["scriptStyle"] = ''
        if cta == 'None':
            analysis_result["contentAnalysis"]["callToAction"] = ''
    return analysis_result


report_json_schema = {
    "type": "object",
    "properties": {
        "structureAnalysis": {
            "type": "object",
            "properties": {
                "pace": {
                    "type": "string",
                    "description": "the whole pace of the video",
                    "enum": ["Fast", "Medium", "Slow"]
                },
                "paragraphThemes": {
                    "type": "Array",
                    "items": {
                        "type": "string",
                        "description": "the theme of each paragraph"
                    },
                    "description": "the themes of paragraphs in the video"
                }
            },
            "required": ["pace", "paragraphThemes"],
        },
        "soundAnalysis": {
            "type": "object",
            "properties": {
                "bgmStyle": {
                    "type": "string",
                    "description": "the style of the background music, None if no background music"
                },
                "narrationTone": {
                    "type": "string",
                    "description": "the tone of voice-over narration, None if no voice-over narration"
                },
                "narrationPace": {
                    "type": "string",
                    "description": "the pace of the voice-over narration, use Fast, Medium, Slow or None(if no voice-over narration)",
                    "enum": ["Fast", "Medium", "Slow", "None"]
                }
            },
            "required": ["bgmStyle", "narrationTone", "narrationPace"],
        },
        "contentAnalysis": {
            "type": "object",
            "properties": {
                "scriptStyle": {
                    "type": "string",
                    "description": "the style of the script, None if no script"
                },
                "coreThemes": {
                    "type": "string",
                    "description": "core theme of the video. e.g, special knowledge presentation, specific product promotion, etc"
                },
                "callToAction": {
                    "type": "string",
                    "description": "call to action, be one of: Register,Purchase, View or None",
                    "enum": ["Register", "Purchase", "View", "None"]
                },
                "mainContent": {
                    "type": "string",
                    "description": "main content of the video describing what this video is presenting"
                }
            },
            "required": ["scriptStyle", "coreThemes", "callToAction", "mainContent"],
        },
    },
    "required": ["structureAnalysis", "soundAnalysis", "contentAnalysis"],
}