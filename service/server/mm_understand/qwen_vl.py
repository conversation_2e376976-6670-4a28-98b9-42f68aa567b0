import os
import requests
import mimetypes
import dashscope
from service import qwen_domestic_key, qwen_intl_url, qwen_intl_key


def call_with_image_url_foreign(url_list: list, sys_prompt='', user_prompt='', local=False, model='qwen-vl-max') -> (bool, str):
    """
    Extract keywords based on the images
    :param url_list: image list
    :param sys_prompt: system prompt
    :param user_prompt: user prompt
    :param local: whether to use local image
    :param model: model name
    :return: str, text contains name of extracted persons
    """
    dashscope.base_http_api_url = qwen_intl_url
    dashscope.api_key = qwen_intl_key
    if not sys_prompt:
        sys_prompt = '你是一名经验丰富的记者，对各类历史人物、娱乐明星、政治人物、体育明星等十分熟悉,特别擅长识别图片中的名人。用户给你提供图片，请按照用户提出的要求进行回答'
    if not user_prompt:
        user_prompt = f"对提供的{len(url_list)}张图片，请完成以下要求：1.判断其中是否有名人，如果有请给出英文名字；2.如果有多个名人，按图片顺序给出；3.除了名字，不要返回其他内容"
    content = []
    if not local:
        for u in url_list:
            content.append({"image": u})
    else:
        for u in url_list:
            content.append({"image": f"data:image;base64,{u}"})

    content.append({"text": user_prompt})
    messages = [{'role': 'system',
                 'content': [{'text': sys_prompt}]},
                {'role': 'user',
                 'content': content}]
    try:
        response = dashscope.MultiModalConversation.call(model=model, messages=messages)
        if response['status_code'] != 200:
            return False, str(response)
    except Exception as e:
        return False, f"Error in call qwen-vl-foreign: {e}"
    try:
        text = response['output']['choices'][0]['message']['content'][0]['text']
        return True, text
    except Exception as e:
        return False, f"Error in get qwen text: {e}"


def call_with_image_url_domestic(image_url_list: list) -> (bool, str):
    """
    Extract keywords based on the images
    :param image_url_list: image list
    :return: str, text contains name of extracted persons
    """
    dashscope.api_key = qwen_domestic_key
    download_path_list = download_resource(image_url_list)
    content = []
    for path in download_path_list:
        content.append({"image": f"file://{path}"})
    content.append({"text": f"对提供的{len(download_path_list)}张图片，请完成以下要求：1.判断其中是否有名人，如果有请给出英文名字；2.如果有多个名人，按图片顺序给出；3.除了名字，不要返回其他内容"})
    messages = [{'role': 'system',
                 'content': [{'text': '你是一名经验丰富的记者，对各类历史人物、娱乐明星、政治人物、体育明星等十分熟悉,特别擅长识别图片中的名人。用户给你提供图片，请按照用户提出的要求进行回答'}]},
                {'role': 'user',
                 'content': content}]
    try:
        response = dashscope.MultiModalConversation.call(model='qwen-vl-max-0809', messages=messages)
        if response['status_code'] != 200:
            return False, str(response)
    except Exception as e:
        return False, f'Error in call qwen-vl-domestic: {e}'
    try:
        text = response['output']['choices'][0]['message']['content'][0]['text']
        delete_file(download_path_list)
        return True, text
    except Exception as e:
        return False, f"Error in get qwen text: {e}"


def download_resource(image_url_list, save_folder='./tmp'):
    # url_list = [x for x in image_url_list if is_image(x)]
    if not os.path.exists(save_folder):   # 如果目录不存在，创建目录
        os.makedirs(save_folder, exist_ok=True)
    output_path = []
    i = 0
    for url in image_url_list:
        # filename = url.split("/")[-1]    # 从 URL 中提取文件名
        filename = 'temp_' + str(i)
        i += 1
        file_path = os.path.join(save_folder, filename)
        if os.path.isfile(file_path):
            output_path.append(os.path.abspath(file_path))
            continue
        try:
            response = requests.get(url, stream=True)  # 发送 HTTP GET 请求下载资源
            response.raise_for_status()  # 如果请求失败则抛出异常
            with open(file_path, 'wb') as f:  # 将文件保存到本地
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
            output_path.append(os.path.abspath(file_path))   # 返回文件的绝对路径
        except requests.exceptions.RequestException as e:
            continue
    return output_path


def is_image(file_path):
    mime_type, _ = mimetypes.guess_type(file_path)
    if mime_type:
        if mime_type.startswith('image'):
            return True
        else:
            return False
    return False


def delete_file(file_path_list):
    for file_path in file_path_list:
        try:
            if os.path.exists(file_path):  # 检查文件是否存在
                os.remove(file_path)   # 删除文件
        except:
            continue


if __name__ == "__main__":
    # "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/1.mp4",
    # "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/7.mp4",
    #  "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/8.mp4",
    # "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/4.mp4",
    # urls = [
    #         "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/12.mp4",
    #         "https://raw.githubusercontent.com/Remembermyid/vison_test/main/test_video/13.mp4",
    #         ]
    urls = [
        # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test1.jpeg",
        #           "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test2.jpeg",
                  "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test3.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test4.png",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test5.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test6.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test7.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test8.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test9.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test10.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test11.jpeg",
                  "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test12.jpeg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test13.jpg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test14.jpg",
                  # "https://raw.githubusercontent.com/Remembermyid/vison_test//main/test_picture/test15.jpg",
                  ]
    print(call_with_image_url_foreign(urls))
    #
    # print(call_with_image_url_domestic(urls))
    # (True, '1. 第一张图片中没有名人。\n2. 第二张图片中的名人是Kamala Harris。\n3. 第三张图片中的名人是John Malkovich。')


