import cv2
import requests
import base64
import tempfile
import json
from service import callWattGPT
from service.lib.media_llm import encode_image
from service.server.call_gpt import CallGpt
from time import sleep
import config
import os
from colorama import Fore
from concurrent.futures import ThreadPoolExecutor, as_completed


prompt_gpt = CallGpt(temperature=0.6)
media_understand_host = config.WATT_AI_QWENVL_HOST + "/v1/chat/completions"
local_model = "neuralmagic/Qwen2.5-VL-7B-Instruct-quantized.w8a8"

image_system_prompt = "You are an advanced AI assistant specialized in analyzing social media images. Keep the description natural and informative while maintaining brevity."
image_user_prompt = """
    Please analyze the image(s) and give a short interpretation about the image(s):
    - Describe the overall scenario depicted in the image(s).
    - Interpret the potential meaning or context of the image(s), considering what message or story it might convey.
    - Identify any particularly striking or unusual features, including any recognizable celebrities or figures. If figures have nickname or stage name, append it to another figure. Use raw language, not translated.
    - Make sure the description is concise and informative, descriptive words and phrases rather than full sentences.
    
    Proceed with your analysis and provide a JSON format output."""

# Updated prompt to include transcript summary when available
image_with_transcript_prompt = """
    Please analyze the image(s) and give a short interpretation about the image(s).
    
    The following is the transcript summary of the video this image(s) is from:
    {transcript_summary}
    
    Using this transcript summary as context:
    - Describe the overall scenario depicted in the image(s).
    - Interpret the potential meaning or context of the image, considering what message or story it might convey.
    - Identify any particularly striking or unusual features, including any recognizable celebrities or figures in the images. If figures have nickname or stage name, append it to another figure. Use raw language, not translated.
    - Make sure the description is concise and informative, descriptive words and phrases rather than full sentences.
    
    Proceed with your analysis and provide a JSON format output."""

vid_json_schema = {
    "type": "object",
    "properties": {
        "desc": {
            "type": "string",
            "description": "A concise description"
        },
        "tags": {
            "type": "array",
            "items": {
                "type": "string",
                "description": "A tag for future searching"
            },
            "description": "A list of related key tags that mostly related to the video (no more than 5 tags)"
        },
        "figures": {
            "type": "array",
            "items": {
                "type": "string",
                "description": "Famous figures or influencers"
            },
            "description": "A list of famous figures or influencers if any"
        }   
    },
    "required": ["desc", "tags"]
}
def get_video_summary(video_url, tweet, event_summary):
    video_status, frames_ = process_video(video_url)
    if not video_status:
        return False, frames_
    status, answer = gpt_with_image(frames_, tweet, event_summary)
    return status, answer


def gpt_with_image(image_base64_list, tweet, summary, model="gpt-4o"):
    system_prompt = """You are an advanced AI assistant specialized in analyzing social media videos. User provide several frames extracted from video, your task is to examine these frames carefully, understand their overall scenario and meaning, especially the high-level idea."""
    tweet_prompt = f"""Here is the context of a tweet:
        <tweet_context>
        {tweet}
        </tweet_context>"""
    if summary:
        tweet_prompt += f"""Here is the event background which is related to the tweet and the video:
        <event_background>
        {summary}
        </event_background>"""
    user_prompt = tweet_prompt + """
        Please analyze the provided video frames and give a detailed interpretation by following steps:

        1. Examine the frames thoroughly, paying attention to all visual elements and hooks.
        2. Consider the tweet context and how it relates to the video frames.
        3. Describe the overall scenario depicted in the frames.
        4. Interpret the potential meaning or context of the frames, considering what message or story it might convey.
        5. Identify any particularly striking or unusual features, including any recognizable celebrities or influencers that contribute to the video's overall impact.

        Before providing your final output, break down your thought process. Include the following steps:
        1. List all visible elements and hooks in the frames
        2. Describe the setting and atmosphere
        3. Note any text or symbols present
        4. Identify colors, lighting, and composition
        5. Describe any people or characters present
        6. Relate the video elements to the tweet context to see any script and CTAs

        This detailed analysis will help ensure a thorough interpretation of the data.

        Important considerations:
        - Pay close attention to nuances in the video that might alter its interpretation.
        - Consider the cultural, social, or historical context provided in the tweet when analyzing the video.
        - Be aware of any temporal or climatic conditions depicted, as they may impact the perceived scenario or context.
        - Focus on how the video relates to or supports the content of the tweet and its topic.

        Please proceed with your analysis and provide the final output as requested in JSON format."""
    json_example = {
        "name": "video_summary",
        "description": "Analysis and summary video frames",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "overall": {
                    "type": "string",
                    "description": "A concise description of the main scene or storyline depicted in the video (1-2 sentences)",
                },
                "context": {
                    "type": "string",
                    "description": "An interpretation of the potential meaning or context of the video, considering the tweet and its topic"
                },
                "notable": {
                    "type": "string",
                    "description": "Identification of striking or unusual features, including any recognizable figures or hooks"
                }
            },
            "required": ["overall", "context", "notable"],
            "additionalProperties": False
        }
    }
    # Getting the base64 string
    image_content = [{"type": "text", "text": user_prompt}, ]
    for i in image_base64_list:
        image_content.append({"type": "image_url",
                              "image_url": {"url": f"data:image/jpeg;base64,{i}"}})
    body = {
        "model": model,
        "response_format": {"type": "json_schema",
                            "json_schema": json_example},
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user",
             "content": image_content}],
        "max_tokens": 500
    }
    status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=body, timeout=200)
    if not status:
        return status, response
    try:
        output_string = (response['result']['data']['choices'][0]['message']['content'])
        output_json = json.loads(output_string)
    except json.JSONDecodeError:
        return False, "JSON Decode Error of output from GPT"
    return True, output_json


def gpt_with_images(image_info_list, tweet, summary):
    system_prompt = """You are an advanced AI assistant specialized in analyzing social media images. User provide an image, your task is to examine it carefully, understand the overall scenario and meaning, especially the high-level idea."""
    tweet_prompt = f"""Here is the context of a tweet:
        <tweet_context>
        {tweet}
        </tweet_context>"""
    if summary:
        tweet_prompt += f"""Here is the event background which is related to the tweet and the image:
        <event_background>
        {summary}
        </event_background>"""
    user_prompt = tweet_prompt + """
        Please analyze the provided image with index {uuid} and give a detailed interpretation by following steps:

        1. Examine the image thoroughly, paying attention to all visual elements.
        2. Consider the tweet context and how it relates to the image.
        3. Describe the overall scenario depicted in the image.
        4. Interpret the potential meaning or context of the image, considering what message or story it might convey.
        5. Identify any particularly striking or unusual features, including any recognizable celebrities or influencers that contribute to the image's overall impact.
        6. Classify the image into one of the 3 categories: "Typographic Poster", "Portrait Poster", "Meme".

        Before providing your final output, break down your thought process. Include the following steps:
        1. List all visible elements in the image
        2. Describe the setting and atmosphere
        3. Note any text or symbols present
        4. Identify colors, lighting, and composition
        5. Describe any people or characters present
        6. Relate the image elements to the tweet context

        This detailed analysis will help ensure a thorough interpretation of the data.

        Important considerations:
        - Pay close attention to nuances in the image that might alter its interpretation.
        - Consider the cultural, social, or historical context provided in the tweet when analyzing the image.
        - Focus on how the image relates to or supports the content of the tweet and its topic.

        Please proceed with your analysis and provide the final output as requested in JSON format."""
    json_example = {
        "name": "image_summary",
        "description": "Analysis and summary image",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "overall": {
                    "type": "string",
                    "description": "A concise description of the main scene or situation depicted in the video (1-2 sentences)",
                },
                "context": {
                    "type": "string",
                    "description": "An interpretation of the potential meaning or context of the video, considering the tweet and its topic"
                },
                "notable": {
                    "type": "string",
                    "description": "Identification of striking or unusual features, including any recognizable figures"
                },
                "index": {
                    "type": "integer",
                    "description": "The unique index of the image"
                },
                "type": {
                    "type": "string",
                    "description": "The category of the image",
                    "enum": ["Typographic Poster", "Portrait Poster", "Meme"]
                }
            },
            "required": ["overall", "context", "notable", "index", "type"],
            "additionalProperties": False
        }
    }
    body_list = []
    for i in image_info_list:
        image_content = [{"type": "text", "text": user_prompt.format(uuid=i.get('uuid'))},
                         {"type": "image_url",
                          "image_url": {"url": i.get('url')}}]

        body = {
            "model": "gpt-4o",
            "response_format": {"type": "json_schema",
                                "json_schema": json_example},
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user",
                 "content": image_content}],
            "max_tokens": 500
        }
        body_list.append(body)

    status, code, responses = callWattGPT.gCallOpenaiChannelChatCompletions(body_list=body_list, timeout=200)
    if not status:
        return status, responses
    output_json_list = []
    try:
        for stat, code_, response_ in responses:
            if stat:
                output_string = response_['result']['data']['choices'][0]['message']['content']
                output_json = json.loads(output_string)
                output_json_list.append(output_json)
    except json.JSONDecodeError:
        return False, "JSON Decode Error of output from GPT"
    return True, output_json_list


def local_qwen_with_images(image_list):
    headers = {"Content-Type": "application/json"}
    message = {
        "role": "user",
        "content": [],
    }
    
    # Process each image item
    for image_item in image_list:
        # Check if the input is already in dict format
        if isinstance(image_item, dict):
            image_url = image_item.get("url")
            transcript_summary = image_item.get("transcript_summary")
            
            # Add appropriate prompt based on whether transcript_summary is available
            if transcript_summary:
                if len(message["content"]) == 0:  # Only add prompt once
                    prompt_text = image_with_transcript_prompt.format(transcript_summary=transcript_summary)
                    message["content"].append({"type": "text", "text": prompt_text})
            else:
                if len(message["content"]) == 0:  # Only add prompt once
                    message["content"].append({"type": "text", "text": image_user_prompt})
        else:
            # Legacy support for string URLs
            image_url = image_item
            if len(message["content"]) == 0:  # Only add prompt once
                message["content"].append({"type": "text", "text": image_user_prompt})
        
        # Add the image to the message
        new_image = {"type": "image_url", "image_url": {"url": f"{image_url}"}}
        message["content"].append(new_image)
    
    payload = {
        "model": local_model,
        "messages": [
            {"role": "system", "content": image_system_prompt},
            message
        ]
    }

    try:
        response = requests.post(media_understand_host, headers=headers, json=payload)
        response.raise_for_status()
        if response.status_code == 200:
            print(Fore.CYAN + f"output:\n {response}" + Fore.RESET)
            try:
                output_ = response.json()
                result = output_["choices"][0]["message"]["content"]
                print(Fore.YELLOW + f"Result: {result}" + Fore.RESET)
                result_json = json.loads(result)
                print(Fore.CYAN + f"Result: {result_json}" + Fore.RESET)
            except json.JSONDecodeError as e:
                print(Fore.RED + f"Error loading json: {e}" + Fore.RESET)
        else:
            print(Fore.RED + f"Error: {response.status_code}" + Fore.RESET)
    except requests.exceptions.HTTPError as errh:
        return False, f"Http Error: {errh}"
    except requests.exceptions.ConnectionError as errc:
        return False, f"Error Connecting: {errc}"
    except requests.exceptions.Timeout as errt:
        return False, f"Timeout Error: {errt}"
    except requests.exceptions.RequestException as err:
        return False, f"Request Error: {err}"
    return True, result_json


def gemini_with_video(video_key, video_type, watttraceid):
    system_prompt = "You are an advanced AI assistant specialized in analyzing social media videos. "
    user_prompt = """
        Please analyze the provided video and give a short interpretation:

        - Describe the overall scenario depicted in the video.
        - Interpret the potential meaning or context of the video, considering what message or story it might convey.
        - Identify any particularly striking or unusual features, including any recognizable celebrities or figures. If figures have nickname or stage name, append it to another figure. Use raw language, not translated.

        Important considerations:
        - Consider the cultural, social, or historical context provided in the post when analyzing the video.
        - Focus on how the video relates to or supports the content and its topic.
        - Make sure the description is concise and informative, better in words and phrases instead of long sentences.
        Proceed with your analysis and provide a JSON format output."""
    
    status, responses = prompt_gpt.callGCPMediaCreate(media_type=video_type, media_key=video_key, system_prompt=system_prompt, 
                                                      user_prompt=user_prompt, model='gemini-2.0-flash-lite', watttraceid=watttraceid, 
                                                      json_shcema=vid_json_schema)
    if not status:
        return False, responses
    task_id = responses
    sleep(8)
    status, response = prompt_gpt.callGCPTaskStatus(task_id, watttraceid=watttraceid)
    if not status:
        return False, response
    try:
        response_data = requests.get(response).text
        result = json.loads(response_data)
        result_text = result['candidates'][0]['content']['parts'][0]['text']
        result_json = json.loads(result_text)
        return True, result_json
    except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
        return False, f"Error processing response: {str(e)}"


def gemini_with_images(image_key, image_type, watttraceid):

    status, responses = prompt_gpt.callGCPMediaCreate(image_type, image_key, image_system_prompt, image_user_prompt, 
                                                      model='gemini-2.0-flash', watttraceid=watttraceid, json_shcema=vid_json_schema)
    if not status:
        return False, responses
    task_id = responses
    sleep(1)
    status, response = prompt_gpt.callGCPTaskStatus(task_id, watttraceid=watttraceid)
    if not status:
        return False, response
    try:
        response_data = requests.get(response).text
        result = json.loads(response_data)
        result_text = result['candidates'][0]['content']['parts'][0]['text']
        result_json = json.loads(result_text)
        return True, result_json
    except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
        return False, f"Error processing response: {str(e)}"


def gemini_with_frames(image_list, watttraceid):
    """
    Process multiple images using Gemini API through callGCPMediaCreate
    Args:
        image_list: List of dictionaries containing 'key' and optional 'transcript_summary'
        watttraceid: Trace ID for the request
    Returns:
        Tuple of (status, result) where result is the parsed JSON response
    """
    # Prepare the prompt based on whether we have transcript summaries
    has_transcript = any(item.get('transcript_summary') for item in image_list)
    if has_transcript:
        transcript_summary_str = json.dumps(image_list[0].get('transcript_summary'), ensure_ascii=False)
        prompt_text = image_with_transcript_prompt.format(transcript_summary=transcript_summary_str)
    else:
        prompt_text = image_user_prompt

    # Call GCP Media Create with multiple images
    status, responses = prompt_gpt.callGCPMediaCreate(
        media_type='image/jpeg',
        media_key=[item['key'] for item in image_list],
        system_prompt=image_system_prompt,
        user_prompt=prompt_text,
        model='gemini-2.0-flash-lite',
        watttraceid=watttraceid,
        json_shcema=vid_json_schema
    )
    
    if not status:
        return False, responses
    
    task_id = responses
    sleep(6)  # Wait for task to complete
    
    # Get task status
    status, response = prompt_gpt.callGCPTaskStatus(task_id, watttraceid=watttraceid)
    if not status:
        return False, response
    
    try:
        response_data = requests.get(response).text
        result = json.loads(response_data)
        result_text = result['candidates'][0]['content']['parts'][0]['text']
        result_json = json.loads(result_text)
        return True, result_json
    except (requests.RequestException, json.JSONDecodeError, KeyError) as e:
        return False, f"Error processing response: {str(e)}"


def process_video(url):
    # Step 1: Download the video
    download_status, video_data = download_video(url)
    if not download_status:
        return False, video_data
    print(Fore.YELLOW + "Downloaded video" + Fore.RESET)
    # Step 2: Save the video to a temporary file
    with tempfile.NamedTemporaryFile(suffix=".mp4") as temp_video:
        temp_video.write(video_data)
        temp_video.flush()  # Ensure data is written to disk
        video_status, frames = extract_frames_from_video(temp_video.name, num_frames=10)
        if not video_status:
            return False, frames
        print("Extracted frames")
        return True, frames
    
def process_frames(url_list):
    frame_data = download_resource(url_list, save_folder='./tmp')
    if not frame_data:
        return False, frame_data
    print(Fore.YELLOW + f"Downloaded {len(frame_data)} images" + Fore.RESET)
    # Save frames to base64
    base64_list = []
    for frame in frame_data:
        if not os.path.exists(frame):
            print(Fore.RED + f"Failed to save frame: {frame}" + Fore.RESET)
            return False, "Failed to save frame"
        base64_list.append(encode_image(frame))
    # delete the tmp folder
    # shutil.rmtree('./tmp')
    return True, base64_list


def download_video(url):
    response = requests.get(url)
    if response.status_code == 200:
        return True, response.content
    else:
        return False, "Failed to download video"
    
def download_single_image(args):
    url, save_path = args
    try:
        # Ensure the save path ends with .jpg
        save_path = os.path.splitext(save_path)[0] + '.jpg'
        
        # Download and save image directly
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        with open(save_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        return os.path.abspath(save_path)
        
    except requests.exceptions.RequestException as e:
        print(Fore.RED + f"Error downloading {url}: {str(e)}" + Fore.RESET)
        return None
    except Exception as e:
        print(Fore.RED + f"Error saving image from {url}: {str(e)}" + Fore.RESET)
        return None

def download_resource(image_url_list, save_folder='./tmp'):
    if not os.path.exists(save_folder):
        os.makedirs(save_folder, exist_ok=True)
    
    # Prepare download tasks
    download_tasks = []
    for i, url in enumerate(image_url_list):
        filename = f'temp_{i}.jpg'  # Ensure .jpg extension
        file_path = os.path.join(save_folder, filename)
        if os.path.isfile(file_path):
            download_tasks.append((None, os.path.abspath(file_path)))
        else:
            download_tasks.append((url, file_path))

    output_paths = []
    # Use ThreadPoolExecutor for parallel downloads
    with ThreadPoolExecutor(max_workers=min(10, len(image_url_list))) as executor:
        future_to_path = {
            executor.submit(download_single_image, (url, path)): path 
            for url, path in download_tasks if url is not None
        }
        
        # Add existing files to output
        output_paths.extend(path for url, path in download_tasks if url is None)
        
        # Collect results from futures
        for future in as_completed(future_to_path):
            result = future.result()
            if result:
                output_paths.append(result)
                print(Fore.GREEN + f"Downloaded {result}" + Fore.RESET)

    return output_paths


def extract_frames_from_video(video_path, num_frames=10):
    # Load video from the saved video path
    video = cv2.VideoCapture(video_path)
    # Check if video opened successfully
    if not video.isOpened():
        return False, "Couldn't read video file"
    frame_count = int(video.get(cv2.CAP_PROP_FRAME_COUNT))  # Get video properties
    # Determine the interval for sampling frames
    step = max(frame_count // num_frames, 1)

    frames = []
    for i in range(num_frames):
        frame_number = i * step
        video.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
        ret, frame = video.read()
        if not ret:
            break
        # Convert the frame to base64
        _, buffer = cv2.imencode('.jpg', frame)
        base64_str = base64.b64encode(buffer).decode('utf-8')
        frames.append(base64_str)

    video.release()
    return True, frames


if __name__ == "__main__":
    # Example usage
    video = 'https://video.twimg.com/ext_tw_video/1848950399296016385/pu/vid/avc1/320x590/qKpqY5xiOPJAOySW.mp4?tag=12'  # Replace with actual video URL
    test = "https://d1g5rjveuc2ya5.cloudfront.net/c2fb1c53e70e3831ab0662aba3748a0c/4e7dea54d9589aceb6904fdfacc67176?Expires=1729817005&Signature=aTUzoBJVnzb7aWhYxXwAKykYoxsI7yc6fBVn7vaLMPV0xFeBZB-Gyou7hJsD4XAoYju5e~~4soXvwG8hD-jZx5gHM-9oJBHX-oz9nLxgzyqqKrLh~Xsd7ZMHf~3wjXi5syEF3iD8l~jTjPuP91CAkHeeMytarK7Ii2gAakh5bI8KuXZ5nsW6tHHV83mmFkNNCTDLN0pMzlq54FC2WMBYR5aizjXOsufOu7U1EkI8FWLoQEZ8Hn7mlHJrVAaNRnd3Kvr1B8N0WSmKQnswgMVEB9mpCwg4DrHfipJWUOnvW56yz2Dn7vVT3q1F2egYSH5R2kDO6wlRDg9BH20IkbLUuA__&Key-Pair-Id=K1WJZL3MXMCMUT"
    # status_, frames_base64 = process_video(test)
    # print(len(frames_base64))
    tweet_ = 'I was so excited to see the new movie trailer! It looks amazing! #movie #trailer #excited'
    get_video_summary(test, tweet_)