import json
import requests
from service.server.call_gpt import CallGpt
from ... import prompts_fusion
from colorama import Fore
import config

rdp_host = config.RDP_HOST
rdp_url = rdp_host + config.RDP_CDN_URL
rdp_token = config.RDP_TOKEN
media_understand_host = config.WATT_AI_QWENVL_HOST + "/v1/chat/completions"
prompt_gpt = CallGpt(temperature=0.8, model="gpt-4o-mini")
# local_model = "Qwen/Qwen2.5-VL-7B-Instruct-GPTQ-Int8"
local_model = "neuralmagic/Qwen2.5-VL-7B-Instruct-quantized.w8a8"


def get_media_resource(media_list: list):
    url_list = []
    for media_ in media_list:
        media_key = media_.get("s3_key", "")
        if media_key == "":
            url_list.append(media_.get("url", ""))
        else:
            headers = {"Content-Type": "application/json", "rdp-token": rdp_token}
            body = {
                "object_key_list": [media_key]
            }
            try:
                response_ = requests.post(rdp_url, json=body, headers=headers, timeout=10)
                if response_.status_code != 200:
                    url_list.append(media_.get("url", ""))
                response = response_.json().get("result", {}).get("data", {})[0]
                if not response.get("object_url"):
                    url_list.append(media_.get("url", ""))
                url_list.append(response.get("object_url"))
            except Exception:
                url_list.append(media_.get("url", ""))

    return True, url_list

def ExtractKeywords(content: str, media_list: list, watttraceid: str = None):
    """
    Extract keywords based on the tweet content and media information
    :param content: tweet content
    :param media_list: media list
    :param watttraceid: watttraceid
    :return: generated reply list
    """
    img_list = [x.get("imgUrl", "") for x in media_list if x.get("mediaType", "") == "image"]
    # vid_list = [x.get("videoUrl", "") for x in media_list if x.get("mediaType", "") == "video"]

    system_prompt = "你是一名经验丰富的记者,对各类名人、新闻事件很熟悉"
    prompt_text = "现在有一个推文: " + content

    def process_media(media_type, media_urls):
        if media_type == "image":
            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "text",
                         "text": "图中的人是谁？"},
                    ],
                }
            ]
            messages[1]["content"].extend([
                {"type": "image_url", "image_url": x} for x in media_urls
            ])
        else:  # video
            vid_url = media_urls[0]
            messages = [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": [
                        {"type": "video_url", "video_url": vid_url},
                        {"type": "text",
                         "text": "视频的人是谁？"},
                    ],
                }
            ]

        headers = {"Content-Type": "application/json"}
        data = {
            "model": local_model,
            "messages": messages
        }
        print(Fore.YELLOW + f"data body:\n {data}" + Fore.RESET)
        try:
            response = requests.post(media_understand_host, headers=headers, data=json.dumps(data))
            response.raise_for_status()
            output_ = response.json()
            print(Fore.YELLOW + f"ExtractKeywords: {output_}" + Fore.RESET)
            try:
                output_ = response.json()
                result = output_["choices"][0]["message"]["content"]
                return True, result
            except json.JSONDecodeError as e:
                print(Fore.RED + f"Error loading json: {e}" + Fore.RESET)
        except requests.exceptions.HTTPError as errh:
            return False, f"Http Error: {errh}"
        except requests.exceptions.ConnectionError as errc:
            return False, f"Error Connecting: {errc}"
        except requests.exceptions.Timeout as errt:
            return False, f"Timeout Error: {errt}"
        except requests.exceptions.RequestException as err:
            return False, f"Request Error: {err}"

    # 优先处理图片
    if img_list:
        status, result = process_media("image", img_list)
        if not status:
            result = ""

    # 如果图片处理结果为空或失败，处理视频
    # if vid_list:
    #     status, result = process_media("video", vid_list)
    #     return status, result

    # 如果既没有图片也没有视频，使用原来的文本处理逻辑
        if result:
            prompt_text = prompt_text + " 配了一张图片:" + result
    user_prompt = prompt_text + " Please try to extract the names of celebrity in English. If there are multiple names, separate them with commas. There there is no name, please return string 'empty'."

    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, watttraceid=watttraceid,
                                                temperature=0.2)
    if status_:
        if output_ in ['null', "empty", ""]:
            return True, 'empty'
        return True, output_
    else:
        return False, output_


def process_media(media_type, media_list):
    system_prompt = "You are good at analyzing videos to describe their content concisely. If applicable, highlight unique visual details, interactions, or unusual elements. Keep the description natural and informative while maintaining brevity."
    headers = {"Content-Type": "application/json"}
    desc_list = []
    for url in media_list:
        content = [
            {"type": f"{media_type}_url", f"{media_type}_url": {"url": url}},
            {
                "type": "text",
                "text": f"Analyze the {media_type} and return description within 100 words. If there are celebrities or public figures in the {media_type}, please include their names."}
        ]

        payload = {
            "model": local_model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": content}
            ]
        }
        try:
            response = requests.post(media_understand_host, headers=headers, json=payload)
            response.raise_for_status()
            if response.status_code == 200:
                print(Fore.CYAN + f"output:\n {response}" + Fore.RESET)
                try:
                    output_ = response.json()
                    result = output_["choices"][0]["message"]["content"]
                except json.JSONDecodeError as e:
                    print(Fore.RED + f"Error loading json: {e}" + Fore.RESET)
        except requests.exceptions.HTTPError as errh:
            return False, f"Http Error: {errh}"
        except requests.exceptions.ConnectionError as errc:
            return False, f"Error Connecting: {errc}"
        except requests.exceptions.Timeout as errt:
            return False, f"Timeout Error: {errt}"
        except requests.exceptions.RequestException as err:
            return False, f"Request Error: {err}"
        desc_list.append(result)
    return True, desc_list

def extract_url_tags(content: str, video_list: list, image_list: list, watttraceid: str = None):
    input_data = {"content": content}
    if video_list:
        input_data["videos"] = video_list
    if image_list:
        input_data["images"] = image_list

    user_prompt = prompts_fusion.get_extract_url_tag_user(input_data)
    status_, output_ = prompt_gpt.callOpenaiGpt("You are a helpful assistant to summarize content",
                                                user_prompt, watttraceid=watttraceid,
                                                temperature=0.9, json_object=True)
    if status_:
        result = json.loads(output_)
        return True, result
    else:
        return False, output_


def ExtractPerson(tweet_list: list, watttraceid: str = None):
    """
    Extract person keywords based on the tweet content and media information
    :param tweet_list: tweet object list with content and comment list
    :param watttraceid: watttraceid
    :return: generated reply list
    """
    output_list = []
    for tweet_ in tweet_list:
        output_list.append({
            "tweetId": tweet_.get("tweetId", ""),
            "tweetAccount": tweet_.get("tweetAccount", ""),
            "persons": "Drake Maye, Lisa Maye"
        })

    return True, 200, output_list


if __name__ == '__main__':
    video_list = ["https://gitlab.com/handuo/msc_storage/-/raw/master/video/zhenhuan.mp4"]
    image_list = ["https://gitlab.com/handuo/msc_storage/-/raw/master/images/hillary.png"]
    status, output = process_media("video", video_list)
    print(status, output)