from service.server.mm_understand.extract_keywords import ExtractKeywords, rdp_url, rdp_token, prompt_gpt
from service.server.mm_understand.qwen_vl import call_with_image_url_foreign
from service.dao.mongo.base_mongo import BaseMongoDB
from service import MONGODB_DATABASE
import requests
import time
import re


def remove_urls(text):
    url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
    return re.sub(url_pattern, '', text)

def contains_tiktok_url(url):
    # 正则表达式检查是否包含 https://tiktok.com/@
    pattern = r'https://tiktok\.com/@'
    return bool(re.search(pattern, url))


def extract_person(tweet_list):
    """
    Extract person from tweet list
    :param tweet_list: list of tweet
    :return: list of person
    """
    out_put = []
    for tweet in tweet_list:
        result = {'tweetId': tweet['tweetId'],
                  'tweetAccount': tweet['tweetAccount'],
                  'persons': 'empty'}
        if contains_tiktok_url(tweet['tweetAccount']):
            out_put.append(result)
            continue
        status, person = single_tweet(tweet['tweetContent'], tweet["mediaList"])
        if status:
            result['persons'] = person
        out_put.append(result)
    return True, out_put


def single_tweet(tweet_, media_list):
    """
    Extract person from single tweet with text and image
    :param tweet_: content of tweet
    :param media_list: image and video
    :return: extracted person
    """
    tweet_text = remove_urls(tweet_)
    # image_url_list, new_media_list = get_media_url_list(media_list)
    system_prompt = "You are an experienced journalist who is familiar with various historical figures, entertainment stars, politicians and sports stars."
    # if len(image_url_list) == 0:  # no image
    if not tweet_text:
        return True, 'empty'
    user_prompt = f"There is a text: {tweet_text}. Please try to extract the names of celebrity in English. If there are multiple names, separate them with commas. If there is no name, please return string 'empty'. Your output must be either extracted names or 'empty'."
    status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, temperature=0.2)
    if status_:
        if output_ in ['null', "empty", ""]:
            return True, 'empty'
        return True, output_
    else:
        return False, output_

    # foreign_status, foreign_answer = call_with_image_url_foreign(image_url_list)
    # if not foreign_status:
    #     mongo_qwen = BaseMongoDB(database=MONGODB_DATABASE, collection='qwen_fail_log')
    #     mongo_qwen.insertSingleData({"tweet": tweet_, "media_list": media_list,
    #                                  "image_list": new_media_list, 'error': foreign_answer,
    #                                  "local_time": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())})
    #     local_status, local_answer = ExtractKeywords(tweet_text, new_media_list)
    #     if local_status:
    #         return True, local_answer
    #     else:
    #         return False, f'Error in foreign, domestic and local'
    # else:
    #     image_prompt = foreign_answer
    # if not tweet_text:
    #     user_prompt = f"There may be some celebrity names get from image:{image_prompt}\n Please try to extract the names of celebrity in English. If there are multiple names, separate them with commas. If there is no celebrity name, please return string 'empty'. Your output must be either extracted names or 'empty'."
    # else:
    #     user_prompt = f"There is a text: {tweet_text}. And some possible names get from image:{image_prompt}\n Please try to extract the names of celebrity in English. If there are multiple names, separate them with commas. If there is no celebrity name, please return string 'empty'. Your output must be either extracted names or 'empty'."
    # status_, output_ = prompt_gpt.callOpenaiGpt(system_prompt, user_prompt, temperature=0.2)
    # if status_:
    #     if output_ in ['null', "empty", ""]:
    #         return True, 'empty'
    #     return True, output_
    # else:
    #     return False, output_


def get_media_url_list(media_list: list) -> (list, list):
    """
    Return image_url list with s3-key list
    :param media_list: image and video
    :return: list of image_url
    """
    url_list = []
    new_media_list = []
    for media in media_list:
        if media["mediaType"] == 'image':
            if media.get("imgUrl"):
                url_list.append(media.get("imgUrl"))
                new_media_list.append(media)
                continue
            media_key = media.get("imgPath")
            headers = {"Content-Type": "application/json", "rdp-token": rdp_token}
            body = {
                "object_key_list": [media_key]
            }
            try:
                response_ = requests.post(rdp_url, json=body, headers=headers, timeout=10)
                if response_.status_code == 200:
                    response = response_.json().get("result", {}).get("data", {})[0]
                    if response.get("object_url"):
                        url_list.append(response.get("object_url"))
                        media['imgUrl'] = response.get("object_url")
                        new_media_list.append(media)
            except:
                continue
    return url_list, new_media_list