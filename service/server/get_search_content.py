from service import callWattGPT
import json
import re


def remove_before_bracket(s):
    match = re.search(r"\[(.*)\]", s, re.DOTALL)
    if match:
        return match.group(0)
    else:
        return s


def get_summary_from_url(urls: list):
    url_str = ''
    for i in range(len(urls)):
        url_str += "    " + str(i + 1) + '.' + urls[i]["link"] + '\n'
    sys_prompt = "You are a helpful assistant. You should completely follow users requirements, and return only with json format"
    user_prompt = ("    There are several urls below: \n" + url_str + """
    Summarize these urls in the following sections:
    url - the original url
    title - the title of the page
    summary - the summary of the page content
    
    Response should strictly be a JSON object with the above sections as keys and the corresponding information as values.
    
    example response:
    [
      {
       "url": "https://www.sail-word.com/news/12234/this-is-a-new-about...",
       "title": "This is a news about...",
       "summary": "<PERSON> has been shoot during the USA president campaign, the shooter is be killed by the FBI..."
      },
      {
       "url":xxx,
       "title":xxx,
       "summary":xxx
      }
    ]
    Please ensure that:
    
    There must be no introductory paragraph
    The summary focuses on the website facts and avoids speculation or unknowns""")
    msg = [{"role": "system", "content": sys_prompt},
           {"role": "user", "content": user_prompt}]
    body = {"messages": msg, "model": "llama-3.1-sonar-small-128k-online", "stream": False}
    error = []
    res_log = []
    for i in range(3):
        status, code, response = callWattGPT.callPplxChannelChatCompletions(body)
        if status:
            res = response["result"]['data']["choices"][0]["message"]["content"]
            res = remove_before_bracket(res)
            try:
                info = json.loads(res)
                if len(info) == len(urls):
                    return True, info
                else:
                    res_log.append(res)
            except Exception as e:
                res_log.append(res)
                error.append(e)
        else:
            error.append(response)
    return False, {"error": error, "response": res_log}


if __name__ == "__main__":
    url_list = ["https://www.ifsc-climbing.org/news/climbing-s-paris2024-preparation-documented-in-climbtoparis-mini-series",
                "https://www.sail-world.com/news/277514/Paris2024-Jo-Aleh-named-as-a-flag-bearer",
                "https://www.wdtn.com/sports/olympics/2024-olympics/paris-2024-opening-ceremony-5-things-to-watch/",
                "https://www.nvidia.com/en-us/geforce/campaigns/black-myth-wukong-bundle/",
                "https://www.nvidia.com/en-us/geforce/news/black-myth-wukong-geforce-rtx-40-series-bundle/",]
    a, b = get_summary_from_url(url_list)
    print(a, b)