from service import callWattGPT
import json


def is_related_news(keyword, content, gpt_model='gpt-4o'):
    if not content:
        return False, f"Error: input of is_related_news is None"
    system_prompt = f'用户通过关键词搜索了多个新闻标题，你需要判断这些新闻是否报道的是相关的事件，并提取出事件内容'
    news_title_str = ''
    for i in range(len(content)):
        news_title_str += f"{str(i+1)}. {content[i]['title']}\n"
    user_prompt = (f"根据关键词{keyword}搜索到了{len(content)}个新闻的标题，顺序排列如下：\n{news_title_str}"
                   f"要求：1.判断这些新闻标题是否报道的是相近的事件；2.如果新闻标题报道的是相近的事件，用一句话输出事件核心内容；3.如果新闻标题报道了不同事件，只对第1个新闻标题进行总结并输出事件内容,4.给出理由")
    tools = [{
        "type": "function",
        "function": {
            "name": "determine_and_abstract_event_form_given_news_titles",
            "description": "Determine whether the given news titles are reporting relevant events. If so, abstract the event from the titles. If not, abstract the event from the first title.",
            "parameters": {
                "type": "object",
                "properties": {
                    "related": {
                        "type": "boolean",
                        "description": "Whether the given news titles are reporting relevant event."
                    },
                    "event": {
                        "type": "string",
                        "description": "The core event abstracted from either all the news titles, or only the first news title. It should be concise and within 5 words.",
                    },
                    "reason": {
                        "type": "string",
                        "description": "The reason why the news titles are related or not.",
                    },
                },
                "required": ["related", "event"]
            }
        }
    }]
    msg = [{"role": "system", "content": system_prompt},
           {"role": "user", "content": user_prompt}]
    body = {"messages": msg, "model": gpt_model, "tools": tools, "tool_choice": "auto"}
    error = []
    for i in range(3):
        status, code, response = callWattGPT.callOpenaiChannelChatCompletionsFunctions(body)
        if status:
            try:
                res = response["result"]['data']["choices"][0]["message"]["tool_calls"]
                info = json.loads(res[0]["function"]["arguments"])
                required_keys = ["related", "event"]
                if all([key in info for key in required_keys]):
                    return True, info
            except Exception as e:
                error.append(e)
        else:
            error.append(response)
    return False, error


def is_related_search(keyword, search_list, gpt_model='gpt-4o'):
    if not search_list:
        return False, f"Error: input of is_related_search is None"
    system_prompt = "用户通过关键词搜索到了多个网页，你需要通过网页标题判断其是否相关，并进行事件总结"
    search_title_str = ''
    for i in range(len(search_list)):
        search_title_str += f"{str(i + 1)}. {search_list[i]['title']}\n"
    user_prompt = (f"根据关键词{keyword}搜索到了{len(search_list)}个网页，它们的标题按顺序排列如下：\n{search_title_str}"
                   f"要求：1.根据标题判断搜索到的网页内容是否为同一件事；2.如果这些网页指的是一件事，用一句话输出事件核心内容；3.如果它们是不同的事，只对第1个标题进行总结并输出事件内容,4.给出理由")
    tools = [{
        "type": "function",
        "function": {
            "name": "determine_and_return_event_form_given_search_titles",
            "description": "Determine whether the given search titles are related. If so, abstract the event. If not, return the event from the first title.",
            "parameters": {
                "type": "object",
                "properties": {
                    "related": {
                        "type": "boolean",
                        "description": "Whether the given search titles are related."
                    },
                    "event": {
                        "type": "string",
                        "description": "The event abstracted from either all the search titles, or only the first search title. It should be concise and within 5 words.",
                    },
                    "reason": {
                        "type": "string",
                        "description": "The reason why the search titles are related or not.",
                    },
                },
                "required": ["related", "event"]
            }
        }
    }]
    msg = [{"role": "system", "content": system_prompt},
           {"role": "user", "content": user_prompt}]
    body = {"messages": msg, "model": gpt_model, "tools": tools, "tool_choice": "auto"}
    error = []
    for i in range(3):
        status, code, response = callWattGPT.callOpenaiChannelChatCompletionsFunctions(body)
        if status:
            try:
                res = response["result"]['data']["choices"][0]["message"]["tool_calls"]
                info = json.loads(res[0]["function"]["arguments"])
                required_keys = ["related", "event"]
                if all([key in info for key in required_keys]):
                    return True, info
            except Exception as e:
                error.append(e)
        else:
            error.append(response)
    return False, error


def is_related_events(keyword, event_list, gpt_model='gpt-4o'):
    if not event_list:
        return False, f"Error: input of is_related_event is None"
    system_prompt = f'用户通过关键词搜索到了两个事件，你需要判断这些事件是否相关，并返回事件内容'
    event_str = ''
    for i in range(len(event_list)):
        event_str += f"{str(i+1)}. {event_list[i]}\n"
    user_prompt = (f"根据关键词{keyword}搜索到了{len(event_list)}个事件，按顺序排列如下：\n{event_str}"
                   f"要求：1.判断这{len(event_list)}事件是否相关；2.如果相关，对它们进行总结，用一句话输出事件内容；3.如果不相关，返回第一个事件内容, 4.给出理由")
    tools = [{
        "type": "function",
        "function": {
            "name": "determine_and_return_event_form_given_events",
            "description": "Determine whether the given events are relevant. If so, abstract them. If not, return the first event.",
            "parameters": {
                "type": "object",
                "properties": {
                    "related": {
                        "type": "boolean",
                        "description": "Whether the given events are relevant."
                    },
                    "event": {
                        "type": "string",
                        "description": "The event abstracted from either all the events, or returned with just the first event. Events should be concise and within 5 words.",
                    },
                    "reason": {
                        "type": "string",
                        "description": "The reason why the events are related or not.",
                    },
                },
                "required": ["related", "event"]
            }
        }
    }]
    msg = [{"role": "system", "content": system_prompt},
           {"role": "user", "content": user_prompt}]
    body = {"messages": msg, "model": gpt_model, "tools": tools, "tool_choice": "auto"}
    error = []
    for i in range(3):
        status, code, response = callWattGPT.callOpenaiChannelChatCompletionsFunctions(body)
        if status:
            try:
                res = response["result"]['data']["choices"][0]["message"]["tool_calls"]
                info = json.loads(res[0]["function"]["arguments"])
                required_keys = ["related", "event"]
                if all([key in info for key in required_keys]):
                    return True, info
            except Exception as e:
                error.append(e)
        else:
            error.append(response)
    return False, error


def compare_tweet_and_search(keyword, tweet_event, tweet_score, search_event, search_score):
    if tweet_event and search_event:
        status, event_dict = is_related_events(keyword, [search_event, tweet_event])
        if not status:
            return False, 'error in compare tweet and search', 0
        is_related, event = event_dict['related'], event_dict['event']
        if is_related:
            return True, event, 1
        else:  # 两个事件不一样，应该给一个比较低的置信度
            if search_score > tweet_score:
                return True, search_event, search_score * 0.8
            else:
                return True, tweet_event, tweet_score * 0.8
    elif tweet_event:
        return True,  tweet_event, tweet_score * 0.8
    elif search_event:
        return True, search_event, search_score * 0.8
    else:
        return False, 'find no result in search and tweets', 0


if __name__ == "__main__":
    b = [{'title': 'Take a glimpse at the opening ceremony site of the Paris 2024 Olympic Games', 'link': 'http://en.people.cn/n3/2024/0726/c90000-20198465.html'}, {'title': 'Larry Dale Long of Columbus, Mississippi | 1955 - 2024 | Obituary', 'link': 'https://www.lowndesfuneralhome.net/obituary/larry-long'}, {'title': "What's the best beach in Massachusetts? Vote in round 2 of our bracket to decide", 'link': 'https://www.capecodtimes.com/story/lifestyle/things-to-do/2024/07/24/best-beaches-massachusetts-cape-cod-north-shore-boston/***********/'}]
    a = [{'title': '‘Wave of air strikes’: Israel attacks Gaza cities north and south', 'link': 'https://www.aljazeera.com/news/liveblog/2024/7/26/israel-war-on-gaza-live-wave-of-air-strikes-hit-southern-northern-cities'}, {'title': 'Harris tells Netanyahu ‘it is time’ to end the war in Gaza and bring the hostages home', 'link': 'https://apnews.com/article/harris-netanyahu-biden-hostage-deal-gaza-41c4e0685e88fd75e9aa8e0d0d859b07'}, {'title': 'Israel-Gaza war live: West Bank Hamas leader dies in Israeli custody, Palestinian authorities say', 'link': 'https://www.theguardian.com/world/live/2024/jul/26/israel-gaza-war-live-west-bank-hamas-leader-dies-in-israeli-custody-palestinian-authorities-say'}]
    c = [{'title': "CLIMBING'S PARIS2024 PREPARATION DOCUMENTED IN #CLIMBTOPARIS MINI-SERIES", 'link': 'https://www.ifsc-climbing.org/news/climbing-s-paris2024-preparation-documented-in-climbtoparis-mini-series'}, {'title': 'Paris2024: Jo Aleh named as a flag bearer for NZ team for Opening Ceremony', 'link': 'https://www.sail-world.com/news/277514/Paris2024-Jo-Aleh-named-as-a-flag-bearer'}, {'title': 'Spectator Live Information', 'link': 'https://olympics.com/en/paris-2024/spectator-live-information'}]
    # print(is_related_news('Paris2024', c))
    # events = ['Israel-Gaza war', 'harris speaking to netanyahu']
    # print(is_related_events('Israelis', events))
    d = [{'title': 'Paris 2024 Olympics - Latest News, Schedules & Results', 'link': 'https://olympics.com/en/paris-2024'}, {'title': 'Paris 2024 Official Ticketing – Olympic and …', 'link': 'https://tickets.paris2024.org/en/'}, {'title': 'Olympic Schedule & Results | Paris 2024 Olympics', 'link': 'https://olympics.com/en/paris-2024/schedule'}]
    d = [{'title': 'NuNew 23rd Birthday - YouTube', 'link': 'https://www.youtube.com/watch?v=y7qZ2xq_1hU'}, {'title': 'NuNew (Chawarin Perdpiriyawong) Profile, Age, Height & Facts', 'link': 'https://kpopsingers.com/nunew-profile-bio-facts/'}]
    # print(is_related_search('NuNew 23rd Birthday', d))

    test_news = [{'title': 'Typhoon Gaemi lashes China after pounding Taiwan, Philippines', 'link': 'https://www.reuters.com/business/environment/typhoon-gaemi-lashes-southeast-china-after-pounding-taiwan-flooding-philippines-2024-07-26/'}, {'title': 'Typhoon Gaemi wreaked the most havoc in the country it didn’t hit directly — the Philippines', 'link': 'https://apnews.com/article/china-taiwan-philippines-typhoon-gaemi-tropical-storm-3a9281c8213e46e5717dafb1f8b571ea'}, {'title': 'Race against time to clear oil spill in Philippines as Typhoon Gaemi wreaks havoc in Asia', 'link': 'https://www.nbcnews.com/news/world/oil-spill-manila-philippines-typhoon-gaemi-havoc-rcna163771'}]
    test_search = [{'title': 'Tropical Storm Gaemi LIVE Tracker, Updates & Forecast - Zoom …', 'link': 'https://zoom.earth/storms/gaemi-2024/'}, {'title': 'Typhoon Gaemi (Carina) hits Taiwan, menaces Philippines and …', 'link': 'https://www.cnn.com/2024/07/24/asia/typhoon-gaemi-taiwan-china-intl-hnk/index.html'}, {'title': 'China Braces for Typhoon Gaemi After Devastation in Taiwan …', 'link': 'https://www.nytimes.com/2024/07/26/world/asia/typhoon-gaemi-china-taiwan.html'}]
    _, news_event = is_related_news('Typhoon Gaemi', test_news)
    print(news_event)
    _, search_event_ = is_related_search('Typhoon Gaemi', test_search)
    print(search_event_)
    print(is_related_events('Typhoon Gaemi',[news_event['event'], search_event_['event']]))
