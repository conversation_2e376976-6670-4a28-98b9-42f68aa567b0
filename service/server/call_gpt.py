from service import callWattGPT
from flask import current_app
from colorama import Fore
import config

class CallGpt:
    """
    Base class for call gpt
    """

    def __init__(self,
                 model: str = "gpt-4o-2024-11-20",
                 temperature=0.8
                 ):

        self.model = model
        self.temperature = temperature

    def callGpt(self, prompt, watttraceid="", temperature=None, json_object=False):

        if temperature:
            self.temperature = temperature

        data = {
            "model": self.model,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "system",
                    "content": "You are a helpful assistant."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }
        if json_object:
            data["response_format"] = {"type": "json_object"}

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=data, timeout=300,
                                                                              watttraceid=watttraceid)

        if status:
            output_string = (response['result']['data']['choices'][0]['message']['content']
                             .replace("\n", "").replace("\\\"", ""))
            return True, output_string
        else:
            current_app.flaskLog.error(
                current_app.fmtSaveOutput(watttraceid, f'调用watt gpt接口失败 - {code}, {response}'))
            return False, f'''调用watt gpt4接口失败 - {code}, {response}'''

    def callOpenaiGpt(self, system_prompt, user_prompt, watttraceid="", model=None, temperature=None,
                      vision=False, json_object=False, json_schema=None):
        if temperature:
            self.temperature = temperature

        data = {
            "model": model if model else self.model,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ]
        }
        if json_object:
            if json_schema:
                # Extract "name" and "schema" from json_schema
                name = json_schema.get("name")
                schema = json_schema.get("schema")
                
                if name and schema:
                    data["response_format"] = {
                        "type": "json_schema", 
                        "json_schema": {
                            "name": name,
                            "schema": schema
                        }
                    }
                else:
                    # Fallback to json_object if schema format is incorrect
                    data["response_format"] = {"type": "json_object"}
            else:
                data["response_format"] = {"type": "json_object"}
        if vision:
            data["max_tokens"] = 500

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=data, timeout=200,
                                                                              watttraceid=watttraceid)

        if status:
            output_string = response['result']['data']['choices'][0]['message']['content']
            return True, output_string
        else:
            return False, f'''调用watt gpt接口失败 - {code}, {response}'''

    def callOpenaiFC(self, system_prompt, user_prompt, functions, watttraceid="", model=None):

        data = {
            "model": self.model if not model else model,
            "temperature": self.temperature,
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user",
                    "content": user_prompt
                }
            ],
            "tools": [
                {'type': 'function', 'function': f} for f in functions
            ]
        }

        status, code, response = callWattGPT.callOpenaiChannelChatCompletions(body=data, timeout=200,
                                                                              watttraceid=watttraceid)

        if status:
            output_string = response['result']['data']['choices'][0]['message']['content']
            return True, output_string
        else:
            return False, f'''调用watt gpt接口失败 - {code}, {response}'''

    def gcallOpenaiGpt(self, system_prompt_list, user_prompt_list, watttraceid="", model=None, temperature=None,
                       vision=False, json_object=False, json_schema=None):
        body_list = []
        output_list = []
        for system_prompt, user_prompt in zip(system_prompt_list, user_prompt_list):
            if temperature:
                self.temperature = temperature

            data = {
                "model": self.model if not model else model,
                "temperature": self.temperature,
                "messages": [
                    {
                        "role": "system",
                        "content": system_prompt
                    },
                    {
                        "role": "user",
                        "content": user_prompt
                    }
                ]
            }
            if json_object:
                if json_schema:
                    # Extract "name" and "schema" from json_schema
                    name = json_schema.get("name")
                    schema = json_schema.get("schema")
                    
                    if name and schema:
                        data["response_format"] = {
                            "type": "json_schema", 
                            "json_schema": {
                                "name": name,
                                "schema": schema
                            }
                        }
                    else:
                        # Fallback to json_object if schema format is incorrect
                        data["response_format"] = {"type": "json_object"}
                else:
                    data["response_format"] = {"type": "json_object"}
            if vision:
                data["max_tokens"] = 500
            body_list.append(data)

        status, code, responses = callWattGPT.gCallOpenaiChannelChatCompletions(body_list=body_list, timeout=300,
                                                                                watttraceid=watttraceid)
        if status:
            for sta_, code_, response_ in responses:
                # output_string = response_['result']['data']['choices'][0]['message']['content']
                try:
                    if isinstance(response_, dict) and \
                            isinstance(response_.get('result'), dict) and \
                            isinstance(response_['result'].get('data'), dict) and \
                            isinstance(response_['result']['data'].get('choices'), list) and len(
                        response_['result']['data']['choices']) > 0 and \
                            isinstance(response_['result']['data']['choices'][0], dict) and \
                            isinstance(response_['result']['data']['choices'][0].get('message'), dict):

                        output_string = response_[
                            'result']['data']['choices'][0]['message']['content']
                    else:
                        return False, f"Unexpected structure in response_ object: {response_}"
                except KeyError as e:
                    return False, f"KeyError: Missing key {e}"
                except TypeError as e:
                    return False, f"TypeError: {e}"
                output_list.append(output_string)
            return True, output_list
        else:
            return False, f'''调用watt gpt接口失败 - {code}, {responses}'''
        
    def callGCPGemini(self, system_prompt, user_prompt, model=None, temperature=None, json_schema=None, max_out_tokens=2048, watttraceid="", thinking_config=None):
        if temperature:
            self.temperature = temperature
        if not model:
            model = 'gemini-2.0-flash'
        body = {
            "model": model,
            "temperature": self.temperature,
            "contents": [
                {
                    "parts": [
                        {
                            "text": system_prompt
                        }
                    ],
                    "role": "model"
                },
                {
                    "parts": [
                        {
                            "text": user_prompt
                        }
                    ],
                    "role": "user"
                }
            ],
            "maxOutputTokens": max_out_tokens,
            "stream": False
        }
        if json_schema:
            body["responseMimeType"] = "application/json"
            body["responseSchema"] = json_schema
        if thinking_config:
            body["thinkingConfig"] = thinking_config
        elif model == config.GEMINI_PRO_MODEL:
            body["thinkingConfig"] = {
                "includeThoughts": False,
                "thinkingBudget": 128
            }
        else:
            body["thinkingConfig"] = {
                "includeThoughts": False,
                "thinkingBudget": 0
            }
        status, code, response = callWattGPT.callGCPChannelChatCompletions(body=body, timeout=200, watttraceid=watttraceid)
        if status:
            try:
                candidate = response['result']['data']['candidates'][0]
                
                # Check if content and parts exist
                if 'content' in candidate and 'parts' in candidate['content'] and len(candidate['content']['parts']) > 0:
                    output_string = candidate['content']['parts'][0]['text']
                    return True, output_string
                else:
                    # Handle cases where content structure is different (e.g., MAX_TOKENS)
                    finish_reason = candidate.get('finishReason', 'UNKNOWN')
                    if finish_reason == 'MAX_TOKENS':
                        return False, f"Response truncated due to max tokens limit. Finish reason: {finish_reason}"
                    elif finish_reason in ['SAFETY', 'RECITATION']:
                        return False, f"Response blocked due to safety/recitation. Finish reason: {finish_reason}"
                    else:
                        return False, f"Unexpected response format. Finish reason: {finish_reason}, Candidate: {candidate}"
                        
            except (KeyError, IndexError, TypeError) as e:
                return False, f"Error parsing GCP Gemini response: {e}, Response: {response}"
        else:
            return False, f'''调用gcp gemin创建任务接口失败 - {code}, {response}'''
        
    def gCallGCPGemini(self, system_prompt_list, user_prompt_list, model=None, temperature=None, json_schema=None, max_out_tokens=1024, watttraceid=""):
        body_list = []
        output_list = []
        for system_prompt, user_prompt in zip(system_prompt_list, user_prompt_list):
            body = {
                "model": model if model else 'gemini-2.0-flash',
                "temperature": self.temperature,
                "contents": [
                    {
                        "parts": [
                            {
                                "text": system_prompt
                            }
                        ],
                        "role": "model"
                    },
                    {
                        "parts": [
                            {
                                "text": user_prompt
                            }
                        ],
                        "role": "user"
                    }
                ],
                "maxOutputTokens": max_out_tokens,
                "stream": False
            }
            if json_schema:
                body["responseMimeType"] = "application/json"
                body["responseSchema"] = json_schema
            body_list.append(body)
        status, code, responses = callWattGPT.gCallGCPChannelChatCompletions(body_list=body_list, timeout=300, watttraceid=watttraceid)
        if status:
            for sta_, code_, response_ in responses:
                try:
                    candidate = response_['result']['data']['candidates'][0]
                    
                    # Check if content and parts exist
                    if 'content' in candidate and 'parts' in candidate['content'] and len(candidate['content']['parts']) > 0:
                        output_string = candidate['content']['parts'][0]['text']
                        output_list.append(output_string)
                    else:
                        # Handle cases where content structure is different (e.g., MAX_TOKENS)
                        finish_reason = candidate.get('finishReason', 'UNKNOWN')
                        error_msg = f"Response error. Finish reason: {finish_reason}"
                        if finish_reason == 'MAX_TOKENS':
                            error_msg = f"Response truncated due to max tokens limit. Finish reason: {finish_reason}"
                        elif finish_reason in ['SAFETY', 'RECITATION']:
                            error_msg = f"Response blocked due to safety/recitation. Finish reason: {finish_reason}"
                        return False, error_msg
                        
                except (KeyError, IndexError, TypeError) as e:
                    return False, f"Error parsing GCP Gemini response: {e}, Response: {response_}"
            return True, output_list
        else:
            return False, f'''调用gcp gemini create task接口失败 - {code}, {responses}'''
        
    
    def callGCPMediaCreate(self, media_type, media_key, system_prompt, user_prompt, model, temperature=None, watttraceid="", json_shcema=None):
        if temperature:
            self.temperature = temperature
        body = {
            "media_type": media_type,
            "object_key": media_key,
            "model": model,
            "temperature": self.temperature,
            "contents": [
                {
                    "parts": [
                        {
                            "text": system_prompt
                        }
                    ],
                    "role": "model"
                },
                {
                    "parts": [
                        {
                            "text": user_prompt
                        }
                    ],
                    "role": "user"
                }
            ],
            "maxOutputTokens": 2048,
        }
        if json_shcema:
            body["responseMimeType"] = "application/json"
            body["responseSchema"] = json_shcema
        status, code, response = callWattGPT.callGCPChannelChatCompletionsMediaCreate(body=body, timeout=200, watttraceid=watttraceid)
        if status:
            output_string = response['result']['data']['task_id']
            return True, output_string
        else:
            return False, f'''调用gcp gemin创建任务接口失败 - {code}, {response}'''
        
    def gCallGCPMediaCreate(self, media_type_list, media_key_list, system_prompt, user_prompt, model=None, temperature=None, watttraceid=""):
        if temperature:
            self.temperature = temperature
        body_list = []
        output_list = []
        for media_type, media_key in zip(media_type_list, media_key_list):
            body = {
                "media_type": media_type,
                "object_key": media_key,
                "model": model if model else 'gemini-2.0-flash',
                "temperature": self.temperature,
                "contents": [
                    {
                        "parts": [
                            {
                                "text": system_prompt
                            }
                        ],
                        "role": "model"
                    },
                    {
                        "parts": [
                            {
                                "text": user_prompt
                            }
                        ],
                        "role": "user"
                    }
                ],
                "maxOutputTokens": 2048,
            }
            body_list.append(body)
        status, code, responses = callWattGPT.gCallGCPChannelChatCompletionsMediaCreate(body_list=body_list, timeout=300,
                                                                                        watttraceid=watttraceid)
        if status:
            for sta_, code_, response_ in responses:
                output_string = response_['result']['data']['task_id']
                output_list.append(output_string)
            return True, output_list
        else:
            return False, f'''调用gcp gemini create task接口失败 - {code}, {responses}'''

    def callGCPTaskStatus(self, task_id, watttraceid="", max_retries=24, retry_interval=2):
        """
        Poll for task status until completion or failure
        
        Args:
            task_id: The task ID to check
            watttraceid: Trace ID for logging
            max_retries: Maximum number of polling attempts
            retry_interval: Time in seconds between polling attempts
        
        Returns:
            (success_bool, result_or_error_message)
        """
        import time
        
        for attempt in range(max_retries):
            body = {
                "task_id": task_id
            }
            status, code, response = callWattGPT.callGCPChannelStatus(body=body, timeout=200,
                                                                      watttraceid=watttraceid)
            if not status:
                return False, f'''调用gcp gemini status check接口失败 - {code}, {response}'''
                
            status_mode = response['result']['data']['task_status']
            
            # Process based on status code
            if status_mode == 1:  # complete
                output_string = response['result']['data']['task_result']
                return True, output_string
                
            elif status_mode in [-1, 3]:  # failed or discarded
                error_msg = response['result']['data'].get('task_error', 'Unknown error')
                return False, f"Task failed with status {status_mode}: {error_msg}"
                
            elif status_mode == -2:  # get result URL failed
                # Can retry once more immediately before sleeping
                status, code, response = callWattGPT.callGCPChannelStatus(body=body, timeout=200,
                                                                          watttraceid=watttraceid)
                if status and response['result']['data']['task_status'] == 1:
                    return True, response['result']['data']['task_result']
                    
            # For status 0 (created), 2 (processing), or any other status, wait and retry
            if attempt < max_retries - 1:
                time.sleep(retry_interval)
        
        # If we've exhausted retries and still don't have a result
        return False, f"Timeout waiting for task {task_id} to complete after {max_retries} attempts"
    
    def callGCPTaskStatusAsync(self, task_id, watttraceid=""):
        body = {
            "task_id": task_id
        }
        status, code, response = callWattGPT.callGCPChannelStatus(body=body, timeout=200,
                                                                  watttraceid=watttraceid)
        if status:
            return True, response['result']['data']['task_status']
        else:
            return False, f'调用gcp gemini status check接口失败 - {code}, {response}'
