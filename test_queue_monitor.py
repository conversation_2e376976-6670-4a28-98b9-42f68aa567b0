#!/usr/bin/env python3
"""
Redis队列监控测试脚本
用于测试持续监控功能，解决超时问题
"""

import asyncio
import sys
import os

# 添加项目路径
sys.path.append(os.path.abspath('.'))

from task.server.account_diagnosis.enhanced_queue_monitor import monitor_queue_continuously


async def simple_message_handler(message):
    """简单的消息处理器"""
    print(f"🎯 处理队列消息:")
    print(f"   消息类型: {type(message)}")
    if isinstance(message, dict):
        print(f"   消息字段: {list(message.keys())}")
        if 'taskInfo' in message:
            task_info = message['taskInfo']
            task_id = task_info.get('taskId', 'unknown')
            print(f"   任务ID: {task_id}")
    print(f"   消息内容: {message}")
    print("-" * 50)
    return True


async def main():
    """主函数"""
    # 从命令行参数获取队列名称，或使用默认值
    queue_name = sys.argv[1] if len(sys.argv) > 1 else "diagnosis_task_queue"
    
    print(f"🚀 开始监控Redis队列: {queue_name}")
    print("按 Ctrl+C 停止监控")
    print("=" * 60)
    
    try:
        await monitor_queue_continuously(
            queue_name=queue_name,
            message_handler=simple_message_handler,
            service_name="test_monitor"
        )
    except KeyboardInterrupt:
        print("\n✅ 监控已停止")
    except Exception as e:
        print(f"❌ 监控异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())