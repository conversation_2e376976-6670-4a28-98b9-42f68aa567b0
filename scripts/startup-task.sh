#!/bin/bash
CWD=$(cd "$(dirname "$0")"; pwd)

cd $CWD
# 修改项目日志目录
LOGDIR="logs"

cd ..

if [ "${run_env}" == "dev" ]; then
  rm -f config.py
  mv config_dev.py dev_config.py

  cd utils
  python3 init_config.py dev_config
  cd ..
elif [ "${run_env}" == "uat" ]; then
  rm -f config.py
  mv config_uat.py uat_config.py

  cd utils
  python3 init_config.py uat_config
  cd ..
elif [ "${run_env}" == "prod" ]; then
  rm -f config.py
  mv config_prod.py prod_config.py

  cd utils
  python3 init_config.py prod_config
  cd ..
elif [ "${run_env}" == "test" ]; then
  rm -f config.py
  mv config_test.py test_config.py

  cd utils
  python3 init_config.py test_config
  cd ..
fi

sed -i '/^LOG_DIR/d' config.py
sed -i "/^#LOG_DIR/i\LOG_DIR = \"${LOGDIR}\"" config.py

sed -i '/^LOG_LEVEL/d' config.py
sed -i "/^#LOG_LEVEL/i\LOG_LEVEL = \"INFO\"" config.py

# start gunicorn
task_name="$1"
if [ "${task_name}" == "account_diagnosis" ]; then
  echo "start ${task_name} task"
  exec python ./task/server/account_diagnosis/run_diagnosis.py
elif [ "${task_name}" == "account_strategy" ]; then
  echo "start ${task_name} task"
  exec python ./task/server/account_diagnosis/script_for_strategy.py
elif [ "${task_name}" == "cover_gen" ]; then
  echo "start ${task_name} task"
  exec python ./task/server/account_diagnosis/script_for_cover_gen.py
elif [ "${task_name}" == "account_review" ]; then
  echo "start ${task_name} task"
  exec python ./task/server/account_diagnosis/script_for_review.py
else
  echo "task_name ${task_name} is not valid"
  exit 0
fi