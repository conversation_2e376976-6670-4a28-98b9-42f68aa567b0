#!/bin/bash
CWD=$(cd "$(dirname "$0")"; pwd)

cd $CWD
# 修改项目日志目录
LOGDIR="logs"

cd ..

if [ "${run_env}" == "dev" ]; then
  rm -f config.py
  mv config_dev.py dev_config.py

  cd utils
  python3 init_config.py dev_config
  cd ..
elif [ "${run_env}" == "test" ]; then
  rm -f config.py
  mv config_test.py test_config.py

  cd utils
  python3 init_config.py test_config
  cd ..
elif [ "${run_env}" == "uat" ]; then
  rm -f config.py
  mv config_uat.py uat_config.py

  cd utils
  python3 init_config.py uat_config
  cd ..
elif [ "${run_env}" == "prod" ]; then
  rm -f config.py
  mv config_prod.py prod_config.py

  cd utils
  python3 init_config.py prod_config
  cd ..
fi



sed -i '/^LOG_DIR/d' config.py
sed -i "/^#LOG_DIR/i\LOG_DIR = \"${LOGDIR}\"" config.py

sed -i '/^LOG_LEVEL/d' config.py
sed -i "/^#LOG_LEVEL/i\LOG_LEVEL = \"INFO\"" config.py

# 创建logs
mkdir -p ${LOGDIR}

# stop gunicorn
# /bin/ps axu | grep python3 | grep '0.0.0.0:8080' | awk '{print $2}' | xargs kill -9

# start gunicorn
exec gunicorn \
  --pid ${LOGDIR}/gunicorn.pid \
  --bind 0.0.0.0:8888\
  --worker-class gevent \
  --workers 10 \
  --threads 1 \
  --worker-connections 5 \
  --max-requests 10000 \
  --backlog 50 \
  --timeout 300 \
  --log-level info \
  run:app
