import json

def extract_tweets(file_path: str, tweet_num: int = 0):
    """
    Extract tweets from a JSON file
    :param file_path: path to the JSON file
    :param tweet_num: number of tweets to extract
    :return: list of tweets
    """

    with open(file_path, "r", encoding='utf-8') as f:
        try:
            data = json.load(f)
        except json.JSONDecodeError:
            print("Error: Invalid JSON file")
            return []
        if tweet_num == 0:
            return data

        texts = [tweet.get("full_text", "") for tweet in data]
        return data[:tweet_num]


if __name__ == '__main__':
    file_path = 'data/test.json'
    tweet_num = 40
    tweets = extract_tweets(file_path, tweet_num)
    print(tweets)